# A2A.EAI - Architettura Generale del Sistema

## Overview
Il sistema A2A.EAI è una piattaforma di Enterprise Application Integration (EAI) che gestisce l'integrazione tra sistemi interni A2A e sistemi esterni del settore energetico. La piattaforma è basata su un'architettura ibrida che combina BizTalk Server per l'orchestrazione dei flussi e Web APIs .NET per servizi specifici.

## Architettura High-Level

### Layer Architetturali

#### 1. **Integration Hub (BizTalk Server)**
- **35+ moduli di integrazione** (INT_*) specializzati per area funzionale
- **423 endpoint totali**: 365 SendPorts + 58 ReceiveLocations
- **Pattern predominanti**:
  - WCF-Custom (SQL Typed Adapter): 343 endpoint
  - WCF-BasicHttp (SOAP): 79 endpoint
  - SB-Messaging (Azure Service Bus): 1 endpoint

#### 2. **Web APIs Layer (.NET)**
- **A2A.EAI.Services.WebApi**: Proxy REST/SOAP, gestione NDUE Programma Base, listener Azure Service Bus
- **A2A.EAI.INT_PIANI_SVC.WebAPI**: Servizi SAPP/CMI (ping, eventi, richieste PVM)
- **A2A.EAI.INT_SA_SVC.WebAPI**: Validazione e trigger per flussi NDUE

#### 3. **Data Layer**
- **Database EAI/NBDO.EAI**: Repository centrale per staging e orchestrazione
- **Azure Service Bus**: Messaging asincrono per comunicazioni Virtus
- **Filesystem/Blob**: Backup DeadLetter e gestione file

### Sistemi Esterni Integrati

#### **NDUE (Sistema Centrale A2A)**
- **Endpoint**: ndue.test.a2a.eu, dctsvw034.group.local
- **Protocolli**: SOAP 1.1/1.2 (NBDOWS/*)
- **Aree**: Settlement, Metering, AIA, Conduzione, Pianificazione

#### **Sistemi Partner**
- **SAPP**: Sistema Applicativo Pianificazione Produzione
- **CMI**: Centro Monitoraggio Impianti
- **GME**: Gestore Mercati Energetici
- **ETRM**: Energy Trading & Risk Management
- **Virtus**: Sistema di ottimizzazione mercati

#### **Database Esterni**
- **SQL Server**: DCTSVW035 (EAI), DCTSVW016 (DWH), Azure SQL
- **Oracle**: DBDISPQ (GEDI), SETAPPD (SettApp), A2AQ (Quotazioni)

## Pattern di Integrazione

### 1. **Database Polling Pattern**
```
SQL TypedPolling → BizTalk Orchestration → External System
```
- Utilizzato per: Monitoraggio tabelle staging, trigger automatici
- Esempi: VirtusGediPolling, SappPvmPolling, EaiPianoPolling

### 2. **Request-Response Pattern**
```
External System → Web API → Database → Response
```
- Utilizzato per: Richieste sincrone, validazioni
- Esempi: PvmRequest, SappEvent, ValidationController

### 3. **Message Queuing Pattern**
```
BizTalk → Azure Service Bus → Background Service → Database
```
- Utilizzato per: Comunicazioni asincrone con Virtus
- Esempi: VirtusSbReceiverReplyQueue, DeadLetter handling

### 4. **Proxy Pattern**
```
Internal System → Web API Proxy → External REST/SOAP → Response
```
- Utilizzato per: Astrazione sistemi esterni
- Esempi: SappCmiProxy (REST/SOAP forwarding)

## Governance e Osservabilità

### **MsysEaiFx Core Framework**
- **Logging centralizzato** con trace-id correlation
- **Message Tracking** per audit trail completo
- **BAM (Business Activity Monitoring)** per KPI business
- **Notification Service** per alerting
- **Access Control Service** per autenticazione/autorizzazione

### **Sicurezza**
- **API Keys** (query parameter `api_key`)
- **Custom Headers**: `msysfx-secret-key`, `msysfx-app-name`
- **AccessControlService** attribute su tutti i controller
- **Swagger** con schema di sicurezza ApiKey

## Caratteristiche Tecniche

### **Scalabilità**
- Architettura modulare per area funzionale
- Separazione tra orchestrazione (BizTalk) e servizi (Web API)
- Database staging per disaccoppiamento temporale

### **Resilienza**
- DeadLetter queues per gestione errori
- Retry logic nelle orchestrazioni BizTalk
- Backup filesystem per messaggi non processabili

### **Monitoraggio**
- Trace-id per correlazione end-to-end
- BAM per metriche business real-time
- Notification service per alerting proattivo

## Deployment e Ambienti

### **Struttura Progetti**
- **Sviluppo/A2A.EAI/**: Soluzioni BizTalk per area
- **Sviluppo/A2A.EAI.WebApi/**: Web APIs specializzate
- **Sviluppo/A2A.EAI.Services/**: Servizi condivisi
- **Sviluppo/A2A.EAI.DB/**: Database projects (EAI, EAISYN)

### **Configurazione**
- **ConfigService**: Gestione runtime di connection strings e parametri
- **Binding Files**: Configurazione endpoint BizTalk per ambiente
- **appsettings**: Configurazione Web APIs per ambiente

## Metriche Sistema (AS-IS)

- **35 aree funzionali** (INT_*)
- **423 endpoint BizTalk** (365 SendPorts + 58 ReceiveLocations)
- **8 endpoint Web API** pubblici
- **3 background services** per Azure Service Bus
- **10+ database** esterni integrati
- **50+ servizi SOAP** NDUE integrati

Questa architettura supporta l'integrazione completa dell'ecosistema energetico A2A, garantendo scalabilità, resilienza e osservabilità per operazioni mission-critical.