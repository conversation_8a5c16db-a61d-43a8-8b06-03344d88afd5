# A2A.EAI - Inventario Sistemi Integrati

## Overview
Il sistema A2A.EAI integra oltre 15 sistemi esterni attraverso diverse modalità di comunicazione. Questa documentazione cataloga tutti i sistemi, le tecnologie di integrazione e i pattern utilizzati.

## Sistemi Principali

### 1. **NDUE (Sistema Centrale A2A)**

#### **Descrizione**
Sistema centrale A2A per la gestione dei dati energetici, settlement e operazioni di mercato.

#### **Endpoint**
- **Produzione**: ndue.test.a2a.eu
- **Test/Dev**: dctsvw034.group.local

#### **Protocolli di Integrazione**
- **SOAP 1.1/1.2**: NBDOWS/* services
- **REST**: N2SoapWebApiCore/* (limitato)

#### **Aree Funzionali Integrate**
- **Settlement Fisico**: Prezzi marginali, sbilanciamenti, liquidazioni
- **Settlement Economico**: Calcolo sbilanciamento economico
- **Metering**: Dati contatori gas/elettrico, energie prodotte
- **AIA (Autorizzazione Integrata Ambientale)**: Emissioni aria/acqua, consumi idrici
- **Conduzione**: Ripartizione energia, tensione effettiva
- **Pianificazione**: PVM, Picasso Import
- **Monitoraggio**: Dighe, terremoti, messaggistica LUNA
- **Mercati**: Import XBID, prezzi saldo commerciale

#### **Servizi SOAP Principali**
```
http://ndue.test.a2a.eu/NBDOWS/BDM/F6*.asmx
http://ndue.test.a2a.eu/NBDOWS/AIA/WS_AIA_*.asmx
http://ndue.test.a2a.eu/NBDOWS/CalcoloSbilanciamentoEconomico.asmx
http://ndue.test.a2a.eu/N2SoapWebApiCore/Dighe/WsControlliDigheTerremoti.asmx
```

### 2. **SAPP (Sistema Applicativo Pianificazione Produzione)**

#### **Descrizione**
Sistema per la pianificazione e gestione della produzione energetica.

#### **Modalità di Integrazione**
- **Web API REST**: A2A.EAI.INT_PIANI_SVC.WebAPI
- **BizTalk Polling**: Database EAI per eventi e piani

#### **Endpoint Esposti**
- `GET /api/Sapp/Ping`: Health check
- `POST /api/Sapp/SappEvent`: Notifica eventi
- `POST /api/Sapp/PvmRequest`: Richiesta piani PVM

#### **Flussi Principali**
- **PVM (Piano di Vendita Mercato)**: Richiesta on-demand tramite stored procedure `[dbo].[PianoRequest]`
- **Eventi**: Tracking eventi con BAM e notifiche
- **Gettoni**: Polling database per elaborazione gettoni

### 3. **CMI (Centro Monitoraggio Impianti)**

#### **Descrizione**
Sistema di monitoraggio e controllo degli impianti di produzione.

#### **Modalità di Integrazione**
- **Web API REST**: Condivisa con SAPP (INT_PIANI_SVC)
- **BizTalk**: INT_BI_CMI per sincronizzazione dati

#### **Flussi Principali**
- **Impianti**: Sincronizzazione anagrafica impianti
- **Mercati MRR**: Dati mercato regolazione
- **Date/Ore**: Sincronizzazione calendari operativi

### 4. **GME (Gestore Mercati Energetici)**

#### **Descrizione**
Gestore dei mercati elettrici italiani.

#### **Modalità di Integrazione**
- **BizTalk**: INT_GME per import/export dati mercato

#### **Flussi Principali**
- **Offerte**: Import offerte MB, MRR
- **Prezzi**: Import prezzi zonali MRR, prezzi presentati AFRR
- **Notifiche**: Bid notifications e unit schedule

### 5. **Virtus (Sistema Ottimizzazione Mercati)**

#### **Descrizione**
Sistema di ottimizzazione per mercati energetici e trading.

#### **Modalità di Integrazione**
- **Azure Service Bus**: Comunicazione asincrona
- **BizTalk**: Multiple INT_MRKOPT_* modules
- **Database**: SQL Server per staging e risultati

#### **Moduli Specializzati**
- **INT_MRKOPT_GEDI**: Integrazione con sistema GEDI (Oracle)
- **INT_MRKOPT_BID**: Gestione offerte mercato
- **INT_MRKOPT_EXPORT**: Export risultati ottimizzazione
- **INT_MRKOPT_IMPORT**: Import dati per ottimizzazione
- **INT_MRKOPT_N2**: Integrazione con NDUE
- **INT_MRKOPT_QTZ**: Gestione quotazioni

#### **Flussi Azure Service Bus**
- **Queue**: mrk-optimizer-biztalk-to-virtus
- **Background Services**: VirtusSbReceiverReplyQueue, VirtusSbReceiverDeadLetter

### 6. **ETRM (Energy Trading & Risk Management)**

#### **Descrizione**
Sistema per trading energetico e gestione rischi.

#### **Modalità di Integrazione**
- **BizTalk**: INT_ETRM

#### **Flussi Principali**
- **Contratti**: SettApp contract management
- **Prezzi**: Contract price management
- **Soglie**: Power threshold management

### 7. **PI System (Plant Information)**

#### **Descrizione**
Sistema di acquisizione dati da impianti industriali e sensori.

#### **Modalità di Integrazione**
- **BizTalk**: INT_PI (37 endpoint)
- **PI Web Services**: PITimeSeries.svc

#### **Endpoint PI**
- `http://epmivpi001.edipower.produzione.energia/PIWebServices/PITimeSeries.svc`

#### **Flussi Principali**
- **AIA**: Dati ambientali (aria, acqua, emissioni)
- **Metering**: Dati produzione e consumo
- **Meteo**: Dati meteorologici e idrologici
- **Strumentistica**: Tensione, temperatura, pressione
- **UVAP**: Unità di misura e controllo

### 8. **SettApp (Settlement Application)**

#### **Descrizione**
Sistema per settlement e fatturazione energetica.

#### **Modalità di Integrazione**
- **BizTalk**: INT_SA_DB_* modules
- **Web API**: A2A.EAI.INT_SA_SVC.WebAPI
- **Oracle**: Database SETAPPD

#### **Moduli Specializzati**
- **INT_SA_DB_VAL**: Validazione e calcolo sbilanciamenti
- **INT_SA_DB_QTSA**: Gestione quotazioni e indici
- **INT_SA_FATT**: Fatturazione

#### **Endpoint Web API**
- `POST /api/Validation/Validate`: Trigger validazione NDUE

### 9. **GEDI (Gestione Dispacciamento)**

#### **Descrizione**
Sistema per gestione dispacciamento e disponibilità impianti.

#### **Modalità di Integrazione**
- **Oracle**: Database DBDISPQ
- **BizTalk**: INT_MRKOPT_GEDI, INT_EXPORT

#### **Flussi Principali**
- **Disponibilità**: Gestione disponibilità impianti
- **Vincoli**: Vincoli di rete
- **Prezzi**: Prezzi attesi e previsioni

### 10. **Database Esterni**

#### **SQL Server Instances**
- **DCTSVW035**: Database EAI principale
- **DCTSVW016**: Data Warehouse (DWH_NBDO, DWH_EZEKE, NbdoEaiBi)
- **epmivtnbdo**: Database NBDO produzione
- **Azure SQL**: a2a-noprod-cruscottosapp-sqlsrv.database.windows.net

#### **Oracle Instances**
- **DBDISPQ**: Sistema GEDI
- **SETAPPD**: Sistema SettApp
- **A2AQ**: Sistema quotazioni

### 11. **Sistemi di Monitoraggio e Controllo**

#### **Protezione Civile**
- **Modalità**: File-based integration
- **Flussi**: Allerte dighe, eventi sismici

#### **LUNA (Logbook Unico Nazionale)**
- **Modalità**: SOAP Web Services
- **Endpoint**: `http://ndue.test.a2a.eu/NBDOWS/MessaggiLUNA.asmx`

#### **ARPA (Agenzia Regionale Protezione Ambiente)**
- **Modalità**: SOAP Web Services
- **Flussi**: Dati ambientali e qualità aria

### 12. **Sistemi Mercato**

#### **XBID (Cross-Border Intraday)**
- **Modalità**: SOAP integration con NDUE
- **Flussi**: Import prezzi, physical programs

#### **Terna (Gestore Rete Trasmissione)**
- **Modalità**: SOAP Web Services
- **Flussi**: Dati metering, scambi zonali, sbilanciamenti

## Riepilogo Tecnologie di Integrazione

### **Protocolli Utilizzati**
- **SOAP 1.1/1.2**: 79 endpoint (18.7%)
- **SQL Typed Procedures**: 343 endpoint (81.1%)
- **Azure Service Bus**: 1 endpoint (0.2%)
- **REST APIs**: 8 endpoint pubblici
- **Oracle DB**: 3 istanze
- **File System**: Backup e staging

### **Pattern di Comunicazione**
- **Sincrono**: SOAP, REST APIs
- **Asincrono**: Azure Service Bus, Database polling
- **Batch**: File-based, scheduled procedures
- **Real-time**: TypedPolling, event-driven

### **Sicurezza e Governance**
- **Autenticazione**: API Keys, custom headers
- **Autorizzazione**: AccessControlService
- **Audit**: Message tracking, BAM
- **Monitoring**: MsysEaiFx framework

Questo inventario copre l'intero ecosistema di integrazione A2A.EAI, evidenziando la complessità e l'ampiezza delle integrazioni nel settore energetico.