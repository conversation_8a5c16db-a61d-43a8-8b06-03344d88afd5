# A2A.EAI - Mappatura Flussi per Area Funzionale

## Overview
Il sistema A2A.EAI è organizzato in 35+ moduli specializzati (INT_*), ognuno responsabile di specifiche aree funzionali del business energetico. Questa documentazione mappa tutti i flussi, endpoint e responsabilità per area.

## Classificazione per Tipologia di Business

### **🏭 PRODUZIONE E IMPIANTI**

#### **INT_PI (Plant Information) - 37 endpoint**
**Responsabilità**: Gestione dati impianti, sensori e produzione

**Flussi verso NDUE (17 endpoint)**:
- **AIA (Autorizzazione Integrata Ambientale)**:
  - `PiExportN2AiaAriaWs` → WS_AIA_Aria.asmx (emissioni aria)
  - `PiExportN2AiaAriaTotWs` → WS_AIA_AriaEmissioniTOT.asmx (totali emissioni)
  - `PiExportN2AiaAcquaEmissioniWs` → WS_AIA_AcquaEmissioni.asmx (emissioni acqua)
  - `PiExportN2AiaConsumiIdriciWs` → WS_AIA_ConsumiIdrici.asmx (consumi idrici)

- **Dati Produzione**:
  - `PiExportN2RipartizioneEnergiaWs` → Conduzione/RipartizioneEnergia.asmx
  - `PiExportProduzioniFvOut` → ProduzioniFv.asmx (fotovoltaico)
  - `PiExportN2MeteoWsOut` → F2DatiMeteoIdroImport.asmx (meteo/idro)

- **Strumentistica**:
  - `PiExportN2TensioneWsOut` → ImportTensioneEffettiva.asmx
  - `PiExportN2ArpaWs` → DatiARPA.asmx (qualità aria)

**Flussi interni (19 endpoint)**:
- **PI System Integration**: `sptPITimeSeriesService` → PITimeSeries.svc
- **Database Operations**: Insert/Update per staging dati impianti
- **Polling**: `WcfReceiveLocation_SqlAdapterBinding_TypedPolling_idPiImportPvmPolling`

#### **INT_ACS (Ambiente, Clima, Sostenibilità) - 11 endpoint**
**Responsabilità**: Gestione dati ambientali e sostenibilità

**Flussi verso NDUE (6 endpoint)**:
- **Gas/Energia**:
  - `WcfSendPort_ConsumiRetragas_*` → ConsumiRetragas.asmx (SOAP 1.1/1.2)
  - `WcfSendPort_ConsumiUnareti_*` → ConsumiUnareti.asmx
  - `WcfSendPort_Ricavi_*` → Ricavi.asmx

**Flussi interni (5 endpoint)**:
- **CO2 Management**: `ACSCO2EmesseRetragasInsert`, `ACSCO2EmesseUnaretiInsert`
- **Vendita Calore**: `ACSVenditaCaloreBresciaInsert`, `ACSVenditaCaloreMilanoInsert`

#### **INT_AMBIENTE (Monitoraggio Ambientale) - 2 endpoint**
**Responsabilità**: Dati laboratorio e monitoraggio ambientale

**Flussi interni**:
- `sptDatiLaboratorioInsert` → Database EAI
- `sptDatiLaboratorioCheck` → Validazione dati laboratorio

### **⚡ MERCATI ENERGETICI**

#### **INT_GME (Gestore Mercati Energetici) - 16 endpoint**
**Responsabilità**: Integrazione con GME per mercati elettrici

**Flussi verso NDUE (4 endpoint)**:
- **Prezzi e Offerte**:
  - `N2PrezziZonaliMrrImport` → F6PrezziZonaliMRR.asmx
  - `PrezziPresentatiAfrrWs` → F6PrezziPresentatiAFRR.asmx
  - `N2OfferteMbImport` → F6OfferteMB.asmx
  - `N2OfferteMrrImport` → F6OfferteMRR.asmx

**Flussi interni (11 endpoint)**:
- **Virtus Integration**: `VirtusUnitScheduleInsert`, `VirtusGmePrezziEsitiZonaliToVirtusCommon`
- **Bid Management**: `GmeBidNotificationInsert`
- **Polling**: `VirtusUnitScheduleMktResExtPolling`

#### **INT_MERCATO (Mercati Energia) - 31 endpoint**
**Responsabilità**: Gestione mercati energetici e import dati Terna

**Flussi verso NDUE (30 endpoint)**:
- **Import Terna**:
  - `WcfSendPort_ImportScambioZonaliTerna_*` → ImportScambiZonaliTerna.asmx
  - `sptNbdoOfferteMsdImport` → F7OfferteMSD.asmx
- **Database NBDO**: `sptNbdoMercatoDelete` → Database epmivtnbdo

**Flussi interni (1 endpoint)**:
- `sptVirtusMercatoInputMsdToVirtusRequest` → Staging Virtus

#### **INT_XBID (Cross-Border Intraday) - 9 endpoint**
**Responsabilità**: Mercato infragiornaliero transfrontaliero

**Flussi verso NDUE (4 endpoint)**:
- `WcfSendPort_ImportXbid_*` → ImportXbid.asmx (SOAP 1.1/1.2)
- `N2ImportPrezziSaldoCommerciale` → PrezziSaldoCommerciale.asmx

**Flussi interni (4 endpoint)**:
- **Physical Programs**: `XbidPhysicalProgramInsert`, `XbidPhysicalProgramUpdateStatus`
- **Virtus Integration**: `VirtusFeasibilityIntervalsToVirtusRequest`

### **🔄 OTTIMIZZAZIONE MERCATI (VIRTUS)**

#### **INT_MRKOPT_GEDI (GEDI Integration) - 48 endpoint**
**Responsabilità**: Integrazione con sistema GEDI per dispacciamento

**Flussi Oracle GEDI (Multiple)**:
- **Disponibilità**: `VirtusGediDispTableSelect`, `VirtusGediDispPrimariaTableSelect`
- **Vincoli**: `VirtusGediVincoliDiReteTableSelect`
- **Prezzi**: `VirtusGediPrezziAttesiInsert`, `VirtusGediMiglioriPrevisioniInsert`

**Polling (14 endpoint)**:
- `VirtusGediUPSAPolling`, `VirtusGediNomineUCPolling`, `VirtusGediDispPrimariaPolling`

#### **INT_MRKOPT_EXPORT (Export Ottimizzazione) - 13 endpoint**
**Responsabilità**: Export risultati ottimizzazione Virtus

**Flussi principali**:
- **Risultati**: `VirtusGenericReplyToEsitoMECentrale`, `VirtusGenericReplyToTIMM`
- **GEDI**: `VirtusGenericReplyToGEDIAux`
- **Offerte**: `VirtusGenericReplyToIpexOffertePce`

#### **INT_MRKOPT_IMPORT (Import Ottimizzazione) - 8 endpoint**
**Responsabilità**: Import dati per ottimizzazione

**Flussi principali**:
- **Notes**: `VirtusNotesUpdateStatus`, `VirtusNotesPolling`
- **PCE Buses**: `VirtusPCEBusesInsert`
- **Guidelines**: `VirtusHydroGuidelinesToVirtusRequest`

#### **INT_MRKOPT_N2 (NDUE Integration) - 5 endpoint**
**Responsabilità**: Integrazione Virtus-NDUE

**Flussi principali**:
- **PVM**: `VirtusPvmGetData`, `VirtusPvmUpdateStatus`, `VirtusPvmPolling`
- **Programma Base**: `VirtusN2PrgBaseUpdateStatus`, `VirtusN2PrgBasePolling`

#### **INT_SC (Sistema Centrale) - 24 endpoint**
**Responsabilità**: Comunicazione con sistema centrale via Service Bus

**Azure Service Bus**:
- `SCToVirtus` → sb://a2a-tst-integrationservices.servicebus.windows.net/mrk-optimizer-biztalk-to-virtus/

**Flussi principali**:
- **RUP**: `VirtusRupPrepareUpdateStatus`, `VirtusRupProcessUpdateStatus`
- **Reports**: `VirtusGenericReplyToDEI`, `VirtusGenericReplyToTPAA`

### **📊 PIANIFICAZIONE E PROGRAMMI**

#### **INT_PVM (Piano di Vendita Mercato) - 37 endpoint**
**Responsabilità**: Gestione piani di vendita e programmi mercato

**Flussi verso NDUE (30 endpoint)**:
- **Import Quantità**: `sptNbdoEsitoMA1MGPImport` → F6QuantityImport.asmx
- **Sbilanciamenti**: `sptNbdoFmsImport` → SbilanciamentiImportFms.asmx
- **Staging NBDO**: `sptNbdoInsertStaging` → Database epmivtnbdo

**Flussi interni (6 endpoint)**:
- **RUP**: `sptEaiRupUpdateStatus`, `sptEaiRupStagingInsert`
- **Unità Produttive**: `sptPvmUnitaProduttiveAbilitazione`
- **CMI**: `sptCmiSemibandaInsert` → Azure SQL DWH_CMI

#### **INT_PIANI (Pianificazione) - 9 endpoint**
**Responsabilità**: Gestione piani produzione SAPP/CMI

**Flussi interni**:
- **Piani**: `EaiPianoSappInsert`, `EaiPianoCmiInsert`, `EaiPianoUpdateStatus`
- **Gettoni**: `EaiGettoniSappInsert`, `EaiGettoniCmiInsert`
- **Polling**: `EaiPianoPolling`, `EaiGettoniPolling`

#### **INT_SAPP (Sistema Applicativo Pianificazione) - 16 endpoint**
**Responsabilità**: Integrazione con sistema SAPP

**Flussi verso NDUE (1 endpoint)**:
- `sptNbdoMercIGCruscottoUpdateStatus` → Database NBDO.EAI

**Flussi interni (11 endpoint)**:
- **Forecast**: `SAPPOptitForecastInsert`, `SAPPOptitForecastUpdateStatus`
- **PVM**: `sptNbdoPvmWeb`, `rlcNbdoPvm`
- **Polling**: `SAPPOptitForecastPolling`, `rlcSappPvmGettoni`

### **💰 SETTLEMENT E FATTURAZIONE**

#### **INT_SETTLEMENT (Settlement Fisico) - 7 endpoint**
**Responsabilità**: Settlement fisico e liquidazioni

**Flussi verso NDUE (3 endpoint)**:
- **Corrispettivi**: `sppCorrispettivoNonArbitraggioMacrozonaleWs` → CorrispettivoNonArbitraggioMacrozonale.asmx
- **Liquidazioni**: `sptLiquidazioneGiornalieraWsImport` → F6LiquidazioneGiornalieraPCE.asmx

**Flussi interni (3 endpoint)**:
- **Metering**: `sptDatiMeteringInsertStg`
- **Liquidazioni**: `sptLiquidazioneGiornalieraUpdate`, `sptLiquidazioneGiornalieraGetData`

#### **INT_SA_DB_VAL (Settlement Validation) - 18 endpoint**
**Responsabilità**: Validazione settlement e calcolo sbilanciamenti

**Flussi verso NDUE (2 endpoint)**:
- `WcfSendPort_CalcoloSbilanciamentoEconomico_*` → CalcoloSbilanciamentoEconomico.asmx (SOAP 1.1/1.2)

**Flussi Oracle SettApp (Multiple)**:
- **Sbilanciamenti**: `SettAppSbilTableSelect`, `SettAppSbilEimmTableSelect`
- **Calcoli**: `SettAppCalcoloSbilUpdateStatus`

#### **INT_SA_DB_QTSA (Quotazioni Settlement) - 4 endpoint**
**Responsabilità**: Gestione quotazioni e indici

**Flussi Oracle**:
- **SettApp**: `SettAppIndexesTable` → Database SETAPPD
- **Quotazioni**: `QuotazioniIndexesTableSelect` → Database A2AQ

#### **INT_SA_FATT (Fatturazione) - 2 endpoint**
**Responsabilità**: Gestione fatturazione

**Flussi interni**:
- `SettAppFatturaEneforbilInsert`, `SettAppFatturaProcessUpdateStatus`

### **📈 BUSINESS INTELLIGENCE E REPORTING**

#### **INT_BI (Business Intelligence) - 19 endpoint**
**Responsabilità**: Integrazione con Data Warehouse

**Flussi verso NDUE (2 endpoint)**:
- **Forecast**: `ForecastInsert` → Database DWH_NBDO
- **Budget**: `BudgetInsert` → Database DWH_NBDO

**Flussi DWH EZEKE (Multiple)**:
- **Processi**: `EzekeDispaccProcess`, `EzekeAnelloProcess`
- **Insert**: `EzekeAnelloInsert`, `EzekeDispaccInsert`
- **Polling**: `EzekeAnelloProcessPolling`, `EzekeDispaccProcessPolling`

#### **INT_BI_CMI (BI Centro Monitoraggio) - 10 endpoint**
**Responsabilità**: BI specifico per CMI

**Flussi verso NDUE (1 endpoint)**:
- `N2MessaggiErroriAFRR` → F6MessaggiErroriAFRR.asmx

**Flussi DWH CMI**:
- **Impianti**: `BiCmiImpiantiInsert` → Database DWH_CMI
- **Mercati**: `MercatiMrrDatiInsert` → Azure SQL DWH_CMI
- **Date/Ore**: `DateOreQuartiEstesaInsert`

### **🔧 IMPORT/EXPORT E UTILITÀ**

#### **INT_IMPORT (Import Dati) - 34 endpoint**
**Responsabilità**: Import dati da sistemi esterni

**Flussi verso NDUE (28 endpoint)**:
- **PVM/GRTN**: `sptNbdoPvmcGrtnImport` → SbilanciamentiImportPvmGrtn.asmx
- **Meteo**: `WcfSendPort_F2DatiMeteoIdroImport_*` → F2DatiMeteoIdroImport.asmx
- **Metering**: `sptDatiMeteringImport` → F10DatiMeteringTernaImport.asmx

**Flussi interni (5 endpoint)**:
- **SCADA**: `sptDatiIdriciScadaWS` → DatiIdriciScada.asmx
- **Segnanti**: `sptImportSegnanti`
- **ERG**: `ErgEnergiaImmessaUpdateStatus`

#### **INT_EXPORT (Export Dati) - 5 endpoint**
**Responsabilità**: Export dati verso sistemi esterni

**Flussi interni**:
- **ERG/GEDI**: `sptErgIndisponibilitaToGediUpdateStatus`, `sptDisponibilitaErgGediInsert`
- **Polling**: `rptErgGediDisponibilitaToGediPolling`

### **🚨 MONITORAGGIO E EVENTI**

#### **INT_EXTEVENT (Eventi Esterni) - 9 endpoint**
**Responsabilità**: Gestione eventi esterni (terremoti, dighe)

**Flussi verso NDUE (2 endpoint)**:
- **Dighe**: `sptControlliDigheTerremotiWs` → WsControlliDigheTerremoti.asmx
- **BI**: `sptBiQuakeAnalysisInsert` → Database NbdoEaiBi

**Flussi interni (5 endpoint)**:
- **Eventi**: `sptEaiQuakeEventInsert`, `sptN2QuakeEventInsert`
- **Protezione Civile**: `sptProtezioneCivileUpdateStatus`

#### **INT_LUNA (Logbook Unico) - 6 endpoint**
**Responsabilità**: Gestione messaggistica LUNA

**Flussi verso NDUE (1 endpoint)**:
- `MessaggiLunaWs` → MessaggiLUNA.asmx

**Flussi interni (3 endpoint)**:
- **Messaggi**: `LunaMessaggiInsert`
- **Status**: `LunaN2UpdateStatus`, `LunaVCRUpdateStatus`

#### **INT_MONITOR (Monitoraggio) - 2 endpoint**
**Responsabilità**: Monitoraggio code e processi

**Flussi interni**:
- `MonitorQueueUpdateStatus`, `MonitorQueuePolling`

### **🏢 GESTIONE AZIENDALE**

#### **INT_BDE (Bollettino Dati Esercizio) - 5 endpoint**
**Responsabilità**: Gestione BDE e validazioni

**Flussi interni**:
- **BDE**: `sptEaiBdeSr`, `sptEaiBdeLb`, `sptEaiBdeCb`, `sptEaiBdeRc`
- **Email**: `sptEaiBdeGetEmailAddr`

#### **INT_ETRM (Energy Trading) - 4 endpoint**
**Responsabilità**: Trading energetico e risk management

**Flussi interni**:
- **Contratti**: `EtrmSettAppContractInsert`, `EtrmSettAppContractPriceInsert`
- **Soglie**: `EtrmSettAppPowerThresholdInsert`
- **Status**: `EtrmSettAppFileUpdateStatus`

#### **INT_LPREPORT (LP Reporting) - 6 endpoint**
**Responsabilità**: Reporting LP (Load Profile)

**Flussi verso NDUE (1 endpoint)**:
- `N2LPReportConsumoContatoriGas` → LpRConsumoContatoriGas.asmx

**Flussi interni (4 endpoint)**:
- **Transform**: `LPReportManualDatiMeteringGasTransform`, `EAILPReportDatiMeteringGasTransform`
- **Status**: `EAILPReportUpdateStatus`, `EAILPReportResubmit`

## Riepilogo per Tipologia

### **Distribuzione Endpoint per Area Business**
- **Mercati e Ottimizzazione**: 143 endpoint (33.8%)
- **Produzione e Impianti**: 50 endpoint (11.8%)
- **Settlement e Fatturazione**: 49 endpoint (11.6%)
- **Pianificazione**: 62 endpoint (14.7%)
- **Import/Export**: 39 endpoint (9.2%)
- **Monitoraggio**: 17 endpoint (4.0%)
- **BI e Reporting**: 29 endpoint (6.9%)
- **Gestione Aziendale**: 15 endpoint (3.5%)
- **Utilità**: 19 endpoint (4.5%)

### **Pattern di Integrazione Predominanti**
1. **Database Polling** (58 ReceiveLocations): Monitoraggio continuo tabelle staging
2. **SOAP Export** (79 SendPorts): Invio dati verso NDUE e sistemi esterni
3. **SQL Operations** (343 SendPorts): Operazioni database per staging e processing
4. **Service Bus** (1 SendPort): Comunicazione asincrona con Virtus

Questa mappatura evidenzia la complessità e l'ampiezza dell'ecosistema A2A.EAI, coprendo l'intera catena del valore energetico.