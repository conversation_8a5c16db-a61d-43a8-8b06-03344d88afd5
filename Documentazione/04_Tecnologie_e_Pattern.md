# A2A.EAI - Tecnologie e Pattern Architetturali

## Overview
Il sistema A2A.EAI implementa un'architettura enterprise ibrida che combina tecnologie Microsoft per l'integrazione con pattern moderni di messaging e API. Questa documentazione analizza le tecnologie utilizzate e i pattern architetturali implementati.

## Stack Tecnologico

### **🔧 Microsoft BizTalk Server**

#### **Versione e Configurazione**
- **BizTalk Server 2020** (presumibilmente)
- **35+ applicazioni BizTalk** (INT_*)
- **423 endpoint totali**: 365 SendPorts + 58 ReceiveLocations

#### **Adapter Utilizzati**
- **WCF-Custom (SQL Typed)**: 343 endpoint (81.1%)
  - Stored procedure calls
  - Database polling
  - Bulk operations
- **WCF-BasicHttp (SOAP)**: 79 endpoint (18.7%)
  - SOAP 1.1/1.2 services
  - External system integration
- **SB-Messaging (Azure Service Bus)**: 1 endpoint (0.2%)
  - Asynchronous messaging with Virtus

#### **Orchestration Patterns**
- **Request-Response**: Synchronous SOAP calls
- **Fire-and-Forget**: Database inserts/updates
- **Polling**: Continuous monitoring of staging tables
- **Correlation**: Message correlation via BAM tracking

### **🌐 .NET Web APIs**

#### **Framework e Versioni**
- **.NET Framework 4.x** (legacy APIs)
- **.NET Core/5+** (modern APIs)
- **ASP.NET Web API 2** per servizi REST

#### **Progetti Web API**
1. **A2A.EAI.Services.WebApi**
   - **Responsabilità**: Proxy REST/SOAP, NDUE integration, Azure Service Bus listeners
   - **Endpoint**: 8 controller pubblici
   - **Pattern**: Proxy, Adapter, Observer

2. **A2A.EAI.INT_PIANI_SVC.WebAPI**
   - **Responsabilità**: SAPP/CMI integration
   - **Endpoint**: Ping, Events, PVM requests
   - **Pattern**: Command, Event-driven

3. **A2A.EAI.INT_SA_SVC.WebAPI**
   - **Responsabilità**: Settlement validation triggers
   - **Endpoint**: Validation controller
   - **Pattern**: Trigger, Validation

#### **Middleware e Framework**
- **Swagger/OpenAPI**: API documentation
- **MsysEaiFx**: Custom framework per governance
- **AccessControlService**: Security attribute
- **Custom Headers**: msysfx-secret-key, msysfx-app-name

### **🗄️ Database Technologies**

#### **SQL Server**
- **DCTSVW035**: Database EAI principale
- **DCTSVW016**: Data Warehouse (DWH_NBDO, DWH_EZEKE, NbdoEaiBi)
- **epmivtnbdo**: Database NBDO produzione
- **Azure SQL**: a2a-noprod-cruscottosapp-sqlsrv.database.windows.net

#### **Oracle**
- **DBDISPQ**: Sistema GEDI (dispacciamento)
- **SETAPPD**: Sistema SettApp (settlement)
- **A2AQ**: Sistema quotazioni

#### **Database Patterns**
- **Staging Tables**: Disaccoppiamento temporale
- **Stored Procedures**: Business logic nel database
- **Polling**: Continuous monitoring via TypedPolling
- **Bulk Operations**: Batch processing per performance

### **☁️ Azure Services**

#### **Azure Service Bus**
- **Namespace**: a2a-tst-integrationservices.servicebus.windows.net
- **Queue**: mrk-optimizer-biztalk-to-virtus
- **Pattern**: Publish-Subscribe, Message Queuing

#### **Background Services**
- **VirtusSbReceiverReplyQueue**: Reply message processing
- **VirtusSbReceiverDeadLetter**: Error handling
- **VirtusSbReceiverService**: Main message processing

## Pattern Architetturali

### **1. Enterprise Integration Patterns**

#### **Message Router**
```
BizTalk Orchestration → Route by Content → Multiple Endpoints
```
- **Implementazione**: Content-based routing nelle orchestrazioni
- **Uso**: Routing messaggi verso sistemi diversi basato su contenuto

#### **Message Translator**
```
Source Format → BizTalk Maps → Target Format
```
- **Implementazione**: BizTalk Maps per trasformazione dati
- **Uso**: Conversione tra formati SOAP, SQL, JSON

#### **Publish-Subscribe**
```
Publisher → Azure Service Bus → Multiple Subscribers
```
- **Implementazione**: Azure Service Bus con multiple queue
- **Uso**: Comunicazione asincrona con Virtus

#### **Polling Consumer**
```
Database Table → TypedPolling → BizTalk Processing
```
- **Implementazione**: 58 ReceiveLocations con TypedPolling
- **Uso**: Monitoraggio continuo tabelle staging

### **2. API Design Patterns**

#### **API Gateway Pattern**
```
External Client → Web API → Multiple Backend Services
```
- **Implementazione**: A2A.EAI.Services.WebApi come gateway
- **Benefici**: Single entry point, cross-cutting concerns

#### **Proxy Pattern**
```
Client → Proxy API → External SOAP/REST Service
```
- **Implementazione**: SappCmiProxy per forwarding REST/SOAP
- **Benefici**: Protocol translation, caching, security

#### **Command Pattern**
```
API Request → Command Object → Business Logic Execution
```
- **Implementazione**: PvmRequest, SappEvent controllers
- **Benefici**: Decoupling, auditing, retry logic

### **3. Data Access Patterns**

#### **Repository Pattern**
```
Business Logic → Repository Interface → Data Access Layer
```
- **Implementazione**: Stored procedures come repository
- **Benefici**: Separation of concerns, testability

#### **Unit of Work**
```
Business Transaction → Multiple Repository Operations → Single Commit
```
- **Implementazione**: Database transactions nelle stored procedures
- **Benefici**: Data consistency, rollback capability

#### **Staging Pattern**
```
External Data → Staging Tables → Validation → Production Tables
```
- **Implementazione**: Tabelle staging per tutti i flussi
- **Benefici**: Data quality, error handling, reprocessing

### **4. Messaging Patterns**

#### **Request-Reply**
```
Client → Request Message → Processing → Reply Message → Client
```
- **Implementazione**: SOAP services con BizTalk orchestrations
- **Uso**: Synchronous operations con NDUE

#### **Fire-and-Forget**
```
Client → Message → Queue → Async Processing
```
- **Implementazione**: Database inserts via SQL adapter
- **Uso**: Logging, auditing, background processing

#### **Dead Letter Queue**
```
Failed Message → Dead Letter Queue → Manual Intervention
```
- **Implementazione**: VirtusSbReceiverDeadLetter service
- **Uso**: Error handling per Azure Service Bus

### **5. Security Patterns**

#### **API Key Authentication**
```
Client Request → API Key Validation → Access Granted/Denied
```
- **Implementazione**: api_key query parameter
- **Uso**: Simple authentication per API pubbliche

#### **Custom Header Authentication**
```
Client Request → Custom Headers → AccessControlService → Validation
```
- **Implementazione**: msysfx-secret-key, msysfx-app-name headers
- **Uso**: Enhanced security per servizi interni

#### **Attribute-Based Access Control**
```
Controller Method → AccessControlService Attribute → Authorization Check
```
- **Implementazione**: [AccessControlService] su tutti i controller
- **Uso**: Declarative security

## Governance e Osservabilità

### **MsysEaiFx Framework**

#### **Logging Pattern**
```
Operation Start → Trace ID Generation → Correlated Logging → Operation End
```
- **Implementazione**: Custom logging framework
- **Benefici**: End-to-end traceability

#### **Message Tracking**
```
Message Received → BAM Tracking → Processing Steps → Completion Status
```
- **Implementazione**: BizTalk BAM + custom tracking
- **Benefici**: Business activity monitoring

#### **Notification Pattern**
```
Error/Event → Notification Service → Alert Distribution
```
- **Implementazione**: Custom notification service
- **Benefici**: Proactive monitoring

### **Configuration Management**

#### **ConfigService Pattern**
```
Application Start → ConfigService → Runtime Configuration → Dynamic Updates
```
- **Implementazione**: Centralized configuration service
- **Benefici**: Environment-specific settings, runtime updates

#### **Binding Files Pattern**
```
Deployment → Environment-Specific Binding → BizTalk Configuration
```
- **Implementazione**: Separate binding files per environment
- **Benefici**: Environment isolation, automated deployment

## Performance e Scalabilità

### **Caching Patterns**
- **Database Connection Pooling**: SQL Server connection reuse
- **Configuration Caching**: ConfigService in-memory cache
- **Message Caching**: BizTalk message box optimization

### **Batch Processing**
- **Bulk Operations**: SQL bulk inserts per performance
- **Scheduled Processing**: Time-based triggers per batch jobs
- **Parallel Processing**: Multiple BizTalk host instances

### **Load Balancing**
- **BizTalk Host Clustering**: Multiple BizTalk servers
- **Database Load Balancing**: Read replicas per reporting
- **API Load Balancing**: Multiple Web API instances

Questa architettura tecnologica supporta un ecosistema enterprise complesso, garantendo scalabilità, resilienza e manutenibilità per operazioni mission-critical nel settore energetico.