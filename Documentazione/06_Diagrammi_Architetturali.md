# A2A.EAI - Diagrammi Architetturali

## Overview
Questa documentazione presenta i diagrammi architetturali del sistema A2A.EAI utilizzando Mermaid per visualizzare l'architettura generale, i flussi principali e le integrazioni per area funzionale.

## 1. Architettura Generale del Sistema

### Diagramma High-Level Architecture

Il diagramma sopra mostra l'architettura generale del sistema A2A.EAI con:
- **External Systems**: Sistemi esterni integrati (NDUE, SAPP, CMI, GME, Virtus, GEDI, PI)
- **Web APIs Layer**: 3 progetti Web API specializzati
- **Integration Hub**: BizTalk Server con 35+ moduli INT_*
- **Data Layer**: Database SQL Server, Oracle e Azure Service Bus
- **Governance**: Framework MsysEaiFx per monitoring e sicurezza

## 2. Flussi di Integrazione per Area Business

### Diagramma Mercati Energetici

Il diagramma sopra mostra i flussi di integrazione per i mercati energetici, evidenziando:
- **Mercati Esterni**: GME, Terna, XBID per dati di mercato
- **Virtus Optimization**: Sistema di ottimizzazione con comunicazione asincrona via Azure Service Bus
- **BizTalk Modules**: 5 moduli specializzati per mercati (143 endpoint totali)
- **NDUE Integration**: Export verso sistema centrale via SOAP services

### Diagramma Pianificazione e Produzione

Il diagramma sopra illustra i flussi per pianificazione e produzione:
- **External Systems**: SAPP, CMI, PI System per dati di pianificazione e impianti
- **Web API Layer**: Servizi REST per SAPP/CMI integration
- **BizTalk Processing**: 4 moduli per gestione piani e dati produzione (99 endpoint)
- **Database Integration**: Staging EAI, NBDO produzione, DWH CMI

## 3. Pattern di Integrazione

### Diagramma Pattern Architetturali

Il diagramma sopra mostra i 5 pattern principali utilizzati nel sistema:

1. **Database Polling Pattern** (58 ReceiveLocations)
   - Monitoraggio continuo tabelle staging
   - Trigger automatici per elaborazione
   - Esempi: VirtusGediPolling, SappPvmPolling

2. **Request-Response Pattern** (8 Web API endpoints)
   - Comunicazione sincrona HTTP
   - Validazioni e richieste on-demand
   - Esempi: PvmRequest, SappEvent

3. **Message Queuing Pattern** (Azure Service Bus)
   - Comunicazione asincrona con Virtus
   - Dead Letter Queue per error handling
   - Background services per processing

4. **Proxy Pattern** (Web API Proxy)
   - Astrazione sistemi esterni
   - Protocol translation (REST/SOAP)
   - Esempi: SappCmiProxy, NDUE integration

5. **Publish-Subscribe Pattern** (Event Distribution)
   - Distribuzione eventi business
   - Notification service
   - Multiple subscribers per evento

## 4. Governance e Sicurezza

### Diagramma MsysEaiFx Framework

Il diagramma sopra illustra il framework di governance MsysEaiFx che fornisce:

**Security Layer**:
- AccessControlService per autenticazione/autorizzazione
- API Key management per accesso controllato
- Custom headers per identificazione applicazioni

**Logging & Tracing**:
- Trace ID generation per correlazione end-to-end
- Structured logging in formato JSON
- Audit trail completo per compliance

**Monitoring**:
- Business Activity Monitoring (BAM) per KPI business
- Application Performance Monitoring per metriche tecniche
- Health checks per status sistema

**Notification**:
- Alert management con livelli Critical/Warning/Info
- Email notifications via SMTP
- Dashboard alerts per visualizzazione real-time

**Configuration**:
- ConfigService centralizzato per tutti i parametri
- Environment management (DEV/TEST/PROD)
- Connection string encryption e rotation

## 5. Riepilogo Architetturale

### Metriche Sistema
- **35+ aree funzionali** (moduli INT_*)
- **423 endpoint BizTalk** (365 SendPorts + 58 ReceiveLocations)
- **8 endpoint Web API** pubblici
- **15+ sistemi esterni** integrati
- **10+ database** (SQL Server, Oracle, Azure SQL)

### Tecnologie Chiave
- **Microsoft BizTalk Server**: Hub di integrazione centrale
- **.NET Web APIs**: Servizi REST specializzati
- **Azure Service Bus**: Messaging asincrono
- **SQL Server/Oracle**: Persistenza dati
- **MsysEaiFx**: Framework governance proprietario

### Pattern Predominanti
1. **Database Polling** (81.1% endpoint): Monitoraggio continuo staging tables
2. **SOAP Integration** (18.7% endpoint): Comunicazione con sistemi esterni
3. **Request-Response**: API sincrone per operazioni real-time
4. **Message Queuing**: Comunicazione asincrona con Virtus
5. **Proxy**: Astrazione e protocol translation

### Aree Business Coperte
- **Mercati Energetici**: GME, Terna, XBID integration
- **Ottimizzazione**: Virtus system con 74 endpoint
- **Pianificazione**: SAPP/CMI integration con 62 endpoint
- **Produzione**: PI System con 50 endpoint
- **Settlement**: SettApp integration con 49 endpoint
- **Business Intelligence**: DWH integration con 29 endpoint

### Governance e Sicurezza
- **End-to-end traceability** via trace-id correlation
- **Multi-layer security** (API keys, custom headers, AccessControlService)
- **Real-time monitoring** con BAM e APM
- **Proactive alerting** con notification service
- **Centralized configuration** per tutti gli ambienti

Questa architettura supporta l'intero ecosistema energetico A2A, garantendo scalabilità, resilienza, sicurezza e osservabilità per operazioni mission-critical nel settore dell'energia.