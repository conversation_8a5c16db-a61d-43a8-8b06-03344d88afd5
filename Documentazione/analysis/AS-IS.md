AS-IS Overview

Scope analyzed:
- BizTalk solutions under `Sviluppo/A2A.EAI/*` with bindings and orchestrations.
- .NET Web APIs under `Sviluppo/A2A.EAI.WebApi/*` and `Sviluppo/A2A.EAI.Services/*`.
- Database projects under `Sviluppo/A2A.EAI.DB/*`.

High-level architecture
- Integration hub based on BizTalk (multiple INT_* solutions):
  - Predominant adapters: `WCF-Custom` (SQL typed adapter) to `EAI` and `NBDO.EAI` databases; `WCF-BasicHttp` to SOAP services; one `SB-Messaging` endpoint.
  - Artifacts mapped via binding files; see full inventory below.
- Web APIs (microservice-style facades/utilities):
  - `A2A.EAI.INT_PIANI_SVC.WebAPI`: endpoints for SAPP/CMI integration (ping, event notification, PVM plan request) and DB access.
  - `A2A.EAI.INT_SA_SVC.WebAPI`: validation trigger to enqueue NDUE flows in DB.
  - `A2A.EAI.Services.WebApi`: REST/SOAP proxy to external systems, receiver for NDUE Programma Base, background Azure Service Bus listeners.
- Observability and governance via MsysEaiFx Core: logging, trace-id middleware, BAM, message tracking, notifications.

Security
- Controllers use a custom `[AccessControlService]` attribute (from MsysEaiFx) and Swagger defines `ApiKey` in query (`api_key`). Some endpoints document headers `msysfx-secret-key` and `msysfx-app-name`.

Endpoints inventory
- BizTalk endpoints: see CSV `Sviluppo/analysis/biztalk_endpoints.csv` (423 rows: 365 SendPorts, 58 ReceiveLocations).
  - Summary JSON: `Sviluppo/analysis/biztalk_endpoints_summary.json` with counts by type/adapter/host.
- Web API endpoints: see `Sviluppo/analysis/webapi_endpoints.md` for full list with references to source files.

Key endpoint types (from bindings)
- Adapters by count: WCF-Custom (343), WCF-BasicHttp (79), SB-Messaging (1).
- SQL servers referenced (mssql adapter addresses): DCTSVW035, DCTSVW016, epmivtnbdo, Azure SQL, etc.
- HTTP SOAP hosts referenced: epmiftsbdo, epmivtsbdo, ndue.test.a2a.eu, dctsvw034, etc.

Representative flows (examples)
- SAPP PVM flow: BizTalk polls EAI DB (typed polling) for PVM items; WebAPI `PvmRequest` serves on-demand plan retrieval; outbound updates via SQL typed procedures and SOAP send ports to NBDO services.
- NDUE Programma Base: `POST /api/Ndue/PrgBase` stores staging records via `[dbo].[VirtusN2PrgBaseInsert]`; background `VirtusSbReceiverReplyQueue` consumes replies from Azure Service Bus and updates DB via `[custom].[VirtusReplyMgmtUpdate]`.

Database artifacts
- Database projects `EAI` and `EAISYN` contain views, tables, synonyms to `NBDO` schemas and stored procedures used by services (e.g., `[dbo].[PianoRequest]`, `SettAppDbValidationService`, `[custom].[VirtusReplyMgmtUpdate]`).

Notes and gaps
- Connection strings and queue names are resolved at runtime via `ConfigService` (not in repo); environments and secrets are externalized.
- Some BizTalk orchestrations embed Service Bus client usage (see `VirtusReceiveAmqp.odx`).

