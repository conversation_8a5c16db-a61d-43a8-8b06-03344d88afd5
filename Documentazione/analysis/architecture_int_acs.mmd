%% Mermaid – INT_ACS Detailed Flow
flowchart TD
  UP[Upstream loaders / ETL] --> EAI[EAI DB]

  EAI -->|TypedPolling PollingStmt| RL[ReceiveLocation]
  RL --> ODX[Orchestrations]

  %% Staging Insert
  ODX --> SQL_INS[WCF-Custom SQL Insert]
  SQL_INS --> EAI

  %% Send to NDUE SOAP
  ODX --> SOAP[WCF-HTTP to NDUE]
  SOAP --> NDUE[NDUE SOAP]
  NDUE --> SOAP

  %% Update Status
  ODX --> SQL_UPD[WCF-Custom SQL Update]
  SQL_UPD --> EAI

  %% Telemetry
  ODX -. BAM/Notify .-> OBS[BAM / Tracking / Notifications]

  %% Notes
  NOTE{{NomeFile dallo staging passato nelle request}}
  EAI -.-> NOTE
