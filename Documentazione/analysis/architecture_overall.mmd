%% Mermaid – Overall AS‑IS Architecture (clickable in many IDEs)
flowchart LR
  subgraph External
    EXTSYS[Partner Systems<br/>SAPP / CMI / GME / Others]
  end

  subgraph NDUE[NDUE]
    NDUE_SOAP["SOAP Services<br/>NBDOWS/*"]
  end

  subgraph Data[Data Layer]
    EAI["EAI / NBDO.EAI Databases"]
    SB["Azure Service Bus"]
    FS["Filesystem / Blob<br/>DeadLetter backups"]
  end

  subgraph WebAPIs[.NET Web APIs]
    SVC["A2A.EAI.Services.WebApi<br/>- NdueController (PrgBase)<br/>- SappCmiProxy (REST/SOAP)<br/>- BG: VirtusSbReceiverReplyQueue<br/>- BG: VirtusSbReceiverDeadLetter"]
    PIANI["A2A.EAI.INT_PIANI_SVC.WebAPI<br/>- SappController<br/>(Ping, SappEvent, PvmRequest)"]
    SA["A2A.EAI.INT_SA_SVC.WebAPI<br/>- ValidationController (Validate)"]
  end

  subgraph BizTalk[BizTalk Layer]
    direction TB
    subgraph INT_ACS[A2A.EAI.INT_ACS]
      RL_ACS["WCF-SQL TypedPolling<br/>Receive Locations<br/>(ACSVenditaCalore*, ACSCO2Emesse*)"]
      ODX_ACS["Orchestrations<br/>CO2Emesse* (Staging/Process)<br/>VenditaCalore* (Staging/Process)"]
      SQL_ACS["WCF-Custom (SQL)<br/>SendPorts Insert/Update"]
      SOAP_ACS["WCF-(Basic)HTTP<br/>SendPorts to NDUE"]
    end
    OTHER_INT["Other INT_*<br/>(INT_IMPORT, INT_MERCATO, INT_PVM, ...)"]
  end

  %% Flows
  EXTSYS --> SVC
  SVC -->|Proxy REST/SOAP| EXTSYS
  SVC -->|PrgBase Insert| EAI
  SVC -.->|Reply updates| EAI
  SVC -.->|DeadLetter files| FS
  SVC -.-> SB

  PIANI -->|PvmRequest sproc| EAI
  SA -->|SettAppDbValidationService| EAI

  EAI --> RL_ACS
  RL_ACS --> ODX_ACS
  ODX_ACS --> SOAP_ACS
  SOAP_ACS --> NDUE_SOAP
  NDUE_SOAP --> SOAP_ACS
  ODX_ACS --> SQL_ACS
  SQL_ACS --> EAI

  %% Observability (conceptual)
  classDef obs fill:#eef,stroke:#99f,color:#000
  OBS["MsysEaiFx<br/>Logging/TraceId<br/>MessageTracking<br/>BAM/Notifications"]:::obs
  SVC -. uses .-> OBS
  PIANI -. uses .-> OBS
  SA -. uses .-> OBS
  ODX_ACS -. emits .-> OBS
