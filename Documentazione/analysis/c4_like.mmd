%% Mermaid – C4-like (Context/Container) view
flowchart TB
  %% People / External Systems
  EXTSYS([Partner Systems])

  %% Systems
  NDUE[[NDUE]]
  A2A[[A2A Integration Platform]]

  %% Containers inside A2A Integration Platform
  subgraph A2A_Containers [A2A Containers]
    direction TB
    BT[BizTalk INT_*]
    API_PIANI[INT_PIANI_SVC WebAPI]
    API_SA[INT_SA_SVC WebAPI]
    API_SVC[A2A.EAI.Services WebApi]
    DB_EAI[(EAI / NBDO.EAI DB)]
    BUS[(Azure Service Bus)]
    LOG[(BAM/Tracking/Logs)]
  end

  %% Relations
  EXTSYS -->|Files/HTTP/SOAP| BT
  BT -->|SOAP| NDUE
  NDUE -->|Sync replies| BT
  BT -->|Insert/Update| DB_EAI

  API_PIANI -->|PvmRequest / Ping / Event| DB_EAI
  API_SA -->|Validate| DB_EAI
  API_SVC -->|PrgBase Insert| DB_EAI
  API_SVC -->|Proxy REST/SOAP| EXTSYS
  API_SVC --> BUS
  BUS --> API_SVC

  %% Observability
  BT -.-> LOG
  API_PIANI -.-> LOG
  API_SA -.-> LOG
  API_SVC -.-> LOG
