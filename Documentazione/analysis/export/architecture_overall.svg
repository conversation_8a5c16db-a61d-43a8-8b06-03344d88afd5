<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2700.140625 867" style="max-width: 2700.14px; background-color: transparent;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#552222;}#my-svg .error-text{fill:#552222;stroke:#552222;}#my-svg .edge-thickness-normal{stroke-width:1px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#333333;stroke:#333333;}#my-svg .marker.cross{stroke:#333333;}#my-svg svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#my-svg p{margin:0;}#my-svg .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#333;}#my-svg .cluster-label span{color:#333;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ECECFF;stroke:#9370DB;stroke-width:1px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#333333!important;stroke-width:0;stroke:#333333;}#my-svg .arrowheadPath{fill:#333333;}#my-svg .edgePath .path{stroke:#333333;stroke-width:2.0px;}#my-svg .flowchart-link{stroke:#333333;fill:none;}#my-svg .edgeLabel{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .edgeLabel p{background-color:rgba(232,232,232, 0.8);}#my-svg .edgeLabel rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .labelBkg{background-color:rgba(232, 232, 232, 0.5);}#my-svg .cluster rect{fill:#ffffde;stroke:#aaaa33;stroke-width:1px;}#my-svg .cluster text{fill:#333;}#my-svg .cluster span{color:#333;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(80, 100%, 96.2745098039%);border:1px solid #aaaa33;border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:rgba(232,232,232, 0.8);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:rgba(232,232,232, 0.8);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:rgba(232,232,232, 0.8);fill:rgba(232,232,232, 0.8);}#my-svg .label-icon{display:inline-block;height:1em;overflow:visible;vertical-align:-0.125em;}#my-svg .node .label-icon path{fill:currentColor;stroke:revert;stroke-width:revert;}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .obs&gt;*{fill:#eef!important;stroke:#99f!important;color:#000!important;}#my-svg .obs span{fill:#eef!important;stroke:#99f!important;color:#000!important;}#my-svg .obs tspan{fill:#000!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"/></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"/></marker><g class="root"><g class="clusters"><g data-look="classic" id="BizTalk" class="cluster"><rect height="529" width="1019.390625" y="8" x="1413.21875" style=""/><g transform="translate(1875.9765625, 8)" class="cluster-label"><foreignObject height="24" width="93.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>BizTalk Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="WebAPIs" class="cluster"><rect height="826" width="335.578125" y="33" x="483.984375" style=""/><g transform="translate(600.6484375, 33)" class="cluster-label"><foreignObject height="24" width="102.25"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>.NET Web APIs</p></span></div></foreignObject></g></g><g data-look="classic" id="Data" class="cluster"><rect height="453" width="295.484375" y="59" x="1067.734375" style=""/><g transform="translate(1176.90625, 59)" class="cluster-label"><foreignObject height="24" width="77.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Data Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="NDUE" class="cluster"><rect height="148" width="209.53125" y="157" x="2482.609375" style=""/><g transform="translate(2567.890625, 157)" class="cluster-label"><foreignObject height="24" width="38.96875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>NDUE</p></span></div></foreignObject></g></g><g data-look="classic" id="External" class="cluster"><rect height="148" width="302.1875" y="248" x="8" style=""/><g transform="translate(129.21875, 248)" class="cluster-label"><foreignObject height="24" width="59.75"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>External</p></span></div></foreignObject></g></g><g data-look="classic" id="INT_ACS" class="cluster"><rect height="313" width="969.390625" y="28" x="1438.21875" style=""/><g transform="translate(1862.6484375, 28)" class="cluster-label"><foreignObject height="24" width="120.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>A2A.EAI.INT_ACS</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EXTSYS_SVC_0" d="M285.188,307.813L289.354,307.344C293.521,306.875,301.854,305.938,320.504,305.469C339.154,305,368.12,305,397.086,305C426.052,305,455.018,305,474.794,305.536C494.569,306.072,505.154,307.145,510.447,307.681L515.739,308.217"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_EXTSYS_0" d="M519.719,343.25L513.763,344.208C507.807,345.167,495.896,347.083,475.457,348.042C455.018,349,426.052,349,397.086,349C368.12,349,339.154,349,321.16,348.373C303.167,347.745,296.146,346.491,292.636,345.864L289.125,345.236"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_EAI_0" d="M739.625,211L752.948,194.167C766.271,177.333,792.917,143.667,826.921,126.833C860.924,110,902.286,110,943.648,110C985.01,110,1026.372,110,1057.749,113.62C1089.125,117.239,1110.516,124.478,1121.211,128.098L1131.907,131.718"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_EAI_2" d="M762.634,211L772.122,201.5C781.61,192,800.586,173,830.755,163.5C860.924,154,902.286,154,943.648,154C985.01,154,1026.372,154,1050.554,154.142C1074.735,154.284,1081.737,154.569,1085.237,154.711L1088.738,154.853"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_FS_0" d="M783.828,413.295L789.784,417.413C795.74,421.53,807.651,429.765,834.288,433.883C860.924,438,902.286,438,943.648,438C985.01,438,1026.372,438,1054.128,438C1081.883,438,1096.031,438,1103.105,438L1110.18,438"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_SB_0" d="M783.828,322L789.784,322C795.74,322,807.651,322,834.288,322C860.924,322,902.286,322,943.648,322C985.01,322,1026.372,322,1055.483,322C1084.594,322,1101.453,322,1109.883,322L1118.313,322"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PIANI_EAI_0" d="M672.443,561L696.963,500.5C721.483,440,770.523,319,815.724,258.5C860.924,198,902.286,198,943.648,198C985.01,198,1026.372,198,1053.536,196.333C1080.699,194.665,1093.663,191.331,1100.146,189.664L1106.628,187.996"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SA_EAI_0" d="M668.167,713L693.399,634.5C718.632,556,769.097,399,815.011,320.5C860.924,242,902.286,242,943.648,242C985.01,242,1026.372,242,1062.986,233.157C1099.6,224.314,1131.466,206.627,1147.399,197.784L1163.332,188.941"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EAI_RL_ACS_0" d="M1271.66,187L1286.92,194.333C1302.18,201.667,1332.699,216.333,1352.126,223.667C1371.552,231,1379.885,231,1388.219,231C1396.552,231,1404.885,231,1413.219,231C1421.552,231,1429.885,231,1437.552,231C1445.219,231,1452.219,231,1455.719,231L1459.219,231"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RL_ACS_ODX_ACS_0" d="M1723.219,231L1727.385,231C1731.552,231,1739.885,231,1747.552,231C1755.219,231,1762.219,231,1765.719,231L1769.219,231"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ODX_ACS_SOAP_ACS_0" d="M2033.219,231L2040.668,231C2048.117,231,2063.016,231,2082.796,231C2102.576,231,2127.237,231,2139.568,231L2151.898,231"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SOAP_ACS_NDUE_SOAP_0" d="M2349.32,220.393L2359.035,219.327C2368.75,218.262,2388.18,216.131,2402.061,215.065C2415.943,214,2424.276,214,2432.609,214C2440.943,214,2449.276,214,2457.609,214C2465.943,214,2474.276,214,2481.951,214.569C2489.627,215.139,2496.644,216.277,2500.152,216.847L2503.661,217.416"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NDUE_SOAP_SOAP_ACS_0" d="M2507.609,251.557L2503.443,252.631C2499.276,253.705,2490.943,255.852,2482.609,256.926C2474.276,258,2465.943,258,2457.609,258C2449.276,258,2440.943,258,2432.609,258C2424.276,258,2415.943,258,2402.718,256.422C2389.493,254.844,2371.377,251.689,2362.319,250.111L2353.261,248.533"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ODX_ACS_SQL_ACS_0" d="M1987.207,156L2002.325,142.5C2017.443,129,2047.678,102,2071.609,89.862C2095.539,77.724,2113.164,80.448,2121.977,81.81L2130.789,83.172"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SQL_ACS_EAI_0" d="M2134.742,108.747L2125.271,109.289C2115.799,109.831,2096.857,110.916,2058.27,111.458C2019.682,112,1961.451,112,1906.501,112C1851.552,112,1799.885,112,1748.219,112C1696.552,112,1644.885,112,1593.219,112C1541.552,112,1489.885,112,1459.885,112C1429.885,112,1421.552,112,1413.219,112C1404.885,112,1396.552,112,1388.219,112C1379.885,112,1371.552,112,1357.247,115.294C1342.941,118.588,1322.663,125.176,1312.525,128.47L1302.386,131.764"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SVC_OBS_0" d="M718.29,433L735.169,461.167C752.047,489.333,785.805,545.667,823.365,573.833C860.924,602,902.286,602,943.648,602C985.01,602,1026.372,602,1071.677,602C1116.982,602,1166.229,602,1215.477,602C1264.724,602,1313.971,602,1342.762,602C1371.552,602,1379.885,602,1388.219,602C1396.552,602,1404.885,602,1413.219,602C1421.552,602,1429.885,602,1459.885,602C1489.885,602,1541.552,602,1593.219,602C1644.885,602,1696.552,602,1748.219,602C1799.885,602,1851.552,602,1906.501,602C1961.451,602,2019.682,602,2061.542,609.003C2103.402,616.006,2128.89,630.012,2141.633,637.016L2154.377,644.019"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PIANI_OBS_0" d="M794.563,640.934L798.729,641.778C802.896,642.623,811.229,644.311,836.077,645.156C860.924,646,902.286,646,943.648,646C985.01,646,1026.372,646,1071.677,646C1116.982,646,1166.229,646,1215.477,646C1264.724,646,1313.971,646,1342.762,646C1371.552,646,1379.885,646,1388.219,646C1396.552,646,1404.885,646,1413.219,646C1421.552,646,1429.885,646,1459.885,646C1489.885,646,1541.552,646,1593.219,646C1644.885,646,1696.552,646,1748.219,646C1799.885,646,1851.552,646,1906.501,646C1961.451,646,2019.682,646,2061.487,649.777C2103.292,653.554,2128.671,661.108,2141.36,664.885L2154.049,668.662"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_SA_OBS_0" d="M784.391,790.873L790.253,792.061C796.115,793.249,807.839,795.624,834.382,796.812C860.924,798,902.286,798,943.648,798C985.01,798,1026.372,798,1071.677,798C1116.982,798,1166.229,798,1215.477,798C1264.724,798,1313.971,798,1342.762,798C1371.552,798,1379.885,798,1388.219,798C1396.552,798,1404.885,798,1413.219,798C1421.552,798,1429.885,798,1459.885,798C1489.885,798,1541.552,798,1593.219,798C1644.885,798,1696.552,798,1748.219,798C1799.885,798,1851.552,798,1906.501,798C1961.451,798,2019.682,798,2061.548,790.702C2103.413,783.404,2128.912,768.807,2141.662,761.509L2154.411,754.211"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ODX_ACS_OBS_0" d="M2033.219,271.928L2040.668,274.274C2048.117,276.619,2063.016,281.309,2094.868,341.208C2126.721,401.106,2175.528,516.212,2199.931,573.764L2224.335,631.317"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(397.0859375, 349)" class="edgeLabel"><g transform="translate(-61.8984375, -12)" class="label"><foreignObject height="24" width="123.796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Proxy REST/SOAP</p></span></div></foreignObject></g></g><g transform="translate(943.6484375, 110)" class="edgeLabel"><g transform="translate(-50.4375, -12)" class="label"><foreignObject height="24" width="100.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>PrgBase Insert</p></span></div></foreignObject></g></g><g transform="translate(943.6484375, 154)" class="edgeLabel"><g transform="translate(-50.1328125, -12)" class="label"><foreignObject height="24" width="100.265625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Reply updates</p></span></div></foreignObject></g></g><g transform="translate(943.6484375, 438)" class="edgeLabel"><g transform="translate(-57.7734375, -12)" class="label"><foreignObject height="24" width="115.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>DeadLetter files</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(943.6484375, 198)" class="edgeLabel"><g transform="translate(-64.7890625, -12)" class="label"><foreignObject height="24" width="129.578125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>PvmRequest sproc</p></span></div></foreignObject></g></g><g transform="translate(943.6484375, 242)" class="edgeLabel"><g transform="translate(-99.0859375, -12)" class="label"><foreignObject height="24" width="198.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>SettAppDbValidationService</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1593.21875, 602)" class="edgeLabel"><g transform="translate(-15.2109375, -12)" class="label"><foreignObject height="24" width="30.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1593.21875, 646)" class="edgeLabel"><g transform="translate(-15.2109375, -12)" class="label"><foreignObject height="24" width="30.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(1593.21875, 798)" class="edgeLabel"><g transform="translate(-15.2109375, -12)" class="label"><foreignObject height="24" width="30.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>uses</p></span></div></foreignObject></g></g><g transform="translate(2142.75906, 438.9299)" class="edgeLabel"><g transform="translate(-19.6953125, -12)" class="label"><foreignObject height="24" width="39.390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>emits</p></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(159.09375, 322)" id="flowchart-EXTSYS-0" class="node default"><rect height="78" width="252.1875" y="-39" x="-126.09375" style="" class="basic label-container"/><g transform="translate(-96.09375, -24)" style="" class="label"><rect/><foreignObject height="48" width="192.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Partner Systems<br />SAPP / CMI / GME / Others</p></span></div></foreignObject></g></g><g transform="translate(2587.375, 231)" id="flowchart-NDUE_SOAP-1" class="node default"><rect height="78" width="159.53125" y="-39" x="-79.765625" style="" class="basic label-container"/><g transform="translate(-49.765625, -24)" style="" class="label"><rect/><foreignObject height="48" width="99.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SOAP Services<br />NBDOWS/*</p></span></div></foreignObject></g></g><g transform="translate(1215.4765625, 160)" id="flowchart-EAI-2" class="node default"><rect height="54" width="245.484375" y="-27" x="-122.7421875" style="" class="basic label-container"/><g transform="translate(-92.7421875, -12)" style="" class="label"><rect/><foreignObject height="24" width="185.484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>EAI / NBDO.EAI Databases</p></span></div></foreignObject></g></g><g transform="translate(1215.4765625, 322)" id="flowchart-SB-3" class="node default"><rect height="54" width="186.328125" y="-27" x="-93.1640625" style="" class="basic label-container"/><g transform="translate(-63.1640625, -12)" style="" class="label"><rect/><foreignObject height="24" width="126.328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Azure Service Bus</p></span></div></foreignObject></g></g><g transform="translate(1215.4765625, 438)" id="flowchart-FS-4" class="node default"><rect height="78" width="202.59375" y="-39" x="-101.296875" style="" class="basic label-container"/><g transform="translate(-71.296875, -24)" style="" class="label"><rect/><foreignObject height="48" width="142.59375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Filesystem / Blob<br />DeadLetter backups</p></span></div></foreignObject></g></g><g transform="translate(651.7734375, 322)" id="flowchart-SVC-5" class="node default"><rect height="222" width="264.109375" y="-111" x="-132.0546875" style="" class="basic label-container"/><g transform="translate(-102.0546875, -96)" style="" class="label"><rect/><foreignObject height="192" width="204.109375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>A2A.EAI.Services.WebApi<br />- NdueController (PrgBase)<br />- SappCmiProxy (REST/SOAP)<br />- BG: VirtusSbReceiverReplyQueue<br />- BG: VirtusSbReceiverDeadLetter</p></span></div></foreignObject></g></g><g transform="translate(651.7734375, 612)" id="flowchart-PIANI-6" class="node default"><rect height="102" width="285.578125" y="-51" x="-142.7890625" style="" class="basic label-container"/><g transform="translate(-112.7890625, -36)" style="" class="label"><rect/><foreignObject height="72" width="225.578125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>A2A.EAI.INT_PIANI_SVC.WebAPI<br />- SappController<br />(Ping, SappEvent, PvmRequest)</p></span></div></foreignObject></g></g><g transform="translate(651.7734375, 764)" id="flowchart-SA-7" class="node default"><rect height="102" width="265.234375" y="-51" x="-132.6171875" style="" class="basic label-container"/><g transform="translate(-102.6171875, -36)" style="" class="label"><rect/><foreignObject height="72" width="205.234375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>A2A.EAI.INT_SA_SVC.WebAPI<br />- ValidationController (Validate)</p></span></div></foreignObject></g></g><g transform="translate(1593.21875, 231)" id="flowchart-RL_ACS-8" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WCF-SQL TypedPolling<br />Receive Locations<br />(ACSVenditaCalore*, ACSCO2Emesse*)</p></span></div></foreignObject></g></g><g transform="translate(1903.21875, 231)" id="flowchart-ODX_ACS-9" class="node default"><rect height="150" width="260" y="-75" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -60)" style="" class="label"><rect/><foreignObject height="120" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Orchestrations<br />CO2Emesse* (Staging/Process)<br />VenditaCalore* (Staging/Process)</p></span></div></foreignObject></g></g><g transform="translate(2252.609375, 102)" id="flowchart-SQL_ACS-10" class="node default"><rect height="78" width="235.734375" y="-39" x="-117.8671875" style="" class="basic label-container"/><g transform="translate(-87.8671875, -24)" style="" class="label"><rect/><foreignObject height="48" width="175.734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WCF-Custom (SQL)<br />SendPorts Insert/Update</p></span></div></foreignObject></g></g><g transform="translate(2252.609375, 231)" id="flowchart-SOAP_ACS-11" class="node default"><rect height="78" width="193.421875" y="-39" x="-96.7109375" style="" class="basic label-container"/><g transform="translate(-66.7109375, -24)" style="" class="label"><rect/><foreignObject height="48" width="133.421875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WCF-(Basic)HTTP<br />SendPorts to NDUE</p></span></div></foreignObject></g></g><g transform="translate(2252.609375, 439)" id="flowchart-OTHER_INT-12" class="node default"><rect height="126" width="260" y="-63" x="-130" style="" class="basic label-container"/><g transform="translate(-100, -48)" style="" class="label"><rect/><foreignObject height="96" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Other INT_*<br />(INT_IMPORT, INT_MERCATO, INT_PVM, ...)</p></span></div></foreignObject></g></g><g transform="translate(2252.609375, 698)" id="flowchart-OBS-43" class="node default obs"><rect height="126" width="189.453125" y="-63" x="-94.7265625" style="fill:#eef !important;stroke:#99f !important" class="basic label-container"/><g transform="translate(-64.7265625, -48)" style="color:#000 !important" class="label"><rect/><foreignObject height="96" width="129.453125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(0, 0, 0) !important; display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#000 !important"><p>MsysEaiFx<br />Logging/TraceId<br />MessageTracking<br />BAM/Notifications</p></span></div></foreignObject></g></g></g></g></g></svg>