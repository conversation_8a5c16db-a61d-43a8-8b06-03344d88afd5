{"INT_SAPP": {"to_ndue": [{"PortName": "sptNbdoMercIGCruscottoUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoMercIGCruscottoUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "SAPPOptitForecastUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/SAPPOptitForecastUpdateStatus.bindinginfo.xml"}, {"PortName": "WcfSendPort_SqlAdapterBinding_TypedProcedures_dbo_Custom", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmCruscottoTypedProcedure.bindinginfo.xml"}, {"PortName": "WcfSendPort_SqlAdapterBinding_TypedProcedures_dbo_Custom", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmTypedProcedure.bindinginfo.xml"}, {"PortName": "SAPPOptitForecastInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/SAPPOptitForecastInsert.bindinginfo.xml"}, {"PortName": "sptNbdoPvmWeb", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/sptNbdoPvmWeb.bindinginfo.xml"}, {"PortName": "WcfSendPort_SqlAdapterBinding_TypedProcedures_app_Custom", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmNonCalcolatiUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "sptSappPvmGettoniUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schSappPvmGettoniUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "WcfSendPort_SqlAdapterBinding_TypedProcedures_app_Custom", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "SAPPCanaveseWS", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://***********:8080/WsOptit.asmx", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/SAPPCanaveseWS.BindingInfo.xml"}, {"PortName": "sptSAPPWS", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://************:8088/WS_SFAP.asmx", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/sptSAPPWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_WS_SFAP_WS_SFAPSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://************:8088/WS_SFAP.asmx", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/sptSAPPWS.BindingInfo.xml"}], "from_other": [{"PortName": "rptSappPvmGettoni", "LocationName": "rlcSappPvmGettoni", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SappPvmGettoni", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schSappPvmGettoniPollingTypedPolling.bindinginfo.xml"}, {"PortName": "WcfReceivePort_SqlAdapterBinding_TypedPolling_sapppvmnoncalcolati_Custom", "LocationName": "WcfReceiveLocation_SqlAdapterBinding_TypedPolling_sapppvmnoncalcolati_Custom", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=sapppvmnoncalcolati", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmNonCalcolatiTypedPolling.bindinginfo.xml"}, {"PortName": "SAPPOptitForecastPolling", "LocationName": "SAPPOptitForecastPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SAPPOptitForecastPolling", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/SAPPOptitForecastPolling.bindinginfo.xml"}, {"PortName": "rptNbdoPvm", "LocationName": "rlcNbdoPvm", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=sapppvm", "File": "Sviluppo/A2A.EAI/INT_SAPP/A2A.EAI.INT_SAPP.Messaging/Bindings/schNbdoPvmTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_LPREPORT": {"to_ndue": [{"PortName": "N2LPReportConsumoContatoriGas", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/BDM/Gas/LpRConsumoContatoriGas.asmx", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/N2LPReportConsumoContatoriGas.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "EAILPReportResubmit", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/EAILPReportResubmitTypedProcedure.bindinginfo.xml"}, {"PortName": "LPReportManualDatiMeteringGasTransform", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/LPReportManualDatiMeteringGasTransform.bindinginfo.xml"}, {"PortName": "EAILPReportDatiMeteringGasTransform", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/EAILPReportDatiMeteringGasTransformTypedProcedure.bindinginfo.xml"}, {"PortName": "EAILPReportUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/EAILPReportUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "EAILPReportPolling", "LocationName": "EAILPReportPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=lpreport", "File": "Sviluppo/A2A.EAI/INT_LPREPORT/A2A.EAI.INT_LPREPORT.Messaging/Binding/EAILPReportTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_BID": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusToEnergyBidOffertePceGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOffertePceGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteMgpGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteMgpGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteMbGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteMbGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteMrrGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteMrrGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteMsdGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteMsdGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteCridaGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteCridaGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusToEnergyBidOfferteAfrrGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_BID/A2A.EAI.INT_MRKOPT_BID.Messaging/Binding/VirtusToEnergyBidOfferteAfrrGetDataTypedProcedure.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_GME": {"to_ndue": [{"PortName": "N2PrezziZonaliMrrImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/nbdows/BDM/F6PrezziZonaliMRR.asmx", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/N2PrezziZonaliMrrImport.BindingInfo.xml"}, {"PortName": "PrezziPresentatiAfrrWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/BDM/F6PrezziPresentatiAFRR.asmx", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/PrezziPresentatiAfrrWs.BindingInfo.xml"}, {"PortName": "N2OfferteMbImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/nbdows/BDM/F6OfferteMB.asmx", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/N2OfferteMbImport.BindingInfo.xml"}, {"PortName": "N2OfferteMrrImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/nbdows/BDM/F6OfferteMRR.asmx", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/N2OfferteMrrImport.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "GmeBidNotificationInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/GmeBidNotificationInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusUnitScheduleMktResExtUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusUnitScheduleMktResExtUpdateStatus.bindinginfo.xml"}, {"PortName": "EaiVirtusReplyToIpexOfferteMsd", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteMsdTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusUnitScheduleInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusUnitScheduleInsert.bindinginfo.xml"}, {"PortName": "VirtusGmePrezziEsitiZonaliToVirtusCommon", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusGmePrezziEsitiZonaliToVirtusCommon_Custom.bindinginfo.xml"}, {"PortName": "EaiVirtusReplyToIpexOfferteMb", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteMbTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiVirtusReplyToIpexOfferteMrr", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteMrr.TypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusUnitScheduleMGPToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusUnitScheduleMGPToVirtusRequest.bindinginfo.xml"}, {"PortName": "EaiVirtusReplyToIpexOfferteAfrr", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteAfrrTypeedProcedure.bindinginfo.xml"}, {"PortName": "VirtusReplyToIpexOfferteCrida", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteCridaTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiVirtusReplyToIpexOfferteMgp", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusReplyToIpexOfferteMgpTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusUnitScheduleMktResExtPolling", "LocationName": "VirtusUnitScheduleMktResExtPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusUnitScheduleMktResExtPolling", "File": "Sviluppo/A2A.EAI/INT_GME/A2A.EAI.INT_GME.Messaging/Bindings/VirtusUnitScheduleMktResExtPolling.bindinginfo.xml"}], "unknown": []}, "INT_EXPORT": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "sptErgIndisponibilitaToGediUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXPORT/A2A.EAI.INT_EXPORT.Messaging/Bindings/ErgIndisponibilitaToGedi/schErgIndisponibilitaToGediUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "sptDisponibilitaErgGediInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXPORT/A2A.EAI.INT_EXPORT.Messaging/Bindings/ErgIndisponibilitaToGedi/sptDisponibilitaErgGediInsert.bindinginfo.xml"}, {"PortName": "sptErgGediDisponibilitaToGediUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXPORT/A2A.EAI.INT_EXPORT.Messaging/Bindings/ErgIndisponibilitaToGedi/sptErgGediDisponibilitaToGediUpdateStatus.bindinginfo.xml"}], "from_other": [{"PortName": "rptErgGediDisponibilitaToGediPolling", "LocationName": "rptErgGediDisponibilitaToGediPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=ErgGediDisponibilitaToGediPolling", "File": "Sviluppo/A2A.EAI/INT_EXPORT/A2A.EAI.INT_EXPORT.Messaging/Bindings/ErgIndisponibilitaToGedi/rptErgGediDisponibilitaToGediPolling.bindinginfo.xml"}, {"PortName": "rptErgIndisponibilitaToGediPolling", "LocationName": "rlcErgIndisponibilitaToGediPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=ErgIndisponibilitaToGedi", "File": "Sviluppo/A2A.EAI/INT_EXPORT/A2A.EAI.INT_EXPORT.Messaging/Bindings/ErgIndisponibilitaToGedi/schErgIndisponibilitaToGediTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_IMPORTAUX": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusImportAuxPreparedOffersDACRIDAToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedOffersDACRIDAToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusImportAuxPreparedOffersMRRToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedOffersMRRToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusImportAuxPreparedOffersAFRRToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedOffersAFRRToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusImportAuxPreparedMIBToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedMIBToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToCaricamentiMassivi", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusGenericReplyToCaricamentiMassivi.bindinginfo.xml"}, {"PortName": "VirtusImportAuxPreparedOffersMBToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedOffersMBToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusPreparedVdtToVirtusGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusPreparedVdtToVirtusGetData.bindinginfo.xml"}, {"PortName": "VirtusPreparedVegToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusPreparedVegToVirtusRequestTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusImportAuxPreparedOffersMSDToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORTAUX/A2A.EAI.INT_MRKOPT_IMPORTAUX.Messaging/Bindings/VirtusImportAuxPreparedOffersMSDToVirtusRequest.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_IMPORT": {"to_ndue": [{"PortName": "sptNbdoPvmcGrtnImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schImportPvmcGrtnWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_SbilanciamentiImportPvmcGrtn_SbilanciamentiImportPvmcGrtnSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schImportPvmcGrtnWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_F2DatiMeteoIdroImport_F2DatiMeteoIdroImportSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoIdroImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schdatiMeteoIdroWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_F2DatiMeteoIdroImport_F2DatiMeteoIdroImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoIdroImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schdatiMeteoIdroWS.BindingInfo.xml"}, {"PortName": "sptDatiMeteringImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/BDM/F10DatiMeteringTernaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiMeteringImport.BindingInfo.xml"}, {"PortName": "sptNbdoSmiImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/SmiImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schSmiImportWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_SmiImport_SmiImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/SmiImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schSmiImportWS.BindingInfo.xml"}, {"PortName": "sptSegnantiImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/bdm/ImportSegnanti.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schSegnantiImportWs.BindingInfo.xml"}, {"PortName": "sptNbdoIskraAdmImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2IskraImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schIskraAdmImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F2IskraImport_F2IskraImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2IskraImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schIskraAdmImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F10DatimeteringTernaImportImport_F10DatimeteringTernaImportImportSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F10DatiMeteringTernaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiMeteringTernaImportWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_F10DatimeteringTernaImportImport_F10DatimeteringTernaImportImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F10DatiMeteringTernaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiMeteringTernaImportWS.BindingInfo.xml"}, {"PortName": "sptNBDORendimentiIdroImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/RendimentiImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schRendimentiIdro.BindingInfo.xml"}, {"PortName": "WcfSendPort_RendimentiImport_RendimentiImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/RendimentiImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schRendimentiIdro.BindingInfo.xml"}, {"PortName": "sptRicostruzioneApiErgWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/RicostruzioneApiErg.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schRicostruzioneApiErgWs.BindingInfo.xml"}, {"PortName": "sptNbdoNMO13Import", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/NMO13Import.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schImportNMO13.BindingInfo.xml"}, {"PortName": "WcfSendPort_NMO13Import_NMO13ImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/NMO13Import.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schImportNMO13.BindingInfo.xml"}, {"PortName": "sptNbdodatiMeteoEpsonImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2EpsonImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schdatiMeteoEpsonWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_F2EpsonImport_F2EpsonImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2EpsonImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schdatiMeteoEpsonWS.BindingInfo.xml"}, {"PortName": "sptNbdodatiMeteoImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiMeteoImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F2DatiMeteoImport_F2DatiMeteoImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiMeteoImport.BindingInfo.xml"}, {"PortName": "sptMeteringFvImportAltri", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/MeteringFvImportAltri.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/DatiMeteringDistributore/schMeteringFvImportAltri.BindingInfo.xml"}, {"PortName": "sptIskraImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/IskraImport.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/Common/schIskraImportWs.BindingInfo.xml"}, {"PortName": "ProtezioneCivileAllertaDigheWS", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/Dighe/WsAllertaDigheProtezioneCivile.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/ProtezioneCivile/ProtezioneCivileAllertaDigheWs.BindingInfo.xml"}, {"PortName": "sptN2EnergiaProdottaFv", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/EnergieProdotteFv.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/EnergiaProdottaFv/schN2EnergieProdotteFv.BindingInfo.xml"}, {"PortName": "sptUvrqContributiReattivaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/BDM/UvrqContributiReattiva.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/UvrqContributiReattiva/ImportUvrqContributiReattiva.BindingInfo.xml"}, {"PortName": "sptUvrpContributoPrimariaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/NBDOWS/BDM/ImportContributoPrimaria.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/UvrpContributiPrimaria/ImportContributoPrimaria.BindingInfo.xml"}, {"PortName": "WcfSendPort_ImportContributoPrimaria_ImportContributoPrimariaSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivtsbdo/NBDOWS/BDM/ImportContributoPrimaria.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/UvrpContributiPrimaria/ImportContributoPrimaria.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptDatiIdriciScadaWS", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmilnint3:81/ws_GetProduzione_New/DatiIdriciScada.asmx", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schDatiIdriciScadaWs.BindingInfo.xml"}, {"PortName": "sptImportSegnanti", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/schImportSegnantiTypedProcedure.bindinginfo.xml"}, {"PortName": "ErgEnergiaImmessaUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/ErgEnergiaImmessa/schErgEnergiaImmessaUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "sptProtezioneCivileAllertaDigheCheck", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/ProtezioneCivile/ProtezioneCivileFileCheckTypedProcedure.bindinginfo.xml"}, {"PortName": "sptDatiMeteringTernaGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/DatiMeteringTerna/schDatiMeteringTernaGetDataTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "rptErgEnergiaImmessaPolling", "LocationName": "rlcErgEnergiaImmessaPolling", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?InboundId=ErgEnergiaImmessaId", "File": "Sviluppo/A2A.EAI/INT_IMPORT/A2A.EAI.INT_IMPORT.Messaging/Bindings/ErgEnergiaImmessa/schErgEnergiaImmessaPolling.bindinginfo.xml"}], "unknown": []}, "INT_ACS": {"to_ndue": [{"PortName": "WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiRetragas.BindingInfo.xml"}, {"PortName": "WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiRetragas.BindingInfo.xml"}, {"PortName": "WcfSendPort_Ricavi_RicaviSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2VenditaCalore.BindingInfo.xml"}, {"PortName": "WcfSendPort_Ricavi_RicaviSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2VenditaCalore.BindingInfo.xml"}, {"PortName": "WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiUnareti.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiUnareti.BindingInfo.xml"}, {"PortName": "WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiUnareti.asmx", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/N2ConsumiUnareti.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "ACSCO2EmesseUnaretiUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSCO2EmesseUnaretiUpdateStatus.bindinginfo.xml"}, {"PortName": "ACSVenditaCaloreBresciaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSVenditaCaloreBresciaInsert.bindinginfo.xml"}, {"PortName": "ACSVenditaCaloreMilanoInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSVenditaCaloreMilanoInsert.bindinginfo.xml"}, {"PortName": "ACSCO2EmesseRetragasInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSCO2EmesseRetragasInsert.bindinginfo.xml"}, {"PortName": "ACSCO2EmesseUnaretiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ACS/A2A.EAI.INT_ACS.Messaging/Bindings/ACSCO2EmesseUnaretiInsert.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_SA_DB_VAL": {"to_ndue": [{"PortName": "WcfSendPort_CalcoloSbilanciamentoEconomico_CalcoloSbilanciamentoEconomicoSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/CalcoloSbilanciamentoEconomico.asmx", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/CalcoloSbilanciamentoEconomico.BindingInfo.xml"}, {"PortName": "WcfSendPort_CalcoloSbilanciamentoEconomico_CalcoloSbilanciamentoEconomicoSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://ndue.test.a2a.eu/NBDOWS/CalcoloSbilanciamentoEconomico.asmx", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/CalcoloSbilanciamentoEconomico.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "SettAppSbilTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://SETAPPD/", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/SettApp/SettAppSbilTableSelect.bindinginfo.xml"}, {"PortName": "SettAppSbilEimmTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://SETAPPD/", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/SettApp/SettAppSbilEimmTableSelect.bindinginfo.xml"}, {"PortName": "SettAppCalcoloSbilUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/EAI/SettAppCalcoloSbilUpdateStatus.bindinginfo.xml"}, {"PortName": "SettAppDbValUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValUpdateStatus.bindinginfo.xml"}, {"PortName": "SettAppDbValN2MsdInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2MsdInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2MgpInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2MgpInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2PvmcInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2PvmcInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2AccCaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2AccCaInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2SbilPceInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2SbilPceInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2MiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2MiInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2SbilInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSAW053//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2SbilInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2PrimaryRegInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2PrimaryRegInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2MrodInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSAW053//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2MrodInsert.bindinginfo.xml"}, {"PortName": "SettAppDbValN2MbInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValN2MbInsert.bindinginfo.xml"}], "from_other": [{"PortName": "SettAppCalcoloSbilPolling", "LocationName": "SettAppCalcoloSbilPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SettAppCalcoloSbilPolling", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/EAI/SettAppCalcoloSbilPolling.bindinginfo.xml"}, {"PortName": "SettAppDbValPolling", "LocationName": "SettAppDbValPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SettAppDbValPolling", "File": "Sviluppo/A2A.EAI/INT_SA_DB_VAL/A2A.EAI.INT_SA_DB_VAL.Messaging/Bindings/N2/SettAppDbValTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_SC": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "SCToVirtus", "LocationName": "", "TransportType": "SB-Messaging", "Address": "sb://a2a-tst-integrationservices.servicebus.windows.net/mrk-optimizer-biztalk-to-virtus/", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/ScToVirtus.bindinginfo.xml"}, {"PortName": "VirtusRupPrepareUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusRupPrepareUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToDEI", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToDEI.bindinginfo.xml"}, {"PortName": "VirtusRupProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusRupProcessUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToTPAA", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToTPAA.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToMIRP", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToMIRP.bindinginfo.xml"}, {"PortName": "VirtusSCEnqueueReport", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCEnqueueReport.bindinginfo.xml"}, {"PortName": "VirtusSCReportGatherInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCReportGatherInsert.bindinginfo.xml"}, {"PortName": "VirtusSCProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCProcessUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusRupPrepareInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusRupPrepareInsert.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToVEG", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToVEG.bindinginfo.xml"}, {"PortName": "VirtusSCDeiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCDeiInsert.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToMIB", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToMIB.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToVDT", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToVDT.bindinginfo.xml"}, {"PortName": "VirtusSCVegInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCVegInsert.bindinginfo.xml"}, {"PortName": "VirtusSCInputGatherInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCInputGatherInsert.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToTPA", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToTPA.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToTPS", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToTPS.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToTPFS", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusGenericReplyToTPFS.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusSCAskReportPolling", "LocationName": "VirtusSCAskReportPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusSCAskReportPolling", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCAskReportPolling.bindinginfo.xml"}, {"PortName": "VirtusVegProcessPolling", "LocationName": "VirtusVegProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusVegProcessPolling", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusVegProcessPolling.bindinginfo.xml"}, {"PortName": "VirtusSCDeiPolling", "LocationName": "VirtusSCDeiPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusSCDeiPolling", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusSCDeiPolling.bindinginfo.xml"}, {"PortName": "VirtusRupPreparePolling", "LocationName": "VirtusRupPreparePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusRupPreparePolling", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusRupPreparePolling.bindinginfo.xml"}, {"PortName": "VirtusRupProcessPolling", "LocationName": "VirtusRupProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusRupProcessPolling", "File": "Sviluppo/A2A.EAI/INT_SC/A2A.EAI.INT_SC.Messaging/Bindings/VirtusRupProcessPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusReplyMgmtRateLimiter", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT/A2A.EAI.INT_MRKOPT.Messaging/Bindings/VirtusReplyMgmtRateLimiter.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_MONITOR": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "MonitorQueueUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MONITOR/A2A.EAI.INT_MONITOR.Messaging/Bindings/MonitorQueueUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "MonitorQueuePolling", "LocationName": "MonitorQueuePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=MonitorQueuePolling", "File": "Sviluppo/A2A.EAI/INT_MONITOR/A2A.EAI.INT_MONITOR.Messaging/Bindings/MonitorQueueTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_LUNA": {"to_ndue": [{"PortName": "MessaggiLunaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/MessaggiLUNA.asmx", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/MessaggiLunaWs.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "LunaMessaggiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/LunaMessaggiInsert.bindinginfo.xml"}, {"PortName": "LunaVCRUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/LunaVCRUpdateStatus.bindinginfo.xml"}, {"PortName": "LunaN2UpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/LunaN2UpdateStatus.bindinginfo.xml"}], "from_other": [{"PortName": "LunaVCRPolling", "LocationName": "LunaVCRPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=LunaVCRPolling", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/LunaVCRPolling.bindinginfo.xml"}, {"PortName": "LunaN2Polling", "LocationName": "LunaN2Polling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=LunaN2Polling", "File": "Sviluppo/A2A.EAI/INT_LUNA/A2A.EAI.INT_LUNA.Messaging/Bindings/LunaN2Polling.bindinginfo.xml"}], "unknown": []}, "INT_SA_FATT": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "SettAppFatturaEneforbilInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_FATT/A2A.EAI.INT_SA_FATT.Messaging/Bindings/EaiSettAppFatturaInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "SettAppFatturaProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_FATT/A2A.EAI.INT_SA_FATT.Messaging/Bindings/EaiSettAppFatturaProcessUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_PIANI": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "EaiPianoUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiPianoUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiPianoCmiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiPianoCmiInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiGettoniSappInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiGettoniSappInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiGettoniCmiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiGettoniCmiInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiPianoSappInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiPianoSappInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiGettoniUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiGettoniUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EaiPianoPiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiPianoPiInsertTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "EaiGettoniPolling", "LocationName": "EaiGettoniPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=GettoniPolling", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiGettoniTypedPolling.bindinginfo.xml"}, {"PortName": "<PERSON>ai<PERSON><PERSON>", "LocationName": "<PERSON>ai<PERSON><PERSON>", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=PianoPolling", "File": "Sviluppo/A2A.EAI/INT_PIANI/A2A.EAI.INT_PIANI.Messaging/Bindings/EaiPianoTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_AMBIENTE": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "sptDatiLaboratorioInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Bindings/schDatiLaboratorioInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptDatiLaboratorioCheck", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_AMBIENTE/A2A.EAI.INT_AMBIENTE.Messaging/Bindings/schDatiLaboratorioCheckTypedProcedure.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_SA_DB_QTSA": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "SettAppIndexesTable", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://SETAPPD/", "File": "Sviluppo/A2A.EAI/INT_SA_DB_QTSA/A2A.EAI.INT_SA_DB_QTSA.Messaging/Bindings/SettAppIndexesTable.bindinginfo.xml"}, {"PortName": "QuotazioniIndexesTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://A2AQ/", "File": "Sviluppo/A2A.EAI/INT_SA_DB_QTSA/A2A.EAI.INT_SA_DB_QTSA.Messaging/Bindings/QuotazioniIndexesTableSelect.bindinginfo.xml"}, {"PortName": "SettAppDbQtSaUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SA_DB_QTSA/A2A.EAI.INT_SA_DB_QTSA.Messaging/Bindings/SettAppDbQtSaUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "SettAppDbQtSaPolling", "LocationName": "SettAppDbQtSaPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SettAppDbQtSaPolling", "File": "Sviluppo/A2A.EAI/INT_SA_DB_QTSA/A2A.EAI.INT_SA_DB_QTSA.Messaging/Bindings/SettAppDbQtSaPolling.bindinginfo.xml"}], "unknown": []}, "INT_SA_DB_Common": {"to_ndue": [], "from_ndue": [], "to_other": [], "from_other": [{"PortName": "SettAppDbNotificationToSaPolling", "LocationName": "SettAppDbNotificationToSaPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=SettAppDbNotificationToSaPolling", "File": "Sviluppo/A2A.EAI/INT_SA_DB_Common/A2A.EAI.INT_SA_DB_Common.Messaging/Bindings/EAI/SettAppDbNotificationToSaTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_BROKER": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "GediRouterGetConfig", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BROKER/A2A.EAI.INT_BROKER.Messaging/Bindings/GediRouterGetConfig.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_PI": {"to_ndue": [{"PortName": "PiExportN2AiaAriaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/AIA/WS_AIA_Aria.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaAriaWs.BindingInfo.xml"}, {"PortName": "PiExportN2LghImportWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/F2LGHImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F2LGHImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2ArpaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/DatiARPA.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/DatiArpaImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2AiaAriaTotWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/AIA/WS_AIA_AriaEmissioniTOT.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaAriaTotWs.BindingInfo.xml"}, {"PortName": "PiExportN2TensioneWsOut", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/ImportTensioneEffettiva.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/ImportTensioneEffettivaImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2TeleregolazioneWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/F1RegFreqItaliaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F1RegFreqItaliaImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2MeteoWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/F2DatiMeteoIdroImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F2DatiMeteoIdroImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2AiaConsumiIdriciWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/AIA/WS_AIA_ConsumiIdrici.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaConsumiIdriciWs.BindingInfo.xml"}, {"PortName": "PiExportN2AiaTransitoriWsGeneric", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/AIA/WS_AIA_TransitoriCustom.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaTransitoriCustom.BindingInfo.xml"}, {"PortName": "PiExportN2IdroWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/F2ScadaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F2ScadaImportWs.BindingInfo.xml"}, {"PortName": "FlussoGenericoPiImportWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/FlussoGenericoPI.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/FlussoGenericoPiImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2RipartizioneEnergiaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/Conduzione/RipartizioneEnergia.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/RipartizioneEnergiaWs.BindingInfo.xml"}, {"PortName": "PiAiaTransitori", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/AIA/WS_AIA_Transitori.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaTransitori.BindingInfo.xml"}, {"PortName": "PiExportN2RicMetWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034/nbdows/BDM/F3ScadaImportRicostruzionePI.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F3ScadaImportRicostruzionePiImportWs.BindingInfo.xml"}, {"PortName": "PiExportN2DatiPrivilegiatiValtellinaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOws/BDM/F2ImportDatiPrivilegiatiValtellina.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/F2ImportDatiPrivilegiatiValtellinaWs.BindingInfo.xml"}, {"PortName": "PiExportN2AiaAcquaEmissioniWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/AIA/WS_AIA_AcquaEmissioni.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/AiaAcquaEmissioni.BindingInfo.xml"}, {"PortName": "PiExportProduzioniFvOut", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/ProduzioniFv.asmx", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/N2/ProduzioniFv.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptPITimeSeriesService", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivpi001.edipower.produzione.energia/PIWebServices/PITimeSeries.svc", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/schPiTimeSeriesService.BindingInfo.xml"}, {"PortName": "PiExportN2RipartizioneEnergia", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/RipartizioneEnergia/PiExportN2RipartizioneEnergia.bindinginfo.xml"}, {"PortName": "PiImportMobygisGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Mobygis/PiImportMobygisGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "PiImportVitecGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Vitec/PiImportVitecGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportModelliPrevisionaliWind", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/ModelliPrevisionali/PiExportModelliPrevisionaliWindTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportModelliPrevisionali", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/ModelliPrevisionali/PiExportModelliPrevisionaliTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportMeteologicaRealtime", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Meteologica/PiExportMeteologicaRealtime.bindinginfo.xml"}, {"PortName": "PiExportN2UvapInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/UVAP/PiExportN2UvapInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportN2Transitori", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Aia/PiExportN2TransitoriTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportN2ConsumiIdrici", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Aia/PiExportN2ConsumiIdriciTypedProcedure.bindinginfo.xml"}, {"PortName": "VirtusPiGetDataToHydroCheck", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Virtus/VirtusPiGetDataToHydroCheck.bindinginfo.xml"}, {"PortName": "PiExportGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Common/PiExportGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportTranscode", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Common/PiExportTranscodeTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Common/PiExportUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportN2RicMetUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/RicMetering/PiExportN2RicMetUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "PiImportPvmUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/PVM/PiImportPvmUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "PiImportPvmGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/PVM/PiImportPvmGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "PiExportN2DatiPrivilegiatiValtellinaCheck", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/DatiPrivilegiatiValtellina/PiExportN2DatiPrivilegiatiValtellinaCheck.bindinginfo.xml"}, {"PortName": "PiImportOptitGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/Optit/PiImportOptitGetDataTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "PiImportPvmPolling", "LocationName": "WcfReceiveLocation_SqlAdapterBinding_TypedPolling_idPiImportPvmPolling_Custom", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?InboundId=idPiImportPvmPolling", "File": "Sviluppo/A2A.EAI/INT_PI/A2A.EAI.INT_PI.Messaging/Bindings/EAI/PVM/PiImportPvmTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_N2": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusPvmUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_N2/A2A.EAI.INT_MRKOPT_N2.Messaging/Binding/VirtusPvmUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusN2PrgBaseUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_N2/A2A.EAI.INT_MRKOPT_N2.Messaging/Binding/VirtusN2PrgBaseUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusPvmGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_N2/A2A.EAI.INT_MRKOPT_N2.Messaging/Binding/VirtusPvmGetData.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusPvmPolling", "LocationName": "VirtusPvmPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusPvmPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_N2/A2A.EAI.INT_MRKOPT_N2.Messaging/Binding/VirtusPvmPolling.bindinginfo.xml"}, {"PortName": "VirtusN2PrgBasePolling", "LocationName": "VirtusN2PrgBasePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusN2PrgBasePolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_N2/A2A.EAI.INT_MRKOPT_N2.Messaging/Binding/VirtusN2PrgBasePolling.bindinginfo.xml"}], "unknown": []}, "INT_ETRM": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "EtrmSettAppContractInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ETRM/A2A.EAI.INT_ETRM.Messaging/Bindings/EtrmSettAppContractInsert.bindinginfo.xml"}, {"PortName": "EtrmSettAppContractPriceInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ETRM/A2A.EAI.INT_ETRM.Messaging/Bindings/EtrmSettAppContractPriceInsert.bindinginfo.xml"}, {"PortName": "EtrmSettAppPowerThresholdInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ETRM/A2A.EAI.INT_ETRM.Messaging/Bindings/EtrmSettAppPowerThresholdInsert.bindinginfo.xml"}, {"PortName": "EtrmSettAppFileUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_ETRM/A2A.EAI.INT_ETRM.Messaging/Bindings/EtrmSettAppFileUpdateStatus.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_MRKOPT_GEDI": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusGediVincoliDiReteTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediVincoliDiReteTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediMiglioriPrevisioniInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMiglioriPrevisioniInsert.bindinginfo.xml"}, {"PortName": "VirtusGediPrezziAttesiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediPrezziAttesiInsert.bindinginfo.xml"}, {"PortName": "VirtusGediDispPrimariaTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispPrimariaTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediDispTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediOptimalPlanInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediOptimalPlanInsert.bindinginfo.xml"}, {"PortName": "VirtusGediPrezziAttesiTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://dbdispq/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediPrezziAttesiTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediOptimalPlanTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediOptimalPlanTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediDispSpegnimentoInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispSpegnimentoInsert.bindinginfo.xml"}, {"PortName": "VirtusGediSemibandaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediSemibandaInsert.bindinginfo.xml"}, {"PortName": "VirtusGediVincoliDiReteInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediVincoliDiReteInsert.bindinginfo.xml"}, {"PortName": "VirtusGediMiglioriPrevisioniTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMiglioriPrevisioniTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediUpsideCridaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsideCridaInsert.bindinginfo.xml"}, {"PortName": "VirtusGediCvrTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediCvrTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediMiglioriPrevisioniNoteInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMiglioriPrevisioniNoteInsert.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCInsert.bindinginfo.xml"}, {"PortName": "VirtusGediDispPrimariaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispPrimariaInsert.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediUpsideTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsideTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediMiglioriPrevisioniNoteTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMiglioriPrevisioniNoteTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediSemibandaTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediSemibandaTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediUPSAInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUPSAInsert.bindinginfo.xml"}, {"PortName": "VirtusGediDispInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispInsert.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCProgrTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCProgrTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediUpsideInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsideInsert.bindinginfo.xml"}, {"PortName": "VirtusGediUPSATableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUPSATableSelect.bindinginfo.xml"}, {"PortName": "VirtusDbSyncGediUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusDbSyncGediUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCProgrInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCProgrInsert.bindinginfo.xml"}, {"PortName": "VirtusGediMsdPricesInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMsdPricesInsert.bindinginfo.xml"}, {"PortName": "VirtusGediUpsideCridaTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsideCridaTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediCvrInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediCvrInsert.bindinginfo.xml"}, {"PortName": "VirtusDbSyncProcessGediUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusDbSyncProcessGediUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusGediMsdPricesTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMsdPricesTableSelect.bindinginfo.xml"}, {"PortName": "VirtusGediDispSpegnimentoTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://DBDISPQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispSpegnimentoTableSelect.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusGediUPSAPolling", "LocationName": "VirtusGediUPSAPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediUPSAPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUPSAPolling.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCPolling", "LocationName": "VirtusGediNomineUCPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediNomineUCPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCPolling.bindinginfo.xml"}, {"PortName": "VirtusGediDispPrimariaPolling", "LocationName": "VirtusGediDispPrimariaPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediDispPrimariaPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispPrimariaPolling.bindinginfo.xml"}, {"PortName": "VirtusGediMsdPricesPolling", "LocationName": "VirtusGediMsdPricesPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediMsdPricesPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMsdPricesPolling.bindinginfo.xml"}, {"PortName": "VirtusGediMiglioriPrevisioniPolling", "LocationName": "VirtusGediMiglioriPrevisioniPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediMiglioriPrevisioniPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediMiglioriPrevisioniPolling.bindinginfo.xml"}, {"PortName": "VirtusGediOptimalPlanPolling", "LocationName": "VirtusGediOptimalPlanPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediOptimalPlanPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediOptimalPlanPolling.bindinginfo.xml"}, {"PortName": "VirtusGediSemibandaPolling", "LocationName": "VirtusGediSemibandaPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediSemibandaPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediSemibandaPolling.bindinginfo.xml"}, {"PortName": "VirtusGediNomineUCProgrPolling", "LocationName": "VirtusGediNomineUCProgrPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediNomineUCProgrPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediNomineUCProgrPolling.bindinginfo.xml"}, {"PortName": "VirtusGediUpsideCridaPolling", "LocationName": "VirtusGediUpsideCridaPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediUpsideCridaPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsideCridaPolling.bindinginfo.xml"}, {"PortName": "VirtusGediDispSpegnimentoPolling", "LocationName": "VirtusGediDispSpegnimentoPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediDispSpegnimentoPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispSpegnimentoPolling.bindinginfo.xml"}, {"PortName": "VirtusGediVincoliDiRetePolling", "LocationName": "VirtusGediVincoliDiRetePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediVincoliDiRetePolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediVincoliDiRetePolling.bindinginfo.xml"}, {"PortName": "VirtusDbSyncGediPolling", "LocationName": "VirtusDbSyncGediPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusDbSyncGediPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusDbSyncGediPolling.bindinginfo.xml"}, {"PortName": "VirtusGediUpsidePolling", "LocationName": "VirtusGediUpsidePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediUpsidePolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediUpsidePolling.bindinginfo.xml"}, {"PortName": "VirtusGediDispPminPmaxPolling", "LocationName": "VirtusGediDispPminPmaxPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusGediDispPminPmaxPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_GEDI/A2A.EAI.INT_MRKOPT_GEDI.Messaging/Binding/VirtusGediDispPminPmaxPolling.bindinginfo.xml"}], "unknown": []}, "INT_PVM": {"to_ndue": [{"PortName": "sptNbdoEsitoMA1MGPImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F6QuantityImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schEsitoMA1MGPImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F6QuantityImport_F6QuantityImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F6QuantityImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schEsitoMA1MGPImport.BindingInfo.xml"}, {"PortName": "sptNbdoFmsImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivsvbd1/NBDOWS/SbilmportFms.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSbilImportFmsWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_SbilanciamentiImportFms_SbilanciamentiImportFmsSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivsvbd1/NBDOWS/SbilmportFms.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSbilImportFmsWS.BindingInfo.xml"}, {"PortName": "sptNbdoInsertStaging", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schA2AInsertStagingRup.bindinginfo.xml"}, {"PortName": "sptScandaleVdtUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schScandaleVdtUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "sptPicassoImportWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/BDM/F1PicassoImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schPicassoImportWs.BindingInfo.xml"}, {"PortName": "sptPrezziInviatiAFRR", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/BDM/F6PrezziInviatiAFRR.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schPrezziInviatiAFRRWs.BindingInfo.xml"}, {"PortName": "sptNbdoRupImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/nbdows/rupimport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_RUPImport_RUPImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/nbdows/rupimport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImport.BindingInfo.xml"}, {"PortName": "sptNbdoInsertRup", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schA2AInsertRup.bindinginfo.xml"}, {"PortName": "sptNbdoReqFreqSFSEImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/SbilmportRegFreq.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/SbilanciamentiImportRegFreq.BindingInfo.xml"}, {"PortName": "WcfSendPort_SbilanciamentiImportRegFreq_SbilanciamentiImportRegFreqSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/SbilmportRegFreq.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/SbilanciamentiImportRegFreq.BindingInfo.xml"}, {"PortName": "sptEaiRupInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schEaiRupInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptNbdoOfferteMsdImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schImportImportOfferteMsd.BindingInfo.xml"}, {"PortName": "WcfSendPort_F7OfferteMSDImport_F7OfferteMSDImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schImportImportOfferteMsd.BindingInfo.xml"}, {"PortName": "sptNbdoRup", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//NBDO?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupTypedProcedure.bindinginfo.xml"}, {"PortName": "sptNbdoReqFreqItaliaImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RegFreqItaliaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schRegFreqItalia.BindingInfo.xml"}, {"PortName": "WcfSendPort_F1RegFreqItaliaImport_F1RegFreqItaliaImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RegFreqItaliaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schRegFreqItalia.BindingInfo.xml"}, {"PortName": "sptNbdoRupImportCoda", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RupImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImportWSCoda.BindingInfo.xml"}, {"PortName": "WcfSendPort_F1RupImport_F1RupImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RupImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImportWSCoda.BindingInfo.xml"}, {"PortName": "sptNbdoRupImportXml", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RupImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImportXml.BindingInfo.xml"}, {"PortName": "WcfSendPort_F1RupImport_F1RupImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F1RupImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schNbdoRupImportXml.BindingInfo.xml"}, {"PortName": "sptScandaleVdtInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schScandaleVdtInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptNbdoSemibandaImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivsvbd1/NBDOWS/BDM/F1SemibandaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSemibandaImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F1SemibandaImport_F1SemibandaImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivsvbd1/NBDOWS/BDM/F1SemibandaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSemibandaImport.BindingInfo.xml"}, {"PortName": "sptNbdoCheckStaging", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schA2ACheckRup.bindinginfo.xml"}, {"PortName": "sptNbdoTeleregolazioneImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/bdm/F1TeleregolanteTernaImport.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/F1TeleregolanteTernaImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_SbilanciamentiImportPvmcGrtn_SbilanciamentiImportPvmcGrtnSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSbilImportPvmcGrtnWS.BindingInfo.xml"}, {"PortName": "WcfSendPort_SbilanciamentiImportPvmcGrtn_SbilanciamentiImportPvmcGrtnSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schSbilImportPvmcGrtnWS.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptEaiRupUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/sptEaiRupUpdateStatus.bindinginfo.xml"}, {"PortName": "sptPvmUnitaProduttiveAbilitazione", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schPvmUnitaProduttiveAbilitazione.Bindinginfo.xml"}, {"PortName": "VirtusBackofficeSpacchettamentoGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/VirtusBackofficeSpacchettamentoGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "sptEaiRupStagingInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/sptEaiRupStagingInsert.bindinginfo.xml"}, {"PortName": "sptCmiSemibandaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schCmiSemibandaInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptCmiPicassoInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schCmiPicassoInsertTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "rptRupPolling", "LocationName": "rlcRupPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=RupPolling", "File": "Sviluppo/A2A.EAI/INT_PVM/A2A.EAI.INT_PVM.Messaging/Bindings/schEaiRupTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_BDE": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "sptEaiBdeSr", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeSrTypedProcedure.bindinginfo.xml"}, {"PortName": "sptEaiBdeLb", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeLbTypedProcedure.bindinginfo.xml"}, {"PortName": "sptEaiBdeGetEmailAddr", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeGetEmailAddrTypedProcedure.bindinginfo.xml"}, {"PortName": "sptEaiBdeCb", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeCbTypedProcedure.bindinginfo.xml"}, {"PortName": "sptEaiBdeRc", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BDE/A2A.EAI.INT_BDE.Messaging/Bindings/schEaiBdeRcTypedProcedure.bindinginfo.xml"}], "from_other": [], "unknown": []}, "INT_XBID": {"to_ndue": [{"PortName": "WcfSendPort_ImportXbid_ImportXbidSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/ImportXbid.asmx", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Schemas/ImportXbid.BindingInfo.xml"}, {"PortName": "WcfSendPort_ImportXbid_ImportXbidSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://ndue.test.a2a.eu/NBDOWS/ImportXbid.asmx", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Schemas/ImportXbid.BindingInfo.xml"}, {"PortName": "ImportXbidOut", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/NBDOws/ImportXbid.asmx", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/ImportXbid.BindingInfo.xml"}, {"PortName": "N2ImportPrezziSaldoCommerciale", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOws/BDM/PrezziSaldoCommerciale.asmx", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/ImportPrezziSaldoCommerciale.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "XbidPhysicalProgramInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/XbidPhysicalProgramInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "XbidPhysicalProgramUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/XbidPhysicalProgramUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusFeasibilityIntervalsToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/VirtusFeasibilityIntervalsToVirtusRequest.bindinginfo.xml"}, {"PortName": "XbidPhysicalProgramInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/XbidPhysicalProgramInsertTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "XbidPhysicalProgramPolling", "LocationName": "XbidPhysicalProgramPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=XbidPhysicalProgramPolling", "File": "Sviluppo/A2A.EAI/INT_XBID/A2A.EAI.INT_XBID.Messaging/Bindings/XbidPhysicalProgramPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_QTZ": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusQuotazioniFuelPricesInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusQuotazioniFuelPricesInsert.bindinginfo.xml"}, {"PortName": "VirtusQuotazioniFuelPricesTableSelect", "LocationName": "", "TransportType": "WCF-Custom", "Address": "oracledb://A2AQ/", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusQuotazioniFuelPricesTableSelect.bindinginfo.xml"}, {"PortName": "VirtusDbSyncQuotazioniUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusDbSyncQuotazioniUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusDbSyncProcessQuotazioniUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusDbSyncProcessQuotazioniUpdateStatus.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusQuotazioniFuelPricesPolling", "LocationName": "VirtusQuotazioniFuelPricesPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusQuotazioniFuelPricesPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusQuotazioniFuelPricesPolling.bindinginfo.xml"}, {"PortName": "VirtusDbSyncQuotazioniPolling", "LocationName": "VirtusDbSyncQuotazioniPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusDbSyncQuotazioniPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusDbSyncQuotazioniPolling.bindinginfo.xml"}, {"PortName": "VirtusQuotazioniCO2PricesPolling", "LocationName": "VirtusQuotazioniCO2PricesPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusQuotazioniCO2PricesPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_QTZ/A2A.EAI.INT_MRKOPT_QTZ.Messaging/Binding/VirtusQuotazioniCO2PricesPolling.bindinginfo.xml"}], "unknown": []}, "INT_BI": {"to_ndue": [{"PortName": "ForecastInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw016//DWH_NBDO?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Forecast/ForecastInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "BudgetInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_NBDO?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Budget/BudgetInsertTypedProcedure.bindinginfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "EzekeDispaccProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeDispaccProcessUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeAnelloProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeAnelloProcessUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeAnelloProcess", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeAnelloProcessTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeAnelloInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeAnelloInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeDispaccProcess", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeDispaccProcessTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeDispaccEvidenzeInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeDispaccEvidenzeInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeConfrontoInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeConfrontoInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeConfrontoProcess", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeConfrontoProcessTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeConfrontoProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeConfrontoProcessUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeDispaccModuliInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeDispaccModuliInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeMatlabZipFileInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/EzekeMatlab/EzekeMatlabZipFileInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeMatlabZipFileProcessUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeMatlabZipFileProcessPolling", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/EzekeMatlab/EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "EzekeMatlabZipFileProcess", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/EzekeMatlab/EzekeMatlabZipFileProcessTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "EzekeAnelloProcessPolling", "LocationName": "EzekeAnelloProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeAnelloProcessPolling", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeAnelloProcessTypedPolling.bindinginfo.xml"}, {"PortName": "EzekeDispaccProcessPolling", "LocationName": "EzekeDispaccProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeDispaccProcessPolling", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeDispaccProcessTypedPolling.bindinginfo.xml"}, {"PortName": "EzekeConfrontoProcessPolling", "LocationName": "EzekeConfrontoProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeConfrontoProcessPolling", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/Ezeke/EzekeConfrontoProcessTypedPolling.bindinginfo.xml"}, {"PortName": "EzekeMatlabZipFileProcessPolling", "LocationName": "EzekeMatlabZipFileProcessPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeMatlabZipFileProcessPolling", "File": "Sviluppo/A2A.EAI/INT_BI/A2A.EAI.INT_BI.Messaging/Bindings/EzekeMatlab/EzekeMatlabZipFileProcessTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_BI_CMI": {"to_ndue": [{"PortName": "N2MessaggiErroriAFRR", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/BDM/F6MessaggiErroriAFRR.asmx", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/RpaEsitoAfrr/N2MessaggiErroriAFRR.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "BiCmiImpiantiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW036//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/SyncImpianti/BiCmiImpiantiInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "MercatiMrrDatiUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/MercatiMrrDati/MercatiMrrDatiUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "MercatiMrrDatiInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/MercatiMrrDati/MercatiMrrDatiInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "DateOreQuartiEstesaInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/SyncDateOreQuartiEstesa/DateOreQuartiEstesaInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "RpaEsitoMrrInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw036//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/RpaEsitoMrr/RpaEsitoMrrInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "RpaEsitoAfrrInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/RpaEsitoAfrr/RpaEsitoAfrrInsertTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "BiCmiSyncImpiantiPolling", "LocationName": "BiCmiSyncImpiantiPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=BiCmiSyncImpianti", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/SyncImpianti/BiCmiSyncImpiantiTypedPolling.bindinginfo.xml"}, {"PortName": "MercatiMrrDatiPolling", "LocationName": "MercatiMrrDatiPolling", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?InboundId=idMercatiRrDati", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/MercatiMrrDati/MercatiMrrDatiTypedPolling.bindinginfo.xml"}, {"PortName": "BiCmiSyncDateOreQuartiEstesaPolling", "LocationName": "BiCmiSyncDateOreQuartiEstesaPolling", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?InboundId=idSyncDateOreQuartiEstesa", "File": "Sviluppo/A2A.EAI/INT_BI_CMI/A2A.EAI.INT_BI_CMI.Messaging/Bindings/SyncDateOreQuartiEstesa/BiCmiSyncDateOreQuartiEstesaTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_EXPORT": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusGenericReplyToEsitoMECentrale", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToEsitoMECentrale.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToTIMM", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToTIMM.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToGEDIAux", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToGEDIAux.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToFoglioStampa", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToFoglioStampa.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToIpexOffertePce", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToIpexOffertePce.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToBackofficeSfilippoDISP", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToBackofficeSfilippoDISP.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToBackofficeSFilippoOFFCRIDA", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToBackofficeSFilippoOFFCRIDA.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToBackofficeSFilippoOFFMGP", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToBackofficeSFilippoOFFMGP.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToEsitoMSDCentrale", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToEsitoMSDCentrale.bindinginfo.xml"}, {"PortName": "VirtusSociMincioGateNotificationUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusSociMincioGateNotificationUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusSociMincioGateInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusSociMincioGateInsert.bindinginfo.xml"}, {"PortName": "VirtusGenericReplyToComDispCteMincio", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusGenericReplyToComDispCteMincio.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusSociMincioNotificationPolling", "LocationName": "VirtusSociMincioNotificationPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusSociMincioNotificationPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_EXPORT/A2A.EAI.INT_MRKOPT_EXPORT.Messaging/Bindings/VirtusSociMincioNotificationPolling.bindinginfo.xml"}], "unknown": []}, "INT_EXTEVENT": {"to_ndue": [{"PortName": "sptControlliDigheTerremotiWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/N2SoapWebApiCore/Dighe/WsControlliDigheTerremoti.asmx", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/schControlliDigheTerremotiWs.BindingInfo.xml"}, {"PortName": "sptBiQuakeAnalysisInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW016//NbdoEaiBi?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/schBiQuakeAnalysisInsertTypedProcedure.bindinginfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptEaiQuakeEventInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//A2A.EAI?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/schEaiQuakeEventInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptQuakeNotificationUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/sptQuakeNotificationUpdateStatus.bindinginfo.xml"}, {"PortName": "sptN2QuakeEventInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/schN2QuakeEventInsertTypedProcedure.bindinginfo.xml"}, {"PortName": "sptProtezioneCivileUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/ProtezioneCivile/schProtezioneCivileUpdateStatusTypedProcedure.bindinginfo.xml"}, {"PortName": "ProtezioneCivileAllertaDigheFileUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/ProtezioneCivile/ProtezioneCivileFileUpdateStatusTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "rptQuakeNotificationPolling", "LocationName": "rlcQuakeNotificationPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=QuakeNotificationPolling", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/Quake/rptQuakeNotificationPolling.bindinginfo.xml"}, {"PortName": "rptProtezioneCivilePolling", "LocationName": "rlcProtezioneCivilePolling", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?InboundId=ProtezioneCivilePolling", "File": "Sviluppo/A2A.EAI/INT_EXTEVENT/A2A.EAI.INT_EXTEVENT.Messaging/Bindings/ProtezioneCivile/schProtezioneCivileTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_SETTLEMENT": {"to_ndue": [{"PortName": "sppCorrispettivoNonArbitraggioMacrozonaleWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://dctsvw034.group.local/nbdows/CorrispettivoNonArbitraggioMacrozonale.asmx", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/CorrispettivoNaMz/schCorrispettivoNaMz.BindingInfo.xml"}, {"PortName": "WcfSendPort_CorrispettivoNonArbitraggioMacrozonale_CorrispettivoNonArbitraggioMacrozonaleSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://dctsvw034.group.local/nbdows/CorrispettivoNonArbitraggioMacrozonale.asmx", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/CorrispettivoNaMz/schCorrispettivoNaMz.BindingInfo.xml"}, {"PortName": "sptLiquidazioneGiornalieraWsImport", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://dctsvw034.group.local/nbdows/BDM/F6LiquidazioneGiornalieraPCE.asmx", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/LiquidGiornaliera/schF6LiquidazioneGiornalieraWs.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptDatiMeteringInsertStg", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/DatiMetering/schDatiMeteringInsertStgTypedProcedure.bindinginfo.xml"}, {"PortName": "sptLiquidazioneGiornalieraUpdate", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/LiquidGiornaliera/schLiquidGiornalieraUpdateTypedProcedure.bindinginfo.xml"}, {"PortName": "sptLiquidazioneGiornalieraGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/LiquidGiornaliera/schLiquidGiornalieraGetDataTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "rptDatiMeteringEnelFilePolling", "LocationName": "rlcDatiMeteringEnelFilePolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=DatiMeteringEnelFilePolling", "File": "Sviluppo/A2A.EAI/INT_SETTLEMENT/A2A.EAI.INT_SETTLEMENT.Messaging/Bindings/DatiMetering/schDatiMeteringEnelFileTypedPolling.bindinginfo.xml"}], "unknown": []}, "INT_MRKOPT_IMPORT": {"to_ndue": [], "from_ndue": [], "to_other": [{"PortName": "VirtusNotesUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusNotesUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusPCEBusesInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusPCEBusesInsert.bindinginfo.xml"}, {"PortName": "VirtusUnitSchedulePCEBusesNotificationUpdateStatus", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusUnitSchedulePCEBusesNotificationUpdateStatus.bindinginfo.xml"}, {"PortName": "VirtusHydroGuidelinesToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusHydroGuidelinesToVirtusRequest.bindinginfo.xml"}, {"PortName": "VirtusUnitCommitmentToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusUnitCommitmentToVirtusRequest.bindinginfo.xml"}, {"PortName": "RispUCcteMincioToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusRispUCcteMincioToVirtusRequestTypedProcedure.bindinginfo.xml"}], "from_other": [{"PortName": "VirtusNotesPolling", "LocationName": "VirtusNotesPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusNotes", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusNotesPolling.bindinginfo.xml"}, {"PortName": "VirtusUnitSchedulePCEBusesNotificationPolling", "LocationName": "VirtusUnitSchedulePCEBusesNotificationPolling", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?InboundId=VirtusUnitSchedulePCEBusesNotificationPolling", "File": "Sviluppo/A2A.EAI/INT_MRKOPT_IMPORT/A2A.EAI.INT_MRKOPT_IMPORT.Messaging/Binding/VirtusUnitSchedulePCEBusesNotificationPolling.bindinginfo.xml"}], "unknown": []}, "INT_MERCATO": {"to_ndue": [{"PortName": "WcfSendPort_ImportScambioZonaliTerna_ImportScambioZonaliTernaSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/NBDOWS/BDM/ImportScambiZonaliTerna.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Schemas/ScambiZonali/ImportScambioZonaliTerna.BindingInfo.xml"}, {"PortName": "WcfSendPort_ImportScambioZonaliTerna_ImportScambioZonaliTernaSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivtsbdo/NBDOWS/BDM/ImportScambiZonaliTerna.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Schemas/ScambiZonali/ImportScambioZonaliTerna.BindingInfo.xml"}, {"PortName": "sptNbdoMercatoDelete", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schMercatoDelete.bindinginfo.xml"}, {"PortName": "sptNbdoOfferteMsdImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schNbdoOfferteMsdImport.BindingInfo.xml"}, {"PortName": "WcfSendPort_F7OfferteMSDImport_F7OfferteMSDImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schNbdoOfferteMsdImport.BindingInfo.xml"}, {"PortName": "sptEmissioniSnamImportWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/SNAMGiornalieroImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schEmissioniSnam.BindingInfo.xml"}, {"PortName": "WcfSendPort_SNAMGiornalieroImport_SNAMGiornalieroImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivtsbdo/nbdows/SNAMGiornalieroImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schEmissioniSnam.BindingInfo.xml"}, {"PortName": "sptNbdoMercatoGMEMGPHeaderInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schMercatoGMEMGPHeaderInsert.bindinginfo.xml"}, {"PortName": "sptNbdoImportSnam", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/SNAMImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportSnam.BindingInfo.xml"}, {"PortName": "WcfSendPort_SNAMImport_SNAMImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/SNAMImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportSnam.BindingInfo.xml"}, {"PortName": "sptNbdoDAATelematicoImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/ImportDAATelematico.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportDAATelematico.BindingInfo.xml"}, {"PortName": "WcfSendPort_ImportDAATelematico_ImportDAATelematicoSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/ImportDAATelematico.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportDAATelematico.BindingInfo.xml"}, {"PortName": "sptOffertePceUpdate", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schOffertePceUpdateTypedProcedure.bindinginfo.xml"}, {"PortName": "sptNbdoPceImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/bdm/F7OffertePCE.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/F7OffertePCEImport.BindingInfo.xml"}, {"PortName": "sptNbdoMercatoGMEMGPCompositeInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schMercatoGMEMGPItemInsert.bindinginfo.xml"}, {"PortName": "WcfSendPort_AggregatiMacrozona_AggregatiMacrozonaSoap", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/AggregatiMacrozona.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schAggregatiMacrozona.BindingInfo.xml"}, {"PortName": "sptNbdoMercatoFEEGMEMGPHeaderInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schMercatoFEEGMEMGPHeaderInsert.bindinginfo.xml"}, {"PortName": "sptMercatoBidNotificationInsert", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://epmivtnbdo//nbdo?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schMercatoBidNotificationInsert.bindinginfo.xml"}, {"PortName": "sptNbdoBdmGrtnImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/FileGrtnImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportBdmGrtn.BindingInfo.xml"}, {"PortName": "WcfSendPort_FileGrtnImport_FileGrtnImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/FileGrtnImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportBdmGrtn.BindingInfo.xml"}, {"PortName": "sptOffertePceGetData", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schOffertePceGetDataTypedProcedure.bindinginfo.xml"}, {"PortName": "sptNbdoPrezziMBPImport", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmiftsbdo/NBDOWS/BDM/F6PrezziMBPImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportMBPF6WS.BindingInfo.xml"}, {"PortName": "WcfSendPort_F6PrezziMBPImport_F6PrezziMBPImportSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmiftsbdo/NBDOWS/BDM/F6PrezziMBPImport.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schImportMBPF6WS.BindingInfo.xml"}, {"PortName": "sptN2PciGas", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/nbdows/BDM/Gas/DatiPCIGas.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schN2PciGas.BindingInfo.xml"}, {"PortName": "sptNbdoImportSegniPrezziZonaliTerna", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/nbdows/BDM/ImportSegniPrezziZonaliTerna.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schNbdoImportSegniPrezziZonaliTerna.BindingInfo.xml"}, {"PortName": "WcfSendPort_ImportSegniPrezziZonaliTerna_ImportSegniPrezziZonaliTernaSoap12", "LocationName": "", "TransportType": "WCF-Custom", "Address": "http://epmivtsbdo/nbdows/BDM/ImportSegniPrezziZonaliTerna.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/schNbdoImportSegniPrezziZonaliTerna.BindingInfo.xml"}, {"PortName": "OffertePceInsertStg", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://dctsvw035//NBDO.EAI?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/OffertePceInsertStgTypedProcedure.bindinginfo.xml"}, {"PortName": "sptImportPrezziSbilTernaWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOws/BDM/ImportPrezziSbilTerna.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/PrezziSbilanciamento/schImportPrezziSbilTernaWs.BindingInfo.xml"}, {"PortName": "sptNbdoPrezziSbilanciamentoZonali", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://epmivtsbdo/NBDOWS/PrezziSbilanciamentoZonali.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/PrezziSbilanciamentoZonali/schPrezziSbilanciamentoZonaliWs.BindingInfo.xml"}, {"PortName": "sptPrezziMarginaliAfrrWs", "LocationName": "", "TransportType": "WCF-BasicHttp", "Address": "http://ndue.test.a2a.eu/NBDOWS/BDM/F6PrezziMarginaliAFRR.asmx", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/PrezziMarginaliAfrr/schPrezziMarginaliAfrrWs.BindingInfo.xml"}], "from_ndue": [], "to_other": [{"PortName": "sptVirtusMercatoInputMsdToVirtusRequest", "LocationName": "", "TransportType": "WCF-Custom", "Address": "mssql://DCTSVW035//EAI?", "File": "Sviluppo/A2A.EAI/INT_MERCATO/A2A.EAI.INT_MERCATO.Messaging/Bindings/VirtusMercatoInputMsdToVirtusRequest.bindinginfo.xml"}], "from_other": [], "unknown": []}}