# INT_* Flows Classification (NDUE vs Others)

## INT_ACS
- To NDUE (SendPorts): 6
  - WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap → http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx
  - WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap12 → http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx
  - WcfSendPort_Ricavi_RicaviSoap → http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx
  - WcfSendPort_Ricavi_RicaviSoap12 → http://dctsvw034.group.local/nbdows/bdm/gas/ricavi.asmx
  - WcfSendPort_ConsumiUnareti_ConsumiUnaretiSoap → http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiUnareti.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 5
  - ACSCO2EmesseUnaretiUpdateStatus → mssql://DCTSVW035//EAI?
  - ACSVenditaCaloreBresciaInsert → mssql://DCTSVW035//EAI?
  - ACSVenditaCaloreMilanoInsert → mssql://DCTSVW035//EAI?
  - ACSCO2EmesseRetragasInsert → mssql://DCTSVW035//EAI?
  - ACSCO2EmesseUnaretiInsert → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_AMBIENTE
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 2
  - sptDatiLaboratorioInsert → mssql://DCTSVW035//EAI?
  - sptDatiLaboratorioCheck → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_BDE
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 5
  - sptEaiBdeSr → mssql://DCTSVW035//EAI?
  - sptEaiBdeLb → mssql://DCTSVW035//EAI?
  - sptEaiBdeGetEmailAddr → mssql://DCTSVW035//EAI?
  - sptEaiBdeCb → mssql://DCTSVW035//EAI?
  - sptEaiBdeRc → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_BI
- To NDUE (SendPorts): 2
  - ForecastInsert → mssql://dctsvw016//DWH_NBDO?
  - BudgetInsert → mssql://DCTSVW016//DWH_NBDO?
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 13
  - EzekeDispaccProcessUpdateStatus → mssql://DCTSVW016//DWH_EZEKE?
  - EzekeAnelloProcessUpdateStatus → mssql://dctsvw016//DWH_EZEKE?
  - EzekeAnelloProcess → mssql://DCTSVW016//DWH_EZEKE?
  - EzekeAnelloInsert → mssql://DCTSVW016//DWH_EZEKE?
  - EzekeDispaccProcess → mssql://DCTSVW016//DWH_EZEKE?
- From Others (ReceiveLocations): 4
  - EzekeAnelloProcessPolling ← mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeAnelloProcessPolling
  - EzekeDispaccProcessPolling ← mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeDispaccProcessPolling
  - EzekeConfrontoProcessPolling ← mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeConfrontoProcessPolling
  - EzekeMatlabZipFileProcessPolling ← mssql://DCTSVW016//DWH_EZEKE?InboundId=EzekeMatlabZipFileProcessPolling

## INT_BI_CMI
- To NDUE (SendPorts): 1
  - N2MessaggiErroriAFRR → http://ndue.test.a2a.eu/NBDOWS/BDM/F6MessaggiErroriAFRR.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 6
  - BiCmiImpiantiInsert → mssql://DCTSVW036//DWH_CMI?
  - MercatiMrrDatiUpdateStatus → mssql://dctsvw035//EAI?
  - MercatiMrrDatiInsert → mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?
  - DateOreQuartiEstesaInsert → mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?
  - RpaEsitoMrrInsert → mssql://dctsvw036//DWH_CMI?
- From Others (ReceiveLocations): 3
  - BiCmiSyncImpiantiPolling ← mssql://DCTSVW035//EAI?InboundId=BiCmiSyncImpianti
  - MercatiMrrDatiPolling ← mssql://dctsvw035//EAI?InboundId=idMercatiRrDati
  - BiCmiSyncDateOreQuartiEstesaPolling ← mssql://dctsvw035//EAI?InboundId=idSyncDateOreQuartiEstesa

## INT_BROKER
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 1
  - GediRouterGetConfig → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_ETRM
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 4
  - EtrmSettAppContractInsert → mssql://DCTSVW035//EAI?
  - EtrmSettAppContractPriceInsert → mssql://DCTSVW035//EAI?
  - EtrmSettAppPowerThresholdInsert → mssql://DCTSVW035//EAI?
  - EtrmSettAppFileUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_EXPORT
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 3
  - sptErgIndisponibilitaToGediUpdateStatus → mssql://DCTSVW035//EAI?
  - sptDisponibilitaErgGediInsert → mssql://DCTSVW035//EAI?
  - sptErgGediDisponibilitaToGediUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - rptErgGediDisponibilitaToGediPolling ← mssql://DCTSVW035//EAI?InboundId=ErgGediDisponibilitaToGediPolling
  - rlcErgIndisponibilitaToGediPolling ← mssql://DCTSVW035//EAI?InboundId=ErgIndisponibilitaToGedi

## INT_EXTEVENT
- To NDUE (SendPorts): 2
  - sptControlliDigheTerremotiWs → http://ndue.test.a2a.eu/N2SoapWebApiCore/Dighe/WsControlliDigheTerremoti.asmx
  - sptBiQuakeAnalysisInsert → mssql://DCTSVW016//NbdoEaiBi?
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 5
  - sptEaiQuakeEventInsert → mssql://DCTSVW035//A2A.EAI?
  - sptQuakeNotificationUpdateStatus → mssql://DCTSVW035//EAI?
  - sptN2QuakeEventInsert → mssql://dctsvw035//EAI?
  - sptProtezioneCivileUpdateStatus → mssql://dctsvw035//EAI?
  - ProtezioneCivileAllertaDigheFileUpdateStatus → mssql://dctsvw035//EAI?
- From Others (ReceiveLocations): 2
  - rlcQuakeNotificationPolling ← mssql://DCTSVW035//EAI?InboundId=QuakeNotificationPolling
  - rlcProtezioneCivilePolling ← mssql://dctsvw035//EAI?InboundId=ProtezioneCivilePolling

## INT_GME
- To NDUE (SendPorts): 4
  - N2PrezziZonaliMrrImport → http://dctsvw034.group.local/nbdows/BDM/F6PrezziZonaliMRR.asmx
  - PrezziPresentatiAfrrWs → http://ndue.test.a2a.eu/NBDOWS/BDM/F6PrezziPresentatiAFRR.asmx
  - N2OfferteMbImport → http://dctsvw034.group.local/nbdows/BDM/F6OfferteMB.asmx
  - N2OfferteMrrImport → http://dctsvw034.group.local/nbdows/BDM/F6OfferteMRR.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 11
  - GmeBidNotificationInsert → mssql://DCTSVW035//EAI?
  - VirtusUnitScheduleMktResExtUpdateStatus → mssql://DCTSVW035//EAI?
  - EaiVirtusReplyToIpexOfferteMsd → mssql://DCTSVW035//EAI?
  - VirtusUnitScheduleInsert → mssql://DCTSVW035//EAI?
  - VirtusGmePrezziEsitiZonaliToVirtusCommon → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - VirtusUnitScheduleMktResExtPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusUnitScheduleMktResExtPolling

## INT_IMPORT
- To NDUE (SendPorts): 28
  - sptNbdoPvmcGrtnImport → http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx
  - WcfSendPort_SbilanciamentiImportPvmcGrtn_SbilanciamentiImportPvmcGrtnSoap12 → http://epmiftsbdo/NBDOWS/SbilmportPvmGrtn.asmx
  - WcfSendPort_F2DatiMeteoIdroImport_F2DatiMeteoIdroImportSoap → http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoIdroImport.asmx
  - WcfSendPort_F2DatiMeteoIdroImport_F2DatiMeteoIdroImportSoap12 → http://epmiftsbdo/NBDOWS/BDM/F2DatiMeteoIdroImport.asmx
  - sptDatiMeteringImport → http://epmivtsbdo/nbdows/BDM/F10DatiMeteringTernaImport.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 5
  - sptDatiIdriciScadaWS → http://epmilnint3:81/ws_GetProduzione_New/DatiIdriciScada.asmx
  - sptImportSegnanti → mssql://DCTSVW035//EAI?
  - ErgEnergiaImmessaUpdateStatus → mssql://dctsvw035//EAI?
  - sptProtezioneCivileAllertaDigheCheck → mssql://dctsvw035//EAI?
  - sptDatiMeteringTernaGetData → mssql://dctsvw035//EAI?
- From Others (ReceiveLocations): 1
  - rlcErgEnergiaImmessaPolling ← mssql://dctsvw035//EAI?InboundId=ErgEnergiaImmessaId

## INT_LPREPORT
- To NDUE (SendPorts): 1
  - N2LPReportConsumoContatoriGas → http://ndue.test.a2a.eu/nbdows/BDM/Gas/LpRConsumoContatoriGas.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 4
  - EAILPReportResubmit → mssql://dctsvw035//EAI?
  - LPReportManualDatiMeteringGasTransform → mssql://DCTSVW035//EAI?
  - EAILPReportDatiMeteringGasTransform → mssql://DCTSVW035//EAI?
  - EAILPReportUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - EAILPReportPolling ← mssql://DCTSVW035//EAI?InboundId=lpreport

## INT_LUNA
- To NDUE (SendPorts): 1
  - MessaggiLunaWs → http://ndue.test.a2a.eu/NBDOWS/MessaggiLUNA.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 3
  - LunaMessaggiInsert → mssql://DCTSVW035//EAI?
  - LunaVCRUpdateStatus → mssql://DCTSVW035//EAI?
  - LunaN2UpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - LunaVCRPolling ← mssql://DCTSVW035//EAI?InboundId=LunaVCRPolling
  - LunaN2Polling ← mssql://DCTSVW035//EAI?InboundId=LunaN2Polling

## INT_MERCATO
- To NDUE (SendPorts): 30
  - WcfSendPort_ImportScambioZonaliTerna_ImportScambioZonaliTernaSoap → http://epmivtsbdo/NBDOWS/BDM/ImportScambiZonaliTerna.asmx
  - WcfSendPort_ImportScambioZonaliTerna_ImportScambioZonaliTernaSoap12 → http://epmivtsbdo/NBDOWS/BDM/ImportScambiZonaliTerna.asmx
  - sptNbdoMercatoDelete → mssql://epmivtnbdo//nbdo?
  - sptNbdoOfferteMsdImport → http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx
  - WcfSendPort_F7OfferteMSDImport_F7OfferteMSDImportSoap12 → http://epmiftsbdo/NBDOWS/BDM/F7OfferteMSD.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 1
  - sptVirtusMercatoInputMsdToVirtusRequest → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_MONITOR
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 1
  - MonitorQueueUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - MonitorQueuePolling ← mssql://DCTSVW035//EAI?InboundId=MonitorQueuePolling

## INT_MRKOPT
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 1
  - VirtusReplyMgmtRateLimiter → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_MRKOPT_BID
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 7
  - VirtusToEnergyBidOffertePceGetData → mssql://dctsvw035//EAI?
  - VirtusToEnergyBidOfferteMgpGetData → mssql://dctsvw035//EAI?
  - VirtusToEnergyBidOfferteMbGetData → mssql://dctsvw035//EAI?
  - VirtusToEnergyBidOfferteMrrGetData → mssql://dctsvw035//EAI?
  - VirtusToEnergyBidOfferteMsdGetData → mssql://dctsvw035//EAI?
- From Others (ReceiveLocations): 0

## INT_MRKOPT_EXPORT
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 12
  - VirtusGenericReplyToEsitoMECentrale → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToTIMM → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToGEDIAux → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToFoglioStampa → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToIpexOffertePce → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - VirtusSociMincioNotificationPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusSociMincioNotificationPolling

## INT_MRKOPT_GEDI
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 34
  - VirtusGediVincoliDiReteTableSelect → oracledb://DBDISPQ/
  - VirtusGediMiglioriPrevisioniInsert → mssql://DCTSVW035//EAI?
  - VirtusGediPrezziAttesiInsert → mssql://DCTSVW035//EAI?
  - VirtusGediDispPrimariaTableSelect → oracledb://DBDISPQ/
  - VirtusGediDispTableSelect → oracledb://DBDISPQ/
- From Others (ReceiveLocations): 14
  - VirtusGediUPSAPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusGediUPSAPolling
  - VirtusGediNomineUCPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusGediNomineUCPolling
  - VirtusGediDispPrimariaPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusGediDispPrimariaPolling
  - VirtusGediMsdPricesPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusGediMsdPricesPolling
  - VirtusGediMiglioriPrevisioniPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusGediMiglioriPrevisioniPolling

## INT_MRKOPT_IMPORT
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 6
  - VirtusNotesUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusPCEBusesInsert → mssql://DCTSVW035//EAI?
  - VirtusUnitSchedulePCEBusesNotificationUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusHydroGuidelinesToVirtusRequest → mssql://DCTSVW035//EAI?
  - VirtusUnitCommitmentToVirtusRequest → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - VirtusNotesPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusNotes
  - VirtusUnitSchedulePCEBusesNotificationPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusUnitSchedulePCEBusesNotificationPolling

## INT_MRKOPT_IMPORTAUX
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 9
  - VirtusImportAuxPreparedOffersDACRIDAToVirtusRequest → mssql://DCTSVW035//EAI?
  - VirtusImportAuxPreparedOffersMRRToVirtusRequest → mssql://DCTSVW035//EAI?
  - VirtusImportAuxPreparedOffersAFRRToVirtusRequest → mssql://DCTSVW035//EAI?
  - VirtusImportAuxPreparedMIBToVirtusRequest → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToCaricamentiMassivi → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 0

## INT_MRKOPT_N2
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 3
  - VirtusPvmUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusN2PrgBaseUpdateStatus → mssql://dctsvw035//EAI?
  - VirtusPvmGetData → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - VirtusPvmPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusPvmPolling
  - VirtusN2PrgBasePolling ← mssql://DCTSVW035//EAI?InboundId=VirtusN2PrgBasePolling

## INT_MRKOPT_QTZ
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 4
  - VirtusQuotazioniFuelPricesInsert → mssql://DCTSVW035//EAI?
  - VirtusQuotazioniFuelPricesTableSelect → oracledb://A2AQ/
  - VirtusDbSyncQuotazioniUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusDbSyncProcessQuotazioniUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 3
  - VirtusQuotazioniFuelPricesPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusQuotazioniFuelPricesPolling
  - VirtusDbSyncQuotazioniPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusDbSyncQuotazioniPolling
  - VirtusQuotazioniCO2PricesPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusQuotazioniCO2PricesPolling

## INT_PI
- To NDUE (SendPorts): 17
  - PiExportN2AiaAriaWs → http://ndue.test.a2a.eu/nbdows/AIA/WS_AIA_Aria.asmx
  - PiExportN2LghImportWs → http://dctsvw034/nbdows/BDM/F2LGHImport.asmx
  - PiExportN2ArpaWs → http://dctsvw034/nbdows/BDM/DatiARPA.asmx
  - PiExportN2AiaAriaTotWs → http://ndue.test.a2a.eu/NBDOWS/AIA/WS_AIA_AriaEmissioniTOT.asmx
  - PiExportN2TensioneWsOut → http://dctsvw034/nbdows/BDM/ImportTensioneEffettiva.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 19
  - sptPITimeSeriesService → http://epmivpi001.edipower.produzione.energia/PIWebServices/PITimeSeries.svc
  - PiExportN2RipartizioneEnergia → mssql://DCTSVW035//EAI?
  - PiImportMobygisGetData → mssql://dctsvw035//EAI?
  - PiImportVitecGetData → mssql://dctsvw035//EAI?
  - PiExportModelliPrevisionaliWind → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - WcfReceiveLocation_SqlAdapterBinding_TypedPolling_idPiImportPvmPolling_Custom ← mssql://dctsvw035//EAI?InboundId=idPiImportPvmPolling

## INT_PIANI
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 7
  - EaiPianoUpdateStatus → mssql://DCTSVW035//EAI?
  - EaiPianoCmiInsert → mssql://DCTSVW035//EAI?
  - EaiGettoniSappInsert → mssql://DCTSVW035//EAI?
  - EaiGettoniCmiInsert → mssql://DCTSVW035//EAI?
  - EaiPianoSappInsert → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - EaiGettoniPolling ← mssql://DCTSVW035//EAI?InboundId=GettoniPolling
  - EaiPianoPolling ← mssql://DCTSVW035//EAI?InboundId=PianoPolling

## INT_PVM
- To NDUE (SendPorts): 30
  - sptNbdoEsitoMA1MGPImport → http://epmiftsbdo/NBDOWS/BDM/F6QuantityImport.asmx
  - WcfSendPort_F6QuantityImport_F6QuantityImportSoap12 → http://epmiftsbdo/NBDOWS/BDM/F6QuantityImport.asmx
  - sptNbdoFmsImport → http://epmivsvbd1/NBDOWS/SbilmportFms.asmx
  - WcfSendPort_SbilanciamentiImportFms_SbilanciamentiImportFmsSoap12 → http://epmivsvbd1/NBDOWS/SbilmportFms.asmx
  - sptNbdoInsertStaging → mssql://epmivtnbdo//nbdo?
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 6
  - sptEaiRupUpdateStatus → mssql://DCTSVW035//EAI?
  - sptPvmUnitaProduttiveAbilitazione → mssql://DCTSVW035//EAI?
  - VirtusBackofficeSpacchettamentoGetData → mssql://DCTSVW035//EAI?
  - sptEaiRupStagingInsert → mssql://DCTSVW035//EAI?
  - sptCmiSemibandaInsert → mssql://a2a-noprod-cruscottosapp-sqlsrv.database.windows.net//DWH_CMI?
- From Others (ReceiveLocations): 1
  - rlcRupPolling ← mssql://DCTSVW035//EAI?InboundId=RupPolling

## INT_SAPP
- To NDUE (SendPorts): 1
  - sptNbdoMercIGCruscottoUpdateStatus → mssql://epmivtnbdo//NBDO.EAI?
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 11
  - SAPPOptitForecastUpdateStatus → mssql://DCTSVW035//EAI?
  - WcfSendPort_SqlAdapterBinding_TypedProcedures_dbo_Custom → mssql://dctsvw035//EAI?
  - WcfSendPort_SqlAdapterBinding_TypedProcedures_dbo_Custom → mssql://dctsvw035//EAI?
  - SAPPOptitForecastInsert → mssql://DCTSVW035//EAI?
  - sptNbdoPvmWeb → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 4
  - rlcSappPvmGettoni ← mssql://DCTSVW035//EAI?InboundId=SappPvmGettoni
  - WcfReceiveLocation_SqlAdapterBinding_TypedPolling_sapppvmnoncalcolati_Custom ← mssql://DCTSVW035//EAI?InboundId=sapppvmnoncalcolati
  - SAPPOptitForecastPolling ← mssql://DCTSVW035//EAI?InboundId=SAPPOptitForecastPolling
  - rlcNbdoPvm ← mssql://DCTSVW035//EAI?InboundId=sapppvm

## INT_SA_DB_Common
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 0
- From Others (ReceiveLocations): 1
  - SettAppDbNotificationToSaPolling ← mssql://DCTSVW035//EAI?InboundId=SettAppDbNotificationToSaPolling

## INT_SA_DB_QTSA
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 3
  - SettAppIndexesTable → oracledb://SETAPPD/
  - QuotazioniIndexesTableSelect → oracledb://A2AQ/
  - SettAppDbQtSaUpdateStatus → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - SettAppDbQtSaPolling ← mssql://DCTSVW035//EAI?InboundId=SettAppDbQtSaPolling

## INT_SA_DB_VAL
- To NDUE (SendPorts): 2
  - WcfSendPort_CalcoloSbilanciamentoEconomico_CalcoloSbilanciamentoEconomicoSoap → http://ndue.test.a2a.eu/NBDOWS/CalcoloSbilanciamentoEconomico.asmx
  - WcfSendPort_CalcoloSbilanciamentoEconomico_CalcoloSbilanciamentoEconomicoSoap12 → http://ndue.test.a2a.eu/NBDOWS/CalcoloSbilanciamentoEconomico.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 14
  - SettAppSbilTableSelect → oracledb://SETAPPD/
  - SettAppSbilEimmTableSelect → oracledb://SETAPPD/
  - SettAppCalcoloSbilUpdateStatus → mssql://DCTSVW035//EAI?
  - SettAppDbValUpdateStatus → mssql://DCTSVW035//EAI?
  - SettAppDbValN2MsdInsert → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 2
  - SettAppCalcoloSbilPolling ← mssql://DCTSVW035//EAI?InboundId=SettAppCalcoloSbilPolling
  - SettAppDbValPolling ← mssql://DCTSVW035//EAI?InboundId=SettAppDbValPolling

## INT_SA_FATT
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 2
  - SettAppFatturaEneforbilInsert → mssql://DCTSVW035//EAI?
  - SettAppFatturaProcessUpdateStatus → mssql://dctsvw035//EAI?
- From Others (ReceiveLocations): 0

## INT_SC
- To NDUE (SendPorts): 0
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 19
  - SCToVirtus → sb://a2a-tst-integrationservices.servicebus.windows.net/mrk-optimizer-biztalk-to-virtus/
  - VirtusRupPrepareUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToDEI → mssql://DCTSVW035//EAI?
  - VirtusRupProcessUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusGenericReplyToTPAA → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 5
  - VirtusSCAskReportPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusSCAskReportPolling
  - VirtusVegProcessPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusVegProcessPolling
  - VirtusSCDeiPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusSCDeiPolling
  - VirtusRupPreparePolling ← mssql://DCTSVW035//EAI?InboundId=VirtusRupPreparePolling
  - VirtusRupProcessPolling ← mssql://DCTSVW035//EAI?InboundId=VirtusRupProcessPolling

## INT_SETTLEMENT
- To NDUE (SendPorts): 3
  - sppCorrispettivoNonArbitraggioMacrozonaleWs → http://dctsvw034.group.local/nbdows/CorrispettivoNonArbitraggioMacrozonale.asmx
  - WcfSendPort_CorrispettivoNonArbitraggioMacrozonale_CorrispettivoNonArbitraggioMacrozonaleSoap12 → http://dctsvw034.group.local/nbdows/CorrispettivoNonArbitraggioMacrozonale.asmx
  - sptLiquidazioneGiornalieraWsImport → http://dctsvw034.group.local/nbdows/BDM/F6LiquidazioneGiornalieraPCE.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 3
  - sptDatiMeteringInsertStg → mssql://DCTSVW035//EAI?
  - sptLiquidazioneGiornalieraUpdate → mssql://dctsvw035//EAI?
  - sptLiquidazioneGiornalieraGetData → mssql://dctsvw035//EAI?
- From Others (ReceiveLocations): 1
  - rlcDatiMeteringEnelFilePolling ← mssql://DCTSVW035//EAI?InboundId=DatiMeteringEnelFilePolling

## INT_XBID
- To NDUE (SendPorts): 4
  - WcfSendPort_ImportXbid_ImportXbidSoap → http://ndue.test.a2a.eu/NBDOWS/ImportXbid.asmx
  - WcfSendPort_ImportXbid_ImportXbidSoap12 → http://ndue.test.a2a.eu/NBDOWS/ImportXbid.asmx
  - ImportXbidOut → http://dctsvw034.group.local/NBDOws/ImportXbid.asmx
  - N2ImportPrezziSaldoCommerciale → http://ndue.test.a2a.eu/NBDOws/BDM/PrezziSaldoCommerciale.asmx
- From NDUE (ReceiveLocations): 0
- To Others (SendPorts): 4
  - XbidPhysicalProgramInsert → mssql://DCTSVW035//EAI?
  - XbidPhysicalProgramUpdateStatus → mssql://DCTSVW035//EAI?
  - VirtusFeasibilityIntervalsToVirtusRequest → mssql://DCTSVW035//EAI?
  - XbidPhysicalProgramInsert → mssql://DCTSVW035//EAI?
- From Others (ReceiveLocations): 1
  - XbidPhysicalProgramPolling ← mssql://DCTSVW035//EAI?InboundId=XbidPhysicalProgramPolling
