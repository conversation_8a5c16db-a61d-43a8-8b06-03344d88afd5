Web API Endpoints (AS-IS)

Projects analyzed: A2A.EAI.Services.WebApi, A2A.EAI.INT_PIANI_SVC.WebAPI, A2A.EAI.INT_SA_SVC.WebAPI.

Security and common
- Access control: Attribute `AccessControlService` applied on most controllers (custom, from MsysEaiFx). See usages in:
  - Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/SappCmiProxyController.cs:25
  - Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/NdueController.cs:14
  - Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.WebAPI/Controllers/SappController.cs:20
  - Sviluppo/A2A.EAI.WebApi/INT_SA_SVC/A2A.EAI.INT_SA_SVC.WebAPI/Controllers/ValidationController.cs:20
- Swagger: `ApiKey` security scheme defined (query `api_key`) in Program.cs of each WebApi.


A2A.EAI.Services.WebApi
- GET /api/Version/Version
  - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/VersionController.cs:16
  - Returns `{ version }` from assembly ProductVersion.

- POST /api/Ndue/PrgBase
  - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/NdueController.cs:27
  - Body: `PrgBaseRequest` (UP, CreatedOn, OriginalFileName, Market, PrgBase[])
    - Schema: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Model/PrgBaseRequest.cs:7
  - Behavior: persists payload via stored procedure `[dbo].[VirtusN2PrgBaseInsert]` with correlation id.
    - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Services/N2PrgBaseService.cs:34

- POST /api/SappCmiProxy/PostMessageRest?targetUrl=...
  - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/SappCmiProxyController.cs:38
  - Body: `SappCmiRestProxyRequestModel` (SecurityToken, Parametri{...})
    - Schema: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Model/SappCmiRestProxyModel.cs:7
  - Behavior: forwards JSON to external REST `targetUrl`, returns base64 of response.

- POST /api/SappCmiProxy/PostMessageSoap
  - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Controllers/SappCmiProxyController.cs:119
  - Body: `SappCmiSoapProxyRequestModel` (url, operation, elementName, base64Content)
    - Schema: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Model/SappCmiSoapProxyModel.cs:6
  - Behavior: wraps provided XML into SOAP 1.1 envelope and posts to `url`, returns base64 of SOAP response body.

- Background services (inputs via Azure Service Bus)
  - VirtusSbReceiverReplyQueue: listens on queue from config (keys `VirtusSbReceiverConnString` and `VirtusSbReceiverReplyQueue`), updates DB via `[custom].[VirtusReplyMgmtUpdate]`.
    - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Services/VirtusSbReceiverReplyQueue.cs:29
  - VirtusSbReceiverDeadLetter: listens on queue from config (`VirtusSbReceiverDeadLetter`), persists messages to filesystem path from config.
    - File: Sviluppo/A2A.EAI.Services/A2A.EAI.Services.WebApi/Services/VirtusSbReceiverDeadLetter.cs:19


A2A.EAI.INT_PIANI_SVC.WebAPI
- GET /api/Sapp/Ping
  - File: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.WebAPI/Controllers/SappController.cs:59
  - Returns `SappResponse { ResultCode, ErrorMessage, ProcessId }`.

- POST /api/Sapp/SappEvent
  - File: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.WebAPI/Controllers/SappController.cs:118
  - Body: `SAPPEventRequest` (SAPP_Transazione_ID, EventType, EventStatus, EventMessage, EventNotification, ...)
    - Schema: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.Domain/Sapp/SappModel.cs:97
  - Behavior: tracks event and may send notifications/BAM relations.
    - Service: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.Application/Sapp/SappService.cs:66

- POST /api/Sapp/PvmRequest?Chiamante=SAPP|CMI
  - File: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.WebAPI/Controllers/SappController.cs:181
  - Body: `PvmReqRequest` (UnitaProduttiva_ID:int, Data:DateTime)
    - Schema: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.Domain/Sapp/SappModel.cs:52
  - Returns: `PvmReqResponse { ResultCode, ErrorMessage, ProcessId, Piano:string }`
  - Behavior: calls `[dbo].[PianoRequest]` to fetch latest plan and returns XML in `Piano`.
    - Service: Sviluppo/A2A.EAI.WebApi/INT_PIANI_SVC/A2A.EAI.INT_PIANI_SVC.Application/Sapp/SappService.cs:177


A2A.EAI.INT_SA_SVC.WebAPI
- POST /api/Validation/Validate
  - File: Sviluppo/A2A.EAI.WebApi/INT_SA_SVC/A2A.EAI.INT_SA_SVC.WebAPI/Controllers/ValidationController.cs:38
  - Body: `ValidateRequest { transactionId:guid, operation:"download" }`
    - Schema: Sviluppo/A2A.EAI.WebApi/INT_SA_SVC/A2A.EAI.INT_SA_SVC.Domain/SettApp/ValidateModel.cs:12
  - Returns: `ReturnMessage { ResultCode, ErrorMessage, ProcessId }`
  - Behavior: executes `SettAppDbValidationService` sproc to enqueue NDUE flows; writes BAM.
    - Service: Sviluppo/A2A.EAI.WebApi/INT_SA_SVC/A2A.EAI.INT_SA_SVC.Application/SettApp/ValidationServices.cs:30


Notes
- Authentication: besides Swagger `ApiKey` definition, controllers show remarks expecting headers like `msysfx-secret-key` and `msysfx-app-name` (see ValidationController XML comments).
- Telemetry: All controllers/services use `MessageTrackingService`, `BamService`, and `NotificationService` (from MsysEaiFx.Core).

