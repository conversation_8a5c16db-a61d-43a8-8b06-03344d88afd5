#!/usr/bin/env python3
import os
import csv
import json
import argparse
from pathlib import Path
from xml.etree import ElementTree as ET

def safe_read_text(path):
    try:
        return Path(path).read_text(encoding="utf-8", errors="ignore")
    except Exception:
        try:
            return Path(path).read_text(encoding="latin-1", errors="ignore")
        except Exception:
            return ""

def parse_btm(file_path):
    data = {"file": str(file_path), "source_schema": None, "target_schema": None, "functoids": 0}
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        src = root.find(".//SrcTree")
        trg = root.find(".//TrgTree")
        if src is not None and "RootNodeName" in src.attrib:
            data["source_schema"] = src.attrib.get("RootNodeName")
        if trg is not None and "RootNodeName" in trg.attrib:
            data["target_schema"] = trg.attrib.get("RootNodeName")
        if not data["source_schema"]:
            src_ns = root.findall(".//SrcSchema")
            if src_ns:
                data["source_schema"] = ",".join([n.attrib.get("Name", "") for n in src_ns if n is not None])
        if not data["target_schema"]:
            trg_ns = root.findall(".//TrgSchema")
            if trg_ns:
                data["target_schema"] = ",".join([n.attrib.get("Name", "") for n in trg_ns if n is not None])
        data["functoids"] = len(root.findall(".//Functoid"))
    except Exception:
        pass
    return data

def parse_odx(file_path):
    data = {"file": str(file_path), "ports": [], "shapes": []}
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        for p in root.findall(".//Port"):
            name = p.attrib.get("Name") or p.attrib.get("NameProp") or ""
            direction = p.attrib.get("Direction") or ""
            data["ports"].append({"name": name, "direction": direction})
        for s in root.findall(".//Shape"):
            st = s.attrib.get("Type") or ""
            nm = s.attrib.get("Name") or ""
            data["shapes"].append({"type": st, "name": nm})
    except Exception:
        pass
    return data

def parse_binding(file_path):
    bindings = []
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        for rl in root.findall(".//ReceiveLocation"):
            app = rl.attrib.get("Name", "")
            addr = rl.findtext(".//Address") or rl.attrib.get("Address", "")
            transport = rl.find(".//TransportType")
            tname = transport.attrib.get("Name") if transport is not None else ""
            pipeline = rl.find(".//ReceivePipeline")
            pipeline_name = pipeline.attrib.get("Name") if pipeline is not None else ""
            inbound_maps = [m.attrib.get("FullName","") for m in rl.findall(".//TransformMaps/TransformMap")]
            bindings.append({
                "type": "ReceiveLocation",
                "name": app, "adapter": tname, "address": addr,
                "pipeline": pipeline_name, "maps": "|".join(inbound_maps),
                "file": str(file_path)
            })
        for sp in root.findall(".//SendPort"):
            name = sp.attrib.get("Name","")
            addr = sp.findtext(".//Address") or ""
            transport = sp.find(".//PrimaryTransport/TransportType")
            tname = transport.attrib.get("Name") if transport is not None else ""
            pipeline = sp.find(".//SendPipeline")
            pipeline_name = pipeline.attrib.get("Name") if pipeline is not None else ""
            outbound_maps = [m.attrib.get("FullName","") for m in sp.findall(".//OutboundTransforms/Transform")]
            bindings.append({
                "type": "SendPort",
                "name": name, "adapter": tname, "address": addr,
                "pipeline": pipeline_name, "maps": "|".join(outbound_maps),
                "file": str(file_path)
            })
    except Exception:
        pass
    return bindings

def inventory_repo(root_dir):
    artifacts = {
        "solutions": [], "projects": [], "maps": [], "schemas": [], "orchestrations": [],
        "pipelines": [], "bindings": [], "custom_pipeline_components": [], "others": []
    }
    for p in Path(root_dir).rglob("*"):
        if p.is_dir():
            continue
        suffix = p.suffix.lower()
        if suffix == ".sln":
            artifacts["solutions"].append(str(p))
        elif suffix in (".btproj", ".csproj", ".vbproj", ".btm", ".odx", ".btp", ".xsd", ".xml", ".dll"):
            if suffix == ".btm":
                artifacts["maps"].append(parse_btm(p))
            elif suffix == ".odx":
                artifacts["orchestrations"].append(parse_odx(p))
            elif suffix == ".xsd":
                artifacts["schemas"].append(str(p))
            elif suffix == ".btp":
                artifacts["pipelines"].append(str(p))
            elif suffix == ".xml" and "binding" in p.name.lower():
                artifacts["bindings"].extend(parse_binding(p))
            elif suffix in (".csproj", ".vbproj"):
                artifacts["projects"].append(str(p))
                text = safe_read_text(p)
                if "IBaseComponent" in text or "Microsoft.BizTalk.Component.Interop" in text:
                    artifacts["custom_pipeline_components"].append(str(p))
            else:
                artifacts["others"].append(str(p))
    return artifacts

def write_csvs(outdir, artifacts):
    out = Path(outdir)
    out.mkdir(parents=True, exist_ok=True)
    if artifacts["bindings"]:
        with open(out/"bindings.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.DictWriter(f, fieldnames=list(artifacts["bindings"][0].keys()))
            w.writeheader(); w.writerows(artifacts["bindings"])
    if artifacts["maps"]:
        with open(out/"maps.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.DictWriter(f, fieldnames=list(artifacts["maps"][0].keys()))
            w.writeheader(); w.writerows(artifacts["maps"])
    if artifacts["orchestrations"]:
        rows = []
        for o in artifacts["orchestrations"]:
            rows.append({
                "file": o["file"],
                "ports": len(o.get("ports", [])),
                "shapes": len(o.get("shapes", []))
            })
        with open(out/"orchestrations.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.DictWriter(f, fieldnames=["file","ports","shapes"])
            w.writeheader(); w.writerows(rows)
    if artifacts["projects"]:
        with open(out/"projects.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.writer(f); w.writerow(["project_path"]); w.writerows([[p] for p in artifacts["projects"]])
    if artifacts["solutions"]:
        with open(out/"solutions.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.writer(f); w.writerow(["solution_path"]); w.writerows([[s] for s in artifacts["solutions"]])
    if artifacts["schemas"]:
        with open(out/"schemas.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.writer(f); w.writerow(["schema_path"]); w.writerows([[s] for s in artifacts["schemas"]])
    if artifacts["custom_pipeline_components"]:
        with open(out/"custom_pipeline_components.csv", "w", newline="", encoding="utf-8") as f:
            w = csv.writer(f); w.writerow(["project_path"]); w.writerows([[s] for s in artifacts["custom_pipeline_components"]])
    with open(out/"artifacts.json", "w", encoding="utf-8") as f:
        json.dump(artifacts, f, ensure_ascii=False, indent=2)

def build_graph_dot(outdir, artifacts):
    lines = ["digraph biztalk {", 'rankdir=LR;', 'node [shape=box];']
    recv = [b for b in artifacts["bindings"] if b["type"]=="ReceiveLocation"]
    sends = [b for b in artifacts["bindings"] if b["type"]=="SendPort"]
    for r in recv:
        ext = f'ext_{abs(hash(r["address"]))}'
        lines.append(f'"{ext}" [label="EXT\\n{r["adapter"]}\\n{r["address"]}"];')
        rl = f'rl_{abs(hash(r["name"]+r["file"]))}'
        lines.append(f'"{rl}" [label="ReceiveLocation\\n{r["name"]}"];')
        lines.append(f'"{ext}" -> "{rl}";')
        if r["maps"]:
            mp = f'map_{abs(hash(r["maps"]))}'
            lines.append(f'"{mp}" [label="Map\\n{r["maps"]}"];')
            lines.append(f'"{rl}" -> "{mp}";')
    for s in sends:
        sp = f'sp_{abs(hash(s["name"]+s["file"]))}'
        lines.append(f'"{sp}" [label="SendPort\\n{s["name"]}"];')
        if s["maps"]:
            mp = f'map_{abs(hash(s["maps"]))}'
            lines.append(f'"{mp}" [label="Map\\n{s["maps"]}"];')
            lines.append(f'"{mp}" -> "{sp}";')
        ext = f'ext_{abs(hash(s["address"]))}'
        lines.append(f'"{sp}" -> "{ext}";')
        lines.append(f'"{ext}" [label="EXT\\n{s["adapter"]}\\n{s["address"]}"];')
    lines.append("}")
    Path(outdir, "biztalk_bindings.dot").write_text("\n".join(lines), encoding="utf-8")

def main():
    ap = argparse.ArgumentParser(description="Inventory & analyze a BizTalk repository")
    ap.add_argument("repo", help="Path to the root folder of the BizTalk code repository")
    ap.add_argument("-o","--out", default="bt_audit_out", help="Output folder")
    args = ap.parse_args()

    artifacts = inventory_repo(args.repo)
    Path(args.out).mkdir(parents=True, exist_ok=True)
    write_csvs(args.out, artifacts)
    build_graph_dot(args.out, artifacts)
    print(f"[OK] Audit complete. Outputs saved under: {args.out}")
    print("CSV: bindings.csv, maps.csv, orchestrations.csv, projects.csv, solutions.csv, schemas.csv, custom_pipeline_components.csv")
    print("Graphviz DOT: biztalk_bindings.dot")

if __name__ == "__main__":
    main()
