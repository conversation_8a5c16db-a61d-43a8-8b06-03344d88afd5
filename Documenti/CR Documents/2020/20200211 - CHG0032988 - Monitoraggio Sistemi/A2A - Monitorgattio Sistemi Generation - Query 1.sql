WITH T_IntegrazioneTipoCaricamento
AS (
	SELECT 
		IntegrazioneTipoCaricamento,
		Descrizione
	FROM 
		app.T_IntegrazioneTipoCaricamento
	WHERE 
		Descrizione LIKE '%FMS%'
		OR Descrizione LIKE 'MSD%'
		OR Descrizione LIKE '%Semibanda%'
		OR Descrizione LIKE '%PV Asta%'
	)
SELECT 
	[IntegrazioneCaricamenti_ID],
	[NomeFile],
	T_IntegrazioneTipoCaricamento.Descrizione,
	[DataInserimentoInCoda],
	[DataInizioCaricamento],
	[DataFineCaricamento],
	T_IntegrazioneStatoCaricamento.Descrizione,
	[Esito],
	[DataRiferimento],
	[CalcoloPVM]
FROM 
	[app].[T_IntegrazioneCodaPVM]
	
	INNER JOIN T_IntegrazioneTipoCaricamento
	ON [T_IntegrazioneCodaPVM].IntegrazioneTipoCaricamento_ID = T_IntegrazioneTipoCaricamento.IntegrazioneTipoCaricamento

	INNER JOIN app.T_IntegrazioneStatoCaricamento
	ON [T_IntegrazioneCodaPVM].IntegrazioneStatoCaricamento_ID = T_IntegrazioneStatoCaricamento.IntegrazioneStatoCaricamento_ID
WHERE 
	DataInserimentoInCoda > DATEADD(day, - 100, getutcdate())
