{"Name": "MicrosysEaiFx", "Databases": [{"ServerName": "DCPRVW141\\MGMT", "Name": "MicrosysEaiFx", "CompatibilityLevel": "CompatLevel130", "SizeMB": 5722.0, "Status": "Completed", "ServerVersion": "13.0.5850.14", "AssessmentRecommendations": [], "ServerEdition": "Enterprise Edition: Core-based Licensing (64-bit)"}], "ServerInstances": [{"ServerName": "DCPRVW140MGMT\\MGMT", "Status": "Completed", "Version": "13.0.5850.14", "AssessmentRecommendations": [{"Category": "FeatureParity", "Severity": "Information", "FeatureParityCategory": "UnsupportedFeature", "RuleId": "<PERSON><PERSON><PERSON><PERSON>", "Title": "SQL Server Agent jobs are not available in Azure SQL Database", "Impact": "SQL Server Agent is a Microsoft Windows service that executes scheduled administrative tasks, which are called jobs in SQL Server. SQL Server Agent jobs are not available in Azure SQL Database.", "Recommendation": "Use Elastic Database Jobs (preview), which are the replacement for SQL Server Agent jobs in Azure SQL Database. Elastic Database Jobs for Azure SQL Database allow you to reliably execute T-SQL scripts that span multiple databases while automatically retrying and providing eventual completion guarantees.  Alternatively consider migrating to Azure SQL Managed Instance or SQL Server on Azure Virtual Machines.", "MoreInfo": "Getting started with Elastic Database Jobs (Preview) (AgentJobsMoreInformationLink)", "ImpactedObjects": [{"Name": "Backup BizTalk Server (BizTalkMgmtDb)", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "CleanupBTFExpiredEntriesJob_BizTalkMgmtDb", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "CommandLog Cleanup", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseBackup - SYSTEM_DATABASES - FULL", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseBackup - USER_DATABASES - DIFF", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseBackup - USER_DATABASES - FULL", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseBackup - USER_DATABASES - LOG", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseIntegrityCheck - SYSTEM_DATABASES", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "DatabaseIntegrityCheck - USER_DATABASES", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "Delete Old BizTalk Backup Files.Delete Old BizTalk Backup Files", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "IndexOptimize - USER_DATABASES", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "Monitor BizTalk Server (BizTalkMgmtDb)", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "MsysFx - AnalyticsExecutionsArchive", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "MsysFx - <PERSON> Al<PERSON>s", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "MsysFx - Critical Alerts Update Stats", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "MsysFx - Purge Tables", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "Output File Cleanup", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "sp_delete_backuphistory", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}, {"Name": "sp_purge_jobhistory", "ObjectType": "Job", "ImpactDetail": "", "SuggestedFixes": []}]}]}], "SourcePlatform": "SqlOnPrem", "Status": "Completed", "StartedOn": "2021-03-09T09:50:45.1792512+00:00", "EndedOn": "2021-03-09T09:50:49.4885005+00:00", "EvaluateFeatureRecommendations": false, "EvaluateCompatibilityIssues": true, "EvaluateFeatureParity": true, "TargetPlatform": "AzureSqlDatabase", "DmaVersion": {}}