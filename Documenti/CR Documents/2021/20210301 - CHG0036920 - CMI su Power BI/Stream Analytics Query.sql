-- DbSegnaliV2
SELECT
    SappSegnali.[IOAnalog_ID],
	SappSegnali.[IODigital_ID],
	SappSegnali.[UP],
	SappSegnali.[PLCTimestamp],
    (CASE WHEN SappSegnali.[IODigital_ID] > 0 THEN 'Digitale' ELSE 'Analogico' END) AS TipoSegnale,
    recordProperty.PropertyName AS CodiceSegnale,
    recordProperty.PropertyValue AS ValoreSegnale
INTO
    DbSegnaliV2
FROM
    SappSegnali

    CROSS APPLY GetRecordProperties(SappSegnali) AS recordProperty
WHERE 
    -- LA NOT IN Non Funziona
	recordProperty.PropertyName <> 'IOAnalog_ID'
    AND recordProperty.PropertyName <> 'IODigital_ID'
    AND recordProperty.PropertyName <> 'UP'
    AND recordProperty.PropertyName <> 'PLCTimestamp'
    AND recordProperty.PropertyName <> 'EventProcessedUtcTime'
    AND recordProperty.PropertyName <> 'PartitionId'
    AND recordProperty.PropertyName <> 'EventEnqueuedUtcTime';

-- DbSegnaliV1
SELECT
    [IOAnalog_ID],
	[IODigital_ID],
	[UP],
	[PLCTimestamp],
	[Eaux_m],
	[Eaux_q],
	[Ecalc_m],
	[Ecalc_q],
	[Ecorr_m],
	[Ecorr_q],
	[EF_m],
	[EF_q],
	[Emis_m],
	[Emis_q],
	[Enet_m],
	[Enet_q],
	[Eout_m],
	[Eout_q],
	[Eprim_m],
	[Eprim_q],
	[Eprod_m],
	[Eprod_q],
	[Ereq_m],
	[Ereq_q],
	[Esbil_m],
	[Esbil_q],
	[Esec_m],
	[Esec_q],
	[EsecT_m],
	[EsecT_q],
	[EVM_m],
	[EVM_q],
	[Freq],
	[LastMinute],
	[LastQuarter],
	[P1tot],
	[P2tot],
	[P3tot],
	[Paux1],
	[Paux2],
	[Paux3],
	[Pcalc],
	[Pcorr],
	[PF],
	[Pnet],
	[Pout],
	[Pp1],
	[Pp2],
	[Pprimtot],
	[Preq],
	[PsecT],
	[Psectot],
	[Ptg1],
	[Ptg2],
	[Ptv],
	[PVM],
	[SB],
	[SBc],
	[SE_m],
	[Sec],
	[SP],
	[SPc],
	[SR_m],
	[ALM],
	[ALM_in],
	[ANS],
	[ANS_in],
	[AS],
	[D1],
	[D2],
	[D3],
	[ES],
	[Eselect],
	[Lim],
	[LOOP_enable],
	[PRIM_enable],
	[Pselect],
	[SEC_enable],
	[WD]
INTO
    DbSegnaliV1
FROM
    SappSegnali;