USE [master]
GO

IF NOT EXISTS (SELECT LOGI<PERSON>NAME FROM master.dbo.syslogins WHERE NAME = 'GROUP\biolap')
BEGIN
	CREATE LOGIN [GROUP\biolap] FROM WINDOWS WITH DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english];
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'GROUP\BIService')
BEGIN
	CREATE LOGIN [GROUP\BIService] FROM WINDOWS WITH DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english];
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'GROUP\GRP-G-BI-CONTATORI-ACS') AND @@SERVERNAME='DCPRAW0BX\N2'
BEGIN
	CREATE LOGIN [GROUP\GRP-G-BI-CONTATORI-ACS] FROM WINDOWS WITH DEFAULT_DATABASE=[DWH_NBDO], DEFAULT_LANGUAGE=[us_english];
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'GROUP\PI.coresight')
BEGIN
	CREATE LOGIN [GROUP\PI.coresight] FROM WINDOWS WITH DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english];
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'GROUP\SQL_NBDO_ReplicaReaders')
BEGIN
	CREATE LOGIN [GROUP\SQL_NBDO_ReplicaReaders] FROM WINDOWS WITH DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english];
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'MonitorSapp')
BEGIN
	CREATE LOGIN [MonitorSapp] WITH PASSWORD=0x0200D8C9059DAD4A4270F66EC2AF19D2330F6D95B0A0DBDDD07D21FA92FA17F3DC1C9416560BEEA7F97F51FEB54CBB7B1859BE48F9F1D9E8C46F70D2BB1EE365FE8F3BEF7188 HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0x63C50FAFF75CD84AB31C9908291C0501;
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'THOR_READ')
BEGIN
	CREATE LOGIN [THOR_READ] WITH PASSWORD=0x0200214E0173143434DDD25580544B83649C725749A8A8039D9FAF8A8676B19AD1994809176A750D12EAD3B4E3D3C156824FE4F63D8BD592ACF23121782ECD7170694FF6C5D0 HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0xE504EE63ED172E4181F37FB05A463967;
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'PInbdo')
BEGIN
	CREATE LOGIN [PInbdo] WITH PASSWORD=0x0200E58CAF482ACAFB893EA632107445BFFE04F8DCC2004625CDB59FBD515FDE6A044ADCA2777BD72259FC51F3F7BD9C17FBAA73CF60CF6084D3922933234E3B310F52A12F9C HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0x7154C2EDF9D48F4F93665F4DCE7731A8;
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'ReportServerUser')
BEGIN
	CREATE LOGIN [ReportServerUser] WITH PASSWORD=0x020063344423D11312FEB2991BE3492CDC09F0717A1857C91C03CCDBDBA70E30034729E841286D04F673D7982A3CC785AE6C230B5A2C4395619B3ED7FBB9E884F3EAB33CBF13 HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0x35D496814973BB47B8A6B8472368688F;
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'PowerBI')
BEGIN
	CREATE LOGIN [PowerBI] WITH PASSWORD=0x0200FBE1F37C73F8107506FD3078CD46276D1D2BD70DD30494BB41D4090F67D481E330A76193A2600666B0EAE617F0567EEA8281BC25C4703B3431E52A84829EEDA1418390F5 HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0xC385EC025DE4A8409A71826B46A425FC;
END;

IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'TestNbdoReaders')
BEGIN
	CREATE LOGIN [TestNbdoReaders] WITH PASSWORD=0x0200B5F8B41DE2D4FA2FD8EB2E93F2641CC57E566B04A7CDB6556BCA7BFE0DD49BE202664C0519A1F669C7F03E8D58DE63262B52329E6DB733660C393FC7F6E584261F4522B4 HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0x4A0AF579587AEA46998BEBE6FD1AC012;
END;


IF NOT EXISTS (SELECT LOGINNAME FROM master.dbo.syslogins WHERE NAME = 'gateway_test')
BEGIN
	CREATE LOGIN [gateway_test] WITH PASSWORD=0x0200610473F97DC11942AF23091284632F93D13F6D8CE3AA35D5B3DD9B4EA238B2B46CB2BC73E36F7C01F663D28CC67F0342ACC81D91D3417BC4B8461F0DD0B7BD5A6FAC61BD HASHED, DEFAULT_DATABASE=[master], DEFAULT_LANGUAGE=[us_english], CHECK_EXPIRATION=OFF, CHECK_POLICY=OFF, SID=0xD49E072CD9453F449317AC4A38D6BEF5;
END;




