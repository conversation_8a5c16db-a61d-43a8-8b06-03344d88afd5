USE [nbdo]
GO

--Database_User
IF (SELECT name FROM sys.database_principals WHERE name = 'gateway_test') IS NULL
BEGIN
	CREATE USER [gateway_test] FOR LOGIN [gateway_test] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'GROUP\BIService') IS NULL
	CREATE USER [GROUP\BIService] FOR LOGIN [GROUP\BIService] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'GROUP\PI.coresight') IS NULL
	CREATE USER [GROUP\PI.coresight] FOR LOGIN [GROUP\PI.coresight] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'GROUP\SQL_NBDO_ReplicaReaders') IS NULL
	CREATE USER [GROUP\SQL_NBDO_ReplicaReaders] FOR LOGIN [GROUP\SQL_NBDO_ReplicaReaders]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'MonitorSapp') IS NULL
	CREATE USER [MonitorSapp] FOR LOGIN [MonitorSapp] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'PInbdo') IS NULL
	CREATE USER [PInbdo] FOR LOGIN [PInbdo] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'PowerBI') IS NULL
	CREATE USER [PowerBI] FOR LOGIN [PowerBI] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'ReportServerUser') IS NULL
	CREATE USER [ReportServerUser] FOR LOGIN [ReportServerUser] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'TestNbdoReaders') IS NULL
	CREATE USER [TestNbdoReaders] FOR LOGIN [TestNbdoReaders] WITH DEFAULT_SCHEMA=[dbo]
END;

IF (SELECT name FROM sys.database_principals WHERE name = 'THOR_READ') IS NULL
	CREATE USER [THOR_READ] FOR LOGIN [THOR_READ] WITH DEFAULT_SCHEMA=[dbo]
END;



