USE [nbdo]
GO

-- Database Role
CREATE ROLE [BPMDGroup]
GO
CREATE ROLE [Nbdo_Readers]
GO
CREATE ROLE [PI_Readers]
GO

-- Role Member
ALTER ROLE [db_datareader] ADD MEMBER [gateway_test]
GO
ALTER ROLE [db_datareader] ADD MEMBER [GROUP\BIService]
GO
ALTER ROLE [PI_Readers] ADD MEMBER [GROUP\PI.coresight]
GO
ALTER ROLE [Nbdo_Readers] ADD MEMBER [GROUP\SQL_NBDO_ReplicaReaders]
GO
ALTER ROLE [db_datareader] ADD MEMBER [MonitorSapp]
GO
ALTER ROLE [PI_Readers] ADD MEMBER [PInbdo]
GO
ALTER ROLE [db_datareader] ADD MEMBER [PowerBI]
GO
ALTER ROLE [db_datareader] ADD MEMBER [ReportServerUser]
GO
ALTER ROLE [Nbdo_Readers] ADD MEMBER [TestNbdoReaders]
GO
ALTER ROLE [db_datareader] ADD MEMBER [THOR_READ]
GO

-- Permission Role 'Nbdo_Readers'
GRANT SELECT ON [app].[VW_SETT_METERING_EPRODL_HH] TO Nbdo_Readers
GRANT SELECT ON [app].[T_EnergiaUTF] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_EPRODL_MM] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_A23ECO_15MIN] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_A23FIS_01MIN] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_A23FIS_01MIN_DETT] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_A23FIS_15MIN] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_A23FIS_15MIN_POTENZE] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_GAS_SNAM] TO Nbdo_Readers
GRANT SELECT ON [app].[T_PCICentrale] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_GAS_SOLARE] TO Nbdo_Readers
GRANT EXECUTE ON [app].[METERING_DataRiferimento] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_HH] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_QMIN] TO Nbdo_Readers
GRANT SELECT ON [app].[T_EventiInOut_Parallelo_MinTec] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_PROGR_CUM_HH] TO Nbdo_Readers
GRANT SELECT ON [app].[T_SKC_EventiTeleriscaldamento] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_IntervalliFattibilita] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_NominaProgrammi] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_TERNAPREZZISBILANCIAMENTO] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_OfferteXBID] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_TERNAPREZZISBILANCIAMENTO_PRELIM] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_PosizioneNettaXBID] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_TERNASCAMBIZONALI] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_ProgrammazionefisicaPortafoglio] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_TERNASEGNOZONALE] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_ProgrammazionefisicaUnitaProduttiva] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_TERNASEGNOZONALE_PRELIM] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_UnitaProduttivePerImpianti] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SbilanciamentoPortafoglio] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_Settlement_Minuti] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_EnergiaLorda] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_VariabiliUVAP] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_EnergieSpalmate] TO Nbdo_Readers
GRANT SELECT ON [app].[ElencoOfferte_MGPMI] TO Nbdo_Readers
GRANT SELECT ON [app].[ElencoOfferte_MSD] TO Nbdo_Readers
GRANT SELECT ON [app].[ElencoOfferte_PCE] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_CorrispettivoNonArbitraggioMacrozonale] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_UVRQ_Corrispettivofisso] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_UVRQ_CorrServiziAusiliari] TO Nbdo_Readers
GRANT SELECT ON [app].[ElencoOfferte_MRR] TO Nbdo_Readers
GRANT SELECT ON [app].[PrezziZonali_MRR] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_GETTONI] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_HH_ANNO] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_GETTONI_ACC_DETT] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_GETTONI_CA_DETT] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_Qty_Valid_MB] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_METERING_QMIN_ANNO] TO Nbdo_Readers
GRANT SELECT ON [app].[vw_sett_metering_hh_WorkAround] TO Nbdo_Readers
GRANT SELECT ON [app].[ElencoOfferte_MRR_Full] TO Nbdo_Readers
GRANT SELECT ON [app].[LiquidazioneGiornaliera_PCE] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_DatiCNA] TO Nbdo_Readers
GRANT SELECT ON [app].[T_TipologiaContatoriEnergia] TO Nbdo_Readers
GRANT SELECT ON [app].[T_PuntoScambioVirtuale] TO Nbdo_Readers
GRANT SELECT ON [app].[T_LettureContatoriMinoriACS] TO Nbdo_Readers
GRANT SELECT ON [app].[T_NucleiSogliaEnergia] TO Nbdo_Readers
GRANT SELECT ON [app].[VW_SETT_PREZZOUNITARIOSBIL] TO Nbdo_Readers
GRANT SELECT ON [app].[T_Impianti] TO Nbdo_Readers
GRANT SELECT ON [app].[T_ServiziAusiliari] TO Nbdo_Readers
GRANT SELECT ON [app].[T_EsitoAvviamento] TO Nbdo_Readers
GRANT EXECUTE ON [app].[UtcToLocal] TO Nbdo_Readers
GRANT SELECT ON [app].[T_Cali] TO Nbdo_Readers
GRANT SELECT ON [app].[T_ContatoriEnergiaCollegati] TO Nbdo_Readers

-- Permission Role 'PI_Readers'
GRANT SELECT ON [app].[T_DateOre] TO PI_Readers
GRANT SELECT ON [app].[T_PartiteEconomicheStimateExPost] TO PI_Readers
GRANT SELECT ON [app].[T_PartiteEconomicheStimateMSD] TO PI_Readers
GRANT SELECT ON [app].[T_DatiEpson] TO PI_Readers
GRANT SELECT ON [app].[T_SbilPVMCMinuti] TO PI_Readers
GRANT SELECT ON [app].[T_Centrali] TO PI_Readers
GRANT SELECT ON [app].[T_KPICausaliAssociateIndisp] TO PI_Readers
GRANT SELECT ON [app].[T_VariabiliUVAPTestata] TO PI_Readers
GRANT SELECT ON [app].[T_KPICruscotto] TO PI_Readers
GRANT SELECT ON [app].[T_VariabiliUVAP] TO PI_Readers
GRANT SELECT ON [app].[T_UnitaProduttive] TO PI_Readers
GRANT SELECT ON [app].[T_SbilPVMCTestata] TO PI_Readers
GRANT SELECT ON [app].[T_SbilPVMC] TO PI_Readers
GRANT SELECT ON [app].[T_ProduzioneNettaGRTN] TO PI_Readers
GRANT SELECT ON [app].[T_BDGTestataSede] TO PI_Readers
GRANT SELECT ON [app].[T_BDGTestataCentrale] TO PI_Readers
GRANT SELECT ON [app].[T_KPIAnagrafica] TO PI_Readers
GRANT SELECT ON [app].[T_BDGDati] TO PI_Readers
GRANT SELECT ON [app].[T_BDGDatiRevised] TO PI_Readers
