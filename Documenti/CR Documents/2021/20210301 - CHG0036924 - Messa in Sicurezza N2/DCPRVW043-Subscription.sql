-- Adding the transactional pull subscription

/****** Begin: Script to be run at Subscriber ******/
use [nbdo]
exec sp_addpullsubscription @publisher = N'DCPRVW118\N2', @publication = N'NBDO_PUB', @publisher_db = N'nbdo', @independent_agent = N'True', @subscription_type = N'pull', @description = N'', @update_mode = N'read only', @immediate_sync = 1

exec sp_addpullsubscription_agent @publisher = N'DCPRVW118\N2', @publisher_db = N'nbdo', @publication = N'NBDO_PUB', @distributor = N'DCPRVW118\N2', @distributor_security_mode = 1, @distributor_login = N'', @distributor_password = N'', @enabled_for_syncmgr = N'False', @frequency_type = 64, @frequency_interval = 0, @frequency_relative_interval = 0, @frequency_recurrence_factor = 0, @frequency_subday = 0, @frequency_subday_interval = 0, @active_start_time_of_day = 0, @active_end_time_of_day = 235959, @active_start_date = 0, @active_end_date = 0, @alt_snapshot_folder = N'\\group.local\generation\app_n2-bi', @working_directory = N'', @use_ftp = N'False', @job_login = N'group\biolap', @job_password = null, @publication_type = 0
GO
/****** End: Script to be run at Subscriber ******/

/****** Begin: Script to be run at Publisher ******/
/*use [nbdo]
-- Parameter @sync_type is scripted as 'automatic', please adjust when appropriate.
exec sp_addsubscription @publication = N'NBDO_PUB', @subscriber = N'DCPRVW043', @destination_db = N'nbdo', @sync_type = N'Automatic', @subscription_type = N'pull', @update_mode = N'read only'
*/
/****** End: Script to be run at Publisher ******/

