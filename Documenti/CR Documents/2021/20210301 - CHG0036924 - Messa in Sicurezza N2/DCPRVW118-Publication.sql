-- Enabling the replication database
use master
exec sp_replicationdboption @dbname = N'nbdo', @optname = N'publish', @value = N'true'
GO

exec [nbdo].sys.sp_addlogreader_agent @job_login = N'group\biolap', @job_password = null, @publisher_security_mode = 1
GO
-- Adding the transactional publication
use [nbdo]
exec sp_addpublication @publication = N'NBDO_PUB', @description = N'Transactional publication of database ''nbdo'' from Publisher ''DCPRVW118\N2''.', @sync_method = N'concurrent', @retention = 0, @allow_push = N'true', @allow_pull = N'true', @allow_anonymous = N'false', @enabled_for_internet = N'false', @snapshot_in_defaultfolder = N'false', @alt_snapshot_folder = N'\\group.local\generation\app_n2-bi', @compress_snapshot = N'false', @ftp_port = 21, @ftp_login = N'anonymous', @allow_subscription_copy = N'false', @add_to_active_directory = N'false', @repl_freq = N'continuous', @status = N'active', @independent_agent = N'true', @immediate_sync = N'false', @allow_sync_tran = N'false', @autogen_sync_procs = N'false', @allow_queued_tran = N'false', @allow_dts = N'false', @replicate_ddl = 1, @allow_initialize_from_backup = N'false', @enabled_for_p2p = N'false', @enabled_for_het_sub = N'false'
GO


exec sp_addpublication_snapshot @publication = N'NBDO_PUB', @frequency_type = 8, @frequency_interval = 64, @frequency_relative_interval = 1, @frequency_recurrence_factor = 1, @frequency_subday = 8, @frequency_subday_interval = 24, @active_start_time_of_day = 0, @active_end_time_of_day = 235959, @active_start_date = 0, @active_end_date = 0, @job_login = N'group\biolap', @job_password = null, @publisher_security_mode = 1
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'sa'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\GRP-INFRA-DATABASE'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\svc_sqldashboard'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\bztservice'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\BizTalk-Server-Administrators'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\svc_networker'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\GRP-INFRA-DATABASE-A2A'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'GROUP\biolap'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'DCPRVW118\a2admin'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'NT Service\MSSQL$N2'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'NT SERVICE\Winmgmt'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'NT SERVICE\SQLWriter'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'NT SERVICE\SQLAgent$N2'
GO
exec sp_grant_publication_access @publication = N'NBDO_PUB', @login = N'distributor_admin'
GO

-- Adding the transactional articles
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'CreateDateTime', @source_owner = N'app', @source_object = N'CreateDateTime', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'CreateDateTime', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'DecimalMax', @source_owner = N'app', @source_object = N'DecimalMax', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'DecimalMax', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'DecimalMin', @source_owner = N'app', @source_object = N'DecimalMin', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'DecimalMin', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ElencoOfferte_MGPMI', @source_owner = N'app', @source_object = N'ElencoOfferte_MGPMI', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'ElencoOfferte_MGPMI', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ElencoOfferte_MRR', @source_owner = N'app', @source_object = N'ElencoOfferte_MRR', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'ElencoOfferte_MRR', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ElencoOfferte_MRR_Full', @source_owner = N'app', @source_object = N'ElencoOfferte_MRR_Full', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'ElencoOfferte_MRR_Full', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ElencoOfferte_MSD', @source_owner = N'app', @source_object = N'ElencoOfferte_MSD', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'ElencoOfferte_MSD', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ElencoOfferte_PCE', @source_owner = N'app', @source_object = N'ElencoOfferte_PCE', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'ElencoOfferte_PCE', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'FirstDayOfYear', @source_owner = N'app', @source_object = N'FirstDayOfYear', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'FirstDayOfYear', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'fn_ParseCommaDelimitedList', @source_owner = N'app', @source_object = N'fn_ParseCommaDelimitedList', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'fn_ParseCommaDelimitedList', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'fnDateTime.Confronto.Intersezione', @source_owner = N'app', @source_object = N'fnDateTime.Confronto.Intersezione', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'fnDateTime.Confronto.Intersezione', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'fnDateTime.Confronto.Intersezione.Periodo', @source_owner = N'app', @source_object = N'fnDateTime.Confronto.Intersezione.Periodo', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'fnDateTime.Confronto.Intersezione.Periodo', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'fnDateTime.FromNullToMax', @source_owner = N'app', @source_object = N'fnDateTime.FromNullToMax', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'fnDateTime.FromNullToMax', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'fnDateTime.FromNullToMin', @source_owner = N'app', @source_object = N'fnDateTime.FromNullToMin', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'fnDateTime.FromNullToMin', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'getCodsapr', @source_owner = N'app', @source_object = N'getCodsapr', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'getCodsapr', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'getDateFromDateTime', @source_owner = N'app', @source_object = N'getDateFromDateTime', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'getDateFromDateTime', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'getOraFromDateTime', @source_owner = N'app', @source_object = N'getOraFromDateTime', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'getOraFromDateTime', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'LiquidazioneGiornaliera_PCE', @source_owner = N'app', @source_object = N'LiquidazioneGiornaliera_PCE', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'LiquidazioneGiornaliera_PCE', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'LocalToUtc', @source_owner = N'app', @source_object = N'LocalToUtc', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'LocalToUtc', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'METERING_DataRiferimento', @source_owner = N'app', @source_object = N'METERING_DataRiferimento', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'METERING_DataRiferimento', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'METERING_DataRiferimento_Anno', @source_owner = N'app', @source_object = N'METERING_DataRiferimento_Anno', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'METERING_DataRiferimento_Anno', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'PrezziZonali_MRR', @source_owner = N'app', @source_object = N'PrezziZonali_MRR', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'PrezziZonali_MRR', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'ReplMonitor', @source_owner = N'app', @source_object = N'ReplMonitor', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'ReplMonitor', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appReplMonitor]', @del_cmd = N'CALL [sp_MSdel_appReplMonitor]', @upd_cmd = N'SCALL [sp_MSupd_appReplMonitor]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'SETT_DataRiferimento', @source_owner = N'app', @source_object = N'SETT_DataRiferimento', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'SETT_DataRiferimento', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AggregazioniMacroZonaliMBP', @source_owner = N'app', @source_object = N'T_AggregazioniMacroZonaliMBP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_AggregazioniMacroZonaliMBP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AggregazioniMacroZonaliMBP]', @del_cmd = N'CALL [sp_MSdel_appT_AggregazioniMacroZonaliMBP]', @upd_cmd = N'SCALL [sp_MSupd_appT_AggregazioniMacroZonaliMBP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Allegato', @source_owner = N'app', @source_object = N'T_Allegato', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Allegato', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Allegato]', @del_cmd = N'CALL [sp_MSdel_appT_Allegato]', @upd_cmd = N'SCALL [sp_MSupd_appT_Allegato]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AnnoMeseOre', @source_owner = N'app', @source_object = N'T_AnnoMeseOre', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_AnnoMeseOre', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AnnoMeseOre]', @del_cmd = N'CALL [sp_MSdel_appT_AnnoMeseOre]', @upd_cmd = N'SCALL [sp_MSupd_appT_AnnoMeseOre]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AnnoOre', @source_owner = N'app', @source_object = N'T_AnnoOre', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_AnnoOre', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AnnoOre]', @del_cmd = N'CALL [sp_MSdel_appT_AnnoOre]', @upd_cmd = N'SCALL [sp_MSupd_appT_AnnoOre]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AreaProduzione', @source_owner = N'app', @source_object = N'T_AreaProduzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_AreaProduzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AreaProduzione]', @del_cmd = N'CALL [sp_MSdel_appT_AreaProduzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_AreaProduzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AreaProduzioneTipoZona', @source_owner = N'app', @source_object = N'T_AreaProduzioneTipoZona', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_AreaProduzioneTipoZona', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AreaProduzioneTipoZona]', @del_cmd = N'CALL [sp_MSdel_appT_AreaProduzioneTipoZona]', @upd_cmd = N'SCALL [sp_MSupd_appT_AreaProduzioneTipoZona]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_AssettoRUP', @source_owner = N'app', @source_object = N'T_AssettoRUP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_AssettoRUP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_AssettoRUP]', @del_cmd = N'CALL [sp_MSdel_appT_AssettoRUP]', @upd_cmd = N'SCALL [sp_MSupd_appT_AssettoRUP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Aste', @source_owner = N'app', @source_object = N'T_Aste', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Aste', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Aste]', @del_cmd = N'CALL [sp_MSdel_appT_Aste]', @upd_cmd = N'SCALL [sp_MSupd_appT_Aste]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_BDGDati', @source_owner = N'app', @source_object = N'T_BDGDati', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_BDGDati', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_BDGDati]', @del_cmd = N'CALL [sp_MSdel_appT_BDGDati]', @upd_cmd = N'SCALL [sp_MSupd_appT_BDGDati]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_BDGDatiRevised', @source_owner = N'app', @source_object = N'T_BDGDatiRevised', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_BDGDatiRevised', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_BDGDatiRevised]', @del_cmd = N'CALL [sp_MSdel_appT_BDGDatiRevised]', @upd_cmd = N'SCALL [sp_MSupd_appT_BDGDatiRevised]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_BDGTestataCentrale', @source_owner = N'app', @source_object = N'T_BDGTestataCentrale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_BDGTestataCentrale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_BDGTestataCentrale]', @del_cmd = N'CALL [sp_MSdel_appT_BDGTestataCentrale]', @upd_cmd = N'SCALL [sp_MSupd_appT_BDGTestataCentrale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_BDGTestataSede', @source_owner = N'app', @source_object = N'T_BDGTestataSede', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_BDGTestataSede', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_BDGTestataSede]', @del_cmd = N'CALL [sp_MSdel_appT_BDGTestataSede]', @upd_cmd = N'SCALL [sp_MSupd_appT_BDGTestataSede]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloGettoneCambioAssetto', @source_owner = N'app', @source_object = N'T_CalcoloGettoneCambioAssetto', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloGettoneCambioAssetto', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloGettoneCambioAssetto]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloGettoneCambioAssetto]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloGettoneCambioAssetto]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPartiteEconomicheMB', @source_owner = N'app', @source_object = N'T_CalcoloPartiteEconomicheMB', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPartiteEconomicheMB', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPartiteEconomicheMB]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPartiteEconomicheMB]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPartiteEconomicheMB]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPartiteEconomicheMSD', @source_owner = N'app', @source_object = N'T_CalcoloPartiteEconomicheMSD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPartiteEconomicheMSD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPartiteEconomicheMSD]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPartiteEconomicheMSD]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPartiteEconomicheMSD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPartiteEconomicheOfferteMB', @source_owner = N'app', @source_object = N'T_CalcoloPartiteEconomicheOfferteMB', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPartiteEconomicheOfferteMB', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPartiteEconomicheOfferteMB]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPartiteEconomicheOfferteMB]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPartiteEconomicheOfferteMB]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPartiteEconomicheOfferteMSD', @source_owner = N'app', @source_object = N'T_CalcoloPartiteEconomicheOfferteMSD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPartiteEconomicheOfferteMSD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPartiteEconomicheOfferteMSD]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPartiteEconomicheOfferteMSD]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPartiteEconomicheOfferteMSD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPMI', @source_owner = N'app', @source_object = N'T_CalcoloPMI', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPMI', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPMI]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPMI]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPMI]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloPUnSbilanciamento', @source_owner = N'app', @source_object = N'T_CalcoloPUnSbilanciamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloPUnSbilanciamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloPUnSbilanciamento]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloPUnSbilanciamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloPUnSbilanciamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloStimaMROA', @source_owner = N'app', @source_object = N'T_CalcoloStimaMROA', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloStimaMROA', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloStimaMROA]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloStimaMROA]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloStimaMROA]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CalcoloStimaMROD', @source_owner = N'app', @source_object = N'T_CalcoloStimaMROD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CalcoloStimaMROD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CalcoloStimaMROD]', @del_cmd = N'CALL [sp_MSdel_appT_CalcoloStimaMROD]', @upd_cmd = N'SCALL [sp_MSupd_appT_CalcoloStimaMROD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Cali', @source_owner = N'app', @source_object = N'T_Cali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Cali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Cali]', @del_cmd = N'CALL [sp_MSdel_appT_Cali]', @upd_cmd = N'SCALL [sp_MSupd_appT_Cali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CausaliIndisponibilita', @source_owner = N'app', @source_object = N'T_CausaliIndisponibilita', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CausaliIndisponibilita', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CausaliIndisponibilita]', @del_cmd = N'CALL [sp_MSdel_appT_CausaliIndisponibilita]', @upd_cmd = N'SCALL [sp_MSupd_appT_CausaliIndisponibilita]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Centrali', @source_owner = N'app', @source_object = N'T_Centrali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Centrali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Centrali]', @del_cmd = N'CALL [sp_MSdel_appT_Centrali]', @upd_cmd = N'SCALL [sp_MSupd_appT_Centrali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CentraliAreeProduzione', @source_owner = N'app', @source_object = N'T_CentraliAreeProduzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CentraliAreeProduzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CentraliAreeProduzione]', @del_cmd = N'CALL [sp_MSdel_appT_CentraliAreeProduzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_CentraliAreeProduzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CodiceSbil', @source_owner = N'app', @source_object = N'T_CodiceSbil', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CodiceSbil', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CodiceSbil]', @del_cmd = N'CALL [sp_MSdel_appT_CodiceSbil]', @upd_cmd = N'SCALL [sp_MSupd_appT_CodiceSbil]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Combustibili', @source_owner = N'app', @source_object = N'T_Combustibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_Combustibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Combustibili]', @del_cmd = N'CALL [sp_MSdel_appT_Combustibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_Combustibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CombustibiliAltriMovimenti', @source_owner = N'app', @source_object = N'T_CombustibiliAltriMovimenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CombustibiliAltriMovimenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CombustibiliAltriMovimenti]', @del_cmd = N'CALL [sp_MSdel_appT_CombustibiliAltriMovimenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_CombustibiliAltriMovimenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CombustibiliFiscali', @source_owner = N'app', @source_object = N'T_CombustibiliFiscali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CombustibiliFiscali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CombustibiliFiscali]', @del_cmd = N'CALL [sp_MSdel_appT_CombustibiliFiscali]', @upd_cmd = N'SCALL [sp_MSupd_appT_CombustibiliFiscali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Componenti', @source_owner = N'app', @source_object = N'T_Componenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Componenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Componenti]', @del_cmd = N'CALL [sp_MSdel_appT_Componenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_Componenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CondizioniFunzionamento', @source_owner = N'app', @source_object = N'T_CondizioniFunzionamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CondizioniFunzionamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CondizioniFunzionamento]', @del_cmd = N'CALL [sp_MSdel_appT_CondizioniFunzionamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_CondizioniFunzionamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsCruscotto', @source_owner = N'app', @source_object = N'T_ConsCruscotto', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsCruscotto', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsCruscotto]', @del_cmd = N'CALL [sp_MSdel_appT_ConsCruscotto]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsCruscotto]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsDati', @source_owner = N'app', @source_object = N'T_ConsDati', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsDati', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsDati]', @del_cmd = N'CALL [sp_MSdel_appT_ConsDati]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsDati]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsDatiEventi', @source_owner = N'app', @source_object = N'T_ConsDatiEventi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsDatiEventi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsDatiEventi]', @del_cmd = N'CALL [sp_MSdel_appT_ConsDatiEventi]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsDatiEventi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsDatiEventiTestata', @source_owner = N'app', @source_object = N'T_ConsDatiEventiTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsDatiEventiTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsDatiEventiTestata]', @del_cmd = N'CALL [sp_MSdel_appT_ConsDatiEventiTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsDatiEventiTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsegneCombustibili', @source_owner = N'app', @source_object = N'T_ConsegneCombustibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsegneCombustibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsegneCombustibili]', @del_cmd = N'CALL [sp_MSdel_appT_ConsegneCombustibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsegneCombustibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsTestataCentrale', @source_owner = N'app', @source_object = N'T_ConsTestataCentrale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsTestataCentrale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsTestataCentrale]', @del_cmd = N'CALL [sp_MSdel_appT_ConsTestataCentrale]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsTestataCentrale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsTestataSede', @source_owner = N'app', @source_object = N'T_ConsTestataSede', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsTestataSede', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsTestataSede]', @del_cmd = N'CALL [sp_MSdel_appT_ConsTestataSede]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsTestataSede]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsTestataUP', @source_owner = N'app', @source_object = N'T_ConsTestataUP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsTestataUP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsTestataUP]', @del_cmd = N'CALL [sp_MSdel_appT_ConsTestataUP]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsTestataUP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ConsuntiviMensiliFV', @source_owner = N'app', @source_object = N'T_ConsuntiviMensiliFV', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ConsuntiviMensiliFV', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ConsuntiviMensiliFV]', @del_cmd = N'CALL [sp_MSdel_appT_ConsuntiviMensiliFV]', @upd_cmd = N'SCALL [sp_MSupd_appT_ConsuntiviMensiliFV]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ContatoriConsumoCombustibile', @source_owner = N'app', @source_object = N'T_ContatoriConsumoCombustibile', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ContatoriConsumoCombustibile', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ContatoriConsumoCombustibile]', @del_cmd = N'CALL [sp_MSdel_appT_ContatoriConsumoCombustibile]', @upd_cmd = N'SCALL [sp_MSupd_appT_ContatoriConsumoCombustibile]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ContatoriEnergia', @source_owner = N'app', @source_object = N'T_ContatoriEnergia', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ContatoriEnergia', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ContatoriEnergia]', @del_cmd = N'CALL [sp_MSdel_appT_ContatoriEnergia]', @upd_cmd = N'SCALL [sp_MSupd_appT_ContatoriEnergia]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ContatoriEnergiaCollegati', @source_owner = N'app', @source_object = N'T_ContatoriEnergiaCollegati', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ContatoriEnergiaCollegati', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ContatoriEnergiaCollegati]', @del_cmd = N'CALL [sp_MSdel_appT_ContatoriEnergiaCollegati]', @upd_cmd = N'SCALL [sp_MSupd_appT_ContatoriEnergiaCollegati]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CorrispettivoNonArbitraggioMacrozonale', @source_owner = N'app', @source_object = N'T_CorrispettivoNonArbitraggioMacrozonale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CorrispettivoNonArbitraggioMacrozonale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CorrispettivoNonArbitraggioMacrozonale]', @del_cmd = N'CALL [sp_MSdel_appT_CorrispettivoNonArbitraggioMacrozonale]', @upd_cmd = N'SCALL [sp_MSupd_appT_CorrispettivoNonArbitraggioMacrozonale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_CruscottoParamAnno', @source_owner = N'app', @source_object = N'T_CruscottoParamAnno', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_CruscottoParamAnno', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_CruscottoParamAnno]', @del_cmd = N'CALL [sp_MSdel_appT_CruscottoParamAnno]', @upd_cmd = N'SCALL [sp_MSupd_appT_CruscottoParamAnno]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Date', @source_owner = N'app', @source_object = N'T_Date', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Date', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Date]', @del_cmd = N'CALL [sp_MSdel_appT_Date]', @upd_cmd = N'SCALL [sp_MSupd_appT_Date]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DateOre', @source_owner = N'app', @source_object = N'T_DateOre', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x00000000080350DF, @identityrangemanagementoption = N'none', @destination_table = N'T_DateOre', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DateOre]', @del_cmd = N'CALL [sp_MSdel_appT_DateOre]', @upd_cmd = N'SCALL [sp_MSupd_appT_DateOre]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DateOreQuarti', @source_owner = N'app', @source_object = N'T_DateOreQuarti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_DateOreQuarti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DateOreQuarti]', @del_cmd = N'CALL [sp_MSdel_appT_DateOreQuarti]', @upd_cmd = N'SCALL [sp_MSupd_appT_DateOreQuarti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DateOreQuartiEstesa', @source_owner = N'app', @source_object = N'T_DateOreQuartiEstesa', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DateOreQuartiEstesa', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DateOreQuartiEstesa]', @del_cmd = N'CALL [sp_MSdel_appT_DateOreQuartiEstesa]', @upd_cmd = N'SCALL [sp_MSupd_appT_DateOreQuartiEstesa]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DateOreQuartiSNAM', @source_owner = N'app', @source_object = N'T_DateOreQuartiSNAM', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DateOreQuartiSNAM', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DateOreQuartiSNAM]', @del_cmd = N'CALL [sp_MSdel_appT_DateOreQuartiSNAM]', @upd_cmd = N'SCALL [sp_MSupd_appT_DateOreQuartiSNAM]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiCNA', @source_owner = N'app', @source_object = N'T_DatiCNA', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiCNA', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiCNA]', @del_cmd = N'CALL [sp_MSdel_appT_DatiCNA]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiCNA]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiEpson', @source_owner = N'app', @source_object = N'T_DatiEpson', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiEpson', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiEpson]', @del_cmd = N'CALL [sp_MSdel_appT_DatiEpson]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiEpson]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiGrezziADM', @source_owner = N'app', @source_object = N'T_DatiGrezziADM', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiGrezziADM', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiGrezziADM]', @del_cmd = N'CALL [sp_MSdel_appT_DatiGrezziADM]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiGrezziADM]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiGrezziADMReattiva', @source_owner = N'app', @source_object = N'T_DatiGrezziADMReattiva', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiGrezziADMReattiva', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiGrezziADMReattiva]', @del_cmd = N'CALL [sp_MSdel_appT_DatiGrezziADMReattiva]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiGrezziADMReattiva]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiIdrologiciInvasi', @source_owner = N'app', @source_object = N'T_DatiIdrologiciInvasi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiIdrologiciInvasi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiIdrologiciInvasi]', @del_cmd = N'CALL [sp_MSdel_appT_DatiIdrologiciInvasi]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiIdrologiciInvasi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiMeteoIdro', @source_owner = N'app', @source_object = N'T_DatiMeteoIdro', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiMeteoIdro', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiMeteoIdro]', @del_cmd = N'CALL [sp_MSdel_appT_DatiMeteoIdro]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiMeteoIdro]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiMeteringGas', @source_owner = N'app', @source_object = N'T_DatiMeteringGas', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiMeteringGas', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiMeteringGas]', @del_cmd = N'CALL [sp_MSdel_appT_DatiMeteringGas]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiMeteringGas]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiMeteringTerna', @source_owner = N'app', @source_object = N'T_DatiMeteringTerna', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiMeteringTerna', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiMeteringTerna]', @del_cmd = N'CALL [sp_MSdel_appT_DatiMeteringTerna]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiMeteringTerna]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiMeteringTernaTestata', @source_owner = N'app', @source_object = N'T_DatiMeteringTernaTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiMeteringTernaTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiMeteringTernaTestata]', @del_cmd = N'CALL [sp_MSdel_appT_DatiMeteringTernaTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiMeteringTernaTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiOfferteMRR', @source_owner = N'app', @source_object = N'T_DatiOfferteMRR', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiOfferteMRR', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiOfferteMRR]', @del_cmd = N'CALL [sp_MSdel_appT_DatiOfferteMRR]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiOfferteMRR]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiOfferteMSD', @source_owner = N'app', @source_object = N'T_DatiOfferteMSD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiOfferteMSD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiOfferteMSD]', @del_cmd = N'CALL [sp_MSdel_appT_DatiOfferteMSD]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiOfferteMSD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiOffertePCE', @source_owner = N'app', @source_object = N'T_DatiOffertePCE', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiOffertePCE', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiOffertePCE]', @del_cmd = N'CALL [sp_MSdel_appT_DatiOffertePCE]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiOffertePCE]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiOperediPresa', @source_owner = N'app', @source_object = N'T_DatiOperediPresa', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiOperediPresa', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiOperediPresa]', @del_cmd = N'CALL [sp_MSdel_appT_DatiOperediPresa]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiOperediPresa]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_DatiStatusUP', @source_owner = N'app', @source_object = N'T_DatiStatusUP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_DatiStatusUP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_DatiStatusUP]', @del_cmd = N'CALL [sp_MSdel_appT_DatiStatusUP]', @upd_cmd = N'SCALL [sp_MSupd_appT_DatiStatusUP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EnergiaProdottaFv', @source_owner = N'app', @source_object = N'T_EnergiaProdottaFv', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_EnergiaProdottaFv', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EnergiaProdottaFv]', @del_cmd = N'CALL [sp_MSdel_appT_EnergiaProdottaFv]', @upd_cmd = N'SCALL [sp_MSupd_appT_EnergiaProdottaFv]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EnergiaUTF', @source_owner = N'app', @source_object = N'T_EnergiaUTF', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_EnergiaUTF', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EnergiaUTF]', @del_cmd = N'CALL [sp_MSdel_appT_EnergiaUTF]', @upd_cmd = N'SCALL [sp_MSupd_appT_EnergiaUTF]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EsitiZonali', @source_owner = N'app', @source_object = N'T_EsitiZonali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x00000000080350DF, @identityrangemanagementoption = N'manual', @destination_table = N'T_EsitiZonali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EsitiZonali]', @del_cmd = N'CALL [sp_MSdel_appT_EsitiZonali]', @upd_cmd = N'SCALL [sp_MSupd_appT_EsitiZonali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EsitoAvviamento', @source_owner = N'app', @source_object = N'T_EsitoAvviamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_EsitoAvviamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EsitoAvviamento]', @del_cmd = N'CALL [sp_MSdel_appT_EsitoAvviamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_EsitoAvviamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EsitoMercato', @source_owner = N'app', @source_object = N'T_EsitoMercato', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x00000000080350DF, @identityrangemanagementoption = N'manual', @destination_table = N'T_EsitoMercato', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EsitoMercato]', @del_cmd = N'CALL [sp_MSdel_appT_EsitoMercato]', @upd_cmd = N'SCALL [sp_MSupd_appT_EsitoMercato]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EventiInOut_Parallelo_MinTec', @source_owner = N'app', @source_object = N'T_EventiInOut_Parallelo_MinTec', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_EventiInOut_Parallelo_MinTec', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EventiInOut_Parallelo_MinTec]', @del_cmd = N'CALL [sp_MSdel_appT_EventiInOut_Parallelo_MinTec]', @upd_cmd = N'SCALL [sp_MSupd_appT_EventiInOut_Parallelo_MinTec]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_EventiPrincipaliIndisp', @source_owner = N'app', @source_object = N'T_EventiPrincipaliIndisp', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_EventiPrincipaliIndisp', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_EventiPrincipaliIndisp]', @del_cmd = N'CALL [sp_MSdel_appT_EventiPrincipaliIndisp]', @upd_cmd = N'SCALL [sp_MSupd_appT_EventiPrincipaliIndisp]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_FamigliaCausaliSbil', @source_owner = N'app', @source_object = N'T_FamigliaCausaliSbil', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_FamigliaCausaliSbil', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_FamigliaCausaliSbil]', @del_cmd = N'CALL [sp_MSdel_appT_FamigliaCausaliSbil]', @upd_cmd = N'SCALL [sp_MSupd_appT_FamigliaCausaliSbil]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_FamigliaUnita', @source_owner = N'app', @source_object = N'T_FamigliaUnita', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_FamigliaUnita', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_FamigliaUnita]', @del_cmd = N'CALL [sp_MSdel_appT_FamigliaUnita]', @upd_cmd = N'SCALL [sp_MSupd_appT_FamigliaUnita]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_FasceOrarieMercato', @source_owner = N'app', @source_object = N'T_FasceOrarieMercato', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_FasceOrarieMercato', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_FasceOrarieMercato]', @del_cmd = N'CALL [sp_MSdel_appT_FasceOrarieMercato]', @upd_cmd = N'SCALL [sp_MSupd_appT_FasceOrarieMercato]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_FattoreMediaTensione', @source_owner = N'app', @source_object = N'T_FattoreMediaTensione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_FattoreMediaTensione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_FattoreMediaTensione]', @del_cmd = N'CALL [sp_MSdel_appT_FattoreMediaTensione]', @upd_cmd = N'SCALL [sp_MSupd_appT_FattoreMediaTensione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Fornitori', @source_owner = N'app', @source_object = N'T_Fornitori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Fornitori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Fornitori]', @del_cmd = N'CALL [sp_MSdel_appT_Fornitori]', @upd_cmd = N'SCALL [sp_MSupd_appT_Fornitori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Funzioni', @source_owner = N'app', @source_object = N'T_Funzioni', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_Funzioni', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Funzioni]', @del_cmd = N'CALL [sp_MSdel_appT_Funzioni]', @upd_cmd = N'SCALL [sp_MSupd_appT_Funzioni]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_GiacenzaCombustibili', @source_owner = N'app', @source_object = N'T_GiacenzaCombustibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_GiacenzaCombustibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_GiacenzaCombustibili]', @del_cmd = N'CALL [sp_MSdel_appT_GiacenzaCombustibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_GiacenzaCombustibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_GiacenzaCombustibiliToller', @source_owner = N'app', @source_object = N'T_GiacenzaCombustibiliToller', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_GiacenzaCombustibiliToller', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_GiacenzaCombustibiliToller]', @del_cmd = N'CALL [sp_MSdel_appT_GiacenzaCombustibiliToller]', @upd_cmd = N'SCALL [sp_MSupd_appT_GiacenzaCombustibiliToller]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Gruppi', @source_owner = N'app', @source_object = N'T_Gruppi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Gruppi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Gruppi]', @del_cmd = N'CALL [sp_MSdel_appT_Gruppi]', @upd_cmd = N'SCALL [sp_MSupd_appT_Gruppi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_GruppiDatiVariabili', @source_owner = N'app', @source_object = N'T_GruppiDatiVariabili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_GruppiDatiVariabili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_GruppiDatiVariabili]', @del_cmd = N'CALL [sp_MSdel_appT_GruppiDatiVariabili]', @upd_cmd = N'SCALL [sp_MSupd_appT_GruppiDatiVariabili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Impianti', @source_owner = N'app', @source_object = N'T_Impianti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Impianti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Impianti]', @del_cmd = N'CALL [sp_MSdel_appT_Impianti]', @upd_cmd = N'SCALL [sp_MSupd_appT_Impianti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_KPIAnagrafica', @source_owner = N'app', @source_object = N'T_KPIAnagrafica', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_KPIAnagrafica', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_KPIAnagrafica]', @del_cmd = N'CALL [sp_MSdel_appT_KPIAnagrafica]', @upd_cmd = N'SCALL [sp_MSupd_appT_KPIAnagrafica]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_KPICausaliAssociateIndisp', @source_owner = N'app', @source_object = N'T_KPICausaliAssociateIndisp', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_KPICausaliAssociateIndisp', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_KPICausaliAssociateIndisp]', @del_cmd = N'CALL [sp_MSdel_appT_KPICausaliAssociateIndisp]', @upd_cmd = N'SCALL [sp_MSupd_appT_KPICausaliAssociateIndisp]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_KPICruscotto', @source_owner = N'app', @source_object = N'T_KPICruscotto', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_KPICruscotto', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_KPICruscotto]', @del_cmd = N'CALL [sp_MSdel_appT_KPICruscotto]', @upd_cmd = N'SCALL [sp_MSupd_appT_KPICruscotto]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LettureContatoriEnergia', @source_owner = N'app', @source_object = N'T_LettureContatoriEnergia', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LettureContatoriEnergia', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LettureContatoriEnergia]', @del_cmd = N'CALL [sp_MSdel_appT_LettureContatoriEnergia]', @upd_cmd = N'SCALL [sp_MSupd_appT_LettureContatoriEnergia]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LettureContatoriEnergiaAusiliari', @source_owner = N'app', @source_object = N'T_LettureContatoriEnergiaAusiliari', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LettureContatoriEnergiaAusiliari', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LettureContatoriEnergiaAusiliari]', @del_cmd = N'CALL [sp_MSdel_appT_LettureContatoriEnergiaAusiliari]', @upd_cmd = N'SCALL [sp_MSupd_appT_LettureContatoriEnergiaAusiliari]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LettureContatoriEnergiaReattiva', @source_owner = N'app', @source_object = N'T_LettureContatoriEnergiaReattiva', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LettureContatoriEnergiaReattiva', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LettureContatoriEnergiaReattiva]', @del_cmd = N'CALL [sp_MSdel_appT_LettureContatoriEnergiaReattiva]', @upd_cmd = N'SCALL [sp_MSupd_appT_LettureContatoriEnergiaReattiva]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LettureContatoriMinoriACS', @source_owner = N'app', @source_object = N'T_LettureContatoriMinoriACS', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LettureContatoriMinoriACS', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LettureContatoriMinoriACS]', @del_cmd = N'CALL [sp_MSdel_appT_LettureContatoriMinoriACS]', @upd_cmd = N'SCALL [sp_MSupd_appT_LettureContatoriMinoriACS]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LiquidazioneGiornalieraPCE', @source_owner = N'app', @source_object = N'T_LiquidazioneGiornalieraPCE', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LiquidazioneGiornalieraPCE', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LiquidazioneGiornalieraPCE]', @del_cmd = N'CALL [sp_MSdel_appT_LiquidazioneGiornalieraPCE]', @upd_cmd = N'SCALL [sp_MSupd_appT_LiquidazioneGiornalieraPCE]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LottiConsegneCombustibili', @source_owner = N'app', @source_object = N'T_LottiConsegneCombustibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LottiConsegneCombustibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LottiConsegneCombustibili]', @del_cmd = N'CALL [sp_MSdel_appT_LottiConsegneCombustibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_LottiConsegneCombustibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_LottiToller', @source_owner = N'app', @source_object = N'T_LottiToller', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_LottiToller', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_LottiToller]', @del_cmd = N'CALL [sp_MSdel_appT_LottiToller]', @upd_cmd = N'SCALL [sp_MSupd_appT_LottiToller]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_MapCausaleSbilRaggrupamento', @source_owner = N'app', @source_object = N'T_MapCausaleSbilRaggrupamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_MapCausaleSbilRaggrupamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_MapCausaleSbilRaggrupamento]', @del_cmd = N'CALL [sp_MSdel_appT_MapCausaleSbilRaggrupamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_MapCausaleSbilRaggrupamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Mesi', @source_owner = N'app', @source_object = N'T_Mesi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Mesi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Mesi]', @del_cmd = N'CALL [sp_MSdel_appT_Mesi]', @upd_cmd = N'SCALL [sp_MSupd_appT_Mesi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_MiscelazioneCombustibile', @source_owner = N'app', @source_object = N'T_MiscelazioneCombustibile', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_MiscelazioneCombustibile', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_MiscelazioneCombustibile]', @del_cmd = N'CALL [sp_MSdel_appT_MiscelazioneCombustibile]', @upd_cmd = N'SCALL [sp_MSupd_appT_MiscelazioneCombustibile]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_NaturaEvento', @source_owner = N'app', @source_object = N'T_NaturaEvento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_NaturaEvento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_NaturaEvento]', @del_cmd = N'CALL [sp_MSdel_appT_NaturaEvento]', @upd_cmd = N'SCALL [sp_MSupd_appT_NaturaEvento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_NodiIdraulici', @source_owner = N'app', @source_object = N'T_NodiIdraulici', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_NodiIdraulici', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_NodiIdraulici]', @del_cmd = N'CALL [sp_MSdel_appT_NodiIdraulici]', @upd_cmd = N'SCALL [sp_MSupd_appT_NodiIdraulici]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_NodoIdraulicoVirtuale', @source_owner = N'app', @source_object = N'T_NodoIdraulicoVirtuale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_NodoIdraulicoVirtuale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_NodoIdraulicoVirtuale]', @del_cmd = N'CALL [sp_MSdel_appT_NodoIdraulicoVirtuale]', @upd_cmd = N'SCALL [sp_MSupd_appT_NodoIdraulicoVirtuale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Nuclei', @source_owner = N'app', @source_object = N'T_Nuclei', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_Nuclei', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Nuclei]', @del_cmd = N'CALL [sp_MSdel_appT_Nuclei]', @upd_cmd = N'SCALL [sp_MSupd_appT_Nuclei]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_NucleiSogliaEnergia', @source_owner = N'app', @source_object = N'T_NucleiSogliaEnergia', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_NucleiSogliaEnergia', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_NucleiSogliaEnergia]', @del_cmd = N'CALL [sp_MSdel_appT_NucleiSogliaEnergia]', @upd_cmd = N'SCALL [sp_MSupd_appT_NucleiSogliaEnergia]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Operatore', @source_owner = N'app', @source_object = N'T_Operatore', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Operatore', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Operatore]', @del_cmd = N'CALL [sp_MSdel_appT_Operatore]', @upd_cmd = N'SCALL [sp_MSupd_appT_Operatore]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_P_Man_Interventi_sui_Gruppi', @source_owner = N'app', @source_object = N'T_P_Man_Interventi_sui_Gruppi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_P_Man_Interventi_sui_Gruppi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_P_Man_Interventi_sui_Gruppi]', @del_cmd = N'CALL [sp_MSdel_appT_P_Man_Interventi_sui_Gruppi]', @upd_cmd = N'SCALL [sp_MSupd_appT_P_Man_Interventi_sui_Gruppi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_P_Man_InterventiSfiori', @source_owner = N'app', @source_object = N'T_P_Man_InterventiSfiori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_P_Man_InterventiSfiori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_P_Man_InterventiSfiori]', @del_cmd = N'CALL [sp_MSdel_appT_P_Man_InterventiSfiori]', @upd_cmd = N'SCALL [sp_MSupd_appT_P_Man_InterventiSfiori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_P_Man_Testata_Centrale_Nucleo', @source_owner = N'app', @source_object = N'T_P_Man_Testata_Centrale_Nucleo', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_P_Man_Testata_Centrale_Nucleo', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_P_Man_Testata_Centrale_Nucleo]', @del_cmd = N'CALL [sp_MSdel_appT_P_Man_Testata_Centrale_Nucleo]', @upd_cmd = N'SCALL [sp_MSupd_appT_P_Man_Testata_Centrale_Nucleo]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_P_Man_Testata_Sede', @source_owner = N'app', @source_object = N'T_P_Man_Testata_Sede', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_P_Man_Testata_Sede', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_P_Man_Testata_Sede]', @del_cmd = N'CALL [sp_MSdel_appT_P_Man_Testata_Sede]', @upd_cmd = N'SCALL [sp_MSupd_appT_P_Man_Testata_Sede]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheMROA', @source_owner = N'app', @source_object = N'T_PartiteEconomicheMROA', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheMROA', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheMROA]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheMROA]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheMROA]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheMROCA', @source_owner = N'app', @source_object = N'T_PartiteEconomicheMROCA', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheMROCA', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheMROCA]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheMROCA]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheMROCA]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheStimate', @source_owner = N'app', @source_object = N'T_PartiteEconomicheStimate', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheStimate', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheStimate]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheStimate]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheStimate]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheStimateExPost', @source_owner = N'app', @source_object = N'T_PartiteEconomicheStimateExPost', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheStimateExPost', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheStimateExPost]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheStimateExPost]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheStimateExPost]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheStimateMB', @source_owner = N'app', @source_object = N'T_PartiteEconomicheStimateMB', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheStimateMB', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheStimateMB]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheStimateMB]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheStimateMB]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheStimateMSD', @source_owner = N'app', @source_object = N'T_PartiteEconomicheStimateMSD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheStimateMSD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheStimateMSD]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheStimateMSD]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheStimateMSD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PartiteEconomicheUVRP', @source_owner = N'app', @source_object = N'T_PartiteEconomicheUVRP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PartiteEconomicheUVRP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PartiteEconomicheUVRP]', @del_cmd = N'CALL [sp_MSdel_appT_PartiteEconomicheUVRP]', @upd_cmd = N'SCALL [sp_MSupd_appT_PartiteEconomicheUVRP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PCICentrale', @source_owner = N'app', @source_object = N'T_PCICentrale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PCICentrale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PCICentrale]', @del_cmd = N'CALL [sp_MSdel_appT_PCICentrale]', @upd_cmd = N'SCALL [sp_MSupd_appT_PCICentrale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PManTestataCentraleSede', @source_owner = N'app', @source_object = N'T_PManTestataCentraleSede', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PManTestataCentraleSede', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PManTestataCentraleSede]', @del_cmd = N'CALL [sp_MSdel_appT_PManTestataCentraleSede]', @upd_cmd = N'SCALL [sp_MSupd_appT_PManTestataCentraleSede]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PostazioniAmbientali', @source_owner = N'app', @source_object = N'T_PostazioniAmbientali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PostazioniAmbientali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PostazioniAmbientali]', @del_cmd = N'CALL [sp_MSdel_appT_PostazioniAmbientali]', @upd_cmd = N'SCALL [sp_MSupd_appT_PostazioniAmbientali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PrezziMSD', @source_owner = N'app', @source_object = N'T_PrezziMSD', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PrezziMSD', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PrezziMSD]', @del_cmd = N'CALL [sp_MSdel_appT_PrezziMSD]', @upd_cmd = N'SCALL [sp_MSupd_appT_PrezziMSD]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PrezziOrariCP', @source_owner = N'app', @source_object = N'T_PrezziOrariCP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PrezziOrariCP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PrezziOrariCP]', @del_cmd = N'CALL [sp_MSdel_appT_PrezziOrariCP]', @upd_cmd = N'SCALL [sp_MSupd_appT_PrezziOrariCP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PrezziSbilanciamentoTerna', @source_owner = N'app', @source_object = N'T_PrezziSbilanciamentoTerna', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PrezziSbilanciamentoTerna', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PrezziSbilanciamentoTerna]', @del_cmd = N'CALL [sp_MSdel_appT_PrezziSbilanciamentoTerna]', @upd_cmd = N'SCALL [sp_MSupd_appT_PrezziSbilanciamentoTerna]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ProduzioneNettaGRTN', @source_owner = N'app', @source_object = N'T_ProduzioneNettaGRTN', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ProduzioneNettaGRTN', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ProduzioneNettaGRTN]', @del_cmd = N'CALL [sp_MSdel_appT_ProduzioneNettaGRTN]', @upd_cmd = N'SCALL [sp_MSupd_appT_ProduzioneNettaGRTN]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ProfiliFunzioni', @source_owner = N'app', @source_object = N'T_ProfiliFunzioni', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ProfiliFunzioni', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ProfiliFunzioni]', @del_cmd = N'CALL [sp_MSdel_appT_ProfiliFunzioni]', @upd_cmd = N'SCALL [sp_MSupd_appT_ProfiliFunzioni]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ProfiliFunzioniFunzioni', @source_owner = N'app', @source_object = N'T_ProfiliFunzioniFunzioni', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_ProfiliFunzioniFunzioni', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ProfiliFunzioniFunzioni]', @del_cmd = N'CALL [sp_MSdel_appT_ProfiliFunzioniFunzioni]', @upd_cmd = N'SCALL [sp_MSupd_appT_ProfiliFunzioniFunzioni]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PuntoScambio', @source_owner = N'app', @source_object = N'T_PuntoScambio', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PuntoScambio', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PuntoScambio]', @del_cmd = N'CALL [sp_MSdel_appT_PuntoScambio]', @upd_cmd = N'SCALL [sp_MSupd_appT_PuntoScambio]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PuntoScambioVirtuale', @source_owner = N'app', @source_object = N'T_PuntoScambioVirtuale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PuntoScambioVirtuale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PuntoScambioVirtuale]', @del_cmd = N'CALL [sp_MSdel_appT_PuntoScambioVirtuale]', @upd_cmd = N'SCALL [sp_MSupd_appT_PuntoScambioVirtuale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PvmcGrtn', @source_owner = N'app', @source_object = N'T_PvmcGrtn', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PvmcGrtn', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PvmcGrtn]', @del_cmd = N'CALL [sp_MSdel_appT_PvmcGrtn]', @upd_cmd = N'SCALL [sp_MSupd_appT_PvmcGrtn]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_PvmcParametri', @source_owner = N'app', @source_object = N'T_PvmcParametri', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_PvmcParametri', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_PvmcParametri]', @del_cmd = N'CALL [sp_MSdel_appT_PvmcParametri]', @upd_cmd = N'SCALL [sp_MSupd_appT_PvmcParametri]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RaggruppamentoCausaleSbil', @source_owner = N'app', @source_object = N'T_RaggruppamentoCausaleSbil', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RaggruppamentoCausaleSbil', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RaggruppamentoCausaleSbil]', @del_cmd = N'CALL [sp_MSdel_appT_RaggruppamentoCausaleSbil]', @upd_cmd = N'SCALL [sp_MSupd_appT_RaggruppamentoCausaleSbil]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RapportoDiProduzione', @source_owner = N'app', @source_object = N'T_RapportoDiProduzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RapportoDiProduzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RapportoDiProduzione]', @del_cmd = N'CALL [sp_MSdel_appT_RapportoDiProduzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_RapportoDiProduzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RapportoDiProduzioneTestata', @source_owner = N'app', @source_object = N'T_RapportoDiProduzioneTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RapportoDiProduzioneTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RapportoDiProduzioneTestata]', @del_cmd = N'CALL [sp_MSdel_appT_RapportoDiProduzioneTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_RapportoDiProduzioneTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RdPOrario', @source_owner = N'app', @source_object = N'T_RdPOrario', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RdPOrario', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RdPOrario]', @del_cmd = N'CALL [sp_MSdel_appT_RdPOrario]', @upd_cmd = N'SCALL [sp_MSupd_appT_RdPOrario]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RdPOrarioTestata', @source_owner = N'app', @source_object = N'T_RdPOrarioTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RdPOrarioTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RdPOrarioTestata]', @del_cmd = N'CALL [sp_MSdel_appT_RdPOrarioTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_RdPOrarioTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RelazioniAllegato', @source_owner = N'app', @source_object = N'T_RelazioniAllegato', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RelazioniAllegato', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RelazioniAllegato]', @del_cmd = N'CALL [sp_MSdel_appT_RelazioniAllegato]', @upd_cmd = N'SCALL [sp_MSupd_appT_RelazioniAllegato]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RelazioniMenu', @source_owner = N'app', @source_object = N'T_RelazioniMenu', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_RelazioniMenu', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RelazioniMenu]', @del_cmd = N'CALL [sp_MSdel_appT_RelazioniMenu]', @upd_cmd = N'SCALL [sp_MSupd_appT_RelazioniMenu]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RipartizioneConsumiCombustibili', @source_owner = N'app', @source_object = N'T_RipartizioneConsumiCombustibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RipartizioneConsumiCombustibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RipartizioneConsumiCombustibili]', @del_cmd = N'CALL [sp_MSdel_appT_RipartizioneConsumiCombustibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_RipartizioneConsumiCombustibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RUP', @source_owner = N'app', @source_object = N'T_RUP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RUP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RUP]', @del_cmd = N'CALL [sp_MSdel_appT_RUP]', @upd_cmd = N'SCALL [sp_MSupd_appT_RUP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_RUPIndisponibili', @source_owner = N'app', @source_object = N'T_RUPIndisponibili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_RUPIndisponibili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_RUPIndisponibili]', @del_cmd = N'CALL [sp_MSdel_appT_RUPIndisponibili]', @upd_cmd = N'SCALL [sp_MSupd_appT_RUPIndisponibili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SbilPerCausale', @source_owner = N'app', @source_object = N'T_SbilPerCausale', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SbilPerCausale', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SbilPerCausale]', @del_cmd = N'CALL [sp_MSdel_appT_SbilPerCausale]', @upd_cmd = N'SCALL [sp_MSupd_appT_SbilPerCausale]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SbilPV', @source_owner = N'app', @source_object = N'T_SbilPV', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x00000000080350DF, @identityrangemanagementoption = N'manual', @destination_table = N'T_SbilPV', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SbilPV]', @del_cmd = N'CALL [sp_MSdel_appT_SbilPV]', @upd_cmd = N'SCALL [sp_MSupd_appT_SbilPV]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SbilPVMC', @source_owner = N'app', @source_object = N'T_SbilPVMC', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SbilPVMC', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SbilPVMC]', @del_cmd = N'CALL [sp_MSdel_appT_SbilPVMC]', @upd_cmd = N'SCALL [sp_MSupd_appT_SbilPVMC]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SbilPVMCMinuti', @source_owner = N'app', @source_object = N'T_SbilPVMCMinuti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SbilPVMCMinuti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SbilPVMCMinuti]', @del_cmd = N'CALL [sp_MSdel_appT_SbilPVMCMinuti]', @upd_cmd = N'SCALL [sp_MSupd_appT_SbilPVMCMinuti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SbilPVMCTestata', @source_owner = N'app', @source_object = N'T_SbilPVMCTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SbilPVMCTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SbilPVMCTestata]', @del_cmd = N'CALL [sp_MSdel_appT_SbilPVMCTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_SbilPVMCTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ScambiZonali', @source_owner = N'app', @source_object = N'T_ScambiZonali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ScambiZonali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ScambiZonali]', @del_cmd = N'CALL [sp_MSdel_appT_ScambiZonali]', @upd_cmd = N'SCALL [sp_MSupd_appT_ScambiZonali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SegnoZonaleTerna', @source_owner = N'app', @source_object = N'T_SegnoZonaleTerna', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SegnoZonaleTerna', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SegnoZonaleTerna]', @del_cmd = N'CALL [sp_MSdel_appT_SegnoZonaleTerna]', @upd_cmd = N'SCALL [sp_MSupd_appT_SegnoZonaleTerna]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Semibanda', @source_owner = N'app', @source_object = N'T_Semibanda', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Semibanda', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Semibanda]', @del_cmd = N'CALL [sp_MSdel_appT_Semibanda]', @upd_cmd = N'SCALL [sp_MSupd_appT_Semibanda]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ServiziAusiliari', @source_owner = N'app', @source_object = N'T_ServiziAusiliari', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ServiziAusiliari', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ServiziAusiliari]', @del_cmd = N'CALL [sp_MSdel_appT_ServiziAusiliari]', @upd_cmd = N'SCALL [sp_MSupd_appT_ServiziAusiliari]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SettlementSbilanciamenti', @source_owner = N'app', @source_object = N'T_SettlementSbilanciamenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SettlementSbilanciamenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SettlementSbilanciamenti]', @del_cmd = N'CALL [sp_MSdel_appT_SettlementSbilanciamenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_SettlementSbilanciamenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Sezioni', @source_owner = N'app', @source_object = N'T_Sezioni', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Sezioni', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Sezioni]', @del_cmd = N'CALL [sp_MSdel_appT_Sezioni]', @upd_cmd = N'SCALL [sp_MSupd_appT_Sezioni]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Sfiori', @source_owner = N'app', @source_object = N'T_Sfiori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Sfiori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Sfiori]', @del_cmd = N'CALL [sp_MSdel_appT_Sfiori]', @upd_cmd = N'SCALL [sp_MSupd_appT_Sfiori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKC_Conduzione', @source_owner = N'app', @source_object = N'T_SKC_Conduzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKC_Conduzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKC_Conduzione]', @del_cmd = N'CALL [sp_MSdel_appT_SKC_Conduzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKC_Conduzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKC_EventiTeleriscaldamento', @source_owner = N'app', @source_object = N'T_SKC_EventiTeleriscaldamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKC_EventiTeleriscaldamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKC_EventiTeleriscaldamento]', @del_cmd = N'CALL [sp_MSdel_appT_SKC_EventiTeleriscaldamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKC_EventiTeleriscaldamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCAvviamenti', @source_owner = N'app', @source_object = N'T_SKCAvviamenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCAvviamenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCAvviamenti]', @del_cmd = N'CALL [sp_MSdel_appT_SKCAvviamenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCAvviamenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCConsQualComb', @source_owner = N'app', @source_object = N'T_SKCConsQualComb', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCConsQualComb', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCConsQualComb]', @del_cmd = N'CALL [sp_MSdel_appT_SKCConsQualComb]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCConsQualComb]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCConsSpecificiMO13', @source_owner = N'app', @source_object = N'T_SKCConsSpecificiMO13', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCConsSpecificiMO13', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCConsSpecificiMO13]', @del_cmd = N'CALL [sp_MSdel_appT_SKCConsSpecificiMO13]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCConsSpecificiMO13]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCEventiManutenzione', @source_owner = N'app', @source_object = N'T_SKCEventiManutenzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCEventiManutenzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCEventiManutenzione]', @del_cmd = N'CALL [sp_MSdel_appT_SKCEventiManutenzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCEventiManutenzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCIntervalliIndisponibilita', @source_owner = N'app', @source_object = N'T_SKCIntervalliIndisponibilita', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCIntervalliIndisponibilita', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCIntervalliIndisponibilita]', @del_cmd = N'CALL [sp_MSdel_appT_SKCIntervalliIndisponibilita]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCIntervalliIndisponibilita]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKCSintesiSfiori', @source_owner = N'app', @source_object = N'T_SKCSintesiSfiori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKCSintesiSfiori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKCSintesiSfiori]', @del_cmd = N'CALL [sp_MSdel_appT_SKCSintesiSfiori]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKCSintesiSfiori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKEvento', @source_owner = N'app', @source_object = N'T_SKEvento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKEvento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKEvento]', @del_cmd = N'CALL [sp_MSdel_appT_SKEvento]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKEvento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKEventoGruppi', @source_owner = N'app', @source_object = N'T_SKEventoGruppi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKEventoGruppi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKEventoGruppi]', @del_cmd = N'CALL [sp_MSdel_appT_SKEventoGruppi]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKEventoGruppi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKEventoIntervalliIndisponibilita', @source_owner = N'app', @source_object = N'T_SKEventoIntervalliIndisponibilita', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKEventoIntervalliIndisponibilita', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKEventoIntervalliIndisponibilita]', @del_cmd = N'CALL [sp_MSdel_appT_SKEventoIntervalliIndisponibilita]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKEventoIntervalliIndisponibilita]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SKEventoIntervalliSfiori', @source_owner = N'app', @source_object = N'T_SKEventoIntervalliSfiori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SKEventoIntervalliSfiori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SKEventoIntervalliSfiori]', @del_cmd = N'CALL [sp_MSdel_appT_SKEventoIntervalliSfiori]', @upd_cmd = N'SCALL [sp_MSupd_appT_SKEventoIntervalliSfiori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Societa', @source_owner = N'app', @source_object = N'T_Societa', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Societa', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Societa]', @del_cmd = N'CALL [sp_MSdel_appT_Societa]', @upd_cmd = N'SCALL [sp_MSupd_appT_Societa]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SottocauseIndisponibilita', @source_owner = N'app', @source_object = N'T_SottocauseIndisponibilita', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SottocauseIndisponibilita', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SottocauseIndisponibilita]', @del_cmd = N'CALL [sp_MSdel_appT_SottocauseIndisponibilita]', @upd_cmd = N'SCALL [sp_MSupd_appT_SottocauseIndisponibilita]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Stati', @source_owner = N'app', @source_object = N'T_Stati', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_Stati', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Stati]', @del_cmd = N'CALL [sp_MSdel_appT_Stati]', @upd_cmd = N'SCALL [sp_MSupd_appT_Stati]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_StatoTempo', @source_owner = N'app', @source_object = N'T_StatoTempo', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_StatoTempo', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_StatoTempo]', @del_cmd = N'CALL [sp_MSdel_appT_StatoTempo]', @upd_cmd = N'SCALL [sp_MSupd_appT_StatoTempo]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_SUContratto', @source_owner = N'app', @source_object = N'T_SUContratto', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_SUContratto', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_SUContratto]', @del_cmd = N'CALL [sp_MSdel_appT_SUContratto]', @upd_cmd = N'SCALL [sp_MSupd_appT_SUContratto]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Teleregolazione', @source_owner = N'app', @source_object = N'T_Teleregolazione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Teleregolazione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Teleregolazione]', @del_cmd = N'CALL [sp_MSdel_appT_Teleregolazione]', @upd_cmd = N'SCALL [sp_MSupd_appT_Teleregolazione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TensioneConnessione', @source_owner = N'app', @source_object = N'T_TensioneConnessione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TensioneConnessione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TensioneConnessione]', @del_cmd = N'CALL [sp_MSdel_appT_TensioneConnessione]', @upd_cmd = N'SCALL [sp_MSupd_appT_TensioneConnessione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoAbilitazione', @source_owner = N'app', @source_object = N'T_TipoAbilitazione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoAbilitazione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoAbilitazione]', @del_cmd = N'CALL [sp_MSdel_appT_TipoAbilitazione]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoAbilitazione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoAvviamento', @source_owner = N'app', @source_object = N'T_TipoAvviamento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoAvviamento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoAvviamento]', @del_cmd = N'CALL [sp_MSdel_appT_TipoAvviamento]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoAvviamento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoCluster', @source_owner = N'app', @source_object = N'T_TipoCluster', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoCluster', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoCluster]', @del_cmd = N'CALL [sp_MSdel_appT_TipoCluster]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoCluster]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoCombustibile', @source_owner = N'app', @source_object = N'T_TipoCombustibile', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoCombustibile', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoCombustibile]', @del_cmd = N'CALL [sp_MSdel_appT_TipoCombustibile]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoCombustibile]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoComponente', @source_owner = N'app', @source_object = N'T_TipoComponente', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoComponente', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoComponente]', @del_cmd = N'CALL [sp_MSdel_appT_TipoComponente]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoComponente]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoLiquidazione', @source_owner = N'app', @source_object = N'T_TipoLiquidazione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoLiquidazione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoLiquidazione]', @del_cmd = N'CALL [sp_MSdel_appT_TipoLiquidazione]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoLiquidazione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipologiaContatoriEnergia', @source_owner = N'app', @source_object = N'T_TipologiaContatoriEnergia', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipologiaContatoriEnergia', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipologiaContatoriEnergia]', @del_cmd = N'CALL [sp_MSdel_appT_TipologiaContatoriEnergia]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipologiaContatoriEnergia]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipologiaEsercizio', @source_owner = N'app', @source_object = N'T_TipologiaEsercizio', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipologiaEsercizio', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipologiaEsercizio]', @del_cmd = N'CALL [sp_MSdel_appT_TipologiaEsercizio]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipologiaEsercizio]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipologiaNodoIdraulico', @source_owner = N'app', @source_object = N'T_TipologiaNodoIdraulico', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipologiaNodoIdraulico', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipologiaNodoIdraulico]', @del_cmd = N'CALL [sp_MSdel_appT_TipologiaNodoIdraulico]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipologiaNodoIdraulico]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipologiaUP', @source_owner = N'app', @source_object = N'T_TipologiaUP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipologiaUP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipologiaUP]', @del_cmd = N'CALL [sp_MSdel_appT_TipologiaUP]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipologiaUP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoMercato', @source_owner = N'app', @source_object = N'T_TipoMercato', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoMercato', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoMercato]', @del_cmd = N'CALL [sp_MSdel_appT_TipoMercato]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoMercato]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoMovimento', @source_owner = N'app', @source_object = N'T_TipoMovimento', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoMovimento', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoMovimento]', @del_cmd = N'CALL [sp_MSdel_appT_TipoMovimento]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoMovimento]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoNodo2', @source_owner = N'app', @source_object = N'T_TipoNodo2', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoNodo2', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoNodo2]', @del_cmd = N'CALL [sp_MSdel_appT_TipoNodo2]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoNodo2]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoOfferta', @source_owner = N'app', @source_object = N'T_TipoOfferta', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoOfferta', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoOfferta]', @del_cmd = N'CALL [sp_MSdel_appT_TipoOfferta]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoOfferta]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoRicostruzione', @source_owner = N'app', @source_object = N'T_TipoRicostruzione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoRicostruzione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoRicostruzione]', @del_cmd = N'CALL [sp_MSdel_appT_TipoRicostruzione]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoRicostruzione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoServizio', @source_owner = N'app', @source_object = N'T_TipoServizio', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TipoServizio', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoServizio]', @del_cmd = N'CALL [sp_MSdel_appT_TipoServizio]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoServizio]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TipoStatistico', @source_owner = N'app', @source_object = N'T_TipoStatistico', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_TipoStatistico', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TipoStatistico]', @del_cmd = N'CALL [sp_MSdel_appT_TipoStatistico]', @upd_cmd = N'SCALL [sp_MSupd_appT_TipoStatistico]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_TollerFisico', @source_owner = N'app', @source_object = N'T_TollerFisico', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_TollerFisico', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_TollerFisico]', @del_cmd = N'CALL [sp_MSdel_appT_TollerFisico]', @upd_cmd = N'SCALL [sp_MSupd_appT_TollerFisico]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UniMisura', @source_owner = N'app', @source_object = N'T_UniMisura', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_UniMisura', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UniMisura]', @del_cmd = N'CALL [sp_MSdel_appT_UniMisura]', @upd_cmd = N'SCALL [sp_MSupd_appT_UniMisura]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UnitaProduttive', @source_owner = N'app', @source_object = N'T_UnitaProduttive', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UnitaProduttive', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UnitaProduttive]', @del_cmd = N'CALL [sp_MSdel_appT_UnitaProduttive]', @upd_cmd = N'SCALL [sp_MSupd_appT_UnitaProduttive]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UnitaProduttiveIntegrazione', @source_owner = N'app', @source_object = N'T_UnitaProduttiveIntegrazione', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UnitaProduttiveIntegrazione', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UnitaProduttiveIntegrazione]', @del_cmd = N'CALL [sp_MSdel_appT_UnitaProduttiveIntegrazione]', @upd_cmd = N'SCALL [sp_MSupd_appT_UnitaProduttiveIntegrazione]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UnitaProduttiveVirtuali', @source_owner = N'app', @source_object = N'T_UnitaProduttiveVirtuali', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UnitaProduttiveVirtuali', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UnitaProduttiveVirtuali]', @del_cmd = N'CALL [sp_MSdel_appT_UnitaProduttiveVirtuali]', @upd_cmd = N'SCALL [sp_MSupd_appT_UnitaProduttiveVirtuali]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UPAbilitate', @source_owner = N'app', @source_object = N'T_UPAbilitate', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UPAbilitate', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UPAbilitate]', @del_cmd = N'CALL [sp_MSdel_appT_UPAbilitate]', @upd_cmd = N'SCALL [sp_MSupd_appT_UPAbilitate]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UPAvviamenti', @source_owner = N'app', @source_object = N'T_UPAvviamenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UPAvviamenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UPAvviamenti]', @del_cmd = N'CALL [sp_MSdel_appT_UPAvviamenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_UPAvviamenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UpDatiVariabili', @source_owner = N'app', @source_object = N'T_UpDatiVariabili', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UpDatiVariabili', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UpDatiVariabili]', @del_cmd = N'CALL [sp_MSdel_appT_UpDatiVariabili]', @upd_cmd = N'SCALL [sp_MSupd_appT_UpDatiVariabili]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UPSAPR', @source_owner = N'app', @source_object = N'T_UPSAPR', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UPSAPR', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UPSAPR]', @del_cmd = N'CALL [sp_MSdel_appT_UPSAPR]', @upd_cmd = N'SCALL [sp_MSupd_appT_UPSAPR]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Utenti', @source_owner = N'app', @source_object = N'T_Utenti', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Utenti', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Utenti]', @del_cmd = N'CALL [sp_MSdel_appT_Utenti]', @upd_cmd = N'SCALL [sp_MSupd_appT_Utenti]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UtentiProfiliFunzioni', @source_owner = N'app', @source_object = N'T_UtentiProfiliFunzioni', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'none', @destination_table = N'T_UtentiProfiliFunzioni', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UtentiProfiliFunzioni]', @del_cmd = N'CALL [sp_MSdel_appT_UtentiProfiliFunzioni]', @upd_cmd = N'SCALL [sp_MSupd_appT_UtentiProfiliFunzioni]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UVRQ_ContributiReattivaOraria', @source_owner = N'app', @source_object = N'T_UVRQ_ContributiReattivaOraria', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UVRQ_ContributiReattivaOraria', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UVRQ_ContributiReattivaOraria]', @del_cmd = N'CALL [sp_MSdel_appT_UVRQ_ContributiReattivaOraria]', @upd_cmd = N'SCALL [sp_MSupd_appT_UVRQ_ContributiReattivaOraria]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UVRQ_ContributiReattivaTestata', @source_owner = N'app', @source_object = N'T_UVRQ_ContributiReattivaTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UVRQ_ContributiReattivaTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UVRQ_ContributiReattivaTestata]', @del_cmd = N'CALL [sp_MSdel_appT_UVRQ_ContributiReattivaTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_UVRQ_ContributiReattivaTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_UVRQParametri', @source_owner = N'app', @source_object = N'T_UVRQParametri', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_UVRQParametri', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_UVRQParametri]', @del_cmd = N'CALL [sp_MSdel_appT_UVRQParametri]', @upd_cmd = N'SCALL [sp_MSupd_appT_UVRQParametri]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ValorizzazioneProgrammi', @source_owner = N'app', @source_object = N'T_ValorizzazioneProgrammi', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ValorizzazioneProgrammi', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ValorizzazioneProgrammi]', @del_cmd = N'CALL [sp_MSdel_appT_ValorizzazioneProgrammi]', @upd_cmd = N'SCALL [sp_MSupd_appT_ValorizzazioneProgrammi]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_VariabiliUVAP', @source_owner = N'app', @source_object = N'T_VariabiliUVAP', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_VariabiliUVAP', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_VariabiliUVAP]', @del_cmd = N'CALL [sp_MSdel_appT_VariabiliUVAP]', @upd_cmd = N'SCALL [sp_MSupd_appT_VariabiliUVAP]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_VariabiliUVAPTestata', @source_owner = N'app', @source_object = N'T_VariabiliUVAPTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_VariabiliUVAPTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_VariabiliUVAPTestata]', @del_cmd = N'CALL [sp_MSdel_appT_VariabiliUVAPTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_VariabiliUVAPTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_Vettori', @source_owner = N'app', @source_object = N'T_Vettori', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_Vettori', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_Vettori]', @del_cmd = N'CALL [sp_MSdel_appT_Vettori]', @upd_cmd = N'SCALL [sp_MSupd_appT_Vettori]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidFeasibilityIntervals', @source_owner = N'app', @source_object = N'T_XbidFeasibilityIntervals', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidFeasibilityIntervals', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidFeasibilityIntervals]', @del_cmd = N'CALL [sp_MSdel_appT_XbidFeasibilityIntervals]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidFeasibilityIntervals]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidNetPositions', @source_owner = N'app', @source_object = N'T_XbidNetPositions', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidNetPositions', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidNetPositions]', @del_cmd = N'CALL [sp_MSdel_appT_XbidNetPositions]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidNetPositions]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XBidOffers', @source_owner = N'app', @source_object = N'T_XBidOffers', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XBidOffers', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XBidOffers]', @del_cmd = N'CALL [sp_MSdel_appT_XBidOffers]', @upd_cmd = N'SCALL [sp_MSupd_appT_XBidOffers]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidPhysicalProgram', @source_owner = N'app', @source_object = N'T_XbidPhysicalProgram', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidPhysicalProgram', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidPhysicalProgram]', @del_cmd = N'CALL [sp_MSdel_appT_XbidPhysicalProgram]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidPhysicalProgram]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidPhysicalProgramTestata', @source_owner = N'app', @source_object = N'T_XbidPhysicalProgramTestata', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidPhysicalProgramTestata', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidPhysicalProgramTestata]', @del_cmd = N'CALL [sp_MSdel_appT_XbidPhysicalProgramTestata]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidPhysicalProgramTestata]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidUnbalancedMovements', @source_owner = N'app', @source_object = N'T_XbidUnbalancedMovements', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidUnbalancedMovements', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidUnbalancedMovements]', @del_cmd = N'CALL [sp_MSdel_appT_XbidUnbalancedMovements]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidUnbalancedMovements]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_XbidUnitPrograms', @source_owner = N'app', @source_object = N'T_XbidUnitPrograms', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_XbidUnitPrograms', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_XbidUnitPrograms]', @del_cmd = N'CALL [sp_MSdel_appT_XbidUnitPrograms]', @upd_cmd = N'SCALL [sp_MSupd_appT_XbidUnitPrograms]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'T_ZoneMacrozone', @source_owner = N'app', @source_object = N'T_ZoneMacrozone', @type = N'logbased', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x000000000803509F, @identityrangemanagementoption = N'manual', @destination_table = N'T_ZoneMacrozone', @destination_owner = N'app', @status = 24, @vertical_partition = N'false', @ins_cmd = N'CALL [sp_MSins_appT_ZoneMacrozone]', @del_cmd = N'CALL [sp_MSdel_appT_ZoneMacrozone]', @upd_cmd = N'SCALL [sp_MSupd_appT_ZoneMacrozone]'
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'udf_FirstSundayOfTheMonth', @source_owner = N'app', @source_object = N'udf_FirstSundayOfTheMonth', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'udf_FirstSundayOfTheMonth', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'udf_LastSundayOfTheMonth', @source_owner = N'app', @source_object = N'udf_LastSundayOfTheMonth', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'udf_LastSundayOfTheMonth', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'UtcAddDays', @source_owner = N'app', @source_object = N'UtcAddDays', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'UtcAddDays', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'UtcToLocal', @source_owner = N'app', @source_object = N'UtcToLocal', @type = N'func schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'UtcToLocal', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VistaSettlement_A23Eco', @source_owner = N'app', @source_object = N'VistaSettlement_A23Eco', @type = N'proc schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VistaSettlement_A23Eco', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VistaSettlement_A23Fis', @source_owner = N'app', @source_object = N'VistaSettlement_A23Fis', @type = N'proc schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VistaSettlement_A23Fis', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VistaSettlement_Cumulati', @source_owner = N'app', @source_object = N'VistaSettlement_Cumulati', @type = N'proc schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VistaSettlement_Cumulati', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VistaSettlement_Minuti', @source_owner = N'app', @source_object = N'VistaSettlement_Minuti', @type = N'proc schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VistaSettlement_Minuti', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VistaSettlement_Pilot', @source_owner = N'app', @source_object = N'VistaSettlement_Pilot', @type = N'proc schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VistaSettlement_Pilot', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_EnergiaLorda', @source_owner = N'app', @source_object = N'VW_EnergiaLorda', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_EnergiaLorda', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_EnergieSpalmate', @source_owner = N'app', @source_object = N'VW_EnergieSpalmate', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_EnergieSpalmate', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_GETTONI', @source_owner = N'app', @source_object = N'VW_GETTONI', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_GETTONI', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_GETTONI_ACC_DETT', @source_owner = N'app', @source_object = N'VW_GETTONI_ACC_DETT', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_GETTONI_ACC_DETT', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_GETTONI_CA_DETT', @source_owner = N'app', @source_object = N'VW_GETTONI_CA_DETT', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_GETTONI_CA_DETT', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_IntervalliFattibilita', @source_owner = N'app', @source_object = N'VW_IntervalliFattibilita', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_IntervalliFattibilita', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_NominaProgrammi', @source_owner = N'app', @source_object = N'VW_NominaProgrammi', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_NominaProgrammi', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_OfferteXBID', @source_owner = N'app', @source_object = N'VW_OfferteXBID', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_OfferteXBID', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_PosizioneNettaXBID', @source_owner = N'app', @source_object = N'VW_PosizioneNettaXBID', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_PosizioneNettaXBID', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_ProgrammazionefisicaPortafoglio', @source_owner = N'app', @source_object = N'VW_ProgrammazionefisicaPortafoglio', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_ProgrammazionefisicaPortafoglio', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_ProgrammazionefisicaUnitaProduttiva', @source_owner = N'app', @source_object = N'VW_ProgrammazionefisicaUnitaProduttiva', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_ProgrammazionefisicaUnitaProduttiva', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_Qty_Valid_MB', @source_owner = N'app', @source_object = N'VW_Qty_Valid_MB', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_Qty_Valid_MB', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SbilanciamentoPortafoglio', @source_owner = N'app', @source_object = N'VW_SbilanciamentoPortafoglio', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SbilanciamentoPortafoglio', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SEB_Minuti', @source_owner = N'app', @source_object = N'VW_SEB_Minuti', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SEB_Minuti', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_A23ECO_15MIN', @source_owner = N'app', @source_object = N'VW_SETT_A23ECO_15MIN', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_A23ECO_15MIN', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_A23FIS_01MIN', @source_owner = N'app', @source_object = N'VW_SETT_A23FIS_01MIN', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_A23FIS_01MIN', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_A23FIS_01MIN_DETT', @source_owner = N'app', @source_object = N'VW_SETT_A23FIS_01MIN_DETT', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_A23FIS_01MIN_DETT', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_A23FIS_15MIN', @source_owner = N'app', @source_object = N'VW_SETT_A23FIS_15MIN', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_A23FIS_15MIN', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_A23FIS_15MIN_POTENZE', @source_owner = N'app', @source_object = N'VW_SETT_A23FIS_15MIN_POTENZE', @type = N'indexed view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_A23FIS_15MIN_POTENZE', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_CorrispettivoNonArbitraggioMacrozonale', @source_owner = N'app', @source_object = N'VW_SETT_CorrispettivoNonArbitraggioMacrozonale', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_CorrispettivoNonArbitraggioMacrozonale', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_DatiCNA', @source_owner = N'app', @source_object = N'VW_SETT_DatiCNA', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_DatiCNA', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_EPRODL_HH', @source_owner = N'app', @source_object = N'VW_SETT_METERING_EPRODL_HH', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_EPRODL_HH', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_EPRODL_MM', @source_owner = N'app', @source_object = N'VW_SETT_METERING_EPRODL_MM', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_EPRODL_MM', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_GAS_SNAM', @source_owner = N'app', @source_object = N'VW_SETT_METERING_GAS_SNAM', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_GAS_SNAM', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_GAS_SOLARE', @source_owner = N'app', @source_object = N'VW_SETT_METERING_GAS_SOLARE', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_GAS_SOLARE', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_HH', @source_owner = N'app', @source_object = N'VW_SETT_METERING_HH', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_HH', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_HH_ANNO', @source_owner = N'app', @source_object = N'VW_SETT_METERING_HH_ANNO', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_HH_ANNO', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_QMIN', @source_owner = N'app', @source_object = N'VW_SETT_METERING_QMIN', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_QMIN', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_METERING_QMIN_ANNO', @source_owner = N'app', @source_object = N'VW_SETT_METERING_QMIN_ANNO', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_METERING_QMIN_ANNO', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_PREZZISBILANCIAMENTOTERNA', @source_owner = N'app', @source_object = N'VW_SETT_PREZZISBILANCIAMENTOTERNA', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_PREZZISBILANCIAMENTOTERNA', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_PREZZOUNITARIOSBIL', @source_owner = N'app', @source_object = N'VW_SETT_PREZZOUNITARIOSBIL', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_PREZZOUNITARIOSBIL', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_PROGR_CUM_HH', @source_owner = N'app', @source_object = N'VW_SETT_PROGR_CUM_HH', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_PROGR_CUM_HH', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_SEGNOZONALETERNA', @source_owner = N'app', @source_object = N'VW_SETT_SEGNOZONALETERNA', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_SEGNOZONALETERNA', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_TERNAPREZZISBILANCIAMENTO', @source_owner = N'app', @source_object = N'VW_SETT_TERNAPREZZISBILANCIAMENTO', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_TERNAPREZZISBILANCIAMENTO', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_TERNAPREZZISBILANCIAMENTO_PRELIM', @source_owner = N'app', @source_object = N'VW_SETT_TERNAPREZZISBILANCIAMENTO_PRELIM', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_TERNAPREZZISBILANCIAMENTO_PRELIM', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_TERNASCAMBIZONALI', @source_owner = N'app', @source_object = N'VW_SETT_TERNASCAMBIZONALI', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_TERNASCAMBIZONALI', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_TERNASEGNOZONALE', @source_owner = N'app', @source_object = N'VW_SETT_TERNASEGNOZONALE', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_TERNASEGNOZONALE', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_TERNASEGNOZONALE_PRELIM', @source_owner = N'app', @source_object = N'VW_SETT_TERNASEGNOZONALE_PRELIM', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_TERNASEGNOZONALE_PRELIM', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_UVRQ_Corrispettivofisso', @source_owner = N'app', @source_object = N'VW_SETT_UVRQ_Corrispettivofisso', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_UVRQ_Corrispettivofisso', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_SETT_UVRQ_CorrServiziAusiliari', @source_owner = N'app', @source_object = N'VW_SETT_UVRQ_CorrServiziAusiliari', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_SETT_UVRQ_CorrServiziAusiliari', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_Settlement_Minuti', @source_owner = N'app', @source_object = N'VW_Settlement_Minuti', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_Settlement_Minuti', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_UnitaProduttivePerImpianti', @source_owner = N'app', @source_object = N'VW_UnitaProduttivePerImpianti', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_UnitaProduttivePerImpianti', @destination_owner = N'app', @status = 16
GO
use [nbdo]
exec sp_addarticle @publication = N'NBDO_PUB', @article = N'VW_VariabiliUVAP', @source_owner = N'app', @source_object = N'VW_VariabiliUVAP', @type = N'view schema only', @description = N'', @creation_script = N'', @pre_creation_cmd = N'drop', @schema_option = 0x0000000008000001, @destination_table = N'VW_VariabiliUVAP', @destination_owner = N'app', @status = 16
GO

-- Adding the transactional subscriptions
use [nbdo]
exec sp_addsubscription @publication = N'NBDO_PUB', @subscriber = N'DCPRVW043', @destination_db = N'nbdo', @subscription_type = N'Pull', @sync_type = N'automatic', @article = N'all', @update_mode = N'read only', @subscriber_type = 0
GO

