<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<wsdl:definitions xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.atos.net/snam/ImpiantiTermoelettrici/wsdl" xmlns:tns0="http://www.atos.net/snam/ImpiantiTermoelettrici/ImpiantiTermoelettriciMessages" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="ImpiantiTermoelettriciSOAPService" targetNamespace="http://www.atos.net/snam/ImpiantiTermoelettrici/wsdl">
  <wsdl:types>
    <xsd:schema xmlns:tns="http://www.atos.net/snam/ImpiantiTermoelettrici/ImpiantiTermoelettriciMessages" elementFormDefault="qualified" targetNamespace="http://www.atos.net/snam/ImpiantiTermoelettrici/ImpiantiTermoelettriciMessages">
      <xsd:complexType name="datiTermoelettriciTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="listaRemi" type="tns:listaRemiTYPE"/>
          <xsd:element minOccurs="0" name="codiceTitolare" type="xsd:string"/>
          <xsd:element minOccurs="0" name="dataInizio" type="xsd:dateTime"/>
          <xsd:element minOccurs="0" name="dataFine" type="xsd:dateTime"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="listaRemiTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="listaRemi" type="xsd:long"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="responseTermoelettriciTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="response" type="tns:responseTYPE"/>
          <xsd:element maxOccurs="1" minOccurs="0" name="errori" type="tns:erroriTYPE"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="responseTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" name="result" nillable="true" type="tns:RiepilogoRemi"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="RiepilogoRemi">
        <xsd:all>
          <xsd:element minOccurs="0" name="Remi" type="xsd:long"/>
          <xsd:element minOccurs="0" name="DenominazioneAOP" type="xsd:string"/>
          <xsd:element minOccurs="0" name="RisultatiLinea" type="tns:RisultatiLineaTYPE"/>
          <xsd:element minOccurs="0" name="RisultatiQualita" type="tns:RisultatiQualitaTYPE"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="erroriTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" name="errore" type="tns:RiepilogoErroriTYPE"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="RiepilogoErroriTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="TipoErrore" type="xsd:string"/>
          <xsd:element minOccurs="0" name="Descrizione" type="xsd:string"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="RisultatiLineaTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="datiLinea" type="tns:RiepilogoLineaTYPE"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="RiepilogoLineaTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="linea" type="xsd:int"/>
          <xsd:element minOccurs="0" name="RisultatiFlowComputer" type="tns:RisultatiFlowComputerTYPE"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="RisultatiFlowComputerTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="risultato" type="tns:RiepilogoFlowTYPE"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="RiepilogoFlowTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="flowComputerSlave" type="xsd:string"/>
          <xsd:element minOccurs="0" name="risultatoDati" type="tns:innerResultDTOTYPE"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="innerResultDTOTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="data" type="xsd:string"/>
          <xsd:element minOccurs="0" name="Vb" type="xsd:int"/>
          <xsd:element minOccurs="0" name="Energia" type="xsd:int"/>
          <xsd:element minOccurs="0" name="provenienza" type="xsd:string"/>
          <xsd:element minOccurs="0" name="PCS" type="xsd:double"/>
          <xsd:element minOccurs="0" name="flow_computer_slave" type="xsd:int"/>
          <xsd:element minOccurs="0" name="Vb_2" type="xsd:int"/>
          <xsd:element minOccurs="0" name="Energia_2" type="xsd:int"/>
          <xsd:element minOccurs="0" name="PCS_2" type="xsd:double"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:complexType name="RisultatiQualitaTYPE">
        <xsd:sequence>
          <xsd:element maxOccurs="unbounded" minOccurs="0" name="datiQualita" type="tns:QualitaTYPE"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="QualitaTYPE">
        <xsd:all>
          <xsd:element minOccurs="0" name="data" type="xsd:dateTime"/>
          <xsd:element minOccurs="0" name="PCS" type="xsd:double"/>
          <xsd:element minOccurs="0" name="PCI" type="xsd:double"/>
          <xsd:element minOccurs="0" name="Rho" type="xsd:double"/>
          <xsd:element minOccurs="0" name="CO2" type="xsd:double"/>
          <xsd:element minOccurs="0" name="TipoQual" type="xsd:string"/>
        </xsd:all>
      </xsd:complexType>
      <xsd:element name="datiTermoelettriciRequest" type="tns:datiTermoelettriciTYPE"/>
      <xsd:element name="datiTermoelettriciResponse" type="tns:responseTermoelettriciTYPE"/>
      <xsd:element name="internallInput" type="tns:InternallInputTYPE"/>
      <xsd:element name="datiTermoelettriciHeader" type="tns:datiTermoelettriciHeaderTYPE"/>
      <xsd:complexType name="datiTermoelettriciHeaderTYPE">
        <xsd:sequence>
          <xsd:element name="mittente" type="tns:RequiredString"/>
          <xsd:element name="destinatario" type="tns:RequiredString"/>
          <xsd:element name="flowID" type="tns:RequiredString"/>
          <xsd:element name="transactionID" type="tns:RequiredString"/>
          <xsd:element name="timestamp" type="xsd:dateTime"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:complexType name="InternallInputTYPE">
        <xsd:sequence>
          <xsd:element name="datiTermoelettriciHeader" type="tns:datiTermoelettriciHeaderTYPE"/>
          <xsd:element name="datiTermoelettrici" type="tns:datiTermoelettriciTYPE"/>
        </xsd:sequence>
      </xsd:complexType>
      <xsd:simpleType name="RequiredString">
        <xsd:restriction base="xsd:string">
          <xsd:minLength value="1"/>
        </xsd:restriction>
      </xsd:simpleType>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="datiTermoelettriciRequest">
    <wsdl:part element="tns0:datiTermoelettriciRequest" name="parameters"/>
  </wsdl:message>
  <wsdl:message name="datiTermoelettriciResponse">
    <wsdl:part element="tns0:datiTermoelettriciResponse" name="parameters"/>
  </wsdl:message>
  <wsdl:portType name="ImpiantiTermoelettriciSOAPService">
    <wsdl:operation name="datiTermoelettriciOperation">
      <wsdl:input message="tns:datiTermoelettriciRequest"/>
      <wsdl:output message="tns:datiTermoelettriciResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="SOAPServiceBinding" type="tns:ImpiantiTermoelettriciSOAPService">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="datiTermoelettriciOperation">
      <soap:operation soapAction="datiTermoelettriciOperation" style="document"/>
      <wsdl:input>
        <soap:body parts="parameters" use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body parts="parameters" use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="ImpiantiTermoelettriciSOAPService">
    <wsdl:port binding="tns:SOAPServiceBinding" name="ImpiantiTermoelettriciSOAPServiceSOAP">
      <soap:address location="http://0.0.0.0:0/TermoelettriciSOAPService"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
