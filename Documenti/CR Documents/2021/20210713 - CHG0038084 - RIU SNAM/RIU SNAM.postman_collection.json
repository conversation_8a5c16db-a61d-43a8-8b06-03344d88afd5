{"info": {"_postman_id": "c1911050-b3d1-473f-9136-a4805f201d89", "name": "RIU SNAM", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "TermoelettriciSOAPService", "request": {"auth": {"type": "basic", "basic": [{"key": "password", "value": "5j2Kn6FYehEs", "type": "string"}, {"key": "username", "value": "IntegrationE083_9060", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "text/xml", "type": "text"}, {"key": "SOAPAction", "value": "\"#POST\"", "type": "text"}], "body": {"mode": "raw", "raw": "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:imp=\"http://www.atos.net/snam/ImpiantiTermoelettrici/ImpiantiTermoelettriciMessages\">\r\n\t<soapenv:Header>\r\n\t\t<imp:datiTermoelettriciHeader>\r\n\t\t\t<imp:mittente>TIBCO</imp:mittente>\r\n\t\t\t<imp:destinatario>PMIS</imp:destinatario>\r\n\t\t\t<imp:flowID>a2a</imp:flowID>\r\n\t\t\t<imp:transactionID>1234</imp:transactionID>\r\n\t\t\t<imp:timestamp>2020-05-26T10:00:00</imp:timestamp>\r\n\t\t</imp:datiTermoelettriciHeader>\r\n\t</soapenv:Header>\r\n\t<soapenv:Body>\r\n\t\t<imp:datiTermoelettriciRequest>\r\n\t\t\t<!--You may enter the following 4 items in any order-->\r\n\t\t\t<!--Optional:-->\r\n\t\t\t<imp:listaRemi>\r\n\t\t\t\t<!--Zero or more repetitions:-->\r\n\t\t\t\t<imp:listaRemi>32177601</imp:listaRemi>\r\n\t\t\t</imp:listaRemi>\r\n\t\t\t<!--Optional:-->\r\n\t\t\t<imp:codiceTitolare/>\r\n\t\t\t<imp:dataInizio>2018-04-04T02:00:00</imp:dataInizio>\r\n\t\t\t<imp:dataFine>2018-04-05T01:00:00</imp:dataFine>\r\n\t\t</imp:datiTermoelettriciRequest>\r\n\t</soapenv:Body>\r\n</soapenv:Envelope>"}, "url": {"raw": "https://servizi-online-ha-gt.snam.it/ImpiantiTermoelettrici/TermoelettriciSOAPService", "protocol": "https", "host": ["servizi-online-ha-gt", "snam", "it"], "path": ["ImpiantiTermoelettrici", "TermoelettriciSOAPService"]}}, "response": []}], "auth": {"type": "basic", "basic": [{"key": "password", "value": "5j2Kn6FYehEs", "type": "string"}, {"key": "username", "value": "IntegrationE083_9060", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}]}