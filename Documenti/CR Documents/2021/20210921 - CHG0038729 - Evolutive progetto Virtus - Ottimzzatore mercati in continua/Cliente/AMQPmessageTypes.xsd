<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2007 rel. 3 (http://www.altova.com) by <PERSON> (Powel ASA) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:mesh="http://www.powel.com/SmE/AMQPmessageTypes" targetNamespace="http://www.powel.com/SmE/AMQPmessageTypes" elementFormDefault="qualified" version="1.0.0.0">
	<xsd:simpleType name="UUID">
		<xsd:annotation>
			<xsd:documentation>Specifies a universally unique id.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:pattern value="[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="CurveType">
		<xsd:annotation>
			<xsd:documentation>Specifies a curve type that defines the interpolation between points.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Unknown"/>
			<xsd:enumeration value="StaircaseStartOfStep"/>
			<xsd:enumeration value="PiecewiseLinear"/>
			<xsd:enumeration value="Staircase"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VersionType">
		<xsd:annotation>
			<xsd:documentation>Specifies a version type. AsOfTime specifies that the version is in effect as of the
      version timestamp. Forecasts are anticipated future values.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="None"/>
			<xsd:enumeration value="AsOfTime"/>
			<xsd:enumeration value="Versions"/>
			<xsd:enumeration value="Forecast"/>
			<xsd:enumeration value="AllForecasts"/>
			<xsd:enumeration value="MergedForecast"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:simpleType name="VersionMethod">
		<xsd:annotation>
			<xsd:documentation>Specifies a version method.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="Standard"/>
			<xsd:enumeration value="Exact"/>
			<xsd:enumeration value="Delta"/>
			<xsd:enumeration value="DeltaNaNIsNaN"/>
		</xsd:restriction>
	</xsd:simpleType>
	<!-- 
    A single point in a timeseries 
    -->
	<xsd:complexType name="TimeseriesPointBase">
		<xsd:annotation>
			<xsd:documentation>Specifies a single point in a time series.</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Timestamp" type="xsd:dateTime" use="required"/>
		<xsd:attribute name="PropagatingFlags" type="xsd:int"/>
		<xsd:attribute name="PrivateFlags" type="xsd:int"/>
	</xsd:complexType>
	<!-- 
    A single point in a timeseries 
    -->
	<xsd:complexType name="TimeseriesPoint">
		<xsd:annotation>
			<xsd:documentation>Specifies a single point in a time series.</xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:TimeseriesPointBase">
				<xsd:attribute name="Value" type="xsd:double"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- 
    An interval with range [Start,End> 
    -->
	<xsd:complexType name="Interval">
		<xsd:annotation>
			<xsd:documentation>Specifies an interval with a range defined by a Start time (inclusive) and an End time (not inclusive).</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Start" type="xsd:dateTime" use="required"/>
		<xsd:attribute name="End" type="xsd:dateTime" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="SubEvent">
		<xsd:annotation>
			<xsd:documentation>Specifies availability sub event</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Start" type="xsd:dateTime" use="required"/>
		<xsd:attribute name="End" type="xsd:dateTime" use="required"/>
		<xsd:attribute name="ValidUntil" type="xsd:string"/>
		<xsd:attribute name="RepetitionType" type="xsd:string"/>
		<xsd:attribute name="Status" type="xsd:string" use="required"/>
		<xsd:attribute name="Description" type="xsd:string"/>
		<xsd:attribute name="Value" type="xsd:double"/>
	</xsd:complexType>
	<xsd:complexType name="EventOccurrenceInstance">
		<xsd:annotation>
			<xsd:documentation>Specifies availability event single or repated occurrence</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Start" type="xsd:dateTime"/>
		<xsd:attribute name="End" type="xsd:dateTime"/>
		<xsd:attribute name="Status" type="xsd:string"/>
		<xsd:attribute name="Id" type="xsd:unsignedInt"/>
	</xsd:complexType>
	<!-- 
    An interval with timeseries points.
    All existing points within the specified interval [Start,End> will be replaced 
    -->
	<xsd:complexType name="TimeseriesSegmentBase">
		<xsd:annotation>
			<xsd:documentation>Specifies a segment consisting of an interval with time series points. All existing points within the 
      interval, defined by a Start time (inclusive) and an End time (not inclusive), will be replaced.</xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Start" type="xsd:dateTime" use="required"/>
		<xsd:attribute name="End" type="xsd:dateTime" use="required"/>
	</xsd:complexType>
	<!-- 
    An interval with timeseries points.
    All existing points within the specified interval [Start,End> will be replaced 
    -->
	<xsd:complexType name="TimeseriesSegment">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a segment consisting of an interval with time series points. All existing points within the
        interval, defined by a Start time (inclusive) and an End time (not inclusive), will be replaced.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:TimeseriesSegmentBase">
				<xsd:sequence minOccurs="0" maxOccurs="unbounded">
					<xsd:element name="Point" type="mesh:TimeseriesPoint"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesSegmentsBase">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a segment consisting of an interval with series points.
        The Version attribute specifies the version to write to.
        This is only valid for entries under a versioned time series, meaning a time series that was 
        created with Versioned set to true.
        If version is not specified for a versioned time series, then the latest version will be updated.
        Specifiying a version that does not already exist will cause the system to create a new version.
        If version is not specified and this is the first write to a time series entry, it will cause the system
        to create a new version using the earliest timestamp found in the input time series points.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Version" type="xsd:dateTime"/>
		<!-- 
        Specifies the Version of the timeseries to write to
        
        This is only valid for entries under a versioned timeseries.
        Meaning a Timeseries that was created with Versioned set to true.
        
        If version is not specified for a versioned timeseries, then the latest version will be updated
        
        Specifiying a version that does not already exist will cause the system to create a new version.
        
        If version is not specified and this is the first write to a timeseries entry will cause the system
        to create a new version using the earliest timestamp found in the input timeseries points 
        
         -->
	</xsd:complexType>
	<!-- 
    A collection of timeseries segments 
    -->
	<xsd:complexType name="TimeseriesSegments">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a collection of time series segments.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:TimeseriesSegmentsBase">
				<xsd:sequence minOccurs="0" maxOccurs="unbounded">
					<xsd:element name="Segment" type="mesh:TimeseriesSegment"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!-- 
    Binds a collection of time series segments to a time series entry object or Timeseries Reference identified by Id 
    (can also identify a time series calculation when retriving data from Mesh).
    DeltaT specifies the time resolution of the time series (e.g., hourly).
    UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.   
    -->
	<xsd:complexType name="TimeseriesPointsBase">
		<xsd:annotation>
			<xsd:documentation>
        Binds a collection of time series segments to a time series entry object or Timeseries Reference identified by Id
        (can also identify a time series calculation when retriving data from Mesh).
        DeltaT specifies the time resolution of the time series (e.g., hourly).
        UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Id" type="mesh:UUID"/>
		<xsd:attribute name="Path" type="xsd:string"/>
		<xsd:attribute name="Search" type="xsd:string"/>
		<xsd:attribute name="DeltaT" type="xsd:duration"/>
	</xsd:complexType>
	<!-- 
    Reply for a collection of time series segments to a time series entry object or Timeseries Reference identified by Id 
    (can also identify a time series calculation when retriving data from Mesh).
    DeltaT specifies the time resolution of the time series (e.g., hourly).
    UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.   
    -->
	<xsd:complexType name="TimeseriesPointsBaseReply">
		<xsd:annotation>
			<xsd:documentation>
        Reply to a collection of time series segments to a time series entry object or Timeseries Reference identified by Id
        (can also identify a time series calculation when retriving data from Mesh).
        DeltaT specifies the time resolution of the time series (e.g., hourly).
        UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Id" type="mesh:UUID" use="optional"/>
		<xsd:attribute name="Path" type="xsd:string" use="optional"/>
		<xsd:attribute name="DeltaT" type="xsd:duration" use="optional"/>
		<xsd:attribute name="Result" type="mesh:TimeseriesResult" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesPoints">
		<xsd:annotation>
			<xsd:documentation>
        Binds a collection of time series segments to a time series entry object.
        UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:TimeseriesPointsBase">
				<xsd:sequence>
					<xsd:element name="Segments" type="mesh:TimeseriesSegments"/>
				</xsd:sequence>
				<xsd:attribute name="UnitOfMeasurement" type="mesh:UUID"/>
				<xsd:attribute name="CurveType" type="mesh:CurveType"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesPointsReply">
		<xsd:annotation>
			<xsd:documentation>
        Reply to a collection of time series segments to a time series entry object.
        UnitOfMeasurement, DeltaT and CurveType will be ignored when storing time series data to Mesh.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:TimeseriesPointsBaseReply">
				<xsd:attribute name="UnitOfMeasurement" type="mesh:UUID" use="optional"/>
				<xsd:attribute name="CurveType" type="mesh:CurveType" use="optional"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<!--
    A collection of TimeseriesPoints, allowing complex updates to multiple timeseries  
    -->
	<xsd:complexType name="TimeseriesPointsCollection">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a collection of TimeseriesPoints, allowing complex updates to multiple time series.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence minOccurs="0" maxOccurs="unbounded">
			<xsd:choice>
				<xsd:element name="Points" type="mesh:TimeseriesPoints"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="RestrictionEvent">
		<xsd:annotation>
			<xsd:documentation>        
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="100">
			<xsd:element name="SubEvent" type="mesh:SubEvent"/>
		</xsd:sequence>
		<xsd:attribute name="Path" type="xsd:string" use="required"/>
		<xsd:attribute name="Search" type="xsd:string"/>
		<xsd:attribute name="EventId" type="xsd:string" use="required"/>
		<xsd:attribute name="Category" type="xsd:string" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="RevisionEvent">
		<xsd:annotation>
			<xsd:documentation>        
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="100">
			<xsd:element name="SubEvent" type="mesh:SubEvent"/>
		</xsd:sequence>
		<xsd:attribute name="Path" type="xsd:string" use="required"/>
		<xsd:attribute name="Search" type="xsd:string"/>
		<xsd:attribute name="EventId" type="xsd:string" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="AvailabilityEventRemove">
		<xsd:annotation>
			<xsd:documentation>        
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Path" type="xsd:string" use="required"/>
		<xsd:attribute name="Search" type="xsd:string"/>
		<xsd:attribute name="EventId" type="xsd:string" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="EventOccurrence">
		<xsd:annotation>
			<xsd:documentation>        
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence maxOccurs="100">
			<xsd:element name="Interval" type="mesh:EventOccurrenceInstance"/>
		</xsd:sequence>
		<xsd:attribute name="TargetEntity" type="xsd:string" use="required"/>
		<xsd:attribute name="EventId" type="xsd:string" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="AvailabilityEvents">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a collection of AvailabilityEvents.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence minOccurs="0" maxOccurs="unbounded">
			<xsd:choice>
				<xsd:element name="RestrictionEvent" type="mesh:RestrictionEvent"/>
				<xsd:element name="RestrictionEventUpdate" type="mesh:RestrictionEvent"/>
				<xsd:element name="RevisionEvent" type="mesh:RevisionEvent"/>
				<xsd:element name="RevisionEventUpdate" type="mesh:RevisionEvent"/>
				<xsd:element name="AvailabilityEventRemove" type="mesh:AvailabilityEventRemove"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:simpleType name="TimeseriesResult">
		<xsd:annotation>
			<xsd:documentation>Result codes for time series updates.</xsd:documentation>
		</xsd:annotation>
		<xsd:restriction base="xsd:string">
			<xsd:enumeration value="SUCCESS"/>
			<xsd:enumeration value="ID/PATH_NOT_FOUND"/>
			<xsd:enumeration value="PATH_NOT_UNIQUE"/>
			<xsd:enumeration value="OTHER_FAILURE"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="InputType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies an input data type.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:choice>
				<xsd:element name="TimeseriesPoints" type="mesh:TimeseriesPointsCollection"/>
				<xsd:element name="AvailabilityEvents" type="mesh:AvailabilityEvents"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="QueryBase">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a query base.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="QueryId" type="mesh:UUID" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="IdOrPathQuery">
		<xsd:annotation>
			<xsd:documentation>
        Specifies an id or path query.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:QueryBase">
				<xsd:attribute name="Id" type="mesh:UUID"/>
				<xsd:attribute name="Path" type="xsd:string"/>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="VersionRequest">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a version request.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="Type" type="mesh:VersionType"/>
		<xsd:attribute name="Timestamp" type="xsd:dateTime"/>
		<xsd:attribute name="Method" type="mesh:VersionMethod"/>
		<xsd:attribute name="MaxVersions" type="xsd:long"/>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesQueryInterval">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a time series query interval.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:Interval">
				<xsd:sequence>
					<xsd:sequence minOccurs="0">
						<xsd:element name="Version" type="mesh:VersionRequest"/>
					</xsd:sequence>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesQuery">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a time series query.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:IdOrPathQuery">
				<xsd:sequence>
					<xsd:sequence minOccurs="0" maxOccurs="unbounded">
						<xsd:element name="Interval" type="mesh:TimeseriesQueryInterval"/>
					</xsd:sequence>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="QueryType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies queries.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence minOccurs="0" maxOccurs="unbounded">
			<xsd:choice>
				<xsd:element name="Timeseries" type="mesh:TimeseriesQuery"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="RequestType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a request type.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="MessageId" type="xsd:string"/>
			<xsd:element name="MessageVersion" type="xsd:string"/>
			<xsd:element name="Sender" type="xsd:string" minOccurs="0"/>
			<xsd:element name="Receiver" type="xsd:string" minOccurs="0"/>
			<xsd:element name="CreationDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:element name="Inputs" type="mesh:InputType" minOccurs="0"/>
			<xsd:element name="Queries" type="mesh:QueryType" minOccurs="0"/>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="QueryReplyBase">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a query reply base.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:attribute name="QueryId" type="mesh:UUID" use="required"/>
	</xsd:complexType>
	<xsd:complexType name="TimeseriesQueryReply">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a time series query reply.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:complexContent>
			<xsd:extension base="mesh:QueryReplyBase">
				<xsd:sequence>
					<xsd:sequence minOccurs="0" maxOccurs="unbounded">
						<xsd:choice>
							<xsd:element name="Points" type="mesh:TimeseriesPoints"/>
						</xsd:choice>
					</xsd:sequence>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="Replies">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a replies element.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence minOccurs="0" maxOccurs="unbounded">
			<xsd:choice>
				<xsd:element name="Timeseries" type="mesh:TimeseriesQueryReply"/>
				<xsd:element name="FileReference" type="xsd:string"/>
			</xsd:choice>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="ReplyType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a reply type.
      </xsd:documentation>
		</xsd:annotation>
		<xsd:sequence>
			<xsd:element name="RequestMessageId" type="xsd:string"/>
			<xsd:element name="MessageVersion" type="xsd:string"/>
			<xsd:element name="Sender" type="xsd:string" minOccurs="0"/>
			<xsd:element name="Receiver" type="xsd:string" minOccurs="0"/>
			<xsd:element name="CreationDate" type="xsd:dateTime" minOccurs="0"/>
			<xsd:sequence minOccurs="0">
				<xsd:element name="ImportSuccess" type="xsd:boolean"/>
				<xsd:element name="ImportError" type="xsd:string" minOccurs="0"/>
			</xsd:sequence>
			<xsd:sequence>
				<xsd:sequence minOccurs="0">
					<xsd:element name="Replies" type="mesh:Replies"/>
				</xsd:sequence>
			</xsd:sequence>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:element name="Request" type="mesh:RequestType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a request.
      </xsd:documentation>
		</xsd:annotation>
	</xsd:element>
	<xsd:element name="Reply" type="mesh:ReplyType">
		<xsd:annotation>
			<xsd:documentation>
        Specifies a reply.
      </xsd:documentation>
		</xsd:annotation>
	</xsd:element>
</xsd:schema>
