<?xml version="1.0" encoding="ISO-8859-1"?>
<PIPEDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:XML-PIPE PIPEDocument.xsd" ReferenceNumber="3295256288" CreationDate="20211123010335" Version="1.0"  xmlns="urn:XML-PIPE">
  <TradingPartnerDirectory>
    <Sender>
      <TradingPartner PartnerType="Operator">
        <CompanyName>Gestore Mercati Energetici SPA</CompanyName>
        <CompanyIdentifier>IDGME</CompanyIdentifier>
      </TradingPartner>
    </Sender>
    <Recipient>
      <TradingPartner PartnerType="Operator">
        <CompanyName>A2A ENERGIEFUTURE S.P.A.</CompanyName>
        <CompanyIdentifier>119201</CompanyIdentifier>
      </TradingPartner>
    </Recipient>
  </TradingPartnerDirectory>
  <PIPTransaction ReferenceNumber="3295256289" InboundMessageCreationDate="20211123" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="119201" Type="FinalXbid" Cummulative="Yes">
      <Market>XB03</Market>
      <Date>20211123</Date>
      <UnitReferenceNumber>UP_S.F._DEL_5</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>119201</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">135,000</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">135,000</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">135,000</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">135,000</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">288,000</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">0,000</Quantity>
    </UnitSchedule>
  </PIPTransaction>
  <PIPTransaction ReferenceNumber="3295256290" InboundMessageCreationDate="20211123" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="119201" Type="FinalXbid" Cummulative="Yes">
      <Market>XB03</Market>
      <Date>20211123</Date>
      <UnitReferenceNumber>UP_S.F._DEL_1</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>119201</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">10,000</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">20,000</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">40,000</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">58,000</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">58,000</Quantity>
    </UnitSchedule>
  </PIPTransaction>
  <PIPTransaction ReferenceNumber="3295256291" InboundMessageCreationDate="20211123" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="119201" Type="FinalXbid" Cummulative="Yes">
      <Market>XB03</Market>
      <Date>20211123</Date>
      <UnitReferenceNumber>UP_S.F._DEL_2</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>119201</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">10,000</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">20,000</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">40,000</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">58,000</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">120,000</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">145,000</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">58,000</Quantity>
    </UnitSchedule>
  </PIPTransaction>
  <PIPTransaction ReferenceNumber="3295256292" InboundMessageCreationDate="20211123" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="119201" Type="FinalXbid" Cummulative="Yes">
      <Market>XB03</Market>
      <Date>20211123</Date>
      <UnitReferenceNumber>UP_S.F._DEL_6</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>119201</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">0,000</Quantity>
    </UnitSchedule>
  </PIPTransaction>
</PIPEDocument>