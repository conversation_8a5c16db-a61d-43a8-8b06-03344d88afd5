﻿#Install-Module -Name AzureRM.ServiceBus 

Login-AzureRmAccount

$resourceGroup = "GTC-PROD-OttimizzazioneMercati-rgp"
$queueNS = "a2a-prd-om-biztalk"

Write-Host ""
Write-Host "------------------------------------------------------------------------------------"
Write-Host "Resource Group:" $resourceGroup
Write-Host "Namespace:" $queueNS
Write-Host "------------------------------------------------------------------------------------"

$queues = Get-AzureRmServiceBusQueue -ResourceGroupName $resourceGroup -Namespace $queueNS -WarningAction SilentlyContinue

foreach ($queue in $queues)
{

    $queueName = $queue.Name
    $queueKey = Get-AzureRmServiceBusKey -ResourceGroup $resourceGroup -Namespace $queueNS -Queue $queueName -Name "virtus-policy" -WarningAction SilentlyContinue

    Write-Host "Queue:" $queueName
    Write-Host "Primary Connection String:" $queueKey.PrimaryConnectionString
    Write-Host "Secondary Connection String: " $queueKey.SecondaryConnectionString
    Write-Host "------------------------------------------------------------------------------------"
}

