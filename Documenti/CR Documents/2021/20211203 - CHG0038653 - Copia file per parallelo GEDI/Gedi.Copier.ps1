﻿<#
    .SYNOPSIS
            Copy only newer and unlocked files between directories

    .DESCRIPTION
            Copies only newer and unlocked files between directories.

            For each configured directory, scans only the newer files and copies to the destionation only unlocked files.

            Needs one json configuration file placed in the same folder the script lives with this structure

            {
                "lockedFileMaxRetry" : 10,     <-- how many retries when a file is locked before giving up
                "lockedFileWaitMs" : 500,      <-- how many milliseconds to wait between retries
                "skipModifiedSinceMs" : 30000, <-- do not consider files modified in the last 30 seconds
                "folders":  [                  <-- list of source and dest folder to use. Directory values must be 
                                                   properly escaped (typically, using two \ instead of one)
                                {
                                    "dest":  "\\\\dfs-test\\destination1",
                                    "source":  "\\\\dfs-test\\source1\\sourcedir"
                                },
                                {
                                    "dest":  "\\\\dfs-test\\destination2",
                                    "source":  "\\\\dfs-test\\source2\\sourcedir"
                                }
                            ]
            }

            Upon first run, it will also create a filecache.json file to keep track
            of the last copied file for each directory

    .NOTES
            Author: Microsys SRL (https://msys.it/)
#>

$ErrorActionPreference = "Stop"


function Test-IsFileLocked {
    <#
        Tests if a file is locked or not accessible
    #>
    [cmdletbinding()]
    param (
        [parameter(Mandatory = $True)]
        [string[]]$Path
    )
    process {
        foreach ($fItem in $Path) {
            $fItem = Convert-Path $fItem
            if ([System.IO.File]::Exists($fItem)) {
                try {
                    $fStream = [System.IO.File]::Open($fItem, 'Open', 'Write')
                    $fStream.Close()
                    $fStream.Dispose()
                    $isLocked = $False
                } catch [System.UnauthorizedAccessException] {
                    $isLocked = 'AccessDenied'
                } catch {
                    $IsLocked = $True
                }
                [pscustomobject]@{
                    File     = $fItem
                    isLocked = $isLocked
                }
            }
        }
    }
}

function Test-ConfigFolder {
    <#
        Tests if a file is locked or not accessible
    #>
    [cmdletbinding()]
    param (
        [parameter(Mandatory = $True)]
        [object[]]$configItem
    )
    begin {
        $sourceFolders = @()
    }
    process {
        foreach ($Item in $configItem) {
            if (!$Item.source) {
                Write-Error "No source folder configured"
            }
            if (!$Item.dest) {
                Write-Error "No dest folder configured"
            }
            if (-not(Test-Path -Path $Item.source -PathType Container)) {
                Write-Error "Directory source '$($Item.source)' not accessible"
            }
            if (-not(Test-Path -Path $Item.dest -PathType Container)) {
                Write-Error "Directory dest '$($Item.dest)' not accessible"
            }
            if ($Item.source -in $sourceFolders) {
                Write-Error "Source directory '$($Item.source)' is duplicated"
            } else {
                $sourceFolders += $Item.source
            }
        }
    }
}



#Load the config file
$configFile = Join-Path $PSScriptRoot 'config.json'
if (-not(Test-Path $configFile)) {
    Write-Error "config.json not found"
}
$configObj = Get-Content -Path $configFile -Raw | ConvertFrom-Json

##Sanity checks for the config
#How many times we try to copy a file before giving up
$waitMs = $configObj.lockedFileWaitMs
if ($null -eq $waitMs) {
    $waitMs = 500
} else {
    $waitMs = [int]$configObj.lockedFileWaitMs
    if ($waitMs -le 0) {
        Write-Error "parameter lockedFileWaitMs must be greater than 0"
    }
}

#How many milliseconds we wait between checks
$maxRetry = $configObj.lockedFileMaxRetry
if ($null -eq $maxRetry) {
    $maxRetry = 10
} else {
    $maxRetry = [int]$configObj.lockedFileMaxRetry
    if ($maxRetry -le 0) {
        Write-Error "parameter maxRetry must be greater than 0"
    }
}

#How many milliseconds in the past we need to check for recently modified files ?
#if it's too recent, we skip it
$skipModifiedSince = $configObj.skipModifiedSinceMs
if ($null -eq $maxRetry) {
    $skipModifiedSince = 1000
} else {
    $skipModifiedSince = [int]$configObj.skipModifiedSinceMs
    if ($maxRetry -lt 0) {
        Write-Error "parameter maxRetry must be greater or equal to 0"
    }
}


#are there folders to check ?
$folders = $configObj.folders
if ($null -eq $folders) {
    Write-Error "No configured folders found in config.json"
} else {
    Test-ConfigFolder -configItem $configObj.folders
}

#Load the cache file
$cacheFile = Join-Path $PSScriptRoot 'filecache.json'
if (Test-Path $cacheFile) {
    $cacheObj = @{ }
    try {
        (Get-Content -Path $cacheFile | ConvertFrom-Json).PSObject.Properties | ForEach-Object { $cacheObj[$_.Name] = [DateTime]::Parse($_.Value, $null, [System.Globalization.DateTimeStyles]::RoundtripKind) }
    } catch {
        Write-Error "Invalid data in filecache.json`n`n$_"
    }
} else {
    $cacheObj = @{ }
    Write-Host "$(Get-Date -f "s"): cache file not found, reverting to default behaviour"
}

#Track scanned directories
$usedKeys = @()

foreach ($configItem in $configObj.folders) {
    $dirwrap = [System.IO.DirectoryInfo]$configItem.source
    $allFiles = $dirwrap.EnumerateFiles()
    $cacheKey = $configItem.source
    $usedKeys += $cacheKey
    if ($cacheObj.ContainsKey($cacheKey)) {
        $lastCreated = $cacheObj[$cacheKey]
    } else {
        $lastCreated = (Get-Date).ToUniversalTime().AddMinutes(-30)
    }
    Write-Host "$(Get-Date -f "s"): **** $($configItem.source)"
    Write-Host "$(Get-Date -f "s"): Searching files created after $($lastCreated.ToString("o"))"
    $lastModifiedTimeLimit = (Get-Date).ToUniversalTime().AddMilliseconds(-$skipModifiedSince)
    $filteredFiles = $allFiles | Where-Object { $_.CreationTimeUtc -GT $lastCreated } | Where-Object { $_.LastWriteTimeUtc -GT $lastModifiedTimeLimit } | Sort-Object -Property CreationTimeUtc
    Write-Host “DEBUG: $($filteredFiles | ConvertTo-Json)”
    Write-Host "$(Get-Date -f "s"): Done searching, copying..."

    #create a flag to signal that a file was locked and it could not be copied
    $stopFlag = $false
    foreach ($f in $filteredFiles) {
        if ($stopFlag) {
            break
        }
        $retryCount = 0
        while ($true) {
            $retryCount += 1
            $fileStatus = Test-IsFileLocked -Path $f.FullName
            if ($fileStatus.isLocked -ne $False) {
                Start-Sleep -Milliseconds $waitMs
            } else {
                try {
                    Copy-Item -Path $f.FullName -Destination $configItem.dest
                    if ($f.CreationTimeUtc -gt $lastCreated) {
                        $lastCreated = $f.CreationTimeUtc
                    }
                    break
                } catch {
                    Start-Sleep -Milliseconds $waitMs
                }
            }
            if ($retryCount -ge $maxRetry) {
                Write-Host "$(Get-Date -f "s"): File $f is locked, abandoning"
                $stopFlag = $true
                break
            }
        }
        #store lastCreated in cache
        $cacheObj[$cacheKey] = $lastCreated.ToString("o")
    }
    Write-Host "$(Get-Date -f "s"): Completed"
    #update the cache and store it on a file
    $cacheObj[$cacheKey] = $lastCreated.ToString("o")
    $cacheObj | ConvertTo-Json | Out-File $cacheFile -Encoding utf8 -Force
}

#remove keys in cache not used in the last cycle
$cacheObjClean = @{ }
$cacheObj.GetEnumerator() | Where-Object Name -in $usedKeys | ForEach-Object { $cacheObjClean[$_.Name] = $_.Value }
$cacheObjClean | ConvertTo-Json | Out-File $cacheFile -Encoding utf8 -Force
