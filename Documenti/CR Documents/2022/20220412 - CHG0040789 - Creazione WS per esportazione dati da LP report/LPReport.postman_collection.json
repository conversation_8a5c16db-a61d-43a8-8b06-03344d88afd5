{"info": {"_postman_id": "d8678055-34b6-43b0-882f-38d89157d80f", "name": "LPReport", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "getProfile by MONTH", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n    \"tokenKey\": \"2022-03-29:c9ac97ac-121b-4ac3-a04d-fa486545f39f\",\r\n    \"dbKey\": \"a2a_db\",\r\n    \"customerKey\": \"gas_2332012\",\r\n    \"from\": \"2022-04-11\",\r\n    \"period\": \"MONTH\",\r\n    \"profile\": 1 \r\n}"}, "url": {"raw": "http://dcpraw01f:8080/SxREST/v1/getProfile", "protocol": "http", "host": ["dcpraw01f"], "port": "8080", "path": ["SxREST", "v1", "getProfile"]}}, "response": []}, {"name": "getProfile by DAY", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n    \"tokenKey\": \"2022-03-29:c9ac97ac-121b-4ac3-a04d-fa486545f39f\",\r\n    \"dbKey\": \"a2a_db\",\r\n    \"customerKey\": \"gas_2332012\",\r\n    \"from\": \"2022-04-11\",\r\n    \"period\": \"DAY\",\r\n    \"profile\": 1 \r\n}"}, "url": {"raw": "http://dcpraw01f:8080/SxREST/v1/getProfile", "protocol": "http", "host": ["dcpraw01f"], "port": "8080", "path": ["SxREST", "v1", "getProfile"]}}, "response": []}]}