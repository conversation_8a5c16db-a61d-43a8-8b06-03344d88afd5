{"info": {"_postman_id": "d9a4e84e-178d-4a36-b0fb-3e5265eb1f19", "name": "ERG", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Current", "item": [{"name": "Authentication", "request": {"method": "POST", "header": [{"key": "user_key", "value": "6485e2e76bf3088f57dcddaa2658338d", "type": "default"}, {"key": "Content-Type", "value": "application/json", "type": "default"}], "body": {"mode": "raw", "raw": "{\"password\":\"K0d2E#4rA3\",\"email\":\"<EMAIL>\"}"}, "url": {"raw": "https://autenticazioneUM.erg.eu/users/authenticate", "protocol": "https", "host": ["autenticazioneUM", "erg", "eu"], "path": ["users", "authenticate"]}}, "response": []}, {"name": "INT_IMPORT Energia Immessa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.p9VZdy5pJFh1iooracYnjmRHbVQOyXp9B3_P5CaM0F4", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "user_key", "value": "89422cb8b353b80802560692ce45a759", "type": "text"}], "url": {"raw": "https://upmeter.erg.eu/meters/UP_SCLSTRIANO_1/2022-06-01/2022-06-02", "protocol": "https", "host": ["upmeter", "erg", "eu"], "path": ["meters", "UP_SCLSTRIANO_1", "2022-06-01", "2022-06-02"]}}, "response": []}, {"name": "INT_EXPORT Indisponibilita", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************.01XClBnEo9FodRHY45n3jKRa2gD15bsZy-PS8B2UlAs", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "user_key", "value": "28e22a4c6b59b93dc03f1f6f69ea5381", "type": "text"}], "url": {"raw": "https://outages.erg.eu/power-erg-outages/UP_SCLSTRIANO_1/2022-06-01/2022-06-02/data?filter=%7B%22offset%22%3A0%2C%22limit%22%3A1000%7D", "protocol": "https", "host": ["outages", "erg", "eu"], "path": ["power-erg-outages", "UP_SCLSTRIANO_1", "2022-06-01", "2022-06-02", "data"], "query": [{"key": "filter", "value": "%7B%22offset%22%3A0%2C%22limit%22%3A1000%7D"}]}}, "response": []}]}, {"name": "New", "item": [{"name": "Authenticate", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "postman.setEnvironmentVariable(\"token\", jsonData.access_token);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/x-www-form-urlencoded", "type": "text"}], "body": {"mode": "u<PERSON><PERSON><PERSON>", "urlencoded": [{"key": "grant_type", "value": "client_credentials", "type": "default"}, {"key": "client_id", "value": "bded910d-3699-49e2-bafd-d8db97e043db", "type": "default"}, {"key": "scope", "value": "API://databrowsing/.default", "type": "default"}, {"key": "client_secret", "value": "ecIxMq7fAM6l06QyZTX4Ldk6dZ4pE6PbXxreje0suco=", "type": "default"}]}, "url": {"raw": "https://login.microsoftonline.com/erg2013.onmicrosoft.com/oauth2/v2.0/token", "protocol": "https", "host": ["login", "microsoftonline", "com"], "path": ["erg2013.onmicrosoft.com", "oauth2", "v2.0", "token"]}}, "response": []}, {"name": "INT_IMPORT Energia Immessa", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"Select\": {\r\n\t\t\"UP_EM\": \"UP\",\r\n\t\t\"DateId\": \"DateId\",\r\n\t\t\"<PERSON><PERSON><PERSON>\": \"Data\",\r\n\t\t\"Ora\": \"Ora\",\r\n\t\t\"QuartoOra\": \"QuartoOra\",\r\n\t\t\"Immesso\": \"Immesso\",\r\n\t\t\"UoM\": \"UoM\"\r\n\t},\r\n\t\"Filter\": \"[<PERSON><PERSON><PERSON>] BETWEEN '20220601' AND '20220602' AND [UP_EM] = 'UP_SCLSTRIANO_1';\"\r\n}"}, "url": {"raw": "https://ergapim.azure-api.net/databrowsing/api/Meter", "protocol": "https", "host": ["ergapim", "azure-api", "net"], "path": ["databrowsing", "api", "<PERSON>er"]}}, "response": []}, {"name": "INT_EXPORT Indisponibilita", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n    \"upname\":[\"UP_SCLSTRIANO_1\"],\r\n    \"fromDate_UTC\": \"2021-12-15T00:00:00+0000\",\r\n    \"toDate_UTC\": \"2021-12-15T23:59:00+0000\"\r\n}"}, "url": {"raw": "https://ergapim.azure-api.net/databrowsing/api/outage", "protocol": "https", "host": ["ergapim", "azure-api", "net"], "path": ["databrowsing", "api", "outage"]}}, "response": []}]}]}