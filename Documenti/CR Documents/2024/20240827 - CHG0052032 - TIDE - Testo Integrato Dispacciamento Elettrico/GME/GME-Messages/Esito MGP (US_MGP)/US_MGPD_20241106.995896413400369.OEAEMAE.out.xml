<?xml version="1.0" encoding="ISO-8859-1"?>
<PIPEDocument xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="urn:XML-PIPE PIPEDocument.xsd" ReferenceNumber="4916870205" CreationDate="20241105113950" Version="1.0"  xmlns="urn:XML-PIPE">
  <TradingPartnerDirectory>
    <Sender>
      <TradingPartner PartnerType="Operator">
        <CompanyName>Gestore Mercati Energetici SPA</CompanyName>
        <CompanyIdentifier>IDGME</CompanyIdentifier>
      </TradingPartner>
    </Sender>
    <Recipient>
      <TradingPartner PartnerType="Operator">
        <CompanyName>A2A SPA</CompanyName>
        <CompanyIdentifier>OEAEMAE</CompanyIdentifier>
      </TradingPartner>
    </Recipient>
  </TradingPartnerDirectory>
  <PIPTransaction ReferenceNumber="4916870206" InboundMessageCreationDate="20241105" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="OEAEMAE" Type="Preliminary" Cummulative="Yes">
      <Market>MGP</Market>
      <Date>20241106</Date>
      <UnitReferenceNumber>UP_CASSANO_2</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>OEAEMAE</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">0,000</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">35,000</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">35,000</Quantity>
    </UnitSchedule>
  </PIPTransaction>
  <PIPTransaction ReferenceNumber="4916870207" InboundMessageCreationDate="20241105" InboundMessageCreationTime="*********">
    <UnitSchedule MarketParticipantNumber="OEAEMAE" Type="Preliminary" Cummulative="Yes">
      <Market>MGP</Market>
      <Date>20241106</Date>
      <UnitReferenceNumber>UP_CHIAVENNA_1</UnitReferenceNumber>
      <ReferenceMarketParticipantNumber>OEAEMAE</ReferenceMarketParticipantNumber>
      <Quantity Hour="1" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="2" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="3" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="4" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="5" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="6" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="7" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="8" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="9" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="10" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="11" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="12" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="13" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="14" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="15" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="16" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="17" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="18" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="19" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="20" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="21" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="22" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="23" UnitOfMeasure="MWh">20,600</Quantity>
      <Quantity Hour="24" UnitOfMeasure="MWh">20,600</Quantity>
    </UnitSchedule>
  </PIPTransaction>
</PIPEDocument>