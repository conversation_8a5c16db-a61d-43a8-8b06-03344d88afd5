<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<include schemaLocation="SimpleTypesv1_0.xsd"/>
	<simpleType name="creationDateTimeType">
		<restriction base="integer">
			<minInclusive value="19000000000000"/>
			<maxInclusive value="21000000000000"/>
		</restriction>
	</simpleType>
	<!-- Body -->
	<element name="PIPEDocument">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartnerDirectory"/>
				<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
			</sequence>
			<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
			<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
			<attribute name="Version" type="string" use="required"/>
		</complexType>
	</element>
	<element name="TradingPartnerDirectory">
		<complexType>
			<sequence>
				<element ref="pd:Sender"/>
				<element ref="pd:Recipient"/>
			</sequence>
		</complexType>
	</element>
	<element name="Sender">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="Recipient">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="TradingPartner">
		<complexType>
			<sequence>
				<element name="CompanyName" type="string"/>
				<element name="CompanyIdentifier" type="string"/>
			</sequence>
			<attribute name="PartnerType" type="string"/>
		</complexType>
	</element>
	<element name="PIPTransaction">
		<complexType>
			<sequence>
				<element ref="pd:EstimatedDemandInformation"/>
			</sequence>
			<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
			<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional"/>
			<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional"/>
		</complexType>
	</element>
	<element name="EstimatedDemandInformation">
		<complexType>
			<sequence>
				<element name="Date" type="pd:dateType"/>
				<element name="ZoneDetail" minOccurs="1" maxOccurs="unbounded">
					<complexType>
						<sequence>
							<element name="Zone" type="pd:zoneNameType"/>
							<element name="EstimatedDemand" minOccurs="1" maxOccurs="25">
								<complexType>
									<simpleContent>
										<extension base="pd:localeDecimal">
											<attribute name="Hour" type="pd:hourIntervalType" use="required"/>
										</extension>
									</simpleContent>
								</complexType>
							</element>
						</sequence>
					</complexType>
				</element>
			</sequence>
		</complexType>
	</element>
</schema> 