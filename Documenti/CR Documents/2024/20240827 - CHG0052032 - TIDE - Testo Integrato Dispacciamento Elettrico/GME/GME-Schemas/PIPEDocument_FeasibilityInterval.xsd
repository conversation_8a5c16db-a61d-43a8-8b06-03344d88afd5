<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
	<include schemaLocation="SimpleTypesv1_0.xsd"/>
	<simpleType name="creationDateTimeType">
		<restriction base="integer">
			<minInclusive value="19000000000000"/>
			<maxInclusive value="21000000000000"/>
		</restriction>
	</simpleType>
	<!-- Body -->
	<element name="PIPEDocument">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartnerDirectory"/>
				<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
			</sequence>
			<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
			<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
			<attribute name="Version" type="string" use="required"/>
		</complexType>
	</element>
	<element name="TradingPartnerDirectory">
		<complexType>
			<sequence>
				<element ref="pd:Sender"/>
				<element ref="pd:Recipient"/>
			</sequence>
		</complexType>
	</element>
	<element name="Sender">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="Recipient">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="TradingPartner">
		<complexType>
			<sequence>
				<element name="CompanyName" type="string"/>
				<element name="CompanyIdentifier" type="string"/>
			</sequence>
			<attribute name="PartnerType" type="string"/>
		</complexType>
	</element>
	<element name="PIPTransaction">
		<complexType>
			<sequence>
				<element ref="pd:FeasibilityIntervals"/>
			</sequence>
		</complexType>
	</element>
	<element name="FeasibilityIntervals">
		<complexType>
			<sequence>
				<element name="Date" type="pd:dateType"/>
				<element name="Market" type="pd:FeasibilityMarketType"/>
				<element name="UnitFeasibilityIntervals" minOccurs="1" maxOccurs="1">
					<complexType>
						<sequence>
							<element name="UnitFeasibilityInterval" minOccurs="1" maxOccurs="1">
								<complexType>
									<sequence>
										<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
										<element name="Period" minOccurs="1" maxOccurs="100">
											<complexType>
												<attribute name="V" type="pd:periodIntervalType" use="required"/>
												<attribute name="FMin" type="pd:localeDecimal" use="required"/>
												<attribute name="FMax" type="pd:localeDecimal" use="required"/>
											</complexType>
										</element>
									</sequence>
								</complexType>
							</element>
						</sequence>
					</complexType>
				</element>
			</sequence>
		</complexType>
	</element>
</schema> 