<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
	<include schemaLocation="SimpleTypesv1_0.xsd"/>
	<simpleType name="creationDateTimeType">
		<restriction base="integer">
			<minInclusive value="19000000000000"/>
			<maxInclusive value="21000000000000"/>
		</restriction>
	</simpleType>
	<!-- Body -->
	<element name="PIPEDocument">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartnerDirectory"/>
				<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
			</sequence>
			<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
			<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
			<attribute name="Version" type="string" use="required"/>
		</complexType>
	</element>
	<element name="TradingPartnerDirectory">
		<complexType>
			<sequence>
				<element ref="pd:Sender"/>
				<element ref="pd:Recipient"/>
			</sequence>
		</complexType>
	</element>
	<element name="Sender">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="Recipient">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="TradingPartner">
		<complexType>
			<sequence>
				<element name="CompanyName" type="string"/>
				<element name="CompanyIdentifier" type="string"/>
			</sequence>
			<attribute name="PartnerType" type="string"/>
		</complexType>
	</element>
	<element name="PIPTransaction">
		<complexType>
			<sequence>
				<element ref="pd:MarketResult"/>
			</sequence>
			<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
			<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="required"/>
			<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="required"/>
		</complexType>
	</element>
	<element name="MarketResult">
		<complexType>
			<sequence>
				<element name="Date" type="pd:dateType"/>
				<element name="MarketDetail">
					<complexType>
						<sequence>
							<element name="Market" type="pd:marketType"/>
							<element name="ZoneDetail" maxOccurs="unbounded">
								<complexType>
									<sequence>
										<element name="Zone" type="string"/>
										<element name="Interval" maxOccurs="unbounded">
											<complexType>
												<sequence>
													<element name="BuyPrice" type="string"/>
													<element name="SellPrice" type="string"/>
													<element name="Generation" type="string"/>
													<element name="Consumption" type="string"/>
												</sequence>
												<attribute name="Hour" type="pd:hourIntervalType" use="optional"/>
												<attribute name="Period" type="pd:periodIntervalType" use="optional"/>
												<attribute name="TimeResolution" type="pd:timeResolutionType" use="optional"/>
											</complexType>
										</element>
									</sequence>
								</complexType>
							</element>
						</sequence>
					</complexType>
				</element>
			</sequence>
		</complexType>
	</element>
</schema>