<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- PIPE Functional Acknowledgement Schema  -->
<!-- Apache Schema -->
<!-- $Revision: 2 $ -->
<!-- GME JSP -->
<!--Copyright (C) Excelergy Corporation, 1998-2003. All rights reserved.  Excelergy is a registered trademark of Excelergy Corporation.-->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
	<include schemaLocation="SimpleTypesv1_0.xsd"/>
	<simpleType name="creationDateTimeType">
		<restriction base="integer">
			<minInclusive value="19000000000000"/>
			<maxInclusive value="21000000000000"/>
		</restriction>
	</simpleType>
	<simpleType name="statusTransactionType1">
		<restriction base="string">
			<enumeration value="Accept"/>
			<enumeration value="Reject"/>
			<enumeration value="Partial"/>
		</restriction>
	</simpleType>
	<!-- Body -->
	<element name="PIPEFunctionalAcknowledgement">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartnerDirectory"/>
				<element ref="pd:RejectInformation" minOccurs="0" maxOccurs="unbounded"/>
				<element ref="pd:TransactionAcknowledgement" minOccurs="0" maxOccurs="unbounded"/>
			</sequence>
			<attribute name="Version" type="string" use="required"/>
			<attribute name="ReferenceNumber" type="pd:lengthFortyType" use="required"/>
			<attribute name="OriginalReferenceNumber" type="pd:lengthFortyType" use="required"/>
			<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
			<attribute name="Status" type="pd:statusTransactionType1" use="required"/>
		</complexType>
	</element>
	<element name="TradingPartnerDirectory">
		<complexType>
			<sequence>
				<element ref="pd:Sender"/>
				<element ref="pd:Recipient"/>
			</sequence>
		</complexType>
	</element>
	<element name="Sender">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="Recipient">
		<complexType>
			<sequence>
				<element ref="pd:TradingPartner"/>
			</sequence>
		</complexType>
	</element>
	<element name="TradingPartner">
		<complexType>
			<sequence>
				<element name="CompanyName" type="pd:lengthSixtyType"/>
				<element name="CompanyIdentifier" type="pd:lengthEightyType"/>
				<element name="ApplicationName" type="pd:lengthSixtyType" minOccurs="0"/>
			</sequence>
			<attribute name="PartnerType" type="pd:partnerTypeType" use="required"/>
		</complexType>
	</element>
	<element name="RejectInformation">
		<complexType>
			<sequence>
				<element name="Reason" type="string"/>
				<element name="ReasonText" type="pd:length250Type" minOccurs="0"/>
			</sequence>
		</complexType>
	</element>
	<element name="TransactionAcknowledgement">
		<complexType>
			<sequence>
				<element ref="pd:RejectInformation" minOccurs="0" maxOccurs="unbounded"/>
			</sequence>
			<attribute name="OriginalReferenceNumber" type="pd:lengthThirtyFiveType" use="required"/>
			<attribute name="Status" type="pd:statusTransactionType" use="required"/>
			<attribute name="PIPTransactionType" type="pd:pipTransctionType" use="optional"/>
			<attribute name="MarketParticipantNumber" type="pd:lengthThirtyFiveType" use="optional"/>
		</complexType>
	</element>
</schema> 
