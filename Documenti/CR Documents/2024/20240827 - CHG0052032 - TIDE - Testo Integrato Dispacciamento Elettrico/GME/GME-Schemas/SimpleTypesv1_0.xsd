<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- Simple Types Schema for Xerces C++-->
<!-- PIPE version="1.0" -->
<!-- $Revision: 57 $ -->
<!-- Shared -->
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
	<simpleType name="creationDateType">
		<restriction base="integer">
			<minInclusive value="19000000"/>
			<maxInclusive value="21000000"/>
		</restriction>
	</simpleType>
	<simpleType name="creationTimeType">
		<restriction base="integer">
			<minInclusive value="0"/>
			<maxInclusive value="23595999"/>
		</restriction>
	</simpleType>
	<simpleType name="creationTimeMillisecsType">
		<restriction base="integer">
			<minInclusive value="0"/>
			<maxInclusive value="235959999"/>
		</restriction>
	</simpleType>
	<simpleType name="currencyEuroType">
		<restriction base="string">
			<enumeration value="Euro"/>
		</restriction>
	</simpleType>
	<simpleType name="dateType">
		<restriction base="integer">
			<pattern value="\d{8}"/>
		</restriction>
	</simpleType>
	<simpleType name="validDateType">
		<restriction base="integer">
			<minInclusive value="20000101"/>
			<maxInclusive value="24991231"/>
		</restriction>
	</simpleType>
	<simpleType name="dealMakerType">
		<restriction base="string">
			<enumeration value="GME"/>
		</restriction>
	</simpleType>
	<simpleType name="hourIntervalType">
		<restriction base="integer">
			<minInclusive value="1"/>
			<maxInclusive value="25"/>
		</restriction>
	</simpleType>
	<simpleType name="quarterPeriodIntervalType">
		<restriction base="integer">
			<minInclusive value="1"/>
			<maxInclusive value="100"/>
		</restriction>
	</simpleType>
	<simpleType name="quarterIntervalType">
		<restriction base="integer">
			<minInclusive value="1"/>
			<maxInclusive value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="minuteType">
		<restriction base="integer">
			<minInclusive value="0"/>
			<maxInclusive value="14"/>
		</restriction>
	</simpleType>
	<simpleType name="integerFiveType">
		<restriction base="integer">
			<minInclusive value="1"/>
			<maxInclusive value="99999"/>
		</restriction>
	</simpleType>
	<simpleType name="integerFifteenType">
		<restriction base="integer">
			<minInclusive value="0"/>
			<maxInclusive value="999999999999999"/>
		</restriction>
	</simpleType>
	<simpleType name="integerTimeType">
		<restriction base="integer">
			<minInclusive value="0000"/>
			<maxInclusive value="2400"/>
		</restriction>
	</simpleType>
	<simpleType name="integerTimeSecondType">
		<restriction base="integer">
			<minInclusive value="000000"/>
			<maxInclusive value="240000"/>
		</restriction>
	</simpleType>
	<simpleType name="interval60Type">
		<restriction base="string">
			<enumeration value="60"/>
		</restriction>
	</simpleType>
	<simpleType name="intervalType">
		<restriction base="string">
			<enumeration value="15"/>
			<enumeration value="30"/>
			<enumeration value="60"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthTwoType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="2"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthThreeType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="3"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthFiveType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="5"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthTenType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="10"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthFifteenType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="15"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthTwentyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="20"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthTwentyFourType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="24"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthTwentyFiveType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="25"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthThirtyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="30"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthThirtyFiveType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="35"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthFortyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="40"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthFiftyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="50"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthSixtyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="60"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthSixtyFourType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="64"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthEightyType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="80"/>
		</restriction>
	</simpleType>
	<simpleType name="length132Type">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="132"/>
		</restriction>
	</simpleType>
	<simpleType name="length250Type">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="250"/>
		</restriction>
	</simpleType>
	<simpleType name="lengthFiveHundredType">
		<restriction base="string">
			<minLength value="1"/>
			<maxLength value="500"/>
		</restriction>
	</simpleType>
	<simpleType name="localeDecimal">
		<restriction base="string">
			<minLength value="1"/>
		</restriction>
	</simpleType>
	<simpleType name="timeResolutionType">
		<restriction base="string">
			<enumeration value="PT15"/>
			<enumeration value="PT60"/>
		</restriction>
	</simpleType>
	<simpleType name="fifteenMinuteScheduleMarketType">
		<restriction base="string">
			<enumeration value="MSD1"/>
			<enumeration value="MSD2"/>
			<enumeration value="MSD3"/>
			<enumeration value="MSD4"/>
			<enumeration value="MSD5"/>
			<enumeration value="MSD6"/>
		</restriction>
	</simpleType>
	<simpleType name="marketType">
		<restriction base="string">
			<enumeration value="MGP"/>
			<enumeration value="MI1"/>
			<enumeration value="MI2"/>
			<enumeration value="MI3"/>
			<enumeration value="MB"/>
			<enumeration value="MSD1"/>
			<enumeration value="MSD2"/>
			<enumeration value="MSD3"/>
			<enumeration value="MSD4"/>
			<enumeration value="MSD5"/>
			<enumeration value="MSD6"/>
			<enumeration value="MBh"/>
			<enumeration value="MRR"/>
			<enumeration value="AFRR"/>
			<enumeration value="XB00"/>
			<enumeration value="XB01"/>
			<enumeration value="XB02"/>
			<enumeration value="XB03"/>
			<enumeration value="XB04"/>
			<enumeration value="XB05"/>
			<enumeration value="XB06"/>
			<enumeration value="XB07"/>
			<enumeration value="XB08"/>
			<enumeration value="XB09"/>
			<enumeration value="XB10"/>
			<enumeration value="XB11"/>
			<enumeration value="XB12"/>
			<enumeration value="XB13"/>
			<enumeration value="XB14"/>
			<enumeration value="XB15"/>
			<enumeration value="XB16"/>
			<enumeration value="XB17"/>
			<enumeration value="XB18"/>
			<enumeration value="XB19"/>
			<enumeration value="XB20"/>
			<enumeration value="XB21"/>
			<enumeration value="XB22"/>
			<enumeration value="XB23"/>
			<enumeration value="XB24"/>
			<enumeration value="XB25"/>
			<enumeration value="XB26"/>
			<enumeration value="XB27"/>
			<enumeration value="XB28"/>
			<enumeration value="XB29"/>
			<enumeration value="XB30"/>
			<enumeration value="XB31"/>
			<enumeration value="XB32"/>
			<enumeration value="XB33"/>
			<enumeration value="XB34"/>
			<enumeration value="XB35"/>
			<enumeration value="XB36"/>
			<enumeration value="XB37"/>
			<enumeration value="XB38"/>
			<enumeration value="XB39"/>
			<enumeration value="XB40"/>
			<enumeration value="XB41"/>
			<enumeration value="XB42"/>
			<enumeration value="XB43"/>
			<enumeration value="XB44"/>
			<enumeration value="XB45"/>
			<enumeration value="XB46"/>
			<enumeration value="XB47"/>
			<enumeration value="XB48"/>
			<enumeration value="XB49"/>
			<enumeration value="XB50"/>
			<enumeration value="XB51"/>
			<enumeration value="XB52"/>
			<enumeration value="XB53"/>
			<enumeration value="XB54"/>
			<enumeration value="XB55"/>
			<enumeration value="XB56"/>
			<enumeration value="XB57"/>
			<enumeration value="XB58"/>
			<enumeration value="XB59"/>
			<enumeration value="XB60"/>
			<enumeration value="XB61"/>
			<enumeration value="XB62"/>
			<enumeration value="XB63"/>
			<enumeration value="XB64"/>
			<enumeration value="XB65"/>
			<enumeration value="XB66"/>
			<enumeration value="XB67"/>
			<enumeration value="XB68"/>
			<enumeration value="XB69"/>
			<enumeration value="XB70"/>
			<enumeration value="XB71"/>
			<enumeration value="XB72"/>
			<enumeration value="XB73"/>
			<enumeration value="XB74"/>
			<enumeration value="XB75"/>
			<enumeration value="XB76"/>
			<enumeration value="XB77"/>
			<enumeration value="XB78"/>
			<enumeration value="XB79"/>
			<enumeration value="XB80"/>
			<enumeration value="XB81"/>
			<enumeration value="XB82"/>
			<enumeration value="XB83"/>
			<enumeration value="XB84"/>
			<enumeration value="XB85"/>
			<enumeration value="XB86"/>
			<enumeration value="XB87"/>
			<enumeration value="XB88"/>
			<enumeration value="XB89"/>
			<enumeration value="XB90"/>
			<enumeration value="XB91"/>
			<enumeration value="XB92"/>
			<enumeration value="XB93"/>
			<enumeration value="XB94"/>
			<enumeration value="XB95"/>
			<enumeration value="XB96"/>
			<enumeration value="XB97"/>
			<enumeration value="XB98"/>
			<enumeration value="XB99"/>
			<enumeration value="XB100"/>
		</restriction>
	</simpleType>
	<simpleType name="marketBidType">
		<restriction base="string">
			<enumeration value="MGP"/>
			<enumeration value="MI1"/>
			<enumeration value="MI2"/>
			<enumeration value="MI3"/>
			<enumeration value="MSD1"/>
			<enumeration value="MBh"/>
			<enumeration value="MRR"/>
			<enumeration value="AFRR"/>
		</restriction>
	</simpleType>
	<simpleType name="FeasibilityMarketType">
		<restriction base="string">
			<enumeration value="MSD1"/>
			<enumeration value="MSD2"/>
			<enumeration value="MSD3"/>
			<enumeration value="MSD4"/>
			<enumeration value="MSD5"/>
			<enumeration value="MSD6"/>
		</restriction>
	</simpleType>
	<simpleType name="marketMGPType">
		<restriction base="string">
			<enumeration value="MGP"/>
		</restriction>
	</simpleType>
	<simpleType name="conventionalPriceMarketType">
		<restriction base="string">
			<enumeration value="MGP"/>
		</restriction>
	</simpleType>
	<simpleType name="noType">
		<restriction base="string">
			<enumeration value="No"/>
		</restriction>
	</simpleType>
	<simpleType name="onOffType">
		<restriction base="string">
			<enumeration value="On"/>
			<enumeration value="Off"/>
		</restriction>
	</simpleType>
	<simpleType name="partnerDistributorType">
		<restriction base="string">
			<enumeration value="Distributor"/>
		</restriction>
	</simpleType>
	<simpleType name="partnerTypeType">
		<restriction base="string">
			<enumeration value="EnergySupplier"/>
			<enumeration value="Distributor"/>
			<enumeration value="Operator"/>
			<enumeration value="Market Participant"/>
			<enumeration value="Tax Exempt Market Participant"/>
		</restriction>
	</simpleType>
	<simpleType name="pipTransactionType">
		<restriction base="string">
			<enumeration value="ApplicationAdvice"/>
			<enumeration value="BidSubmittal"/>
			<enumeration value="BidAwardResponse"/>
			<enumeration value="BidAwardRequest"/>
			<enumeration value="BidNotification"/>
			<enumeration value="ConventionalUtilizationFactor"/>
			<enumeration value="EnergyUtilizationCoefficient"/>
			<enumeration value="EstimatedDemandInformation"/>
			<enumeration value="EstimatedPriceInformation"/>
			<enumeration value="RelevantExchangePoint"/>
			<enumeration value="UnitInformation"/>
			<enumeration value="UnitMargins"/>
			<enumeration value="UnitSchedule"/>
			<enumeration value="FifteenMinuteSchedule"/>
			<enumeration value="ZoneInformation"/>
			<enumeration value="GeneralizedConstraintsDetail"/>
			<enumeration value="FeasibilityIntervals"/>
		</restriction>
	</simpleType>
	<simpleType name="purposeSellType">
		<restriction base="string">
			<enumeration value="Sell"/>
		</restriction>
	</simpleType>
	<simpleType name="purposeTradeType">
		<restriction base="string">
			<enumeration value="Buy"/>
			<enumeration value="Sell"/>
		</restriction>
	</simpleType>
	<simpleType name="statusAcceptType">
		<restriction base="string">
			<enumeration value="Accept"/>
		</restriction>
	</simpleType>
	<simpleType name="statusTransactionType">
		<restriction base="string">
			<enumeration value="Accept"/>
			<enumeration value="Reject"/>
		</restriction>
	</simpleType>
	<simpleType name="statusRejectType">
		<restriction base="string">
			<enumeration value="Reject"/>
		</restriction>
	</simpleType>
	<simpleType name="termHourType">
		<restriction base="string">
			<enumeration value="Hour"/>
		</restriction>
	</simpleType>
	<simpleType name="termRestrictedType">
		<restriction base="string">
			<enumeration value="Hour"/>
			<enumeration value="Day"/>
		</restriction>
	</simpleType>
	<simpleType name="timeType">
		<restriction base="integer">
			<pattern value="\d{4}"/>
		</restriction>
	</simpleType>
	<simpleType name="trueFalseType">
		<restriction base="string">
			<enumeration value="True"/>
			<enumeration value="False"/>
		</restriction>
	</simpleType>
	<simpleType name="unitScheduleType">
		<restriction base="string">
			<enumeration value="PreliminaryProvisional"/>
			<enumeration value="Preliminary"/>
			<enumeration value="FirstFinal"/>
			<enumeration value="FirstUpdated"/>
			<enumeration value="FinalXbid"/>
		</restriction>
	</simpleType>
	<simpleType name="generalizedContraintsDetailName">
		<restriction base="string">
			<enumeration value="GC_COUP"/>
		</restriction>
	</simpleType>
	<simpleType name="fifteenMinutesScheduleType">
		<restriction base="string">
			<enumeration value="Original"/>
			<enumeration value="Corrected"/>
		</restriction>
	</simpleType>
	<simpleType name="bidTaxCodeType">
		<restriction base="string">
			<enumeration value="V1"/>
			<enumeration value="V2"/>
			<enumeration value="V3"/>
			<enumeration value="V4"/>
			<enumeration value="V5"/>
			<enumeration value="V8"/>
			<enumeration value="VC"/>
			<enumeration value="VS"/>
			<enumeration value="VR"/>
			<enumeration value="NC"/>
		</restriction>
	</simpleType>
	<simpleType name="offerTaxCodeType">
		<restriction base="string">
			<enumeration value="A1"/>
			<enumeration value="A2"/>
			<enumeration value="A3"/>
			<enumeration value="A4"/>
			<enumeration value="A5"/>
			<enumeration value="A6"/>
			<enumeration value="A7"/>
			<enumeration value="A8"/>
			<enumeration value="AL"/>
			<enumeration value="AN"/>
			<enumeration value="AS"/>
			<enumeration value="AR"/>
			<enumeration value="NC"/>
		</restriction>
	</simpleType>
	<simpleType name="feeTaxCodeType">
		<restriction base="string">
			<enumeration value="V1"/>
			<enumeration value="V3"/>
		</restriction>
	</simpleType>
	<simpleType name="unitOfMeasureBidType">
		<restriction base="string">
			<enumeration value="MW"/>
			<enumeration value="MWh"/>
		</restriction>
	</simpleType>
	<simpleType name="unitOfMeasureMWhType">
		<restriction base="string">
			<enumeration value="MW"/>
			<enumeration value="MWh"/>
		</restriction>
	</simpleType>
	<simpleType name="yesNoType">
		<restriction base="string">
			<enumeration value="Yes"/>
			<enumeration value="No"/>
		</restriction>
	</simpleType>
	<simpleType name="UnitYESNOType">
		<restriction base="string">
			<enumeration value="YES"/>
			<enumeration value="NO"/>
		</restriction>
	</simpleType>
	<simpleType name="zoneNameType">
		<restriction base="string">
			<enumeration value="CNOR"/>
			<enumeration value="CSUD"/>
			<enumeration value="NORD"/>
			<enumeration value="SARD"/>
			<enumeration value="SICI"/>
			<enumeration value="CALA"/>
			<enumeration value="COAC"/>
			<enumeration value="COUP"/>
			<enumeration value="GREC"/>
			<enumeration value="AUST"/>
			<enumeration value="SVIZ"/>
			<enumeration value="CORS"/>
			<enumeration value="SLOV"/>
			<enumeration value="FRAN"/>
			<enumeration value="SUD"/>
			<enumeration value="BSP"/>
			<enumeration value="MALT"/>
			<enumeration value="MONT"/>
			<enumeration value="XAUS"/>
			<enumeration value="XFRA"/>
			<enumeration value="XGRE"/>
			<enumeration value="XSVI"/>
		</restriction>
	</simpleType>
	<simpleType name="mustRunType">
		<restriction base="string">
			<enumeration value="MustRun"/>
			<enumeration value="InternationalExchanges"/>
			<enumeration value="BilateralContracts"/>
			<enumeration value="AllMustRunTypes"/>
			<enumeration value="AllTypesExceptBilateralContracts"/>
			<enumeration value="EssentialforSystemSecurity"/>
			<enumeration value="CIP6"/>
			<enumeration value="NotprogrammablerenewableSources"/>
			<enumeration value="OtherrenewableSources"/>
			<enumeration value="Cogeneration"/>
			<enumeration value="NationalSources"/>
		</restriction>
	</simpleType>
	<simpleType name="scopeType">
		<restriction base="string">
			<enumeration value="RS"/>
			<enumeration value="AS"/>
			<enumeration value="GR1"/>
			<enumeration value="GR2"/>
			<enumeration value="GR3"/>
			<enumeration value="GR4"/>
			<enumeration value="AC"/>
			<enumeration value="CA"/>
		</restriction>
	</simpleType>
	<simpleType name="quarterType">
		<restriction base="string">
			<enumeration value="1"/>
			<enumeration value="2"/>
			<enumeration value="3"/>
			<enumeration value="4"/>
		</restriction>
	</simpleType>
	<simpleType name="bAType">
		<restriction base="string">
			<enumeration value="Rev"/>
			<enumeration value="Norev"/>
			<enumeration value="Netting"/>
		</restriction>
	</simpleType>
	<simpleType name="scopeTypeRR">
		<restriction base="string">
			<enumeration value="GR1"/>
			<enumeration value="GR2"/>
			<enumeration value="GR3"/>
		</restriction>
	</simpleType>
</schema>