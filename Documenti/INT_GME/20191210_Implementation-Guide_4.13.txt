PIPEDocument.xsd (for UnitMargins)
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:UnitMargins"/>
</sequence>
</complexType>
</element>
<element name="UnitMargins">
<complexType>
<sequence>
<element name="Date" type="pd:dateType"/>
<element name="Market" type="pd:marketType"/>
<element name="UnitDetail" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="Hour" maxOccurs="25">
<complexType>
<simpleContent>
<extension base="pd:hourIntervalType">
<attribute name="MarginUp" type="pd:localeDecimal" use="required"/>
<attribute name="MarginDown" type="pd:localeDecimal" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
</sequence>
</complexType>
</element>
</sequence>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyType" use="optional"/>
</complexType>
</element>

</schema>

BidSubmittal MGP (single)
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='MGPoPRIMOP20020516165855' CreationDate='20020516165855' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>Primo operatore</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='Yes'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>1</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,534</BidQuantity>
<EnergyPrice>53,4</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
•
• [etc.]
•

<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='Yes'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,44838</BidQuantity>
<EnergyPrice>44,84</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
</PIPEDocument>

BidSubmittal MGP (multiple)
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='MGPoPRIMOP20020516165856' CreationDate='20020516165855' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>Primo operatore</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='Yes'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>1</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,64856</BidQuantity>
<EnergyPrice>64,86</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='No'>
<Market>MGP</Market>
<Date>20020320</Date>

<Hour>1</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,769</BidQuantity>
<EnergyPrice>76,9</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='No'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>1</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,89016</BidQuantity>
<EnergyPrice>89,02</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
•
• [etc.]
•
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='Yes'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>2,55726</BidQuantity>
<EnergyPrice>55,73</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='No'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>

<BidQuantity UnitOfMeasure='MWh'>2,67612</BidQuantity>
<EnergyPrice>67,61</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
<PIPTransaction>
<BidSubmittal Purpose='Buy' PredefinedOffer='No' ReplacementIndicator='No'>
<Market>MGP</Market>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>1,26358</BidQuantity>
<EnergyPrice>73,64</EnergyPrice>
</BidSubmittal>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidSubmittal MGP (single-multiple)
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidSubmittal"/>
</sequence>
</complexType>
</element>
<element name="BidSubmittal">
<complexType>
<sequence>
<element name="Market" type="pd:marketMGPType"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="BidQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureMWhType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="EnergyPrice" type="pd:localeDecimal"/>
</sequence>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="Purpose" type="pd:purposeTradeType" use="required"/>
<attribute name="PredefinedOffer" type="pd:yesNoType" use="required"/>
<attribute name="ReplacementIndicator" type="pd:yesNoType" use="required"/>
</complexType>
</element>
</schema>

BidSubmittal MI1/2/3/4/5/6/7
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='MA1oPRIMOP20020516165856' CreationDate='20020516165855' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>Primo operatore</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidSubmittal Purpose='Buy' ReplacementIndicator='Yes'>
<Market>MIn</Market>
<Date>20020916</Date>
<Hour>1</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure='MWh'>1000</BidQuantity>
<EnergyPrice>30</EnergyPrice>
<MaximumDailyEnergy>0</MaximumDailyEnergy>
</BidSubmittal>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidSubmittal MI1/2/3/4/5/6/7
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidSubmittal"/>
</sequence>
</complexType>
</element>
<element name="BidSubmittal">
<complexType>
<sequence>
<element name="Market" type="pd:marketBidType"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="BidQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureMWhType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="EnergyPrice" type="pd:localeDecimal"/>
<element name="MaximumDailyEnergy" type="pd:localeDecimal" minOccurs="0"/>
</sequence>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="BalancedReferenceNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="Purpose" type="pd:purposeTradeType" use="required"/>
<attribute name="ReplacementIndicator" type="pd:yesNoType" use="required"/>
</complexType>
</element>
</schema>

BidSubmittal MSD
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='MSDOEccSNaggytaaL20135032' CreationDate='20091129135032' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>IDAU</CompanyName>
<CompanyIdentifier>IDAU</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidSubmittal PredefinedOffer='No' RifStand='MI1'>
<Market>MSD</Market>
<Date>20100101</Date>
<Hour>1</Hour>
<UnitReferenceNumber>UP_XXXXXX_1</UnitReferenceNumber>
<Offer PresentedOffer='No' Purpose='Sell' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>1,15</BidQuantity>
<EnergyPrice>23</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Buy' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>2</BidQuantity>
<EnergyPrice>24</EnergyPrice>

<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>3</BidQuantity>
<EnergyPrice>25,6</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Buy' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>4</BidQuantity>
<EnergyPrice>26</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>5</BidQuantity>
<EnergyPrice>27</EnergyPrice>
<SourceOffer>CONTR</SourceOffer>
<ContractId>1</ContractId>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>6</BidQuantity>
<EnergyPrice>28</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>7</BidQuantity>
<EnergyPrice>29</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>8</BidQuantity>
<EnergyPrice>30</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Sell' Scope='GR3'>
<BidQuantity UnitOfMeasure='MWh'>9</BidQuantity>
<EnergyPrice>31</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR3'>
<BidQuantity UnitOfMeasure='MWh'>10</BidQuantity>

<EnergyPrice>32</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AC'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>3200</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='CA'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>3600</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
</BidSubmittal>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidSubmittal MSD
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<simpleType name="sourceOfferType">
<restriction base="string">
<enumeration value="UESS"/>
<enumeration value="SPOT"/>
<enumeration value="CONTR"/>
</restriction>
</simpleType>
<simpleType name="MI1MI2Type">
<restriction base="string">
<enumeration value="MI1"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>

</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidSubmittal"/>
</sequence>
</complexType>
</element>
<element name="BidSubmittal">
<complexType>
<sequence>
<element name="Market" type="pd:fifteenMinuteScheduleMarketType"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="Offer" minOccurs="12" maxOccurs="12">
<complexType>
<sequence>
<element name="BidQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">

<attribute name="UnitOfMeasure" type="pd:unitOfMeasureMWhType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="EnergyPrice" type="pd:localeDecimal"/>
<element name="SourceOffer" type="pd:sourceOfferType"/>
<element name="ContractId" type="pd:localeDecimal" minOccurs="0"/>
</sequence>
<attribute name="PresentedOffer" type="pd:yesNoType" use="required"/>
<attribute name="Purpose" type="pd:purposeTradeType" use="required"/>
<attribute name="Scope" type="pd:scopeType" use="required"/>
</complexType>
</element>
</sequence>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="PredefinedOffer" type="pd:yesNoType" use="required"/>
<attribute name="RifStand" type="pd:MI1MI2Type" use="optional"/>
</complexType>
</element>
</schema>

BidSubmittal MB
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='MSDOEEPSNL20091219134851' CreationDate='20091219134851' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>OEYYYYY</CompanyName>
<CompanyIdentifier>OEYYYYY</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidSubmittal PredefinedOffer='No'>
<Market>MB2</Market>
<Date>20091219</Date>
<Hour>8</Hour>
<UnitReferenceNumber>UP_XXXXX_1</UnitReferenceNumber>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>23</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>2</BidQuantity>
<EnergyPrice>24</EnergyPrice>

<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>3</BidQuantity>
<EnergyPrice>25</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>4</BidQuantity>
<EnergyPrice>26</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>5</BidQuantity>
<EnergyPrice>27</EnergyPrice>
<SourceOffer>CONTR</SourceOffer>
<ContractId>1</ContractId>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>6</BidQuantity>
<EnergyPrice>28</EnergyPrice>
<SourceOffer>CONTR</SourceOffer>
<ContractId>2</ContractId>
</Offer>
<Offer PresentedOffer='No' Purpose='Sell' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>7</BidQuantity>
<EnergyPrice>29</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Buy' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>8</BidQuantity>
<EnergyPrice>30</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Sell' Scope='GR3'>
<BidQuantity UnitOfMeasure='MWh'>9</BidQuantity>
<EnergyPrice>31</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Buy' Scope='GR3'>

<BidQuantity UnitOfMeasure='MWh'>10</BidQuantity>
<EnergyPrice>32</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR4'>
<BidQuantity UnitOfMeasure='MWh'>9</BidQuantity>
<EnergyPrice>31</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR4'>
<BidQuantity UnitOfMeasure='MWh'>10</BidQuantity>
<EnergyPrice>32</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AC'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>3200</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='CA'>
<BidQuantity UnitOfMeasure='MWh'>0</BidQuantity>
<EnergyPrice>3600</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
</BidSubmittal>
</PIPTransaction>
<PIPTransaction>
<BidSubmittal PredefinedOffer='No'>
<Market>MB4</Market>
<Date>20091219</Date>
<Hour>22</Hour>
<UnitReferenceNumber>UP_XXXXX_1</UnitReferenceNumber>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>23</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='RS'>
<BidQuantity UnitOfMeasure='MWh'>2</BidQuantity>

<EnergyPrice>24</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>3</BidQuantity>
<EnergyPrice>25</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='AS'>
<BidQuantity UnitOfMeasure='MWh'>4</BidQuantity>
<EnergyPrice>26</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>5</BidQuantity>
<EnergyPrice>27</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR1'>
<BidQuantity UnitOfMeasure='MWh'>6</BidQuantity>
<EnergyPrice>28</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>7</BidQuantity>
<EnergyPrice>29</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR2'>
<BidQuantity UnitOfMeasure='MWh'>8</BidQuantity>
<EnergyPrice>30</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='GR3'>
<BidQuantity UnitOfMeasure='MWh'>9</BidQuantity>
<EnergyPrice>31</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Buy' Scope='GR3'>
<BidQuantity UnitOfMeasure='MWh'>10</BidQuantity>

<EnergyPrice>32</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Sell' Scope='GR4'>
<BidQuantity UnitOfMeasure='MWh'>9</BidQuantity>
<EnergyPrice>31</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='No' Purpose='Buy' Scope='GR4'>
<BidQuantity UnitOfMeasure='MWh'>10</BidQuantity>
<EnergyPrice>32</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='AC'>
<BidQuantity UnitOfMeasure='MWh'>1</BidQuantity>
<EnergyPrice>3200</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
<Offer PresentedOffer='Yes' Purpose='Sell' Scope='CA'>
<BidQuantity UnitOfMeasure='MWh'>0</BidQuantity>
<EnergyPrice>3600</EnergyPrice>
<SourceOffer>SPOT</SourceOffer>
</Offer>
</BidSubmittal>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidSubmittal MB
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<simpleType name="sourceOfferType">
<restriction base="string">
<enumeration value="UESS"/>
<enumeration value="SPOT"/>
<enumeration value="CONTR"/>
</restriction>
</simpleType>
<simpleType name="scopeType">
<restriction base="string">
<enumeration value="RS"/>
<enumeration value="AS"/>
<enumeration value="AC"/>
<enumeration value="CA"/>
<enumeration value="GR1"/>
<enumeration value="GR2"/>
<enumeration value="GR3"/>
<enumeration value="GR4"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>

</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidSubmittal"/>
</sequence>
</complexType>
</element>
<element name="BidSubmittal">
<complexType>
<sequence>
<element name="Market" type="pd:marketBidType"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="Offer" minOccurs="14" maxOccurs="14">

<complexType>
<sequence>
<element name="BidQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureMWhType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="EnergyPrice" type="pd:localeDecimal"/>
<element name="SourceOffer" type="pd:sourceOfferType"/>
<element name="ContractId" type="pd:localeDecimal" minOccurs="0"/>
</sequence>
<attribute name="PresentedOffer" type="pd:yesNoType" use="required"/>
<attribute name="Purpose" type="pd:purposeTradeType" use="required"/>
<attribute name="Scope" type="pd:scopeType" use="required"/>
</complexType>
</element>
</sequence>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="PredefinedOffer" type="pd:yesNoType" use="required"/>
</complexType>
</element>
</schema>

BidRevocation
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='BidRevoke012ACUN2002050918' CreationDate='20020509183738' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType='Distributor'>
<CompanyName>Primo operatore</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType='Distributor'>
<CompanyName>GME</CompanyName>
<CompanyIdentifier>7456474854</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction>
<BidRevocation PredefinedOffer = "No">
<Market>MGP</Market>
<Date>20020721</Date>
<Hour>1</Hour>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<MarketParticipantReferenceNumber>AX0001</MarketParticipantReferenceNumber>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
</BidRevocation>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidRevocation
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">

<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidRevocation"/>
</sequence>
</complexType>
</element>
<element name="BidRevocation">
<complexType>
<sequence>
<element name="Market" type="pd:marketType"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="MarketParticipantNumber" type="pd:lengthThirtyType"/>
<element name="MarketParticipantReferenceNumber" type="pd:lengthThirtyType" minOccurs="0" maxOccurs="1"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
</sequence>
<attribute name="PredefinedOffer" type="pd:yesNoType" use="required"/>
</complexType>
</element>
</schema>

FifteenMinuteSchedule MSD1/2/3/4
<?xml version='1.0' encoding='ISO-8859-1'?>
<PIPEDocument xmlns='urn:XML-PIPE'
xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
xsi:schemaLocation='urn:XML-PIPE PIPEDocument.xsd'
ReferenceNumber='FiftMin20031209175846' CreationDate='20031209175846' Version='1.0'>
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>PRIMO OPERATORE</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="92720546478906" InboundMessageCreationDate ="20090929" InboundMessageCreationTime
="*********">
<FifteenMinuteSchedule Type='Original'>
<Market>MSD1</Market>
<Date>20031210</Date>
<UnitReferenceNumber>IdAuBaronia</UnitReferenceNumber>
<HourDetail>
<Hour>1</Hour>
<Quantity QuarterInterval='1'>511,75</Quantity>
<Quantity QuarterInterval='2'>511,75</Quantity>
<Quantity QuarterInterval='3'>511,75</Quantity>

<Quantity QuarterInterval='4'>511,75</Quantity>
</HourDetail>
<HourDetail>
<Hour>2</Hour>
<Quantity QuarterInterval='1'>511,75</Quantity>
<Quantity QuarterInterval='2'>511,75</Quantity>
<Quantity QuarterInterval='3'>511,75</Quantity>
<Quantity QuarterInterval='4'>511,75</Quantity>
</HourDetail>
<HourDetail>
<Hour>3</Hour>
<Quantity QuarterInterval='1'>511,75</Quantity>
<Quantity QuarterInterval='2'>511,75</Quantity>
<Quantity QuarterInterval='3'>511,75</Quantity>
<Quantity QuarterInterval='4'>511,75</Quantity>
</HourDetail>
•
• [etc.]
•
<HourDetail>
<Hour>23</Hour>
<Quantity QuarterInterval='1'>-5</Quantity>
<Quantity QuarterInterval='2'>-5</Quantity>
<Quantity QuarterInterval='3'>-5</Quantity>
<Quantity QuarterInterval='4'>-5</Quantity>
</HourDetail>
<HourDetail>
<Hour>24</Hour>
<Quantity QuarterInterval='1'>-9,687</Quantity>
<Quantity QuarterInterval='2'>-10</Quantity>
<Quantity QuarterInterval='3'>-9,687</Quantity>
<Quantity QuarterInterval='4'>-9,687</Quantity>
</HourDetail>
</FifteenMinuteSchedule>
</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for FifteenMinuteSchedule MSD1/2/3/4
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">

<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:FifteenMinuteSchedule"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name="FifteenMinuteSchedule">
<complexType>
<sequence>
<element name="Market" type="pd:fifteenMinuteScheduleMarketType"/>
<element name="Date" type="pd:dateType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="HourDetail" minOccurs="23" maxOccurs="25">
<complexType>
<sequence>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="Quantity" minOccurs="4" maxOccurs="4">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="QuarterInterval" type="pd:quarterIntervalType" use="required"/>
<attribute name="Minute" type="pd:minuteType" use="optional"/>
</extension>
</simpleContent>
</complexType>
</element>
</sequence>
</complexType>

</element>
</sequence>
<attribute name="MarketParticipantNumber" type="string" use="optional"/>
<attribute name="Type" type="pd:fifteenMinutesScheduleType" use="required"/>
</complexType>
</element>
</schema>

BidNotification for MGP/MI
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20020516175054" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>PRIMO OPERATORE</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction Status ="Accept" ReferenceNumber ="21360001047256" InboundMessageCreationDate ="20020516"
InboundMessageCreationTime ="*********">
<BidNotification Purpose ="Buy" PartialAcceptedQuantityIndicator ="No">
<Market>MGP</Market>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<GMEReferenceNumber>21360546158131</GMEReferenceNumber>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<AwardedQuantity UnitOfMeasure ="MWh">1,273</AwardedQuantity>
<AwardedPrice>11,88</AwardedPrice>
<AwardedValue>15,12</AwardedValue>
</BidNotification>
</PIPTransaction>

<PIPTransaction Status ="Accept" ReferenceNumber ="21360001047257" InboundMessageCreationDate ="20020516"
InboundMessageCreationTime ="*********">
<BidNotification Purpose ="Buy" PartialAcceptedQuantityIndicator ="No">
<Market>MGP</Market>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<GMEReferenceNumber>21360546158130</GMEReferenceNumber>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<AwardedQuantity UnitOfMeasure ="MWh">2,695</AwardedQuantity>
<AwardedPrice>11,88</AwardedPrice>
<AwardedValue>32,02</AwardedValue>
</BidNotification>
</PIPTransaction>
•
• [etc.]
•
<PIPTransaction Status ="Accept" ReferenceNumber ="21360001047277" InboundMessageCreationDate ="20020516"
InboundMessageCreationTime ="*********">
<BidNotification Purpose ="Sell" PartialAcceptedQuantityIndicator ="No">
<Market>MGP</Market>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<GMEReferenceNumber>21360546158155</GMEReferenceNumber>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnP2</UnitReferenceNumber>
<AwardedQuantity UnitOfMeasure ="MWh">69,417</AwardedQuantity>
<AwardedPrice>11,88</AwardedPrice>
<AwardedValue>-824,67</AwardedValue>
</BidNotification>
</PIPTransaction>
<PIPTransaction Status ="Reject" ReferenceNumber ="21360001047283" InboundMessageCreationDate ="20020516"
InboundMessageCreationTime ="*********">
<BidNotification Purpose ="Sell" PredefinedOffer ="No">
<RejectInformation>
<Reason>Unaccepted</Reason>
<ReasonText>Not accepted by market algorithm</ReasonText>

</RejectInformation>
<Market>MGP</Market>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<GMEReferenceNumber>21360546158202</GMEReferenceNumber>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnP2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure ="MWh">62,946</BidQuantity>
<EnergyPrice>12,60</EnergyPrice>
</BidNotification>
</PIPTransaction>
<PIPTransaction Status ="Reject" ReferenceNumber ="21360001047287" InboundMessageCreationDate ="20020516"
InboundMessageCreationTime ="*********">
<BidNotification Purpose ="Sell" PredefinedOffer ="No">
<RejectInformation>
<Reason>Unaccepted</Reason>
<ReasonText>Not accepted by market algorithm</ReasonText>
</RejectInformation>
<Market>MGP</Market>
<MarketParticipantNumber>PRIMOP</MarketParticipantNumber>
<GMEReferenceNumber>21360546158203</GMEReferenceNumber>
<Date>20020320</Date>
<Hour>24</Hour>
<UnitReferenceNumber>UnP2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure ="MWh">55,144</BidQuantity>
<EnergyPrice>15,25</EnergyPrice>
</BidNotification>
</PIPTransaction></PIPEDocument>
</PIPEDocument>

BidNotification for MSD
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20091218110434" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>PRIMO OPERATORE</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="93520515564276" OriginalReferenceNumber ="93520515441500"
InboundMessageCreationDate ="20091218" InboundMessageCreationTime ="*********">
<BidNotification Scope ="RS" Status ="Accept" Purpose ="Buy" PartialAcceptedQuantityIndicator ="Yes">
<Market>MSD1</Market>
<GMEReferenceNumber>93520515441500</GMEReferenceNumber>
<VerifiedSourceOffer>SPOT</VerifiedSourceOffer>
<ContractID>0</ContractID>
<Date>20091219</Date>
<Hour>1</Hour>
<UnitReferenceNumber> UnP2</UnitReferenceNumber>
<AwardedQuantity UnitOfMeasure ="MWh">3,001</AwardedQuantity>
<AwardedPrice>2,000000</AwardedPrice>
<AwardedValue>6,00</AwardedValue>
<ReservedQuantity UnitOfMeasure ="MWh">2,000</ReservedQuantity>
</BidNotification>

</PIPTransaction>
<PIPTransaction ReferenceNumber ="93520515564278" OriginalReferenceNumber ="93520515441500"
InboundMessageCreationDate ="20091218" InboundMessageCreationTime ="*********">
<BidNotification Scope ="GR1" Status ="Reject" Purpose ="Buy" PredefinedOffer ="No">
<RejectInformation>
<Reason>Unaccepted</Reason>
<ReasonText>Not accepted by market algorithm</ReasonText>
</RejectInformation>
<Market>MSD1</Market>
<GMEReferenceNumber>93520515441500</GMEReferenceNumber>
<VerifiedSourceOffer>CONTR</VerifiedSourceOffer>
<ContractID>2</ContractID>
<Date>20091219</Date>
<Hour>1</Hour>
<UnitReferenceNumber> UnP2</UnitReferenceNumber>
<BidQuantity UnitOfMeasure ="MWh">6,000</BidQuantity>
<ReservedQuantity UnitOfMeasure ="MWh">2,000</ReservedQuantity>
<EnergyPrice>28,00</EnergyPrice>
</BidNotification>
</PIPTransaction>
</PIPEDocument>

BidNotification for MB
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20091218110434" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>PRIMO OPERATORE</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="93520515564276" OriginalReferenceNumber ="93520515441500"
InboundMessageCreationDate ="20091218" InboundMessageCreationTime ="*********">
<BidNotification Scope ="RS" BAType ="Netting" Status ="Accept" Purpose ="Buy" Quarter ="1"
PartialAcceptedQuantityIndicator ="No">
<Market>MB</Market>
<GMEReferenceNumber>93520517441500</GMEReferenceNumber>
<VerifiedSourceOffer>MSD</VerifiedSourceOffer>
<ContractID>0</ContractID>
<Date>20091219</Date>
<Hour>1</Hour>
<UnitReferenceNumber> UnP2</UnitReferenceNumber>
<AwardedQuantity UnitOfMeasure ="MWh">3,001</AwardedQuantity>
<AwardedPrice>2,000000</AwardedPrice>
<AwardedValue>6,00</AwardedValue>
</BidNotification>

</PIPTransaction>
</PIPEDocument>

PIPEDocument.xsd for BidNotification Accept-Reject
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<complexType name="tyReservedQuantity">
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureBidType" use="optional"/>
</extension>
</simpleContent>
</complexType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>

<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:BidNotification"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="OriginalReferenceNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name="BidNotification">
<complexType>
<choice>
<sequence>
<element name="Market" type="pd:marketType"/>
<element name="MarketParticipantNumber" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="GMEReferenceNumber" type="pd:lengthThirtyType"/>
<element name="VerifiedSourceOffer" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="ContractID" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="AwardedQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">

<attribute name="UnitOfMeasure" type="pd:unitOfMeasureBidType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="AwardedPrice" type="pd:localeDecimal"/>
<element name="AwardedValue" type="pd:localeDecimal"/>
<element name="ReservedQuantity" type="pd:tyReservedQuantity" minOccurs="0"/>
</sequence>
<sequence>
<element name="RejectInformation">
<complexType>
<sequence>
<element name="Reason" type="string"/>
<element name="ReasonText" type="pd:lengthFiveHundredType" minOccurs="0"/>
</sequence>
</complexType>
</element>
<element name="Market" type="pd:marketType"/>
<element name="MarketParticipantNumber" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="GMEReferenceNumber" type="pd:lengthThirtyType"/>
<element name="VerifiedSourceOffer" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="ContractID" type="pd:lengthThirtyType" minOccurs="0"/>
<element name="Date" type="pd:dateType"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="BidQuantity">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureBidType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
<element name="ReservedQuantity" type="pd:tyReservedQuantity" minOccurs="0"/>
<element name="EnergyPrice" type="pd:localeDecimal"/>
</sequence>
</choice>
<attribute name="Status" type="pd:statusTransactionType" use="required"/>
<attribute name="Purpose" type="pd:purposeTradeType" use="required"/>
<attribute name="Scope" type="pd:scopeType" use="optional"/>
<attribute name="Quarter" type="pd:quarterType" use="optional"/>
<attribute name="BAType" type="pd:bAType" use="optional"/>
<attribute name="BalancedReferenceNumber" type="pd:lengthThirtyType" use="optional"/>
<attribute name="PredefinedOffer" type="pd:yesNoType" use="optional"/>
<attribute name="PartialAcceptedQuantityIndicator" type="pd:yesNoType" use="optional"/>

</complexType>
</element>
</schema>

UnitSchedules
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20020516180231" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Operator">
<CompanyName>GRTN</CompanyName>
<CompanyIdentifier>2093842039</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="21360001052062" InboundMessageCreationDate ="20020516" InboundMessageCreationTime
="*********">
<UnitSchedule Type ="Preliminary" Cummulative ="No" MarketParticipantNumber ="PRIMOP">
<Market>MGP</Market>
<Date>20020320</Date>
<UnitReferenceNumber>UnC2</UnitReferenceNumber>
<ReferenceMarketParticipantNumber>PRIMOP</ReferenceMarketParticipantNumber>
<Quantity Hour ="1" UnitOfMeasure ="MWh">8,308</Quantity>
<Quantity Hour ="2" UnitOfMeasure ="MWh">8,129</Quantity>
<Quantity Hour ="3" UnitOfMeasure ="MWh">9,17</Quantity>
<Quantity Hour ="4" UnitOfMeasure ="MWh">7,774</Quantity>
<Quantity Hour ="5" UnitOfMeasure ="MWh">9,075</Quantity>
<Quantity Hour ="6" UnitOfMeasure ="MWh">8,015</Quantity>
<Quantity Hour ="7" UnitOfMeasure ="MWh">8,19</Quantity>
<Quantity Hour ="8" UnitOfMeasure ="MWh">8,325</Quantity>

<Quantity Hour ="9" UnitOfMeasure ="MWh">8,483</Quantity>
<Quantity Hour ="10" UnitOfMeasure ="MWh">8,807</Quantity>
<Quantity Hour ="11" UnitOfMeasure ="MWh">5,992</Quantity>
<Quantity Hour ="12" UnitOfMeasure ="MWh">5,968</Quantity>
<Quantity Hour ="13" UnitOfMeasure ="MWh">9,883</Quantity>
<Quantity Hour ="14" UnitOfMeasure ="MWh">5,899</Quantity>
<Quantity Hour ="15" UnitOfMeasure ="MWh">9,869</Quantity>
<Quantity Hour ="16" UnitOfMeasure ="MWh">9,934</Quantity>
<Quantity Hour ="17" UnitOfMeasure ="MWh">9,817</Quantity>
<Quantity Hour ="18" UnitOfMeasure ="MWh">9,78</Quantity>
<Quantity Hour ="19" UnitOfMeasure ="MWh">9,707</Quantity>
<Quantity Hour ="20" UnitOfMeasure ="MWh">9,133</Quantity>
<Quantity Hour ="21" UnitOfMeasure ="MWh">9,146</Quantity>
<Quantity Hour ="22" UnitOfMeasure ="MWh">9,077</Quantity>
<Quantity Hour ="23" UnitOfMeasure ="MWh">9,011</Quantity>
<Quantity Hour ="24" UnitOfMeasure ="MWh">8,945</Quantity>
</UnitSchedule>
</PIPTransaction>
<PIPTransaction ReferenceNumber ="21360001052063" InboundMessageCreationDate ="20020516" InboundMessageCreationTime
="*********">
<UnitSchedule Type ="Preliminary" Cummulative ="No" MarketParticipantNumber ="PRIMOP">
<Market>MGP</Market>
<Date>20020320</Date>
<UnitReferenceNumber>UnP2</UnitReferenceNumber>
<ReferenceMarketParticipantNumber>PRIMOP</ReferenceMarketParticipantNumber>
<Quantity Hour ="1" UnitOfMeasure ="MWh">144,22</Quantity>
<Quantity Hour ="2" UnitOfMeasure ="MWh">147,842</Quantity>
<Quantity Hour ="3" UnitOfMeasure ="MWh">151,625</Quantity>
<Quantity Hour ="4" UnitOfMeasure ="MWh">155,365</Quantity>
<Quantity Hour ="5" UnitOfMeasure ="MWh">153,478</Quantity>
<Quantity Hour ="6" UnitOfMeasure ="MWh">147,92</Quantity>
<Quantity Hour ="7" UnitOfMeasure ="MWh">137,032</Quantity>
<Quantity Hour ="8" UnitOfMeasure ="MWh">120,697</Quantity>
<Quantity Hour ="9" UnitOfMeasure ="MWh">114,774</Quantity>
<Quantity Hour ="10" UnitOfMeasure ="MWh">103,397</Quantity>
<Quantity Hour ="11" UnitOfMeasure ="MWh">91,854</Quantity>
<Quantity Hour ="12" UnitOfMeasure ="MWh">86,68</Quantity>
<Quantity Hour ="13" UnitOfMeasure ="MWh">101,393</Quantity>
<Quantity Hour ="14" UnitOfMeasure ="MWh">87,078</Quantity>

<Quantity Hour ="15" UnitOfMeasure ="MWh">94,761</Quantity>
<Quantity Hour ="16" UnitOfMeasure ="MWh">105,319</Quantity>
<Quantity Hour ="17" UnitOfMeasure ="MWh">110,396</Quantity>
<Quantity Hour ="18" UnitOfMeasure ="MWh">109,901</Quantity>
<Quantity Hour ="19" UnitOfMeasure ="MWh">112,234</Quantity>
<Quantity Hour ="20" UnitOfMeasure ="MWh">62,808</Quantity>
<Quantity Hour ="21" UnitOfMeasure ="MWh">66,719</Quantity>
<Quantity Hour ="22" UnitOfMeasure ="MWh">67,84</Quantity>
<Quantity Hour ="23" UnitOfMeasure ="MWh">68,973</Quantity>
<Quantity Hour ="24" UnitOfMeasure ="MWh">70,118</Quantity>
</UnitSchedule>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for UnitSchedules
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:UnitSchedule"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name="UnitSchedule">
<complexType>
<sequence>
<element name="Market" type="pd:marketType"/>
<element name="Date" type="pd:dateType"/>
<element name="UnitReferenceNumber" type="pd:lengthSixtyType"/>
<element name="ReferenceMarketParticipantNumber" type="string"/>
<element name="UnbalancedMarketParticipantNumber" type="string" minOccurs="0"/>
<element name="Quantity" maxOccurs="25">
<complexType>
<simpleContent>
<extension base="pd:localeDecimal">
<attribute name="Hour" type="pd:hourIntervalType" use="required"/>
<attribute name="UnitOfMeasure" type="pd:unitOfMeasureMWhType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
</sequence>
<attribute name="MarketParticipantNumber" type="string" use="required"/>
<attribute name="Type" type="pd:unitScheduleType" use="required"/>
<attribute name="Cummulative" type="pd:yesNoType" use="required"/>
</complexType>

</element>
</schema>

ZoneInformation
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20040912164324" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>ITALGEN S.P.A.</CompanyName>
<CompanyIdentifier>OEITLCM</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="42560017207715" InboundMessageCreationDate ="20040912" InboundMessageCreationTime
="*********">
<ZoneInformation>
<Date>20040913</Date>
<MarketDetail>
<Market>MGP</Market>
<InterZonalDetail>
<FromZone>NORD</FromZone>
<ToZone>CNOR</ToZone>
<InterZonalLimits>
<Hour>1</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>

<Hour>2</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>3</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>4</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>5</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>6</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>7</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>8</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>9</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>

<Hour>10</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>11</Hour>
<ConnectionLimit>1200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>12</Hour>
<ConnectionLimit>1300</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>13</Hour>
<ConnectionLimit>1400</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>14</Hour>
<ConnectionLimit>1500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>15</Hour>
<ConnectionLimit>1600</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>16</Hour>
<ConnectionLimit>1700</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>17</Hour>
<ConnectionLimit>1800</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>

<Hour>18</Hour>
<ConnectionLimit>1900</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>19</Hour>
<ConnectionLimit>2000</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>20</Hour>
<ConnectionLimit>2100</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>21</Hour>
<ConnectionLimit>2200</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>22</Hour>
<ConnectionLimit>2300</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>23</Hour>
<ConnectionLimit>2400</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>24</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
</InterZonalDetail>
<InterZonalDetail>
<FromZone>NORD</FromZone>
<ToZone>TBRV</ToZone>
<InterZonalLimits>
<Hour>1</Hour>

<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>2</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>3</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>4</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>5</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>6</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>7</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>8</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>9</Hour>

<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>10</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>11</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>12</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>13</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>14</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>15</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>16</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>17</Hour>

<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>18</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>19</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>20</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>21</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>22</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>23</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
<InterZonalLimits>
<Hour>24</Hour>
<ConnectionLimit>2500</ConnectionLimit>
<ConnectionCoefficient>1</ConnectionCoefficient>
</InterZonalLimits>
</InterZonalDetail>
•

• [etc.]
•
</MarketDetail>
</ZoneInformation>
</PIPTransaction></PIPEDocument> </PIPTransaction></PIPEDocument>
</PIPEDocument>

PIPEDocument.xsd for ZoneInformation
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">

<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name = "PIPTransaction">
<complexType>
<sequence>
<element ref = "pd:ZoneInformation"/>
</sequence>
<attribute name = "ReferenceNumber" type = "pd:lengthThirtyType" use="required"/>
<attribute name = "InboundMessageCreationDate" type = "pd:creationDateType" use="optional"/>
<attribute name = "InboundMessageCreationTime" type = "pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name = "ZoneInformation">
<complexType>
<sequence>
<element name = "Date" type = "pd:dateType"/>
<element name = "MarketDetail" minOccurs="1" maxOccurs="unbounded">
<complexType>
<sequence>
<element name = "Market" type = "pd:marketType"/>
<element name = "InterZonalDetail" minOccurs="1" maxOccurs="unbounded">
<complexType>
<sequence>
<element name = "FromZone" type = "pd:zoneNameType"/>
<element name = "ToZone" type = "pd:zoneNameType"/>
<element name = "InterZonalLimits" minOccurs="1" maxOccurs="25">
<complexType>
<sequence>
<element name = "Hour" type = "pd:hourIntervalType"/>
<element name = "ConnectionLimit" type = "pd:localeDecimal"/>

<element name = "ConnectionCoefficient" type = "string"/>
</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
<element name="GeneralizedConstraintsDetail" minOccurs="0" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="GeneralizedConstraints" minOccurs="1" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="Hour" type="pd:hourIntervalType"/>
<element name="Value" type="pd:localeDecimal"/>
<element name="ZoneSensitivity" minOccurs="0"
maxOccurs="unbounded">
<complexType>
<sequence>
<element name="Zone"
type="pd:zoneNameType"/>
<element name="Value"
type="pd:localeDecimal"/>
</sequence>
</complexType>
</element>
</sequence>
<attribute name="Name" type="pd:lengthSixtyType"/>
</complexType>
</element>
</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</schema>

EstimatedDemandInformation
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20040912164403" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>ITALGEN S.P.A.</CompanyName>
<CompanyIdentifier>OEITLCM</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="42560017234788" InboundMessageCreationDate ="20040912" InboundMessageCreationTime
="*********">
<EstimatedDemandInformation>
<Date>20040913</Date>
<ZoneDetail>
<Zone>NORD</Zone>
<EstimatedDemand Hour ="1">100,45</EstimatedDemand>
<EstimatedDemand Hour ="2">6962,23</EstimatedDemand>
<EstimatedDemand Hour ="3">6725,78</EstimatedDemand>
<EstimatedDemand Hour ="4">6607,55</EstimatedDemand>
<EstimatedDemand Hour ="5">6850,57</EstimatedDemand>
<EstimatedDemand Hour ="6">7277,5</EstimatedDemand>
<EstimatedDemand Hour ="7">8472,91</EstimatedDemand>
<EstimatedDemand Hour ="8">10476,19</EstimatedDemand>

<EstimatedDemand Hour ="9">11087,03</EstimatedDemand>
<EstimatedDemand Hour ="10">11481,12</EstimatedDemand>
<EstimatedDemand Hour ="11">11336,62</EstimatedDemand>
<EstimatedDemand Hour ="12">11054,19</EstimatedDemand>
<EstimatedDemand Hour ="13">10614,12</EstimatedDemand>
<EstimatedDemand Hour ="14">10725,78</EstimatedDemand>
<EstimatedDemand Hour ="15">11034,48</EstimatedDemand>
<EstimatedDemand Hour ="16">11277,5</EstimatedDemand>
<EstimatedDemand Hour ="17">11697,87</EstimatedDemand>
<EstimatedDemand Hour ="18">12000</EstimatedDemand>
<EstimatedDemand Hour ="19">11625,62</EstimatedDemand>
<EstimatedDemand Hour ="20">10784,89</EstimatedDemand>
<EstimatedDemand Hour ="21">9950,74</EstimatedDemand>
<EstimatedDemand Hour ="22">9254,52</EstimatedDemand>
<EstimatedDemand Hour ="23">8538,59</EstimatedDemand>
<EstimatedDemand Hour ="24">7206</EstimatedDemand>
</ZoneDetail>
<ZoneDetail>
<Zone>CNOR</Zone>
<EstimatedDemand Hour ="1">2600</EstimatedDemand>
<EstimatedDemand Hour ="2">2465,79</EstimatedDemand>
<EstimatedDemand Hour ="3">2382,05</EstimatedDemand>
<EstimatedDemand Hour ="4">2340,18</EstimatedDemand>
<EstimatedDemand Hour ="5">2426,25</EstimatedDemand>
<EstimatedDemand Hour ="6">2577,45</EstimatedDemand>
<EstimatedDemand Hour ="7">3000,82</EstimatedDemand>
<EstimatedDemand Hour ="8">3710,32</EstimatedDemand>
<EstimatedDemand Hour ="9">3926,66</EstimatedDemand>
<EstimatedDemand Hour ="10">4066,23</EstimatedDemand>
<EstimatedDemand Hour ="11">4015,05</EstimatedDemand>
<EstimatedDemand Hour ="12">3915,02</EstimatedDemand>
<EstimatedDemand Hour ="13">3759,17</EstimatedDemand>
<EstimatedDemand Hour ="14">3798,71</EstimatedDemand>
<EstimatedDemand Hour ="15">3908,05</EstimatedDemand>
<EstimatedDemand Hour ="16">3994,12</EstimatedDemand>
<EstimatedDemand Hour ="17">4142,99</EstimatedDemand>
<EstimatedDemand Hour ="18">4250</EstimatedDemand>
<EstimatedDemand Hour ="19">4117,41</EstimatedDemand>
<EstimatedDemand Hour ="20">3819,65</EstimatedDemand>
<EstimatedDemand Hour ="21">3524,22</EstimatedDemand>

<EstimatedDemand Hour ="22">3277,64</EstimatedDemand>
<EstimatedDemand Hour ="23">3024,08</EstimatedDemand>
<EstimatedDemand Hour ="24">2600</EstimatedDemand>
</ZoneDetail>
<ZoneDetail>
<Zone>CSUD</Zone>
<EstimatedDemand Hour ="1">2990</EstimatedDemand>
<EstimatedDemand Hour ="2">3191,02</EstimatedDemand>
<EstimatedDemand Hour ="3">3082,65</EstimatedDemand>
<EstimatedDemand Hour ="4">3028,46</EstimatedDemand>
<EstimatedDemand Hour ="5">3139,85</EstimatedDemand>
<EstimatedDemand Hour ="6">3335,52</EstimatedDemand>
<EstimatedDemand Hour ="7">3883,42</EstimatedDemand>
<EstimatedDemand Hour ="8">4801,59</EstimatedDemand>
<EstimatedDemand Hour ="9">5081,55</EstimatedDemand>
<EstimatedDemand Hour ="10">5262,18</EstimatedDemand>
<EstimatedDemand Hour ="11">5195,95</EstimatedDemand>
<EstimatedDemand Hour ="12">5066,5</EstimatedDemand>
<EstimatedDemand Hour ="13">4864,81</EstimatedDemand>
<EstimatedDemand Hour ="14">4915,98</EstimatedDemand>
<EstimatedDemand Hour ="15">5057,47</EstimatedDemand>
<EstimatedDemand Hour ="16">5168,86</EstimatedDemand>
<EstimatedDemand Hour ="17">5361,52</EstimatedDemand>
<EstimatedDemand Hour ="18">5500</EstimatedDemand>
<EstimatedDemand Hour ="19">5328,41</EstimatedDemand>
<EstimatedDemand Hour ="20">4943,08</EstimatedDemand>
<EstimatedDemand Hour ="21">4560,76</EstimatedDemand>
<EstimatedDemand Hour ="22">4241,65</EstimatedDemand>
<EstimatedDemand Hour ="23">3913,52</EstimatedDemand>
<EstimatedDemand Hour ="24">2990</EstimatedDemand>
</ZoneDetail>
<ZoneDetail>
<Zone>SUD</Zone>
<EstimatedDemand Hour ="1">3190</EstimatedDemand>
<EstimatedDemand Hour ="2">2900,93</EstimatedDemand>
<EstimatedDemand Hour ="3">2802,41</EstimatedDemand>
<EstimatedDemand Hour ="4">2753,15</EstimatedDemand>
<EstimatedDemand Hour ="5">2854,41</EstimatedDemand>
<EstimatedDemand Hour ="6">3032,29</EstimatedDemand>
<EstimatedDemand Hour ="7">3530,38</EstimatedDemand>

<EstimatedDemand Hour ="8">4365,08</EstimatedDemand>
<EstimatedDemand Hour ="9">4619,59</EstimatedDemand>
<EstimatedDemand Hour ="10">4783,8</EstimatedDemand>
<EstimatedDemand Hour ="11">4723,59</EstimatedDemand>
<EstimatedDemand Hour ="12">4605,91</EstimatedDemand>
<EstimatedDemand Hour ="13">4422,55</EstimatedDemand>
<EstimatedDemand Hour ="14">4469,07</EstimatedDemand>
<EstimatedDemand Hour ="15">4597,7</EstimatedDemand>
<EstimatedDemand Hour ="16">4698,96</EstimatedDemand>
<EstimatedDemand Hour ="17">4874,11</EstimatedDemand>
<EstimatedDemand Hour ="18">5000</EstimatedDemand>
<EstimatedDemand Hour ="19">4844,01</EstimatedDemand>
<EstimatedDemand Hour ="20">4493,71</EstimatedDemand>
<EstimatedDemand Hour ="21">4146,14</EstimatedDemand>
<EstimatedDemand Hour ="22">3856,05</EstimatedDemand>
<EstimatedDemand Hour ="23">3557,74</EstimatedDemand>
<EstimatedDemand Hour ="24">3185,55</EstimatedDemand>
</ZoneDetail>
<ZoneDetail>
<Zone>SICI</Zone>
<EstimatedDemand Hour ="1">910</EstimatedDemand>
<EstimatedDemand Hour ="2">870,28</EstimatedDemand>
<EstimatedDemand Hour ="3">840,72</EstimatedDemand>
<EstimatedDemand Hour ="4">825,94</EstimatedDemand>
<EstimatedDemand Hour ="5">856,32</EstimatedDemand>
<EstimatedDemand Hour ="6">909,69</EstimatedDemand>
<EstimatedDemand Hour ="7">1059,11</EstimatedDemand>
<EstimatedDemand Hour ="8">1309,52</EstimatedDemand>
<EstimatedDemand Hour ="9">1385,88</EstimatedDemand>
<EstimatedDemand Hour ="10">1435,14</EstimatedDemand>
<EstimatedDemand Hour ="11">1417,08</EstimatedDemand>
<EstimatedDemand Hour ="12">1381,77</EstimatedDemand>
<EstimatedDemand Hour ="13">1326,77</EstimatedDemand>
<EstimatedDemand Hour ="14">1340,72</EstimatedDemand>
<EstimatedDemand Hour ="15">1379,31</EstimatedDemand>
<EstimatedDemand Hour ="16">1409,69</EstimatedDemand>
<EstimatedDemand Hour ="17">1462,23</EstimatedDemand>
<EstimatedDemand Hour ="18">1500</EstimatedDemand>
<EstimatedDemand Hour ="19">1453,2</EstimatedDemand>
<EstimatedDemand Hour ="20">1348,11</EstimatedDemand>

<EstimatedDemand Hour ="21">1243,84</EstimatedDemand>
<EstimatedDemand Hour ="22">1156,81</EstimatedDemand>
<EstimatedDemand Hour ="23">1067,32</EstimatedDemand>
<EstimatedDemand Hour ="24">910</EstimatedDemand>
</ZoneDetail>
<ZoneDetail>
<Zone>SARD</Zone>
<EstimatedDemand Hour ="1">480</EstimatedDemand>
<EstimatedDemand Hour ="2">435,14</EstimatedDemand>
<EstimatedDemand Hour ="3">420,36</EstimatedDemand>
<EstimatedDemand Hour ="4">412,97</EstimatedDemand>
<EstimatedDemand Hour ="5">428,16</EstimatedDemand>
<EstimatedDemand Hour ="6">454,84</EstimatedDemand>
<EstimatedDemand Hour ="7">529,56</EstimatedDemand>
<EstimatedDemand Hour ="8">654,76</EstimatedDemand>
<EstimatedDemand Hour ="9">692,94</EstimatedDemand>
<EstimatedDemand Hour ="10">717,57</EstimatedDemand>
<EstimatedDemand Hour ="11">708,54</EstimatedDemand>
<EstimatedDemand Hour ="12">690,89</EstimatedDemand>
<EstimatedDemand Hour ="13">663,38</EstimatedDemand>
<EstimatedDemand Hour ="14">670,36</EstimatedDemand>
<EstimatedDemand Hour ="15">689,66</EstimatedDemand>
<EstimatedDemand Hour ="16">704,84</EstimatedDemand>
<EstimatedDemand Hour ="17">731,12</EstimatedDemand>
<EstimatedDemand Hour ="18">750</EstimatedDemand>
<EstimatedDemand Hour ="19">726,6</EstimatedDemand>
<EstimatedDemand Hour ="20">674,06</EstimatedDemand>
<EstimatedDemand Hour ="21">621,92</EstimatedDemand>
<EstimatedDemand Hour ="22">578,41</EstimatedDemand>
<EstimatedDemand Hour ="23">533,66</EstimatedDemand>
<EstimatedDemand Hour ="24">477,83</EstimatedDemand>
</ZoneDetail>
</EstimatedDemandInformation>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for EstimatedDemandInformation
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>

<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name = "PIPTransaction">
<complexType>
<sequence>
<element ref = "pd:EstimatedDemandInformation"/>
</sequence>
<attribute name = "ReferenceNumber" type = "pd:lengthThirtyType" use="required"/>
<attribute name = "InboundMessageCreationDate" type = "pd:creationDateType" use="optional"/>
<attribute name = "InboundMessageCreationTime" type = "pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name = "EstimatedDemandInformation">
<complexType>
<sequence>
<element name = "Date" type = "pd:dateType"/>
<element name = "ZoneDetail" minOccurs="1" maxOccurs="unbounded">
<complexType>
<sequence>
<element name = "Zone" type = "pd:zoneNameType"/>
<element name = "EstimatedDemand" minOccurs="1" maxOccurs="25">
<complexType>
<simpleContent>
<extension base = "pd:localeDecimal">
<attribute name = "Hour" type = "pd:hourIntervalType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>
</sequence>

</complexType>
</element>
</sequence>
</complexType>
</element>
</schema>

EstimatedPriceInformation
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20040912164519" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>ITALGEN S.P.A.</CompanyName>
<CompanyIdentifier>OEITLCM</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="42560017234918" InboundMessageCreationDate ="20040912" InboundMessageCreationTime
="*********">
<EstimatedPriceInformation>
<Market>MGP</Market>
<Date>20040913</Date>
<ZoneDetail>
<Zone>NORD</Zone>
<EstimatedPrice Hour ="1">23,24</EstimatedPrice>
<EstimatedPrice Hour ="2">26,34</EstimatedPrice>
<EstimatedPrice Hour ="3">29,44</EstimatedPrice>
<EstimatedPrice Hour ="4">30,99</EstimatedPrice>
<EstimatedPrice Hour ="5">34,87</EstimatedPrice>
<EstimatedPrice Hour ="6">38,74</EstimatedPrice>
<EstimatedPrice Hour ="7">42,61</EstimatedPrice>

<EstimatedPrice Hour ="8">46,49</EstimatedPrice>
<EstimatedPrice Hour ="9">54,24</EstimatedPrice>
<EstimatedPrice Hour ="10">61,98</EstimatedPrice>
<EstimatedPrice Hour ="11">56,56</EstimatedPrice>
<EstimatedPrice Hour ="12">50,36</EstimatedPrice>
<EstimatedPrice Hour ="13">46,49</EstimatedPrice>
<EstimatedPrice Hour ="14">50,36</EstimatedPrice>
<EstimatedPrice Hour ="15">54,24</EstimatedPrice>
<EstimatedPrice Hour ="16">58,11</EstimatedPrice>
<EstimatedPrice Hour ="17">61,98</EstimatedPrice>
<EstimatedPrice Hour ="18">69,73</EstimatedPrice>
<EstimatedPrice Hour ="19">77,48</EstimatedPrice>
<EstimatedPrice Hour ="20">61,98</EstimatedPrice>
<EstimatedPrice Hour ="21">46,49</EstimatedPrice>
<EstimatedPrice Hour ="22">38,74</EstimatedPrice>
<EstimatedPrice Hour ="23">30,99</EstimatedPrice>
<EstimatedPrice Hour ="24">23,24</EstimatedPrice>
</ZoneDetail>
<ZoneDetail>
<Zone>CNOR</Zone>
<EstimatedPrice Hour ="1">23,71</EstimatedPrice>
<EstimatedPrice Hour ="2">26,87</EstimatedPrice>
<EstimatedPrice Hour ="3">30,03</EstimatedPrice>
<EstimatedPrice Hour ="4">31,61</EstimatedPrice>
<EstimatedPrice Hour ="5">35,56</EstimatedPrice>
<EstimatedPrice Hour ="6">39,51</EstimatedPrice>
<EstimatedPrice Hour ="7">43,47</EstimatedPrice>
<EstimatedPrice Hour ="8">47,42</EstimatedPrice>
<EstimatedPrice Hour ="9">55,32</EstimatedPrice>
<EstimatedPrice Hour ="10">63,22</EstimatedPrice>
<EstimatedPrice Hour ="11">57,69</EstimatedPrice>
<EstimatedPrice Hour ="12">51,37</EstimatedPrice>
<EstimatedPrice Hour ="13">47,42</EstimatedPrice>
<EstimatedPrice Hour ="14">51,37</EstimatedPrice>
<EstimatedPrice Hour ="15">55,32</EstimatedPrice>
<EstimatedPrice Hour ="16">59,27</EstimatedPrice>
<EstimatedPrice Hour ="17">63,22</EstimatedPrice>
<EstimatedPrice Hour ="18">71,13</EstimatedPrice>
<EstimatedPrice Hour ="19">79,03</EstimatedPrice>
<EstimatedPrice Hour ="20">63,22</EstimatedPrice>

<EstimatedPrice Hour ="21">47,42</EstimatedPrice>
<EstimatedPrice Hour ="22">39,51</EstimatedPrice>
<EstimatedPrice Hour ="23">31,61</EstimatedPrice>
<EstimatedPrice Hour ="24">23,71</EstimatedPrice>
</ZoneDetail>
<ZoneDetail>
<Zone>CSUD</Zone>
<EstimatedPrice Hour ="1">20,92</EstimatedPrice>
<EstimatedPrice Hour ="2">23,71</EstimatedPrice>
<EstimatedPrice Hour ="3">26,5</EstimatedPrice>
<EstimatedPrice Hour ="4">27,89</EstimatedPrice>
<EstimatedPrice Hour ="5">31,38</EstimatedPrice>
<EstimatedPrice Hour ="6">34,87</EstimatedPrice>
<EstimatedPrice Hour ="7">38,35</EstimatedPrice>
<EstimatedPrice Hour ="8">41,84</EstimatedPrice>
<EstimatedPrice Hour ="9">48,81</EstimatedPrice>
<EstimatedPrice Hour ="10">55,79</EstimatedPrice>
<EstimatedPrice Hour ="11">50,9</EstimatedPrice>
<EstimatedPrice Hour ="12">45,33</EstimatedPrice>
<EstimatedPrice Hour ="13">41,84</EstimatedPrice>
<EstimatedPrice Hour ="14">45,33</EstimatedPrice>
<EstimatedPrice Hour ="15">48,81</EstimatedPrice>
<EstimatedPrice Hour ="16">52,3</EstimatedPrice>
<EstimatedPrice Hour ="17">55,79</EstimatedPrice>
<EstimatedPrice Hour ="18">62,76</EstimatedPrice>
<EstimatedPrice Hour ="19">69,73</EstimatedPrice>
<EstimatedPrice Hour ="20">55,79</EstimatedPrice>
<EstimatedPrice Hour ="21">41,84</EstimatedPrice>
<EstimatedPrice Hour ="22">34,87</EstimatedPrice>
<EstimatedPrice Hour ="23">27,89</EstimatedPrice>
<EstimatedPrice Hour ="24">20,92</EstimatedPrice>
</ZoneDetail>
<ZoneDetail>
<Zone>SUD</Zone>
<EstimatedPrice Hour ="1">19,99</EstimatedPrice>
<EstimatedPrice Hour ="2">22,65</EstimatedPrice>
<EstimatedPrice Hour ="3">25,32</EstimatedPrice>
<EstimatedPrice Hour ="4">26,65</EstimatedPrice>
<EstimatedPrice Hour ="5">29,98</EstimatedPrice>
<EstimatedPrice Hour ="6">33,32</EstimatedPrice>

<EstimatedPrice Hour ="7">36,65</EstimatedPrice>
<EstimatedPrice Hour ="8">39,98</EstimatedPrice>
<EstimatedPrice Hour ="9">46,64</EstimatedPrice>
<EstimatedPrice Hour ="10">53,31</EstimatedPrice>
<EstimatedPrice Hour ="11">48,64</EstimatedPrice>
<EstimatedPrice Hour ="12">43,31</EstimatedPrice>
<EstimatedPrice Hour ="13">39,98</EstimatedPrice>
<EstimatedPrice Hour ="14">43,31</EstimatedPrice>
<EstimatedPrice Hour ="15">46,64</EstimatedPrice>
<EstimatedPrice Hour ="16">49,97</EstimatedPrice>
<EstimatedPrice Hour ="17">53,31</EstimatedPrice>
<EstimatedPrice Hour ="18">59,97</EstimatedPrice>
<EstimatedPrice Hour ="19">66,63</EstimatedPrice>
<EstimatedPrice Hour ="20">53,31</EstimatedPrice>
<EstimatedPrice Hour ="21">39,98</EstimatedPrice>
<EstimatedPrice Hour ="22">33,32</EstimatedPrice>
<EstimatedPrice Hour ="23">26,65</EstimatedPrice>
<EstimatedPrice Hour ="24">19,99</EstimatedPrice>
</ZoneDetail>
<ZoneDetail>
<Zone>SICI</Zone>
<EstimatedPrice Hour ="1">23,31</EstimatedPrice>
<EstimatedPrice Hour ="2">26,42</EstimatedPrice>
<EstimatedPrice Hour ="3">29,53</EstimatedPrice>
<EstimatedPrice Hour ="4">31,08</EstimatedPrice>
<EstimatedPrice Hour ="5">34,97</EstimatedPrice>
<EstimatedPrice Hour ="6">38,86</EstimatedPrice>
<EstimatedPrice Hour ="7">42,74</EstimatedPrice>
<EstimatedPrice Hour ="8">46,63</EstimatedPrice>
<EstimatedPrice Hour ="9">54,4</EstimatedPrice>
<EstimatedPrice Hour ="10">62,17</EstimatedPrice>
<EstimatedPrice Hour ="11">56,73</EstimatedPrice>
<EstimatedPrice Hour ="12">50,51</EstimatedPrice>
<EstimatedPrice Hour ="13">46,63</EstimatedPrice>
<EstimatedPrice Hour ="14">50,51</EstimatedPrice>
<EstimatedPrice Hour ="15">54,4</EstimatedPrice>
<EstimatedPrice Hour ="16">58,28</EstimatedPrice>
<EstimatedPrice Hour ="17">62,17</EstimatedPrice>
<EstimatedPrice Hour ="18">69,94</EstimatedPrice>
<EstimatedPrice Hour ="19">77,71</EstimatedPrice>

<EstimatedPrice Hour ="20">62,17</EstimatedPrice>
<EstimatedPrice Hour ="21">46,63</EstimatedPrice>
<EstimatedPrice Hour ="22">38,86</EstimatedPrice>
<EstimatedPrice Hour ="23">31,08</EstimatedPrice>
<EstimatedPrice Hour ="24">23,31</EstimatedPrice>
</ZoneDetail>
•
• [etc.]
•
</EstimatedPriceInformation>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for EstimatedPriceInformation
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>

<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name = "PIPTransaction">
<complexType>
<sequence>
<element ref = "pd:EstimatedPriceInformation"/>
</sequence>
<attribute name = "ReferenceNumber" type = "pd:lengthThirtyType" use="required"/>
<attribute name = "InboundMessageCreationDate" type = "pd:creationDateType" use="optional"/>
<attribute name = "InboundMessageCreationTime" type = "pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name = "EstimatedPriceInformation">
<complexType>
<sequence>
<element name = "Market" type = "pd:conventionalPriceMarketType"/>
<element name = "Date" type = "pd:dateType"/>
<element name = "ZoneDetail" minOccurs="1" maxOccurs="unbounded">
<complexType>
<sequence>
<element name = "Zone" type = "pd:zoneNameType"/>
<element name = "EstimatedPrice" minOccurs="1" maxOccurs="25">
<complexType>
<simpleContent>
<extension base = "pd:localeDecimal">
<attribute name = "Hour" type = "pd:hourIntervalType" use="required"/>
</extension>
</simpleContent>
</complexType>
</element>

</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</schema>

MarketResult
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20041223175634" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>GRTN</CompanyName>
<CompanyIdentifier>IDGRTN</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="43580025557599" InboundMessageCreationDate ="20041223" InboundMessageCreationTime
="*********">
<MarketResult>
<Date>20041224</Date>
<MarketDetail>
<Market>MGP</Market>
<ZoneDetail>
<Zone>NAT</Zone>
<Interval Hour ="1">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.966,464</Generation>
<Consumption>6.966,464</Consumption>
</Interval>

<Interval Hour ="2">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.952,164</Generation>
<Consumption>6.952,164</Consumption>
</Interval>
<Interval Hour ="3">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.953,164</Generation>
<Consumption>6.953,164</Consumption>
</Interval>
<Interval Hour ="4">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.945,164</Generation>
<Consumption>6.945,164</Consumption>
</Interval>
<Interval Hour ="5">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.948,164</Generation>
<Consumption>6.948,164</Consumption>
</Interval>
<Interval Hour ="6">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.969,164</Generation>
<Consumption>6.969,164</Consumption>
</Interval>
<Interval Hour ="7">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.777,244</Generation>
<Consumption>6.777,244</Consumption>
</Interval>
<Interval Hour ="8">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.899,491</Generation>

<Consumption>6.899,491</Consumption>
</Interval>
<Interval Hour ="9">
<BuyPrice>500,000000</BuyPrice>
<SellPrice>500,000000</SellPrice>
<Generation>6.941,157</Generation>
<Consumption>6.941,157</Consumption>
</Interval>
•
• [etc.]
•
</ZoneDetail>
</MarketDetail>
</MarketResult>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for MarketResult
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:MarketResult"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="required"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="required"/>
</complexType>
</element>
<element name="MarketResult">
<complexType>
<sequence>
<element name="Date" type="pd:dateType"/>
<element name="MarketDetail">
<complexType>
<sequence>
<element name="Market" type="pd:marketType"/>
<element name="ZoneDetail" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="Zone" type="string"/>
<element name="Interval" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="BuyPrice" type="string"/>
<element name="SellPrice" type="string"/>
<element name="Generation" type="string"/>
<element name="Consumption" type="string"/>
</sequence>
<attribute name="Hour" type="pd:hourIntervalType" use="required"/>
</complexType>
</element>

</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</schema>

MarketResultMB
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="20100309173645" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME SPA</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>Primo operatore</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="100680559379369" InboundMessageCreationDate ="20100309" InboundMessageCreationTime
="*********">
<MarketResultMB>
<Date>20100228</Date>
<Market type ="PRELIMINARY">MB</Market>
<Hour>24</Hour>
<MarketDetail>
<ZoneDetail>
<Zone>CNOR</Zone>
<RSResult>
<MaxSellPrice>72,500000</MaxSellPrice>
<MinBuyPrice>0,000000</MinBuyPrice>
<BuyPrice>2,966272</BuyPrice>
<SellPrice>72,500000</SellPrice>
<Generation_no165>3,694</Generation_no165>

<Consumption_no165>68,088</Consumption_no165>
<Generation_165>11,666</Generation_165>
<Consumption_165>20,772</Consumption_165>
</RSResult>
<ASResult>
<MaxSellPrice>0</MaxSellPrice>
<MinBuyPrice>4,960000</MinBuyPrice>
<BuyPrice>4,978023</BuyPrice>
<SellPrice>0</SellPrice>
<Generation_no165>0,000</Generation_no165>
<Consumption_no165>13,290</Consumption_no165>
<Generation_165>0,634</Generation_165>
<Consumption_165>0,000</Consumption_165>
</ASResult>
</ZoneDetail>
•
• [etc.]
•
<ZoneDetail>
<Zone>SUD</Zone>
<RSResult>
<MaxSellPrice>0</MaxSellPrice>
<MinBuyPrice>0</MinBuyPrice>
<BuyPrice>0</BuyPrice>
<SellPrice>0</SellPrice>
<Generation_no165>0,000</Generation_no165>
<Consumption_no165>0,000</Consumption_no165>
<Generation_165>0,000</Generation_165>
<Consumption_165>0,000</Consumption_165>
</RSResult>
<ASResult>
<MaxSellPrice>0</MaxSellPrice>
<MinBuyPrice>0</MinBuyPrice>
<BuyPrice>0</BuyPrice>
<SellPrice>0</SellPrice>
<Generation_no165>0,000</Generation_no165>
<Consumption_no165>0,000</Consumption_no165>
<Generation_165>0,000</Generation_165>
<Consumption_165>0,000</Consumption_165>

</ASResult>
</ZoneDetail>
</MarketDetail>
</MarketResultMB>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for MarketResultMB
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<simpleType name="resultType">
<restriction base="string">
<enumeration value="PRELIMINARY"/>
<enumeration value="DEFINITIVE"/>
</restriction>
</simpleType>
<simpleType name="localeDecimal0">
<restriction base="string">
<minLength value="0"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>

</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:MarketResultMB"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="required"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="required"/>
</complexType>
</element>
<element name="MarketResultMB">
<complexType>
<sequence>
<element name="Date" type="pd:dateType"/>
<element name="Market" type="pd:MarketType1"/>
<element name="Hour" type="pd:hourIntervalType"/>
<element ref="pd:MarketDetail"/>
</sequence>
</complexType>
</element>
<complexType name="MarketType1">
<simpleContent>

<extension base="pd:marketType">
<attribute name="type" type="pd:resultType" use="required"/>
</extension>
</simpleContent>
</complexType>
<element name="MarketDetail">
<complexType>
<sequence>
<element ref="pd:ZoneDetail" maxOccurs="unbounded"/>
</sequence>
</complexType>
</element>
<element name="ZoneDetail">
<complexType>
<sequence>
<element name="Zone" type="pd:zoneNameType"/>
<element ref="pd:RSResult"/>
<element ref="pd:ASResult"/>
</sequence>
</complexType>
</element>
<element name="RSResult">
<complexType>
<sequence>
<element name="MaxSellPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="MinBuyPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="BuyPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="SellPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="Generation_no165" type="pd:localeDecimal"/>
<element name="Consumption_no165" type="pd:localeDecimal"/>
<element name="Generation_165" type="pd:localeDecimal"/>
<element name="Consumption_165" type="pd:localeDecimal"/>
</sequence>
</complexType>
</element>
<element name="ASResult">
<complexType>
<sequence>
<element name="MaxSellPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="MinBuyPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="BuyPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="SellPrice" type="pd:localeDecimal0" minOccurs="0"/>
<element name="Generation_no165" type="pd:localeDecimal"/>
<element name="Consumption_no165" type="pd:localeDecimal"/>
<element name="Generation_165" type="pd:localeDecimal"/>
<element name="Consumption_165" type="pd:localeDecimal"/>
</sequence>

</complexType>
</element>
</schema>

PIPEFunctionalAcknowledgement (rif. TransactionAcknowledgement-xsd)
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEFunctionalAcknowledgement xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEFunctionalAcknowledgementv1_0.xsd"
ReferenceNumber="21360000298986" OriginalReferenceNumber="MGPoPRIMOP20020516165855" Status="Accept"
CreationDate="20020516170659" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>PRIMO OPERATORE</CompanyName>
<CompanyIdentifier>PRIMOP</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<TransactionAcknowledgement Status = "Accept" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001040687"/>
<TransactionAcknowledgement Status = "Accept" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001040688"/>
<TransactionAcknowledgement Status = "Accept" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001040689"/>
<TransactionAcknowledgement Status = "Accept" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001040690"/>

•
• [etc.]
•
<TransactionAcknowledgement Status = "Accept" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001042582"/>
<TransactionAcknowledgement Status = "Reject" PIPTransactionType = "BidSubmittal" OriginalReferenceNumber =
"21360001039417">
<RejectInformation>
<Reason></Reason>
<ReasonText>The market session is not open, therefore, the (BidSubmittal) with reference number
(21360001039417) cannot be posted.</ReasonText>
</RejectInformation>
</TransactionAcknowledgement>
</PIPEFunctionalAcknowledgement>

PIPEFunctionalAcknowledgementv1_0.xsd for TransactionAcknowledgement
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- PIPE Functional Acknowledgement Schema -->
<!-- Apache Schema -->
<!-- $Revision: 2 $ -->
<!-- GME JSP -->
<!--Copyright (C) Excelergy Corporation, 1998-2003. All rights reserved. Excelergy is a registered trademark of Excelergy Corporation.-->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<simpleType name="statusTransactionType1">
<restriction base="string">
<enumeration value="Accept"/>
<enumeration value="Reject"/>
<enumeration value="Partial"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEFunctionalAcknowledgement">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:RejectInformation" minOccurs="0" maxOccurs="unbounded"/>
<element ref="pd:TransactionAcknowledgement" minOccurs="0" maxOccurs="unbounded"/>
</sequence>
<attribute name="Version" type="string" use="required"/>
<attribute name="ReferenceNumber" type="pd:lengthFortyType" use="required"/>
<attribute name="OriginalReferenceNumber" type="pd:lengthFortyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Status" type="pd:statusTransactionType1" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>

</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="pd:lengthSixtyType"/>
<element name="CompanyIdentifier" type="pd:lengthEightyType"/>
<element name="ApplicationName" type="pd:lengthSixtyType" minOccurs="0"/>
</sequence>
<attribute name="PartnerType" type="pd:partnerTypeType" use="required"/>
</complexType>
</element>
<element name="RejectInformation">
<complexType>
<sequence>
<element name="Reason" type="string"/>
<element name="ReasonText" type="pd:length250Type" minOccurs="0"/>
</sequence>
</complexType>
</element>
<element name="TransactionAcknowledgement">
<complexType>
<sequence>
<element ref="pd:RejectInformation" minOccurs="0" maxOccurs="unbounded"/>
</sequence>
<attribute name="OriginalReferenceNumber" type="pd:lengthThirtyFiveType" use="required"/>
<attribute name="Status" type="pd:statusTransactionType" use="required"/>
<attribute name="PIPTransactionType" type="pd:pipTransctionType" use="optional"/>
<attribute name="MarketParticipantNumber" type="pd:lengthThirtyFiveType" use="optional"/>
</complexType>
</element>
</schema>

Fattura
<?xml version='1.0' encoding='ISO-8859-1' ?>
<PIPEDocument xmlns = "urn:XML-PIPE"
xmlns:xsi = "http://www.w3.org/2001/XMLSchema-instance"
xsi:schemaLocation = "urn:XML-PIPE PIPEDocument.xsd"
ReferenceNumber="*********" CreationDate="********112844" Version="1.0">
<TradingPartnerDirectory>
<Sender>
<TradingPartner PartnerType = "Operator">
<CompanyName>GME</CompanyName>
<CompanyIdentifier>IDGME</CompanyIdentifier>
</TradingPartner>
</Sender>
<Recipient>
<TradingPartner PartnerType = "Market Participant">
<CompanyName>AcquirenteUnico</CompanyName>
<CompanyIdentifier>IDAU</CompanyIdentifier>
</TradingPartner>
</Recipient>
</TradingPartnerDirectory>
<PIPTransaction ReferenceNumber ="PIPRFN2004116102841539" InboundMessageCreationDate ="********"
InboundMessageCreationTime ="*********">
<Fattura>
<DOCUMENT>C</DOCUMENT>
<DOCUMENT_ID>**************</DOCUMENT_ID>
<HeaderFattura>
<ABP_ID>**************</ABP_ID>
<ACCOUNT_NUMBER>1749053</ACCOUNT_NUMBER>
<DOCUMENT_DATE>********</DOCUMENT_DATE>
<DOCUMENT_TYPE>ME</DOCUMENT_TYPE>
<TRX_TYPE>BID</TRX_TYPE>
<PERIOD>012004</PERIOD>
<TAX_REFERENCE_FROM>1234567890123456789</TAX_REFERENCE_FROM>
<OP_NAME_FROM>Gestore del Mercato Elettrico S.P.A.</OP_NAME_FROM>
<SDC_CODE_FROM>IDGME</SDC_CODE_FROM>

<STREET_FROM>Via Palmiano 101</STREET_FROM>
<CITY_FROM>Roma</CITY_FROM>
<PROVINCE_FROM>RM</PROVINCE_FROM>
<ZIPCODE_FROM>00197</ZIPCODE_FROM>
<COUNTRY_FROM>ITA</COUNTRY_FROM>
<LEGAL_NOTES_FROM/>
<PHONE_FROM>55-55-555-5555</PHONE_FROM>
<FAX_FROM/>
<EMAIL_FROM/>
<DOCUMENT_OBJECT>Operazioni svolte sul mercato elettrico nel periodo indicato</DOCUMENT_OBJECT>
<TAX_INFO/>
<PAYMENT_INFO>Pagamenti come da art. 66 e seguenti Disciplina del Mercato Elettrico</PAYMENT_INFO>
<INVOICE_NOTE1/>
<TAX_REFERENCE_TO/>
<OP_NAME_TO>AcquirenteUnico</OP_NAME_TO>
<SDC_CODE_TO>IDAU</SDC_CODE_TO>
<STREET_TO>x</STREET_TO>
<CITY_TO>x</CITY_TO>
<PROVINCE_TO>RM</PROVINCE_TO>
<ZIPCODE_TO>X</ZIPCODE_TO>
<COUNTRY_TO>ITA</COUNTRY_TO>
<STREET_TO_2/>
<CITY_TO_2/>
<PROVINCE_TO_2/>
<ZIPCODE_TO_2/>
<COUNTRY_TO_2/>
<AMOUNT>21.505.517,82</AMOUNT>
<TAX_AMOUNT>4.301.103,56</TAX_AMOUNT>
<TOTAL_AMOUNT>25.806.621,38</TOTAL_AMOUNT>
<QUANTITY>1.512,000</QUANTITY>
<INVOICE_NUMBER/>
<INVOICE_DATE/>
<INVOICE_DUE_DATE/>
</HeaderFattura>
<Summary1>
<AMOUNT>21.505.517,82</AMOUNT>
<TAX_CODE>V1</TAX_CODE>
<TAX_AMOUNT>4.301.103,56</TAX_AMOUNT>
<TOTAL_AMOUNT>25.806.621,38</TOTAL_AMOUNT>
<QUANTITY>1.512,000</QUANTITY>

</Summary1>
<Summary2>
<TAX_CODE>V1</TAX_CODE>
<MARKET>MGP</MARKET>
<AMOUNT>8.492.809,50</AMOUNT>
<QUANTITY>812,000</QUANTITY>
</Summary2>
<Summary2>
<TAX_CODE>V1</TAX_CODE>
<MARKET>MI1</MARKET>
<AMOUNT>13.012.708,32</AMOUNT>
<QUANTITY>700,000</QUANTITY>
</Summary2>
<Summary3>
<TAX_CODE>V1</TAX_CODE>
<MARKET>MGP</MARKET>
<UNIT_CODE/>
<UNIT_TYPE>CONS</UNIT_TYPE>
<FLOW_DATE>********</FLOW_DATE>
<UNIT_OF_MEASURE>MWH</UNIT_OF_MEASURE>
<AMOUNT>8.492.809,50</AMOUNT>
<QUANTITY>812,000</QUANTITY>
</Summary3>
<Summary3>
<TAX_CODE>V1</TAX_CODE>
<MARKET>MI1</MARKET>
<UNIT_CODE/>
<UNIT_TYPE>CONS</UNIT_TYPE>
<FLOW_DATE>********</FLOW_DATE>
<UNIT_OF_MEASURE>MWH</UNIT_OF_MEASURE>
<AMOUNT>13.012.708,32</AMOUNT>
<QUANTITY>700,000</QUANTITY>
</Summary3>
<ElencoLinee>
<Linea>
<UNIT_TYPE>CONS</UNIT_TYPE>
<UNIT_CODE>IdAuBelluno</UNIT_CODE>
<MARKET>MGP</MARKET>
<SUPPLY_CODE>40140002898948</SUPPLY_CODE>
<TAX_CODE>V1</TAX_CODE>

<FLOW_DATE>********</FLOW_DATE>
<FLOW_HOUR>1</FLOW_HOUR>
<UNIT_OF_MEASURE>MWH</UNIT_OF_MEASURE>
<QUANTITY>142,824</QUANTITY>
<UNIT_SELLING_PRICE>67</UNIT_SELLING_PRICE>
<LINE_AMOUNT>9.569,21</LINE_AMOUNT>
</Linea>
<Linea>
<UNIT_TYPE>CONS</UNIT_TYPE>
<UNIT_CODE>IdAuFoggia</UNIT_CODE>
<MARKET>MGP</MARKET>
<SUPPLY_CODE>40140002898828</SUPPLY_CODE>
<TAX_CODE>V1</TAX_CODE>
<FLOW_DATE>********</FLOW_DATE>
<FLOW_HOUR>1</FLOW_HOUR>
<UNIT_OF_MEASURE>MWH</UNIT_OF_MEASURE>
<QUANTITY>350,357</QUANTITY>
<UNIT_SELLING_PRICE>67</UNIT_SELLING_PRICE>
<LINE_AMOUNT>23.473,92</LINE_AMOUNT>
</Linea>
•
• [etc.]
•
<Linea>
<UNIT_TYPE>CONS</UNIT_TYPE>
<UNIT_CODE>IdAuCatania</UNIT_CODE>
<MARKET>MI1</MARKET>
<SUPPLY_CODE>40140002909032</SUPPLY_CODE>
<TAX_CODE>V1</TAX_CODE>
<FLOW_DATE>********</FLOW_DATE>
<FLOW_HOUR>24</FLOW_HOUR>
<UNIT_OF_MEASURE>MWH</UNIT_OF_MEASURE>
<QUANTITY>199,898</QUANTITY>
<UNIT_SELLING_PRICE>45</UNIT_SELLING_PRICE>
<LINE_AMOUNT>8.995,41</LINE_AMOUNT>
</Linea>
</ElencoLinee>

</Fattura>
</PIPTransaction></PIPEDocument>

PIPEDocument.xsd for Fattura
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
<include schemaLocation="SimpleTypesv1_0.xsd"/>
<simpleType name="creationDateTimeType">
<restriction base="integer">
<minInclusive value="19000000000000"/>
<maxInclusive value="21000000000000"/>
</restriction>
</simpleType>
<!-- Body -->
<element name="PIPEDocument">
<complexType>
<sequence>
<element ref="pd:TradingPartnerDirectory"/>
<element ref="pd:PIPTransaction" maxOccurs="unbounded"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="CreationDate" type="pd:creationDateTimeType" use="required"/>
<attribute name="Version" type="string" use="required"/>
</complexType>
</element>
<element name="TradingPartnerDirectory">
<complexType>
<sequence>
<element ref="pd:Sender"/>
<element ref="pd:Recipient"/>
</sequence>
</complexType>
</element>
<element name="Sender">
<complexType>
<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="Recipient">
<complexType>

<sequence>
<element ref="pd:TradingPartner"/>
</sequence>
</complexType>
</element>
<element name="TradingPartner">
<complexType>
<sequence>
<element name="CompanyName" type="string"/>
<element name="CompanyIdentifier" type="string"/>
</sequence>
<attribute name="PartnerType" type="string"/>
</complexType>
</element>
<element name="PIPTransaction">
<complexType>
<sequence>
<element ref="pd:Fattura"/>
</sequence>
<attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required"/>
<attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional"/>
<attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional"/>
</complexType>
</element>
<element name="Fattura">
<complexType>
<sequence>
<element name="DOCUMENT" type="string"/>
<element name="DOCUMENT_ID" type="string"/>
<element ref="pd:HeaderFattura"/>
<element name="Summary1" minOccurs="0" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="AMOUNT" type="string"/>
<element name="TAX_CODE" type="string"/>
<element name="TAX_AMOUNT" type="string"/>
<element name="TOTAL_AMOUNT" type="string"/>
<element name="QUANTITY" type="string"/>
</sequence>
</complexType>
</element>
<element name="Summary2" minOccurs="0" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="TAX_CODE" type="string"/>
<element name="MARKET" type="string"/>
<element name="AMOUNT" type="string"/>

<element name="QUANTITY" type="string"/>
</sequence>
</complexType>
</element>
<element name="Summary3" minOccurs="0" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="TAX_CODE" type="string"/>
<element name="MARKET" type="string"/>
<element name="UNIT_CODE" type="string"/>
<element name="UNIT_TYPE" type="string"/>
<element name="FLOW_DATE" type="string"/>
<element name="UNIT_OF_MEASURE" type="string"/>
<element name="AMOUNT" type="string"/>
<element name="QUANTITY" type="string"/>
</sequence>
</complexType>
</element>
<element ref="pd:ElencoLinee"/>
</sequence>
</complexType>
</element>
<element name="HeaderFattura">
<complexType>
<sequence>
<element name="ABP_ID" type="string"/>
<element name="ACCOUNT_NUMBER" type="string"/>
<element name="DOCUMENT_DATE" type="string"/>
<element name="DOCUMENT_TYPE" type="string"/>
<element name="TRX_TYPE" type="string"/>
<element name="PERIOD" type="string"/>
<element name="TAX_REFERENCE_FROM" type="string"/>
<element name="OP_NAME_FROM" type="string"/>
<element name="SDC_CODE_FROM" type="string"/>
<element name="STREET_FROM" type="string"/>
<element name="CITY_FROM" type="string"/>
<element name="PROVINCE_FROM" type="string"/>
<element name="ZIPCODE_FROM" type="string"/>
<element name="COUNTRY_FROM" type="string"/>
<element name="LEGAL_NOTES_FROM" type="string"/>
<element name="PHONE_FROM" type="string"/>
<element name="FAX_FROM" type="string"/>
<element name="EMAIL_FROM" type="string"/>
<element name="DOCUMENT_OBJECT" type="string"/>
<element name="TAX_INFO" type="string"/>
<element name="PAYMENT_INFO" type="string"/>
<element name="INVOICE_NOTE1" type="string"/>

<element name="TAX_REFERENCE_TO" type="string"/>
<element name="OP_NAME_TO" type="string"/>
<element name="SDC_CODE_TO" type="string"/>
<element name="STREET_TO" type="string"/>
<element name="CITY_TO" type="string"/>
<element name="PROVINCE_TO" type="string"/>
<element name="ZIPCODE_TO" type="string"/>
<element name="COUNTRY_TO" type="string"/>
<element name="STREET_TO_2" type="string"/>
<element name="CITY_TO_2" type="string"/>
<element name="PROVINCE_TO_2" type="string"/>
<element name="ZIPCODE_TO_2" type="string"/>
<element name="COUNTRY_TO_2" type="string"/>
<element name="AMOUNT" type="string"/>
<element name="TAX_AMOUNT" type="string"/>
<element name="QUANTITY" type="string"/>
<element name="TOTAL_AMOUNT" type="string"/>
<element name="INVOICE_NUMBER" type="string"/>
<element name="INVOICE_DATE" type="string"/>
<element name="INVOICE_DUE_DATE" type="string"/>
</sequence>
</complexType>
</element>
<element name="ElencoLinee">
<complexType>
<sequence>
<element name="Linea" minOccurs="0" maxOccurs="unbounded">
<complexType>
<sequence>
<element name="UNIT_TYPE" type="string"/>
<element name="UNIT_CODE" type="string"/>
<element name="MARKET" type="string"/>
<element name="SUPPLY_CODE" type="string"/>
<element name="TAX_CODE" type="string"/>
<element name="FLOW_DATE" type="string"/>
<element name="FLOW_HOUR" type="string"/>
<element name="UNIT_OF_MEASURE" type="string"/>
<element name="QUANTITY" type="string"/>
<element name="UNIT_SELLING_PRICE" type="string"/>
<element name="LINE_AMOUNT" type="string"/>
</sequence>
</complexType>
</element>
</sequence>
</complexType>
</element>
</schema>

SimpleTypesv1_0.xsd (to be included in every schema)
<?xml version="1.0" encoding="ISO-8859-1"?>
<!-- Simple Types Schema for Xerces C++-->
<!-- PIPE version="1.0" -->
<!-- $Revision: 57 $ -->
<!-- Shared -->
<schema xmlns="http://www.w3.org/2001/XMLSchema"
targetNamespace = "urn:XML-PIPE"
xmlns:pd = "urn:XML-PIPE"
elementFormDefault="qualified">
<simpleType name="creationDateType">
<restriction base="integer">
<minInclusive value="19000000"/>
<maxInclusive value="21000000"/>
</restriction>
</simpleType>
<simpleType name="creationTimeType">
<restriction base="integer">
<minInclusive value="0"/>
<maxInclusive value="23595999"/>
</restriction>
</simpleType>
<simpleType name="creationTimeMillisecsType">
<restriction base="integer">
<minInclusive value="0"/>
<maxInclusive value="235959999"/>
</restriction>
</simpleType>
<simpleType name="currencyEuroType">
<restriction base="string">
<enumeration value="Euro"/>
</restriction>
</simpleType>
<simpleType name="dateType">
<restriction base="integer">
<pattern value="\d{8}"/>
</restriction>

</simpleType>
<simpleType name="dealMakerType">
<restriction base="string">
<enumeration value="GME"/>
</restriction>
</simpleType>
<simpleType name="hourIntervalType">
<restriction base="integer">
<minInclusive value="1"/>
<maxInclusive value="25"/>
</restriction>
</simpleType>
<!--
-->
<simpleType name="quarterIntervalType">
<restriction base="integer">
<minInclusive value="1"/>
<maxInclusive value="4"/>
</restriction>
</simpleType>
<simpleType name="minuteType">
<restriction base="integer">
<minInclusive value="0"/>
<maxInclusive value="14"/>
</restriction>
</simpleType>
<simpleType name="integerFiveType">
<restriction base="integer">
<minInclusive value="1"/>
<maxInclusive value="99999"/>
</restriction>
</simpleType>
<simpleType name="integerFifteenType">
<restriction base="integer">
<minInclusive value="0"/>
<maxInclusive value="999999999999999"/>
</restriction>
</simpleType>
<simpleType name="integerTimeType">
<restriction base="integer">
<minInclusive value="0000"/>
<maxInclusive value="2400"/>

</restriction>
</simpleType>
<simpleType name="integerTimeSecondType">
<restriction base="integer">
<minInclusive value="000000"/>
<maxInclusive value="240000"/>
</restriction>
</simpleType>
<simpleType name="interval60Type">
<restriction base="string">
<enumeration value="60"/>
</restriction>
</simpleType>
<simpleType name="intervalType">
<restriction base="string">
<enumeration value="15"/>
<enumeration value="30"/>
<enumeration value="60"/>
</restriction>
</simpleType>
<simpleType name="lengthTwoType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="2"/>
</restriction>
</simpleType>
<simpleType name="lengthThreeType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="3"/>
</restriction>
</simpleType>
<simpleType name="lengthFiveType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="5"/>
</restriction>
</simpleType>
<simpleType name="lengthTenType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="10"/>
</restriction>
</simpleType>
<simpleType name="lengthFifteenType">
<restriction base="string">

<minLength value="1"/>
<maxLength value="15"/>
</restriction>
</simpleType>
<simpleType name="lengthTwentyType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="20"/>
</restriction>
</simpleType>
<simpleType name="lengthTwentyFourType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="24"/>
</restriction>
</simpleType>
<simpleType name="lengthTwentyFiveType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="25"/>
</restriction>
</simpleType>
<simpleType name="lengthThirtyType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="30"/>
</restriction>
</simpleType>
<simpleType name="lengthThirtyFiveType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="35"/>
</restriction>
</simpleType>
<simpleType name="lengthFortyType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="40"/>
</restriction>
</simpleType>
<simpleType name="lengthFiftyType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="50"/>
</restriction>
</simpleType>
<simpleType name="lengthSixtyType">

<restriction base="string">
<minLength value="1"/>
<maxLength value="60"/>
</restriction>
</simpleType>
<simpleType name="lengthSixtyFourType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="64"/>
</restriction>
</simpleType>
<simpleType name="lengthEightyType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="80"/>
</restriction>
</simpleType>
<simpleType name="length132Type">
<restriction base="string">
<minLength value="1"/>
<maxLength value="132"/>
</restriction>
</simpleType>
<simpleType name="length250Type">
<restriction base="string">
<minLength value="1"/>
<maxLength value="250"/>
</restriction>
</simpleType>
<simpleType name="lengthFiveHundredType">
<restriction base="string">
<minLength value="1"/>
<maxLength value="500"/>
</restriction>
</simpleType>
<simpleType name="localeDecimal">
<restriction base="string">
<minLength value="1"/>
</restriction>
</simpleType>
<simpleType name="fifteenMinuteScheduleMarketType">
<restriction base="string">
<enumeration value = "MSD1"/>
<enumeration value = "MSD2"/>
<enumeration value = "MSD3"/>
<enumeration value = "MSD4"/>

<enumeration value = "MSD5"/>
<enumeration value = "MSD6"/>
</restriction>
</simpleType>
<simpleType name="marketType">
<restriction base="string">
<enumeration value = "MGP"/>
<enumeration value = "MI1"/>
<enumeration value = "MI2"/>
<enumeration value = "MI3"/>
<enumeration value = "MI4"/>
<enumeration value = "MI5"/>
<enumeration value = "MI6"/>
<enumeration value = "MI7"/>
<enumeration value = "MB"/>
<enumeration value = "MSD1"/>
<enumeration value = "MSD2"/>
<enumeration value = "MSD3"/>
<enumeration value = "MSD4"/>
<enumeration value = "MSD5"/>
<enumeration value = "MSD6"/>
<enumeration value = "MB1"/>
<enumeration value = "MB2"/>
<enumeration value = "MB3"/>
<enumeration value = "MB4"/>
<enumeration value = "MB5"/>
<enumeration value = "MB6"/>
</restriction>
</simpleType>
<simpleType name="marketBidType">
<restriction base="string">
<enumeration value = "MGP"/>
<enumeration value = "MI1"/>
<enumeration value = "MI2"/>
<enumeration value = "MI3"/>
<enumeration value = "MI4"/>
<enumeration value = "MI5"/>
<enumeration value = "MI6"/>
<enumeration value = "MI7"/>
<enumeration value = "MSD1"/>
<enumeration value = "MB2"/>
<enumeration value = "MB3"/>
<enumeration value = "MB4"/>
<enumeration value = "MB5"/>
<enumeration value = "MB6"/>

</restriction>
</simpleType>
<simpleType name="marketMGPType">
<restriction base="string">
<enumeration value = "MGP"/>
</restriction>
</simpleType>
<simpleType name="conventionalPriceMarketType">
<restriction base="string">
<enumeration value = "MGP"/>
</restriction>
</simpleType>
<simpleType name="noType">
<restriction base="string">
<enumeration value="No"/>
</restriction>
</simpleType>
<simpleType name="onOffType">
<restriction base="string">
<enumeration value="On"/>
<enumeration value="Off"/>
</restriction>
</simpleType>
<simpleType name="partnerDistributorType">
<restriction base="string">
<enumeration value="Distributor"/>
</restriction>
</simpleType>
<simpleType name="partnerTypeType">
<restriction base="string">
<enumeration value="EnergySupplier"/>
<enumeration value="Distributor"/>
<enumeration value="Operator"/>
<enumeration value="Market Participant"/>
<enumeration value="Tax Exempt Market Participant"/>
</restriction>
</simpleType>
<simpleType name="pipTransctionType">
<restriction base="string">
<enumeration value="ApplicationAdvice"/>
<enumeration value="BidSubmittal"/>

<enumeration value="BidAwardResponse"/>
<enumeration value="BidAwardRequest"/>
<enumeration value="BidNotification"/>
<enumeration value="ConventionalUtilizationFactor"/>
<enumeration value="EnergyUtilizationCoefficient"/>
<enumeration value="EstimatedDemandInformation"/>
<enumeration value="EstimatedPriceInformation"/>
<enumeration value="RelevantExchangePoint"/>
<enumeration value="UnitInformation"/>
<enumeration value="UnitMargins"/>
<enumeration value="UnitSchedule"/>
<enumeration value="FifteenMinuteSchedule"/>
<enumeration value="ZoneInformation"/>
</restriction>
</simpleType>
<simpleType name="purposeSellType">
<restriction base="string">
<enumeration value="Sell"/>
</restriction>
</simpleType>
<simpleType name="purposeTradeType">
<restriction base="string">
<enumeration value="Buy"/>
<enumeration value="Sell"/>
</restriction>
</simpleType>
<simpleType name="statusAcceptType">
<restriction base="string">
<enumeration value="Accept"/>
</restriction>
</simpleType>
<simpleType name="statusTransactionType">
<restriction base="string">
<enumeration value="Accept"/>
<enumeration value="Reject"/>
</restriction>
</simpleType>
<simpleType name="statusRejectType">
<restriction base="string">
<enumeration value="Reject"/>
</restriction>
</simpleType>
<simpleType name="termHourType">

<restriction base="string">
<enumeration value="Hour"/>
</restriction>
</simpleType>
<simpleType name="termRestrictedType">
<restriction base="string">
<enumeration value="Hour"/>
<enumeration value="Day"/>
</restriction>
</simpleType>
<simpleType name="timeType">
<restriction base="integer">
<pattern value="\d{4}"/>
</restriction>
</simpleType>
<simpleType name="trueFalseType">
<restriction base="string">
<enumeration value="True"/>
<enumeration value="False"/>
</restriction>
</simpleType>
<simpleType name="unitScheduleType">
<restriction base="string">
<enumeration value="PreliminaryProvisional"/>
<enumeration value="Preliminary"/>
<enumeration value="FirstFinal"/>
<enumeration value="FirstUpdated"/>
</restriction>
</simpleType>
<simpleType name="fifteenMinutesScheduleType">
<restriction base="string">
<enumeration value="Original"/>
<enumeration value="Corrected"/>
</restriction>
</simpleType>
<simpleType name="bidTaxCodeType">
<restriction base="string">
<enumeration value="V1"/>
<enumeration value="V2"/>
<enumeration value="V3"/>
<enumeration value="V4"/>
<enumeration value="V5"/>
<enumeration value="V8"/>

<enumeration value="VC"/>
<enumeration value="VS"/>
<enumeration value="VR"/>
<enumeration value="NC"/>
</restriction>
</simpleType>
<simpleType name="offerTaxCodeType">
<restriction base="string">
<enumeration value="A1"/>
<enumeration value="A2"/>
<enumeration value="A3"/>
<enumeration value="A4"/>
<enumeration value="A5"/>
<enumeration value="A6"/>
<enumeration value="A7"/>
<enumeration value="A8"/>
<enumeration value="AL"/>
<enumeration value="AN"/>
<enumeration value="AS"/>
<enumeration value="AR"/>
<enumeration value="NC"/>
</restriction>
</simpleType>
<simpleType name="feeTaxCodeType">
<restriction base="string">
<enumeration value="V1"/>
<enumeration value="V3"/>
</restriction>
</simpleType>
<simpleType name="unitOfMeasureBidType">
<restriction base="string">
<enumeration value="MW"/>
<enumeration value="MWh"/>
</restriction>
</simpleType>
<simpleType name="unitOfMeasureMWhType">
<restriction base="string">
<enumeration value="MWh"/>
</restriction>
</simpleType>
<simpleType name="yesNoType">
<restriction base="string">
<enumeration value="Yes"/>

<enumeration value="No"/>
</restriction>
</simpleType>
<simpleType name="UnitYESNOType">
<restriction base="string">
<enumeration value="YES"/>
<enumeration value="NO"/>
</restriction>
</simpleType>
<simpleType name="zoneNameType">
<restriction base="string">
<enumeration value="CNOR"/>
<enumeration value="CSUD"/>
<enumeration value="NORD"/>
<enumeration value="SARD"/>
<enumeration value="SICI"/>
<enumeration value="COAC"/>
<enumeration value="GREC"/>
<enumeration value="AUST"/>
<enumeration value="SVIZ"/>
<enumeration value="CORS"/>
<enumeration value="SLOV"/>
<enumeration value="FRAN"/>
<enumeration value="ROSN"/>
<enumeration value="SUD"/>
<enumeration value="BSP"/>
<enumeration value="MALT"/>
<enumeration value="MONT"/>
<enumeration value="XAUS"/>
<enumeration value="XFRA"/>
<enumeration value="XSVI"/>
</restriction>
</simpleType>
<simpleType name="mustRunType">
<restriction base="string">
<enumeration value = "MustRun"/>
<enumeration value = "InternationalExchanges"/>
<enumeration value = "BilateralContracts"/>
<enumeration value = "AllMustRunTypes"/>
<enumeration value = "AllTypesExceptBilateralContracts"/>
<enumeration value = "EssentialforSystemSecurity"/>
<enumeration value = "CIP6"/>
<enumeration value = "NotprogrammablerenewableSources"/>
<enumeration value = "OtherrenewableSources"/>
<enumeration value = "Cogeneration"/>
<enumeration value = "NationalSources"/>
</restriction>

</simpleType>
<simpleType name="scopeType">
<restriction base="string">
<enumeration value="RS"/>
<enumeration value="AS"/>
<enumeration value="GR1"/>
<enumeration value="GR2"/>
<enumeration value="GR3"/>
<enumeration value="GR4"/>
<enumeration value="AC"/>
<enumeration value="CA"/>
</restriction>
</simpleType>
<simpleType name="quarterType">
<restriction base="string">
<enumeration value="1"/>
<enumeration value="2"/>
<enumeration value="3"/>
<enumeration value="4"/>
</restriction>
</simpleType>
<simpleType name="bAType">
<restriction base="string">
<enumeration value="Rev"/>
<enumeration value="Norev"/>
<enumeration value="Netting"/>
</restriction>
</simpleType>
</schema>