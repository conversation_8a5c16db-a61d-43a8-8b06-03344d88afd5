<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
  <include schemaLocation="SimpleTypesv1_0.xsd" />
  <simpleType name="creationDateTimeType">
    <restriction base="integer">
      <minInclusive value="19000000000000" />
      <maxInclusive value="21000000000000" />
    </restriction>
  </simpleType>
  <!-- Body -->
  <element name="PIPEDocument">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartnerDirectory" />
        <element ref="pd:PIPTransaction" maxOccurs="unbounded" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="CreationDate" type="pd:creationDateTimeType" use="required" />
      <attribute name="Version" type="string" use="required" />
    </complexType>
  </element>
  <element name="TradingPartnerDirectory">
    <complexType>
      <sequence>
        <element ref="pd:Sender" />
        <element ref="pd:Recipient" />
      </sequence>
    </complexType>
  </element>
  <element name="Sender">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="Recipient">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="TradingPartner">
    <complexType>
      <sequence>
        <element name="CompanyName" type="string" />
        <element name="CompanyIdentifier" type="string" />
      </sequence>
      <attribute name="PartnerType" type="string" />
    </complexType>
  </element>
  <element name="PIPTransaction">
    <complexType>
      <sequence>
        <element ref="pd:Fattura" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional" />
      <attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional" />
    </complexType>
  </element>
  <element name="Fattura">
    <complexType>
      <sequence>
        <element name="DOCUMENT" type="string" />
        <element name="DOCUMENT_ID" type="string" />
        <element ref="pd:HeaderFattura" />
        <element name="Summary1" minOccurs="0" maxOccurs="unbounded">
          <complexType>
            <sequence>
              <element name="AMOUNT" type="string" />
              <element name="TAX_CODE" type="string" />
              <element name="TAX_AMOUNT" type="string" />
              <element name="TOTAL_AMOUNT" type="string" />
              <element name="QUANTITY" type="string" />
            </sequence>
          </complexType>
        </element>
        <element name="Summary2" minOccurs="0" maxOccurs="unbounded">
          <complexType>
            <sequence>
              <element name="TAX_CODE" type="string" />
              <element name="MARKET" type="string" />
              <element name="AMOUNT" type="string" />
              <element name="QUANTITY" type="string" />
            </sequence>
          </complexType>
        </element>
        <element name="Summary3" minOccurs="0" maxOccurs="unbounded">
          <complexType>
            <sequence>
              <element name="TAX_CODE" type="string" />
              <element name="MARKET" type="string" />
              <element name="UNIT_CODE" type="string" />
              <element name="UNIT_TYPE" type="string" />
              <element name="FLOW_DATE" type="string" />
              <element name="UNIT_OF_MEASURE" type="string" />
              <element name="AMOUNT" type="string" />
              <element name="QUANTITY" type="string" />
            </sequence>
          </complexType>
        </element>
        <element ref="pd:ElencoLinee" />
      </sequence>
    </complexType>
  </element>
  <element name="HeaderFattura">
    <complexType>
      <sequence>
        <element name="ABP_ID" type="string" />
        <element name="ACCOUNT_NUMBER" type="string" />
        <element name="DOCUMENT_DATE" type="string" />
        <element name="DOCUMENT_TYPE" type="string" />
        <element name="TRX_TYPE" type="string" />
        <element name="PERIOD" type="string" />
        <element name="TAX_REFERENCE_FROM" type="string" />
        <element name="OP_NAME_FROM" type="string" />
        <element name="SDC_CODE_FROM" type="string" />
        <element name="STREET_FROM" type="string" />
        <element name="CITY_FROM" type="string" />
        <element name="PROVINCE_FROM" type="string" />
        <element name="ZIPCODE_FROM" type="string" />
        <element name="COUNTRY_FROM" type="string" />
        <element name="LEGAL_NOTES_FROM" type="string" />
        <element name="PHONE_FROM" type="string" />
        <element name="FAX_FROM" type="string" />
        <element name="EMAIL_FROM" type="string" />
        <element name="DOCUMENT_OBJECT" type="string" />
        <element name="TAX_INFO" type="string" />
        <element name="PAYMENT_INFO" type="string" />
        <element name="INVOICE_NOTE1" type="string" />
        <element name="TAX_REFERENCE_TO" type="string" />
        <element name="OP_NAME_TO" type="string" />
        <element name="SDC_CODE_TO" type="string" />
        <element name="STREET_TO" type="string" />
        <element name="CITY_TO" type="string" />
        <element name="PROVINCE_TO" type="string" />
        <element name="ZIPCODE_TO" type="string" />
        <element name="COUNTRY_TO" type="string" />
        <element name="STREET_TO_2" type="string" />
        <element name="CITY_TO_2" type="string" />
        <element name="PROVINCE_TO_2" type="string" />
        <element name="ZIPCODE_TO_2" type="string" />
        <element name="COUNTRY_TO_2" type="string" />
        <element name="AMOUNT" type="string" />
        <element name="TAX_AMOUNT" type="string" />
        <element name="QUANTITY" type="string" />
        <element name="TOTAL_AMOUNT" type="string" />
        <element name="INVOICE_NUMBER" type="string" />
        <element name="INVOICE_DATE" type="string" />
        <element name="INVOICE_DUE_DATE" type="string" />
      </sequence>
    </complexType>
  </element>
  <element name="ElencoLinee">
    <complexType>
      <sequence>
        <element name="Linea" minOccurs="0" maxOccurs="unbounded">
          <complexType>
            <sequence>
              <element name="UNIT_TYPE" type="string" />
              <element name="UNIT_CODE" type="string" />
              <element name="MARKET" type="string" />
              <element name="SUPPLY_CODE" type="string" />
              <element name="TAX_CODE" type="string" />
              <element name="FLOW_DATE" type="string" />
              <element name="FLOW_HOUR" type="string" />
              <element name="UNIT_OF_MEASURE" type="string" />
              <element name="QUANTITY" type="string" />
              <element name="UNIT_SELLING_PRICE" type="string" />
              <element name="LINE_AMOUNT" type="string" />
            </sequence>
          </complexType>
        </element>
      </sequence>
    </complexType>
  </element>
</schema>