<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified">
  <include schemaLocation="SimpleTypesv1_0.xsd" />
  <simpleType name="creationDateTimeType">
    <restriction base="integer">
      <minInclusive value="19000000000000" />
      <maxInclusive value="21000000000000" />
    </restriction>
  </simpleType>
  <!-- Body -->
  <element name="PIPEDocument">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartnerDirectory" />
        <element ref="pd:PIPTransaction" maxOccurs="unbounded" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="CreationDate" type="pd:creationDateTimeType" use="required" />
      <attribute name="Version" type="string" use="required" />
    </complexType>
  </element>
  <element name="TradingPartnerDirectory">
    <complexType>
      <sequence>
        <element ref="pd:Sender" />
        <element ref="pd:Recipient" />
      </sequence>
    </complexType>
  </element>
  <element name="Sender">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="Recipient">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="TradingPartner">
    <complexType>
      <sequence>
        <element name="CompanyName" type="string" />
        <element name="CompanyIdentifier" type="string" />
      </sequence>
      <attribute name="PartnerType" type="string" />
    </complexType>
  </element>
  <element name="PIPTransaction">
    <complexType>
      <sequence>
        <element ref="pd:ZoneInformation" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional" />
      <attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional" />
    </complexType>
  </element>
  <element name="ZoneInformation">
    <complexType>
      <sequence>
        <element name="Date" type="pd:dateType" />
        <element name="MarketDetail" minOccurs="1" maxOccurs="unbounded">
          <complexType>
            <sequence>
              <element name="Market" type="pd:marketType" />
              <element name="InterZonalDetail" minOccurs="1" maxOccurs="unbounded">
                <complexType>
                  <sequence>
                    <element name="FromZone" type="pd:zoneNameType" />
                    <element name="ToZone" type="pd:zoneNameType" />
                    <element name="InterZonalLimits" minOccurs="1" maxOccurs="25">
                      <complexType>
                        <sequence>
                          <element name="Hour" type="pd:hourIntervalType" />
                          <element name="ConnectionLimit" type="pd:localeDecimal" />
                          <element name="ConnectionCoefficient" type="string" />
                        </sequence>
                      </complexType>
                    </element>
                  </sequence>
                </complexType>
              </element>
              <element name="GeneralizedConstraintsDetail" minOccurs="0" maxOccurs="unbounded">
                <complexType>
                  <sequence>
                    <element name="GeneralizedConstraints" minOccurs="1" maxOccurs="unbounded">
                      <complexType>
                        <sequence>
                          <element name="Hour" type="pd:hourIntervalType" />
                          <element name="Value" type="pd:localeDecimal" />
                          <element name="ZoneSensitivity" minOccurs="0" maxOccurs="unbounded">
                            <complexType>
                              <sequence>
                                <element name="Zone" type="pd:zoneNameType" />
                                <element name="Value" type="pd:localeDecimal" />
                              </sequence>
                            </complexType>
                          </element>
                        </sequence>
                        <attribute name="Name" type="pd:lengthSixtyType" />
                      </complexType>
                    </element>
                  </sequence>
                </complexType>
              </element>
            </sequence>
          </complexType>
        </element>
      </sequence>
    </complexType>
  </element>
</schema>