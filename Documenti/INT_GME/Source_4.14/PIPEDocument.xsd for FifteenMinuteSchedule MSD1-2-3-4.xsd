<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema targetNamespace="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
  <include schemaLocation="SimpleTypesv1_0.xsd" />
  <simpleType name="creationDateTimeType">
    <restriction base="integer">
      <minInclusive value="19000000000000" />
      <maxInclusive value="21000000000000" />
    </restriction>
  </simpleType>
  <!-- Body -->
  <element name="PIPEDocument">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartnerDirectory" />
        <element ref="pd:PIPTransaction" maxOccurs="unbounded" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="CreationDate" type="pd:creationDateTimeType" use="required" />
      <attribute name="Version" type="string" use="required" />
    </complexType>
  </element>
  <element name="TradingPartnerDirectory">
    <complexType>
      <sequence>
        <element ref="pd:Sender" />
        <element ref="pd:Recipient" />
      </sequence>
    </complexType>
  </element>
  <element name="Sender">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="Recipient">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="TradingPartner">
    <complexType>
      <sequence>
        <element name="CompanyName" type="string" />
        <element name="CompanyIdentifier" type="string" />
      </sequence>
      <attribute name="PartnerType" type="string" />
    </complexType>
  </element>
  <element name="PIPTransaction">
    <complexType>
      <sequence>
        <element ref="pd:FifteenMinuteSchedule" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="optional" />
      <attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="optional" />
      <attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="optional" />
    </complexType>
  </element>
  <element name="FifteenMinuteSchedule">
    <complexType>
      <sequence>
        <element name="Market" type="pd:fifteenMinuteScheduleMarketType" />
        <element name="Date" type="pd:dateType" />
        <element name="UnitReferenceNumber" type="pd:lengthSixtyType" />
        <element name="HourDetail" minOccurs="23" maxOccurs="25">
          <complexType>
            <sequence>
              <element name="Hour" type="pd:hourIntervalType" />
              <element name="Quantity" minOccurs="4" maxOccurs="4">
                <complexType>
                  <simpleContent>
                    <extension base="pd:localeDecimal">
                      <attribute name="QuarterInterval" type="pd:quarterIntervalType" use="required" />
                      <attribute name="Minute" type="pd:minuteType" use="optional" />
                    </extension>
                  </simpleContent>
                </complexType>
              </element>
            </sequence>
          </complexType>
        </element>
      </sequence>
      <attribute name="MarketParticipantNumber" type="string" use="optional" />
      <attribute name="Type" type="pd:fifteenMinutesScheduleType" use="required" />
    </complexType>
  </element>
</schema>