<!-- UnitSchedule -->
<!-- PIPE version="1.0" -->
<!-- $Revision: 6 $ -->
<!-- GME JSP -->
<schema xmlns:pd="urn:XML-PIPE" xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" elementFormDefault="qualified">
  <include schemaLocation="SimpleTypesv1_0.xsd" />
  <simpleType name="creationDateTimeType">
    <restriction base="integer">
      <minInclusive value="19000000000000" />
      <maxInclusive value="21000000000000" />
    </restriction>
  </simpleType>
  <simpleType name="resultType">
    <restriction base="string">
      <enumeration value="PRELIMINARY" />
      <enumeration value="DEFINITIVE" />
    </restriction>
  </simpleType>
  <simpleType name="localeDecimal0">
    <restriction base="string">
      <minLength value="0" />
    </restriction>
  </simpleType>
  <!-- Body -->
  <element name="PIPEDocument">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartnerDirectory" />
        <element ref="pd:PIPTransaction" maxOccurs="unbounded" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="CreationDate" type="pd:creationDateTimeType" use="required" />
      <attribute name="Version" type="string" use="required" />
    </complexType>
  </element>
  <element name="TradingPartnerDirectory">
    <complexType>
      <sequence>
        <element ref="pd:Sender" />
        <element ref="pd:Recipient" />
      </sequence>
    </complexType>
  </element>
  <element name="Sender">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="Recipient">
    <complexType>
      <sequence>
        <element ref="pd:TradingPartner" />
      </sequence>
    </complexType>
  </element>
  <element name="TradingPartner">
    <complexType>
      <sequence>
        <element name="CompanyName" type="string" />
        <element name="CompanyIdentifier" type="string" />
      </sequence>
      <attribute name="PartnerType" type="string" />
    </complexType>
  </element>
  <element name="PIPTransaction">
    <complexType>
      <sequence>
        <element ref="pd:MarketResultMB" />
      </sequence>
      <attribute name="ReferenceNumber" type="pd:lengthThirtyType" use="required" />
      <attribute name="InboundMessageCreationDate" type="pd:creationDateType" use="required" />
      <attribute name="InboundMessageCreationTime" type="pd:creationTimeMillisecsType" use="required" />
    </complexType>
  </element>
  <element name="MarketResultMB">
    <complexType>
      <sequence>
        <element name="Date" type="pd:dateType" />
        <element name="Market" type="pd:MarketType1" />
        <element name="Hour" type="pd:hourIntervalType" />
        <element ref="pd:MarketDetail" />
      </sequence>
    </complexType>
  </element>
  <complexType name="MarketType1">
    <simpleContent>
      <extension base="pd:marketType">
        <attribute name="type" type="pd:resultType" use="required" />
      </extension>
    </simpleContent>
  </complexType>
  <element name="MarketDetail">
    <complexType>
      <sequence>
        <element ref="pd:ZoneDetail" maxOccurs="unbounded" />
      </sequence>
    </complexType>
  </element>
  <element name="ZoneDetail">
    <complexType>
      <sequence>
        <element name="Zone" type="pd:zoneNameType" />
        <element ref="pd:RSResult" />
        <element ref="pd:ASResult" />
      </sequence>
    </complexType>
  </element>
  <element name="RSResult">
    <complexType>
      <sequence>
        <element name="MaxSellPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="MinBuyPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="BuyPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="SellPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="Generation_no165" type="pd:localeDecimal" />
        <element name="Consumption_no165" type="pd:localeDecimal" />
        <element name="Generation_165" type="pd:localeDecimal" />
        <element name="Consumption_165" type="pd:localeDecimal" />
      </sequence>
    </complexType>
  </element>
  <element name="ASResult">
    <complexType>
      <sequence>
        <element name="MaxSellPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="MinBuyPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="BuyPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="SellPrice" type="pd:localeDecimal0" minOccurs="0" />
        <element name="Generation_no165" type="pd:localeDecimal" />
        <element name="Consumption_no165" type="pd:localeDecimal" />
        <element name="Generation_165" type="pd:localeDecimal" />
        <element name="Consumption_165" type="pd:localeDecimal" />
      </sequence>
    </complexType>
  </element>
</schema>