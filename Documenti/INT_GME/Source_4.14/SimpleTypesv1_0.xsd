<!-- Simple Types Schema for Xerces C++-->
<!-- PIPE version="1.0" -->
<!-- $Revision: 57 $ -->
<!-- Shared -->
<schema xmlns="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:XML-PIPE" xmlns:pd="urn:XML-PIPE" elementFormDefault="qualified">
  <simpleType name="creationDateType">
    <restriction base="integer">
      <minInclusive value="19000000" />
      <maxInclusive value="21000000" />
    </restriction>
  </simpleType>
  <simpleType name="creationTimeType">
    <restriction base="integer">
      <minInclusive value="0" />
      <maxInclusive value="23595999" />
    </restriction>
  </simpleType>
  <simpleType name="creationTimeMillisecsType">
    <restriction base="integer">
      <minInclusive value="0" />
      <maxInclusive value="235959999" />
    </restriction>
  </simpleType>
  <simpleType name="currencyEuroType">
    <restriction base="string">
      <enumeration value="Euro" />
    </restriction>
  </simpleType>
  <simpleType name="dateType">
    <restriction base="integer">
      <pattern value="\d{8}" />
    </restriction>
  </simpleType>
  <simpleType name="dealMakerType">
    <restriction base="string">
      <enumeration value="GME" />
    </restriction>
  </simpleType>
  <simpleType name="hourIntervalType">
    <restriction base="integer">
      <minInclusive value="1" />
      <maxInclusive value="25" />
    </restriction>
  </simpleType>
  <!--
-->
  <simpleType name="quarterIntervalType">
    <restriction base="integer">
      <minInclusive value="1" />
      <maxInclusive value="4" />
    </restriction>
  </simpleType>
  <simpleType name="minuteType">
    <restriction base="integer">
      <minInclusive value="0" />
      <maxInclusive value="14" />
    </restriction>
  </simpleType>
  <simpleType name="integerFiveType">
    <restriction base="integer">
      <minInclusive value="1" />
      <maxInclusive value="99999" />
    </restriction>
  </simpleType>
  <simpleType name="integerFifteenType">
    <restriction base="integer">
      <minInclusive value="0" />
      <maxInclusive value="999999999999999" />
    </restriction>
  </simpleType>
  <simpleType name="integerTimeType">
    <restriction base="integer">
      <minInclusive value="0000" />
      <maxInclusive value="2400" />
    </restriction>
  </simpleType>
  <simpleType name="integerTimeSecondType">
    <restriction base="integer">
      <minInclusive value="000000" />
      <maxInclusive value="240000" />
    </restriction>
  </simpleType>
  <simpleType name="interval60Type">
    <restriction base="string">
      <enumeration value="60" />
    </restriction>
  </simpleType>
  <simpleType name="intervalType">
    <restriction base="string">
      <enumeration value="15" />
      <enumeration value="30" />
      <enumeration value="60" />
    </restriction>
  </simpleType>
  <simpleType name="lengthTwoType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="2" />
    </restriction>
  </simpleType>
  <simpleType name="lengthThreeType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="3" />
    </restriction>
  </simpleType>
  <simpleType name="lengthFiveType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="5" />
    </restriction>
  </simpleType>
  <simpleType name="lengthTenType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="10" />
    </restriction>
  </simpleType>
  <simpleType name="lengthFifteenType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="15" />
    </restriction>
  </simpleType>
  <simpleType name="lengthTwentyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="20" />
    </restriction>
  </simpleType>
  <simpleType name="lengthTwentyFourType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="24" />
    </restriction>
  </simpleType>
  <simpleType name="lengthTwentyFiveType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="25" />
    </restriction>
  </simpleType>
  <simpleType name="lengthThirtyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="30" />
    </restriction>
  </simpleType>
  <simpleType name="lengthThirtyFiveType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="35" />
    </restriction>
  </simpleType>
  <simpleType name="lengthFortyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="40" />
    </restriction>
  </simpleType>
  <simpleType name="lengthFiftyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="50" />
    </restriction>
  </simpleType>
  <simpleType name="lengthSixtyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="60" />
    </restriction>
  </simpleType>
  <simpleType name="lengthSixtyFourType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="64" />
    </restriction>
  </simpleType>
  <simpleType name="lengthEightyType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="80" />
    </restriction>
  </simpleType>
  <simpleType name="length132Type">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="132" />
    </restriction>
  </simpleType>
  <simpleType name="length250Type">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="250" />
    </restriction>
  </simpleType>
  <simpleType name="lengthFiveHundredType">
    <restriction base="string">
      <minLength value="1" />
      <maxLength value="500" />
    </restriction>
  </simpleType>
  <simpleType name="localeDecimal">
    <restriction base="string">
      <minLength value="1" />
    </restriction>
  </simpleType>
  <simpleType name="fifteenMinuteScheduleMarketType">
    <restriction base="string">
      <enumeration value="MSD1" />
      <enumeration value="MSD2" />
      <enumeration value="MSD3" />
      <enumeration value="MSD4" />
      <enumeration value="MSD5" />
      <enumeration value="MSD6" />
    </restriction>
  </simpleType>
  <simpleType name="marketType">
    <restriction base="string">
      <enumeration value="MGP" />
      <enumeration value="MI1" />
      <enumeration value="MI2" />
      <enumeration value="MI3" />
      <enumeration value="MI4" />
      <enumeration value="MI5" />
      <enumeration value="MI6" />
      <enumeration value="MI7" />
      <enumeration value="MB" />
      <enumeration value="MSD1" />
      <enumeration value="MSD2" />
      <enumeration value="MSD3" />
      <enumeration value="MSD4" />
      <enumeration value="MSD5" />
      <enumeration value="MSD6" />
      <enumeration value="MBh" />
      <enumeration value="MRR" />
    </restriction>
  </simpleType>
  <simpleType name="marketBidType">
    <restriction base="string">
      <enumeration value="MGP" />
      <enumeration value="MI1" />
      <enumeration value="MI2" />
      <enumeration value="MI3" />
      <enumeration value="MI4" />
      <enumeration value="MI5" />
      <enumeration value="MI6" />
      <enumeration value="MI7" />
      <enumeration value="MSD1" />
      <enumeration value="MBh" />
      <enumeration value="MRR" />
    </restriction>
  </simpleType>
  <simpleType name="marketMGPType">
    <restriction base="string">
      <enumeration value="MGP" />
    </restriction>
  </simpleType>
  <simpleType name="conventionalPriceMarketType">
    <restriction base="string">
      <enumeration value="MGP" />
    </restriction>
  </simpleType>
  <simpleType name="noType">
    <restriction base="string">
      <enumeration value="No" />
    </restriction>
  </simpleType>
  <simpleType name="onOffType">
    <restriction base="string">
      <enumeration value="On" />
      <enumeration value="Off" />
    </restriction>
  </simpleType>
  <simpleType name="partnerDistributorType">
    <restriction base="string">
      <enumeration value="Distributor" />
    </restriction>
  </simpleType>
  <simpleType name="partnerTypeType">
    <restriction base="string">
      <enumeration value="EnergySupplier" />
      <enumeration value="Distributor" />
      <enumeration value="Operator" />
      <enumeration value="Market Participant" />
      <enumeration value="Tax Exempt Market Participant" />
    </restriction>
  </simpleType>
  <simpleType name="pipTransctionType">
    <restriction base="string">
      <enumeration value="ApplicationAdvice" />
      <enumeration value="BidSubmittal" />
      <enumeration value="BidAwardResponse" />
      <enumeration value="BidAwardRequest" />
      <enumeration value="BidNotification" />
      <enumeration value="ConventionalUtilizationFactor" />
      <enumeration value="EnergyUtilizationCoefficient" />
      <enumeration value="EstimatedDemandInformation" />
      <enumeration value="EstimatedPriceInformation" />
      <enumeration value="RelevantExchangePoint" />
      <enumeration value="UnitInformation" />
      <enumeration value="UnitMargins" />
      <enumeration value="UnitSchedule" />
      <enumeration value="FifteenMinuteSchedule" />
      <enumeration value="ZoneInformation" />
    </restriction>
  </simpleType>
  <simpleType name="purposeSellType">
    <restriction base="string">
      <enumeration value="Sell" />
    </restriction>
  </simpleType>
  <simpleType name="purposeTradeType">
    <restriction base="string">
      <enumeration value="Buy" />
      <enumeration value="Sell" />
    </restriction>
  </simpleType>
  <simpleType name="statusAcceptType">
    <restriction base="string">
      <enumeration value="Accept" />
    </restriction>
  </simpleType>
  <simpleType name="statusTransactionType">
    <restriction base="string">
      <enumeration value="Accept" />
      <enumeration value="Reject" />
    </restriction>
  </simpleType>
  <simpleType name="statusRejectType">
    <restriction base="string">
      <enumeration value="Reject" />
    </restriction>
  </simpleType>
  <simpleType name="termHourType">
    <restriction base="string">
      <enumeration value="Hour" />
    </restriction>
  </simpleType>
  <simpleType name="termRestrictedType">
    <restriction base="string">
      <enumeration value="Hour" />
      <enumeration value="Day" />
    </restriction>
  </simpleType>
  <simpleType name="timeType">
    <restriction base="integer">
      <pattern value="\d{4}" />
    </restriction>
  </simpleType>
  <simpleType name="trueFalseType">
    <restriction base="string">
      <enumeration value="True" />
      <enumeration value="False" />
    </restriction>
  </simpleType>
  <simpleType name="unitScheduleType">
    <restriction base="string">
      <enumeration value="PreliminaryProvisional" />
      <enumeration value="Preliminary" />
      <enumeration value="FirstFinal" />
      <enumeration value="FirstUpdated" />
    </restriction>
  </simpleType>
  <simpleType name="fifteenMinutesScheduleType">
    <restriction base="string">
      <enumeration value="Original" />
      <enumeration value="Corrected" />
    </restriction>
  </simpleType>
  <simpleType name="bidTaxCodeType">
    <restriction base="string">
      <enumeration value="V1" />
      <enumeration value="V2" />
      <enumeration value="V3" />
      <enumeration value="V4" />
      <enumeration value="V5" />
      <enumeration value="V8" />
      <enumeration value="VC" />
      <enumeration value="VS" />
      <enumeration value="VR" />
      <enumeration value="NC" />
    </restriction>
  </simpleType>
  <simpleType name="offerTaxCodeType">
    <restriction base="string">
      <enumeration value="A1" />
      <enumeration value="A2" />
      <enumeration value="A3" />
      <enumeration value="A4" />
      <enumeration value="A5" />
      <enumeration value="A6" />
      <enumeration value="A7" />
      <enumeration value="A8" />
      <enumeration value="AL" />
      <enumeration value="AN" />
      <enumeration value="AS" />
      <enumeration value="AR" />
      <enumeration value="NC" />
    </restriction>
  </simpleType>
  <simpleType name="feeTaxCodeType">
    <restriction base="string">
      <enumeration value="V1" />
      <enumeration value="V3" />
    </restriction>
  </simpleType>
  <simpleType name="unitOfMeasureBidType">
    <restriction base="string">
      <enumeration value="MW" />
      <enumeration value="MWh" />
    </restriction>
  </simpleType>
  <simpleType name="unitOfMeasureMWhType">
    <restriction base="string">
      <enumeration value="MWh" />
    </restriction>
  </simpleType>
  <simpleType name="yesNoType">
    <restriction base="string">
      <enumeration value="Yes" />
      <enumeration value="No" />
    </restriction>
  </simpleType>
  <simpleType name="UnitYESNOType">
    <restriction base="string">
      <enumeration value="YES" />
      <enumeration value="NO" />
    </restriction>
  </simpleType>
  <simpleType name="zoneNameType">
    <restriction base="string">
      <enumeration value="CNOR" />
      <enumeration value="CSUD" />
      <enumeration value="NORD" />
      <enumeration value="SARD" />
      <enumeration value="SICI" />
      <enumeration value="CALA" />
      <enumeration value="COAC" />
      <enumeration value="GREC" />
      <enumeration value="AUST" />
      <enumeration value="SVIZ" />
      <enumeration value="CORS" />
      <enumeration value="SLOV" />
      <enumeration value="FRAN" />
      <enumeration value="SUD" />
      <enumeration value="BSP" />
      <enumeration value="MALT" />
      <enumeration value="MONT" />
      <enumeration value="XAUS" />
      <enumeration value="XFRA" />
      <enumeration value="XGRE" />
      <enumeration value="XSVI" />
    </restriction>
  </simpleType>
  <simpleType name="mustRunType">
    <restriction base="string">
      <enumeration value="MustRun" />
      <enumeration value="InternationalExchanges" />
      <enumeration value="BilateralContracts" />
      <enumeration value="AllMustRunTypes" />
      <enumeration value="AllTypesExceptBilateralContracts" />
      <enumeration value="EssentialforSystemSecurity" />
      <enumeration value="CIP6" />
      <enumeration value="NotprogrammablerenewableSources" />
      <enumeration value="OtherrenewableSources" />
      <enumeration value="Cogeneration" />
      <enumeration value="NationalSources" />
    </restriction>
  </simpleType>
  <simpleType name="scopeType">
    <restriction base="string">
      <enumeration value="RS" />
      <enumeration value="AS" />
      <enumeration value="GR1" />
      <enumeration value="GR2" />
      <enumeration value="GR3" />
      <enumeration value="GR4" />
      <enumeration value="AC" />
      <enumeration value="CA" />
    </restriction>
  </simpleType>
  <simpleType name="quarterType">
    <restriction base="string">
      <enumeration value="1" />
      <enumeration value="2" />
      <enumeration value="3" />
      <enumeration value="4" />
    </restriction>
  </simpleType>
  <simpleType name="bAType">
    <restriction base="string">
      <enumeration value="Rev" />
      <enumeration value="Norev" />
      <enumeration value="Netting" />
    </restriction>
  </simpleType>
</schema>