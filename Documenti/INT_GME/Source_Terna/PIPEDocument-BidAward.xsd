<?xml version="1.0" encoding="iso-8859-1"?>
<xs:schema targetNamespace="urn:XML-PIPE" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
    <xs:element name="PIPEDocument">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="TradingPartnerDirectory">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="Sender">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="TradingPartner">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="CompanyName" type="xs:string" />
													<xs:element name="CompanyIdentifier" type="xs:string" />
												</xs:sequence>
												<xs:attribute name="PartnerType" type="xs:string" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="Recipient">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="TradingPartner">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="CompanyName" type="xs:string" />
													<xs:element name="CompanyIdentifier" type="xs:string" />
												</xs:sequence>
												<xs:attribute name="PartnerType" type="xs:string" use="required" />
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="PIPTransaction" maxOccurs="unbounded">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="BidAwardResponse">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="Market" type="xs:string" />
										<xs:element name="Date" type="xs:unsignedInt" />
										<xs:element name="Hour" type="xs:unsignedByte" />
										<xs:element name="BidAwardDetails">
											<xs:complexType>
												<xs:sequence>
													<xs:element maxOccurs="unbounded" name="BidAward">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="MarketParticipantNumber" type="xs:string" />
																<xs:element name="UnitReferenceNumber" type="xs:string" />
																<xs:element name="GMEReferenceNumber" type="xs:string" />
																<xs:element minOccurs="0" maxOccurs="unbounded" name="BidAwardComponentRS">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="VerifiedSourceOffer" type="xs:string" />
																			<xs:element name="ValidatedQuantity">
																				<xs:complexType>
																					<xs:simpleContent>
																						<xs:extension base="xs:string">
																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" />
																						</xs:extension>
																					</xs:simpleContent>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="ValidatedPrice" type="xs:string" />
 																			<xs:element name="ReservedQuantity" minOccurs="0">
 																				<xs:complexType>
 																					<xs:simpleContent>
 																						<xs:extension base="xs:string"> 
 																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" /> 
 																						</xs:extension> 
 																					</xs:simpleContent> 
 																				</xs:complexType> 
 																			</xs:element>																																						 
																		</xs:sequence>
																		<xs:attribute name="Scope" type="xs:string" use="required" />
																		<xs:attribute name="Step" type="xs:unsignedByte" use="required" />																		
																	</xs:complexType>
																</xs:element>
																
																<xs:element minOccurs="0" maxOccurs="unbounded" name="BidAwardComponentMS">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="VerifiedSourceOffer" type="xs:string" />
																			<xs:element name="ValidatedQuantity">
																				<xs:complexType>
																					<xs:simpleContent>
																						<xs:extension base="xs:string">
																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" />
																						</xs:extension>
																					</xs:simpleContent>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="ValidatedPrice" type="xs:string" />
 																			<xs:element name="ReservedQuantity" minOccurs="0">
 																				<xs:complexType> 
 																					<xs:simpleContent> 
 																						<xs:extension base="xs:string"> 
 																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" /> 
 																						</xs:extension> 
 																					</xs:simpleContent> 
 																				</xs:complexType> 
 																			</xs:element>																																						 
																		</xs:sequence>
																		<xs:attribute name="Scope" type="xs:string" use="required" />
																		<xs:attribute name="Step" type="xs:unsignedByte" use="required" />																		
																	</xs:complexType>
																</xs:element>
																<xs:element minOccurs="0" maxOccurs="unbounded" name="BidAwardComponentSG">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="VerifiedSourceOffer" type="xs:string" />
																			<xs:element name="ValidatedQuantity">
																				<xs:complexType>
																					<xs:simpleContent>
																						<xs:extension base="xs:string">
																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" />
																						</xs:extension>
																					</xs:simpleContent>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="ValidatedPrice" type="xs:string" />
 																			<xs:element name="ReservedQuantity" minOccurs="0">
 																				<xs:complexType> 
 																					<xs:simpleContent> 
 																						<xs:extension base="xs:string"> 
 																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" /> 
 																						</xs:extension> 
 																					</xs:simpleContent> 
 																				</xs:complexType> 
 																			</xs:element>																																						 
																		</xs:sequence>
																		<xs:attribute name="Scope" type="xs:string" use="required" />
																		<xs:attribute name="Step" type="xs:unsignedByte" use="required" />																		
																	</xs:complexType>
																</xs:element>
																<xs:element minOccurs="0" maxOccurs="unbounded" name="BidAwardComponentRR">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="ValidatedQuantity">
																				<xs:complexType>
																					<xs:simpleContent>
																						<xs:extension base="xs:string">
																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" />
																						</xs:extension>
																					</xs:simpleContent>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="ValidatedPrice" type="xs:string" />
																			<xs:element name="AcceptedQuantity">
																				<xs:complexType>
																					<xs:simpleContent>
																						<xs:extension base="xs:string">
																							<xs:attribute name="UnitOfMeasure" type="xs:string" use="required" />
																						</xs:extension>
																					</xs:simpleContent>
																				</xs:complexType>
																			</xs:element>																																						
																		</xs:sequence>
																		<xs:attribute name="Scope" type="xs:string" use="required" />
																		<xs:attribute name="Step" type="xs:unsignedByte" use="required" />																		
																	</xs:complexType>
																</xs:element>
															</xs:sequence>															
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
                        <xs:attribute name="OriginalReferenceNumber" type="xs:string" use="required" />
					</xs:complexType>
				</xs:element>
			</xs:sequence>
			<xs:attribute name="CreationDate" type="xs:unsignedLong" use="required" />
			<xs:attribute name="ReferenceNumber" type="xs:string" use="required" />
			<xs:attribute name="Version" type="xs:string" use="required" />
		</xs:complexType>
	</xs:element>
</xs:schema>