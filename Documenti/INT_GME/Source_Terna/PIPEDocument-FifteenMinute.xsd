<?xml version="1.0" encoding="iso-8859-1"?>
<xs:schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="unqualified" elementFormDefault="qualified">
  <xs:element name="PIPEDocument">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="TradingPartnerDirectory">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Sender">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="TradingPartner">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element type="xs:string" name="CompanyName"/>
                          <xs:element type="xs:string" name="CompanyIdentifier"/>
                        </xs:sequence>
                        <xs:attribute type="xs:string" name="PartnerType"/>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
              <xs:element name="Recipient">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element name="TradingPartner">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element type="xs:string" name="CompanyName"/>
                          <xs:element type="xs:string" name="CompanyIdentifier"/>
                        </xs:sequence>
                        <xs:attribute type="xs:string" name="PartnerType"/>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PIPTransaction">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FifteenMinuteSchedule">
                <xs:complexType>
                  <xs:sequence>
                    <xs:element type="xs:string" name="Market"/>
                    <xs:element type="xs:string" name="Date"/>
                    <xs:element type="xs:string" name="UnitReferenceNumber"/>
                    <xs:element name="HourDetail" maxOccurs="unbounded" minOccurs="0">
                      <xs:complexType>
                        <xs:sequence>
                          <xs:element type="xs:string" name="Hour"/>
                          <xs:element name="Quantity" maxOccurs="unbounded" minOccurs="0">
                            <xs:complexType>
                              <xs:simpleContent>
                                <xs:extension base="xs:string">
                                  <xs:attribute type="xs:string" name="Minute" use="optional"/>
                                  <xs:attribute type="xs:int" name="QuarterInterval" use="optional"/>
                                </xs:extension>
                              </xs:simpleContent>
                            </xs:complexType>
                          </xs:element>
                        </xs:sequence>
                      </xs:complexType>
                    </xs:element>
                  </xs:sequence>
                  <xs:attribute type="xs:string" name="MarketParticipantNumber"/>
                  <xs:attribute type="xs:string" name="Type"/>
                </xs:complexType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
      <xs:attribute type="xs:string" name="CreationDate"/>
      <xs:attribute type="xs:string" name="ReferenceNumber"/>
      <xs:attribute type="xs:string" name="Version"/>
    </xs:complexType>
  </xs:element>
</xs:schema>