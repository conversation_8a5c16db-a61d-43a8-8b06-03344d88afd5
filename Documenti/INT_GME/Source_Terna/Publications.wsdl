<?xml version="1.0" encoding="UTF-8"?><wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:tns="http://www.terna.it/wsl/services/ext" xmlns:xsd="http://www.w3.org/2001/XMLSchema" name="Publications" targetNamespace="http://www.terna.it/wsl/services/ext">
	<wsdl:types>
		<xsd:schema elementFormDefault="unqualified" targetNamespace="http://www.terna.it/wsl/services/ext">

 <!-- Autenticazione -->
			<xsd:element name="operator" type="xsd:string"/>

			<!-- Gestione file -->
			<xsd:complexType name="FileBodyType">
				<xsd:choice>
					<xsd:element name="XMLBody" type="xsd:string"/>
					<xsd:element name="BinaryBody" type="xsd:base64Binary"/>
				</xsd:choice> 
			</xsd:complexType>
			<xsd:complexType name="FileInfoType">
				<xsd:all>
					<xsd:element maxOccurs="1" minOccurs="0" name="title" type="xsd:string"/>
					<xsd:element name="name" type="xsd:string"/>
					<xsd:element name="id" type="xsd:long"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="date" type="xsd:dateTime"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="type" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="mimetype" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="state" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="notes" type="xsd:string"/>
				</xsd:all>
			</xsd:complexType>
			<xsd:complexType name="FileTransportType">
				<xsd:all>
					<xsd:element maxOccurs="1" minOccurs="0" name="title" type="xsd:string"/>
					<xsd:element name="name" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="id" type="xsd:long"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="date" type="xsd:dateTime"/>
					<xsd:element name="type" type="xsd:string"/>
					<xsd:element name="mimetype" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="state" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="notes" type="xsd:string"/>
					<xsd:element maxOccurs="1" minOccurs="1" name="body" type="tns:FileBodyType"/>
				</xsd:all>
			</xsd:complexType>

			<!-- Gestione Baseline -->
			<xsd:simpleType name="NumQuarterType">
				<xsd:annotation>
					<xsd:documentation>Numero quarto d'ora</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:nonNegativeInteger">
					<xsd:minInclusive value="1"/>
					<xsd:maxInclusive value="4"/>
				</xsd:restriction>
			</xsd:simpleType>
			<xsd:simpleType name="ActionType">
				<xsd:annotation>
					<xsd:documentation>Tipo di azione</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:string">
					<xsd:enumeration value="CREATION"/>
					<xsd:enumeration value="MODIFICATION"/>
				</xsd:restriction>
			</xsd:simpleType>
			
			<xsd:simpleType name="messageCategoryTypeList">
				<xsd:annotation>
					<xsd:documentation>Categoria messaggi</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:string"> 
					<xsd:enumeration value="SENT"/>
					<xsd:enumeration value="RECEIVED"/>
					<xsd:enumeration value="DELETED"/>
				</xsd:restriction>
			</xsd:simpleType>

			<xsd:complexType name="messageTransportType"> 
				<xsd:annotation>
					<xsd:documentation>Informazioni relative al messaggio e all'eventuale allegato</xsd:documentation>
				</xsd:annotation>
					<xsd:all>
						<xsd:element maxOccurs="1" minOccurs="0" name="message" type="tns:messageInfoType"/>
						<xsd:element maxOccurs="1" minOccurs="0" name="body" type="tns:messageBodyType"/>
					</xsd:all>
			</xsd:complexType>

			<xsd:complexType name="messageBodyType"> 
				<xsd:annotation>
					<xsd:documentation>Informazioni relative all allegato contenuto nel messaggio</xsd:documentation>
				</xsd:annotation>
					<xsd:sequence>
						<xsd:element minOccurs="0" name="messageBody" type="xsd:string"/>
						<xsd:element maxOccurs="unbounded" minOccurs="0" name="attachment" type="tns:attachmentTransportType"/>
					</xsd:sequence>
			</xsd:complexType>

			<xsd:complexType name="attachmentTransportType"> 
				<xsd:annotation>
					<xsd:documentation>Allegato contenuto nel messaggio</xsd:documentation>
				</xsd:annotation>
					<xsd:all>
						<xsd:element maxOccurs="1" minOccurs="0" name="attachmentName" type="xsd:string"/>
						<xsd:element maxOccurs="1" minOccurs="0" name="attachmentType" type="xsd:string"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="binaryAttachment" type="xsd:base64Binary"/>
					</xsd:all>
			</xsd:complexType>

			<xsd:complexType name="messageInfoType"> 
				<xsd:annotation>
					<xsd:documentation>Informazioni del messaggio</xsd:documentation>
				</xsd:annotation>
					<xsd:all>
						<xsd:element maxOccurs="1" minOccurs="1" name="idMessage" type="xsd:long"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="subject" type="xsd:string"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="sender" type="xsd:string"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="recipient" type="xsd:string"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="messageCategory" type="tns:messageCategoryTypeList"/> 
						<xsd:element maxOccurs="1" minOccurs="1" name="hasAttachment" type="xsd:boolean"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="messageDate" type="xsd:dateTime"/>
						<xsd:element maxOccurs="1" minOccurs="0" name="read" type="xsd:boolean"/>
						<xsd:element maxOccurs="1" minOccurs="1" name="messagePriority" type="xsd:string"/>
					</xsd:all>
			</xsd:complexType>
			
			<xsd:simpleType name="QuantityType">
				<xsd:annotation>
					<xsd:documentation>Valori relativi alle potenze</xsd:documentation>
				</xsd:annotation>
				<xsd:restriction base="xsd:float">
					<xsd:minInclusive value="-9999999.99"/>
					<xsd:maxInclusive value="9999999.99"/>
				</xsd:restriction>
			</xsd:simpleType>
			<xsd:complexType name="QuarterType">
				<xsd:sequence>
					<xsd:element maxOccurs="1" minOccurs="1" name="numQuarter" type="tns:NumQuarterType"/>
					<xsd:element maxOccurs="1" minOccurs="1" name="quantity" type="tns:QuantityType"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="HourType">
				<xsd:sequence>
					<xsd:element maxOccurs="4" minOccurs="1" name="quarter" type="tns:QuarterType"/>
				</xsd:sequence>
				<xsd:attribute name="period" type="xsd:long" use="required"/>
			</xsd:complexType>
			<xsd:complexType name="BaselineType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="1" name="hour" type="tns:HourType"/>
				</xsd:sequence>
				<xsd:attribute name="glbId" type="xsd:string" use="required"/>
				<xsd:attribute name="dateRef" type="xsd:date" use="required"/>
				<xsd:attribute name="action" type="tns:ActionType" use="required"/>
			</xsd:complexType>
			<xsd:complexType name="ListFilesRequestType">
				<xsd:sequence>
					<xsd:element name="numFiles" type="xsd:long"/>
					<xsd:element name="dateFrom" type="xsd:date"/>
					<xsd:element name="dateTo" type="xsd:date"/>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="type" type="xsd:string"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="BaselineResultType">
				<xsd:sequence>
					<xsd:element name="result" type="xsd:boolean"/>
					<xsd:element name="description" type="xsd:string"/>
				</xsd:sequence>
				<xsd:attribute name="glbId" type="xsd:string" use="required"/>
				<xsd:attribute name="dateRef" type="xsd:date" use="required"/>
				<xsd:attribute name="versionNumber" type="xsd:nonNegativeInteger"/>
			</xsd:complexType>
			<xsd:complexType name="ListFilesResponseType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="file" type="tns:FileInfoType"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="DownloadFilesRequestType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="1" name="id" type="xsd:long"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="DownloadedFilesType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="file" type="tns:FileTransportType"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="UploadBaselinesRequestType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="1" name="baselines" type="tns:BaselineType"/>
				</xsd:sequence>
			</xsd:complexType>
			<xsd:complexType name="UploadBaselinesResponse">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="1" name="baselineResult" type="tns:BaselineResultType"/>
				</xsd:sequence>
			</xsd:complexType>
			
			<xsd:complexType name="ListMessagesRequestType">
				<xsd:sequence>
					<xsd:element maxOccurs="1" minOccurs="1" name="dateFrom" type="xsd:date"/>
					<xsd:element maxOccurs="1" minOccurs="1" name="dateTo" type="xsd:date"/>
					<xsd:element maxOccurs="1" minOccurs="1" name="messageCategory" type="tns:messageCategoryTypeList"/>
					<xsd:element maxOccurs="1" minOccurs="0" name="numMessages" type="xsd:nonNegativeInteger"/> 
				</xsd:sequence>
			</xsd:complexType>
			
			<xsd:complexType name="ListMessagesResponseType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="message" type="tns:messageInfoType"/> 
				</xsd:sequence>
			</xsd:complexType>

			<xsd:complexType name="DownloadMessagesRequestType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="id" type="xsd:long"/>
				</xsd:sequence>
			</xsd:complexType>

			<xsd:complexType name="DownloadMessagesResponseType">
				<xsd:sequence>
					<xsd:element maxOccurs="unbounded" minOccurs="0" name="messages" type="tns:messageTransportType"/>
				</xsd:sequence>
			</xsd:complexType>
			
			<!--
                Seguono gli elementi top-level che definiscono, secondo la codifica
                "document", i nomi delle operazioni e le relative risposte da parte
                del servizio web.  
            -->

			<!-- Lista dei file disponibili da scaricare -->
			<xsd:element name="listFiles" type="tns:ListFilesRequestType"/>
			<xsd:element name="listFilesResponse" type="tns:ListFilesResponseType"/>

			<xsd:element name="downloadFiles" type="tns:DownloadFilesRequestType"/>
			<xsd:element name="downloadedFiles" type="tns:DownloadedFilesType"/>

			<xsd:element name="forceDownloadFiles" type="tns:DownloadFilesRequestType"/>
			<xsd:element name="forceDownloadedFiles" type="tns:DownloadedFilesType"/>

			<xsd:element name="uploadBaselines" type="tns:UploadBaselinesRequestType"/>
			<xsd:element name="uploadBaselinesResponse" type="tns:UploadBaselinesResponse"/>

			<xsd:element name="listMessages" type="tns:ListMessagesRequestType"/>
			<xsd:element name="listMessagesResponse" type="tns:ListMessagesResponseType"/>

			<xsd:element name="downloadMessages" type="tns:DownloadMessagesRequestType"/>
			<xsd:element name="downloadMessagesResponse" type="tns:DownloadMessagesResponseType"/>
		</xsd:schema>
	</wsdl:types>

	<wsdl:message name="listFilesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:listFiles" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="listFilesResponse">
		<wsdl:part element="tns:listFilesResponse" name="parameters"/>
	</wsdl:message>

	<!--
         Richiesta di scaricamento di uno o piu' file.
         Il servizio restituisce un SOAP Fault nel caso un file sia gia'
         stato scaricato precedentemente
    -->
	<wsdl:message name="downloadFilesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:downloadFiles" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="downloadFilesResponse">
		<wsdl:part element="tns:downloadedFiles" name="parameters"/>
	</wsdl:message>

	<!--
         Richiesta di scaricamento "forzato" di uno o piu' file.
         Nel caso in cui un file risulti gia' scaricato non verra' lanciato
         alcun SOAP Fault, ma il file sara' semplicemente restituito
         nuovamente al richiedente
    -->
	<wsdl:message name="forceDownloadFilesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:forceDownloadFiles" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="forceDownloadFilesResponse">
		<wsdl:part element="tns:forceDownloadedFiles" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="uploadBaselinesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:uploadBaselines" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="uploadBaselinesResponse">
		<wsdl:part element="tns:uploadBaselinesResponse" name="parameters"/>
	</wsdl:message>
	
	<wsdl:message name="listMessagesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:listMessages" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="listMessagesResponse">
		<wsdl:part element="tns:listMessagesResponse" name="parameters"/>
	</wsdl:message>
	
	<wsdl:message name="downloadMessagesRequest">
		<wsdl:part element="tns:operator" name="operator"/>
		<wsdl:part element="tns:downloadMessages" name="parameters"/>
	</wsdl:message>
	<wsdl:message name="downloadMessagesResponse">
		<wsdl:part element="tns:downloadMessagesResponse" name="parameters"/>
	</wsdl:message>	

	<wsdl:portType name="PublicationsPT">
		<wsdl:operation name="listFiles">
			<wsdl:input message="tns:listFilesRequest"/>
			<wsdl:output message="tns:listFilesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="downloadFiles">
			<wsdl:input message="tns:downloadFilesRequest"/>
			<wsdl:output message="tns:downloadFilesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="forceDownloadFiles">
			<wsdl:input message="tns:forceDownloadFilesRequest"/>
			<wsdl:output message="tns:forceDownloadFilesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="uploadBaselines">
			<wsdl:input message="tns:uploadBaselinesRequest"/>
			<wsdl:output message="tns:uploadBaselinesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="listMessages">
			<wsdl:input message="tns:listMessagesRequest"/>
			<wsdl:output message="tns:listMessagesResponse"/>
		</wsdl:operation>
		<wsdl:operation name="downloadMessages">
			<wsdl:input message="tns:downloadMessagesRequest"/>
			<wsdl:output message="tns:downloadMessagesResponse"/>
		</wsdl:operation>
	</wsdl:portType>

	<wsdl:binding name="PublicationsB" type="tns:PublicationsPT">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="listFiles">
			<wsdl:input>
				<soap:header message="tns:listFilesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="downloadFiles">
			<wsdl:input>
				<soap:header message="tns:downloadFilesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="forceDownloadFiles">
			<wsdl:input>
				<soap:header message="tns:forceDownloadFilesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="uploadBaselines">
			<wsdl:input>
				<soap:header message="tns:listFilesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="listMessages">
			<wsdl:input>
				<soap:header message="tns:listMessagesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="downloadMessages">
			<wsdl:input>
				<soap:header message="tns:downloadMessagesRequest" part="operator" use="literal"/>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body parts="parameters" use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>

	<wsdl:service name="PublicationsService">
		<wsdl:port binding="tns:PublicationsB" name="PublicationsP">
			<soap:address location="http://covpmlj02-b:8280/gdr-ws/ws/wsgdr/Publications"/>
		</wsdl:port>
	</wsdl:service>

</wsdl:definitions>