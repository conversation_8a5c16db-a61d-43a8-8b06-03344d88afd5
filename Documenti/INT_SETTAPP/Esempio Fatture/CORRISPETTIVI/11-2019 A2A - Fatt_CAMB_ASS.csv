﻿TESTATA/DETTAGLIO;<PERSON><PERSON><PERSON><PERSON> SOCIALE;PARTITA IVA;CODICE CONTRATTO;ANNO DI RIFERIMENTO;MESE DI RIFERIMENTO;IDENTIFICATIVO NATURA ECONOMICA;FLAG A/R/S;IMPOR<PERSON> BENESTARE;QUANTITA';FLAG ATTIVO PASSIVO (A/P);IDENTIFICATIVO BENESTARE;IDENT. BENEST. DI RIFERIMENTO;MODALITA' REGOLAZ. ECONOMICA;N° FATTURA/N° NOTA DI CREDITO;DATA SCADENZA;N° FATTURA/ N° NOTA DI CREDITO DI RIFERIMENTO
T;A2A SPA;P.IVA ="11957540153";DI002603;2019;11;FATT_CAMB_ASS;A;-31675;1;P;20190457217P;;Utente emette fattura;;;
TESTATA/DETTAGLIO;CODICE UP;DATA RIFERIMENTO CORRISPETTIVO;ULTIMO CALCOLO [Euro];CALCOLO PRECEDENTE [Euro];DELTA DA REGOLARE [Euro];PREZZO VALIDO CAMBIO ASSETTO [Euro];CAMBIO ASSETTO [#];FORMULA NUMERICA;FORMULA TESTUALE;FLG_STO
D;UP_CASSANO_2;01/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;02/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;03/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;04/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;05/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;06/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;07/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;08/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;09/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;10/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;11/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;12/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;13/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;14/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;15/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;16/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;17/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;18/11/2019 00:00;0;;0;25000;0;"F=-25000*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;19/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;20/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;21/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;22/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;23/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;24/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;25/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;26/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;27/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;28/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;29/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CASSANO_2;30/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;01/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;02/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;03/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;04/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;05/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;06/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;07/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;08/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;09/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;10/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;11/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;12/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;13/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;14/11/2019 00:00;-31675;;-31675;31675;1;"F=-31675*[2-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;15/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;16/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;17/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;18/11/2019 00:00;0;;0;25000;0;"F=-25000*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;19/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;20/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-2]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;21/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;22/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;23/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;24/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;25/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;26/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;27/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;28/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;29/11/2019 00:00;0;;0;31675;0;"F=-31675*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_1;30/11/2019 00:00;0;;0;31675;0;"F=-31675*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;01/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;02/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;03/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;04/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;05/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;06/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;07/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;08/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;09/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;10/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;11/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;12/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;13/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;14/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;15/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;16/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;17/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;18/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;19/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;20/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;21/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;22/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;23/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;24/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;25/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;26/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;27/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;28/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;29/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CHIVASSO_2;30/11/2019 00:00;0;;0;1;0;"F=-1*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;01/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;02/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;03/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;04/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;05/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;06/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;07/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;08/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;09/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;10/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;11/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;12/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;13/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;14/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;15/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;16/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;17/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;18/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;19/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;20/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;21/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;22/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;23/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;24/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;25/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;26/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;27/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;28/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;29/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_1;30/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;01/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;02/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;03/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;04/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;05/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;06/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;07/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;08/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;09/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;10/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;11/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;12/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;13/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;14/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;15/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;16/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;17/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;18/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;19/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;20/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;21/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;22/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;23/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;24/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;25/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;26/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;27/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;28/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;29/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CNTRLDSCND_2;30/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;01/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;02/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;03/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;04/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;05/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;06/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;07/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;08/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;09/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;10/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;11/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;12/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;13/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;14/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;15/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;16/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;17/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;18/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;19/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;20/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;21/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;22/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;23/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;24/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;25/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;26/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;27/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;28/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;29/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_CTE_DEL_M_2;30/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;01/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;02/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;03/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;04/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;05/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;06/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;07/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;08/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;09/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;10/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;11/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;12/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;13/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;14/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;15/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;16/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;17/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;18/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;19/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;20/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;21/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;22/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;23/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;24/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;25/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;26/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;27/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;28/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;29/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_1;30/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;01/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;02/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;03/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;04/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;05/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;06/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;07/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;08/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;09/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;10/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;11/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;12/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;13/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;14/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;15/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;16/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;17/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;18/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;19/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;20/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;21/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;22/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;23/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;24/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;25/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;26/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;27/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;28/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;29/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_GISSI_2;30/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;01/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;02/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;03/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;04/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;05/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;06/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;07/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;08/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;09/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;10/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;11/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;12/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;13/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;14/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;15/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;16/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;17/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;18/11/2019 00:00;0;;0;0;0;"F=-0*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;19/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;20/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;21/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;22/11/2019 00:00;0;;0;32500;0;"F=-32500*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;23/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;24/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;25/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;26/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;27/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;28/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;29/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_PIACENZA_4;30/11/2019 00:00;0;;0;32500;0;"F=-32500*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;01/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;02/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;03/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;04/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;05/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;06/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;07/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;08/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;09/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;10/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;11/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;12/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;13/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;14/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;15/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;16/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;17/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;18/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;19/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;20/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;21/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;22/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;23/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;24/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;25/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;26/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;27/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;28/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;29/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_3;30/11/2019 00:00;0;;0;5;0;"F=-5*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;01/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;02/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;03/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;04/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;05/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;06/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;07/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;08/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;09/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;10/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;11/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;12/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;13/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;14/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;15/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;16/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;17/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;18/11/2019 00:00;0;;0;0;0;"F=-0*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;19/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;20/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;21/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;22/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;23/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;24/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;25/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;26/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;27/11/2019 00:00;0;;0;32580;0;"F=-32580*[1-1]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;28/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;29/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N
D;UP_SERMIDE_4;30/11/2019 00:00;0;;0;32580;0;"F=-32580*[0-0]";"F=-G*[NassMB-NassMI]";N

