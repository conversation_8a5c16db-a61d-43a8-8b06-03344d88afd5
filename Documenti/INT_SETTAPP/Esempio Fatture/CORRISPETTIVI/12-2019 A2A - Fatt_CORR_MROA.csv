﻿TESTATA/DETTAGLIO;<PERSON><PERSON><PERSON><PERSON> SOCIALE;PARTITA IVA;CODICE CONTRATTO;ANNO DI RIFERIMENTO;MESE DI RIFERIMENTO;IDENTIFICATIVO NATURA ECONOMICA;FLAG A/R/S;IMPOR<PERSON> BENESTARE;QUANTITA';FLAG ATTIVO PASSIVO (A/P);IDENTIFICATIVO BENESTARE;IDENT. BENEST. DI RIFERIMENTO;MODALITA' REGOLAZ. ECONOMICA;N° FATTURA/N° NOTA DI CREDITO;DATA SCADENZA;N° FATTURA/ N° NOTA DI CREDITO DI RIFERIMENTO
T;A2A SPA;P.IVA ="11957540153";DI002603;2019;12;CORR_MROA;A;65000;1;A;20191415705A;;Utente riceve fattura;0000002002001820;24/02/2020;
TESTATA/DETTAGLIO;CODICE UP;DATA RIFERIMENTO CORRISPETTIVO;ULTIMO CALCOLO [EURO];CALCOLO PRECEDENTE [EURO];DELTA DA REGOLARE [EURO];PREZZO VALIDO ACCENSIONE [EURO];NMROA[#];FORMULA NUMERICA;FORMULA TESTUALE
D;UP_CASSANO_2;01/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;02/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;03/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;04/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;05/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;06/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;07/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;08/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;09/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;10/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;11/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;12/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;13/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;14/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;15/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;16/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;17/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;18/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;19/12/2019 ;0;;0;31000;0;"F=31000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;20/12/2019 ;0;;0;31000;0;"F=31000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;21/12/2019 ;0;;0;22000;0;"F=22000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;22/12/2019 ;0;;0;22000;0;"F=22000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;23/12/2019 ;0;;0;22000;0;"F=22000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;24/12/2019 ;0;;0;22000;0;"F=22000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;25/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;26/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;27/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;28/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;29/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;30/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CASSANO_2;31/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;01/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;02/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;03/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;04/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;05/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;06/12/2019 ;0;;0;42000;0;"F=42000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;07/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;08/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;09/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;10/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;11/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;12/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;13/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;14/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;15/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;16/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;17/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;18/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;19/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;20/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;21/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;22/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;23/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;24/12/2019 ;0;;0;190000;0;"F=190000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;25/12/2019 ;0;;0;190000;0;"F=190000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;26/12/2019 ;0;;0;190000;0;"F=190000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;27/12/2019 ;0;;0;265000;0;"F=265000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;28/12/2019 ;0;;0;265000;0;"F=265000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;29/12/2019 ;0;;0;190000;0;"F=190000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;30/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_1;31/12/2019 ;0;;0;190000;0;"F=190000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;01/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;02/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;03/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;04/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;05/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;06/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;07/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;08/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;09/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;10/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;11/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;12/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;13/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;14/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;15/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;16/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;17/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;18/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;19/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;20/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;21/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;22/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;23/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;24/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;25/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;26/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;27/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;28/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;29/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;30/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CHIVASSO_2;31/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;01/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;02/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;03/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;04/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;05/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;06/12/2019 ;0;;0;90000;0;"F=90000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;07/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;08/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;09/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;10/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;11/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;12/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;13/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;14/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;15/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;16/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;17/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;18/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;19/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;20/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;21/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;22/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;23/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;24/12/2019 ;0;;0;160000;0;"F=160000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;25/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;26/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;27/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;28/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;29/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;30/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_1;31/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;01/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;02/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;03/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;04/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;05/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;06/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;07/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;08/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;09/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;10/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;11/12/2019 ;0;;0;80000;0;"F=80000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;12/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;13/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;14/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;15/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;16/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;17/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;18/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;19/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;20/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;21/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;22/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;23/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;24/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;25/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;26/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;27/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;28/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;29/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;30/12/2019 ;0;;0;53000;0;"F=53000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CNTRLDSCND_2;31/12/2019 ;65000;;65000;65000;1;"F=65000 * min(1 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;01/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;02/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;03/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;04/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;05/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;06/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;07/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;08/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;09/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;10/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;11/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;12/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;13/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;14/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;15/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;16/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;17/12/2019 ;0;;0;60000;0;"F=60000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;18/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;19/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;20/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;21/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;22/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;23/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;24/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;25/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;26/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;27/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;28/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;29/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;30/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_CTE_DEL_M_2;31/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;01/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;02/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;03/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;04/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;05/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;06/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;07/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;08/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;09/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;10/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;11/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;12/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;13/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;14/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;15/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;16/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;17/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;18/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;19/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;20/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;21/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;22/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;23/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;24/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;25/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;26/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;27/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;28/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;29/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;30/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_1;31/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;01/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;02/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;03/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;04/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;05/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;06/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;07/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;08/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;09/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;10/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;11/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;12/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;13/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;14/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;15/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;16/12/2019 ;0;;0;40000;0;"F=40000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;17/12/2019 ;0;;0;40000;0;"F=40000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;18/12/2019 ;0;;0;40000;0;"F=40000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;19/12/2019 ;0;;0;40000;0;"F=40000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;20/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;21/12/2019 ;0;;0;18000;0;"F=18000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;22/12/2019 ;0;;0;18000;0;"F=18000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;23/12/2019 ;0;;0;18000;0;"F=18000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;24/12/2019 ;0;;0;18000;0;"F=18000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;25/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;26/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;27/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;28/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;29/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;30/12/2019 ;0;;0;28000;0;"F=28000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_GISSI_2;31/12/2019 ;0;;0;35000;0;"F=35000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;01/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;02/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;03/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;04/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;05/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;06/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;07/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;08/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;09/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;10/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;11/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;12/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;13/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;14/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;15/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;16/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;17/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;18/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;19/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;20/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;21/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;22/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;23/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;24/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;25/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;26/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;27/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;28/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;29/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;30/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_1;31/12/2019 ;0;;0;110000;0;"F=110000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;01/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;02/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;03/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;04/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;05/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;06/12/2019 ;0;;0;120000;0;"F=120000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;07/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;08/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;09/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;10/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;11/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;12/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;13/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;14/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;15/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;16/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;17/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;18/12/2019 ;0;;0;155000;0;"F=155000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;19/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;20/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;21/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;22/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;23/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;24/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;25/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;26/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;27/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;28/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;29/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;30/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_MONFALCO_2;31/12/2019 ;0;;0;135000;0;"F=135000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;01/12/2019 ;0;;0;40000;0;"F=40000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;02/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;03/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;04/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;05/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;06/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;07/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;08/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;09/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;10/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;11/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;12/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;13/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;14/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;15/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;16/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;17/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;18/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;19/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;20/12/2019 ;0;;0;25000;0;"F=25000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;21/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;22/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;23/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;24/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;25/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;26/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;27/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;28/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;29/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;30/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_PIACENZA_4;31/12/2019 ;0;;0;20000;0;"F=20000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;01/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;02/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;03/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;04/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;05/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;06/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;07/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;08/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;09/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;10/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;11/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;12/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;13/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;14/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;15/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;16/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;17/12/2019 ;0;;0;55000;0;"F=55000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;18/12/2019 ;0;;0;50000;0;"F=50000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;19/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;20/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;21/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;22/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;23/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;24/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;25/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;26/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;27/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;28/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;29/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;30/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_3;31/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;01/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;02/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;03/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;04/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;05/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;06/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;07/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;08/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;09/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;10/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;11/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;12/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;13/12/2019 ;0;;0;51000;0;"F=51000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;14/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;15/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;16/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;17/12/2019 ;0;;0;45000;0;"F=45000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;18/12/2019 ;0;;0;48000;0;"F=48000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;19/12/2019 ;0;;0;30000;0;"F=30000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;20/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [1]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;21/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;22/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;23/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;24/12/2019 ;0;;0;15000;0;"F=15000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;25/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;26/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;27/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;28/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;29/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;30/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
D;UP_SERMIDE_4;31/12/2019 ;0;;0;23000;0;"F=23000 * min(0 ; [0]+)";"F=G* min(SUM NMROA ACCENS;[NaccMB-NaccMI]+)"
 
TESTATA/DETTAGLIO;CODICE_UP;DATA RIF;ID ACCENSIONE MB;DATA RIF q';DATA RIF q;SBILANCIAMENTO FISICO q'-1 [MWh];SBILANCIAMENTO FISICO q+1 [MWh];ENERGIA PROGRAMMATA q+1 [MWh];NMROA ACCENS [#];COND FORMULA NUMERICA;COND FORMULA TESTUALE
D2;UP_CASSANO_2;02/12/2019 00:00:00;1;01/12/2019 23:45:00;02/12/2019 05:30:00;0;-1,827;61,875;0;"F=|-1,827| <=,05 * 61,875";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CASSANO_2;04/12/2019 00:00:00;1;04/12/2019 03:45:00;04/12/2019 05:30:00;0;-,803;59,063;0;"F=|-,803| <=,05 * 59,063";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CASSANO_2;06/12/2019 00:00:00;1;06/12/2019 02:45:00;06/12/2019 05:30:00;0;,025;59,063;0;"F=0 <= 2,5 AND ,025 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CASSANO_2;09/12/2019 00:00:00;1;09/12/2019 02:45:00;09/12/2019 05:30:00;0;,521;59,063;0;"F=0 <= 2,5 AND ,521 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CASSANO_2;16/12/2019 00:00:00;1;16/12/2019 00:45:00;16/12/2019 05:30:00;0;-,371;59,063;0;"F=|-,371| <=,05 * 59,063";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;02/12/2019 00:00:00;1;02/12/2019 02:45:00;02/12/2019 05:30:00;0;-,46;58,281;0;"F=|-,46| <=,05 * 58,281";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;09/12/2019 00:00:00;1;09/12/2019 02:45:00;09/12/2019 05:30:00;,243;-,126;58,281;0;"F=|-,126| <=,05 * 58,281";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;12/12/2019 00:00:00;1;12/12/2019 03:45:00;12/12/2019 05:30:00;0;-,387;46,656;0;"F=|-,387| <=,05 * 46,656";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;16/12/2019 00:00:00;1;16/12/2019 02:45:00;16/12/2019 05:30:00;0;,057;58,281;0;"F=0 <= 2,5 AND ,057 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;23/12/2019 00:00:00;1;23/12/2019 01:45:00;23/12/2019 04:00:00;0;-,369;43,75;0;"F=|-,369| <=,05 * 43,75";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;26/12/2019 00:00:00;1;26/12/2019 13:45:00;26/12/2019 16:00:00;0;-,289;55,465;0;"F=|-,289| <=,05 * 55,465";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CHIVASSO_1;30/12/2019 00:00:00;1;30/12/2019 01:45:00;30/12/2019 04:00:00;0;-,035;43,75;0;"F=|-,035| <=,05 * 43,75";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_1;04/12/2019 00:00:00;1;04/12/2019 05:15:00;04/12/2019 06:00:00;,38;2,68;76,74;0;"F=,38 <= 2,5 AND 2,68 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_1;09/12/2019 00:00:00;1;09/12/2019 04:45:00;09/12/2019 06:30:00;,76;,831;69,469;0;"F=,76 <= 2,5 AND ,831 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_1;18/12/2019 00:00:00;1;18/12/2019 06:15:00;18/12/2019 07:00:00;,76;,843;58,817;0;"F=,76 <= 2,5 AND ,843 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_1;22/12/2019 00:00:00;2;22/12/2019 14:45:00;22/12/2019 16:30:00;0;,7;52,5;0;"F=0 <= 2,5 AND ,7 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_1;22/12/2019 00:00:00;1;21/12/2019 23:15:00;22/12/2019 00:15:00;,38;-1,2;52,5;0;"F=|-1,2| <=,05 * 52,5";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_1;23/12/2019 00:00:00;1;23/12/2019 22:15:00;23/12/2019 23:00:00;,76;-,767;70,687;0;"F=|-,767| <=,05 * 70,687";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_1;26/12/2019 00:00:00;1;25/12/2019 23:15:00;26/12/2019 00:15:00;,76;,32;52,5;0;"F=,76 <= 2,5 AND ,32 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_1;28/12/2019 00:00:00;1;27/12/2019 23:15:00;28/12/2019 00:15:00;,76;-,82;52,5;0;"F=|-,82| <=,05 * 52,5";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_1;31/12/2019 00:00:00;1;30/12/2019 23:15:00;31/12/2019 00:15:00;,76;-,06;52,5;0;"F=|-,06| <=,05 * 52,5";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;03/12/2019 00:00:00;1;03/12/2019 14:15:00;03/12/2019 15:00:00;,38;,36;65;0;"F=,38 <= 2,5 AND ,36 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CNTRLDSCND_2;15/12/2019 00:00:00;1;15/12/2019 13:45:00;15/12/2019 15:30:00;,38;-,2;71,26;0;"F=|-,2| <=,05 * 71,26";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;27/12/2019 00:00:00;1;26/12/2019 23:15:00;27/12/2019 00:15:00;,38;-,02;65;0;"F=|-,02| <=,05 * 65";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;28/12/2019 00:00:00;1;27/12/2019 23:15:00;28/12/2019 00:00:00;,38;-,4;65;0;"F=|-,4| <=,05 * 65";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;29/12/2019 00:00:00;1;28/12/2019 23:15:00;29/12/2019 00:00:00;,76;-,4;65;0;"F=|-,4| <=,05 * 65";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;30/12/2019 00:00:00;1;29/12/2019 23:15:00;30/12/2019 00:15:00;,76;-,4;65;0;"F=|-,4| <=,05 * 65";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CNTRLDSCND_2;31/12/2019 00:00:00;1;30/12/2019 23:15:00;31/12/2019 00:15:00;0;-65;65;1;"F=|-65| >,1 * 65";"F=|SBIL(q+1)| > Ksoglia2 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;02/12/2019 00:00:00;1;02/12/2019 04:30:00;02/12/2019 07:00:00;0;1,175;78,297;0;"F=0 <= 2,5 AND 1,175 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CTE_DEL_M_2;04/12/2019 00:00:00;1;04/12/2019 05:45:00;04/12/2019 07:30:00;0;-,496;92,75;0;"F=|-,496| <=,05 * 92,75";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;06/12/2019 00:00:00;1;06/12/2019 05:45:00;06/12/2019 07:30:00;0;-,043;55,469;0;"F=|-,043| <=,05 * 55,469";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;09/12/2019 00:00:00;1;09/12/2019 04:30:00;09/12/2019 07:00:00;0;-,029;51,62;0;"F=|-,029| <=,05 * 51,62";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;12/12/2019 00:00:00;1;12/12/2019 05:45:00;12/12/2019 07:30:00;0;-,013;59,5;0;"F=|-,013| <=,05 * 59,5";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;13/12/2019 00:00:00;1;13/12/2019 05:00:00;13/12/2019 07:00:00;0;,821;71,564;0;"F=0 <= 2,5 AND ,821 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_CTE_DEL_M_2;16/12/2019 00:00:00;1;16/12/2019 04:30:00;16/12/2019 07:00:00;0;-,22;54,64;0;"F=|-,22| <=,05 * 54,64";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_CTE_DEL_M_2;19/12/2019 00:00:00;1;19/12/2019 04:30:00;19/12/2019 07:00:00;0;-,174;40;0;"F=|-,174| <=,05 * 40";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_1;04/12/2019 00:00:00;1;04/12/2019 13:45:00;04/12/2019 16:30:00;0;-,199;75,006;0;"F=|-,199| <=,05 * 75,006";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_1;09/12/2019 00:00:00;1;09/12/2019 14:45:00;09/12/2019 16:30:00;0;-,283;76,192;0;"F=|-,283| <=,05 * 76,192";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_1;11/12/2019 00:00:00;1;11/12/2019 14:45:00;11/12/2019 16:30:00;0;-,144;73,651;0;"F=|-,144| <=,05 * 73,651";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_1;18/12/2019 00:00:00;1;18/12/2019 04:30:00;18/12/2019 07:00:00;0;,029;67,695;0;"F=0 <= 2,5 AND ,029 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_GISSI_2;10/12/2019 00:00:00;1;10/12/2019 13:45:00;10/12/2019 16:30:00;0;-,09;75,072;0;"F=|-,09| <=,05 * 75,072";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_2;12/12/2019 00:00:00;1;11/12/2019 22:15:00;12/12/2019 00:15:00;0;,083;59,068;0;"F=0 <= 2,5 AND ,083 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_GISSI_2;16/12/2019 00:00:00;1;16/12/2019 04:30:00;16/12/2019 07:00:00;0;-,371;81,987;0;"F=|-,371| <=,05 * 81,987";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_2;17/12/2019 00:00:00;1;17/12/2019 06:30:00;17/12/2019 08:00:00;0;,698;59,699;0;"F=0 <= 2,5 AND ,698 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_GISSI_2;22/12/2019 00:00:00;1;22/12/2019 14:30:00;22/12/2019 17:00:00;0;-,647;77,939;0;"F=|-,647| <=,05 * 77,939";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_2;25/12/2019 00:00:00;1;24/12/2019 23:15:00;25/12/2019 01:15:00;0;-,743;60;0;"F=|-,743| <=,05 * 60";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_GISSI_2;27/12/2019 00:00:00;1;26/12/2019 23:00:00;27/12/2019 01:00:00;0;,774;59,547;0;"F=0 <= 2,5 AND ,774 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_GISSI_2;30/12/2019 00:00:00;1;30/12/2019 05:30:00;30/12/2019 08:00:00;0;-,604;75,145;0;"F=|-,604| <=,05 * 75,145";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_MONFALCO_1;09/12/2019 00:00:00;1;09/12/2019 02:45:00;09/12/2019 07:30:00;0;,024;33,031;0;"F=0 <= 2,5 AND ,024 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_MONFALCO_1;16/12/2019 00:00:00;1;16/12/2019 02:45:00;16/12/2019 07:30:00;0;,567;31,25;0;"F=0 <= 2,5 AND ,567 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;03/12/2019 00:00:00;1;03/12/2019 05:45:00;03/12/2019 08:00:00;,912;,61;55,083;0;"F=,912 <= 2,5 AND ,61 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;05/12/2019 00:00:00;1;05/12/2019 02:45:00;05/12/2019 05:30:00;1,155;1,148;60,625;0;"F=1,155 <= 2,5 AND 1,148 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;12/12/2019 00:00:00;1;12/12/2019 05:45:00;12/12/2019 08:00:00;,73;1,968;51,05;0;"F=,73 <= 2,5 AND 1,968 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;13/12/2019 00:00:00;1;13/12/2019 05:45:00;13/12/2019 08:00:00;,608;,221;55,35;0;"F=,608 <= 2,5 AND ,221 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;16/12/2019 00:00:00;1;16/12/2019 07:45:00;16/12/2019 10:00:00;,608;-,016;55,283;0;"F=|-,016| <=,05 * 55,283";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_PIACENZA_4;17/12/2019 00:00:00;1;17/12/2019 03:45:00;17/12/2019 05:30:00;,547;-,068;60,625;0;"F=|-,068| <=,05 * 60,625";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_PIACENZA_4;19/12/2019 00:00:00;1;19/12/2019 05:15:00;19/12/2019 07:00:00;0;1,161;54,167;0;"F=0 <= 2,5 AND 1,161 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;20/12/2019 00:00:00;1;20/12/2019 06:15:00;20/12/2019 08:00:00;0;,49;58;0;"F=0 <= 2,5 AND ,49 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_3;12/12/2019 00:00:00;1;12/12/2019 01:45:00;12/12/2019 07:30:00;0;-,184;63,188;0;"F=|-,184| <=,05 * 63,188";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_SERMIDE_4;03/12/2019 00:00:00;1;03/12/2019 04:45:00;03/12/2019 07:00:00;0;,078;65,06;0;"F=0 <= 2,5 AND ,078 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;04/12/2019 00:00:00;1;04/12/2019 05:30:00;04/12/2019 07:00:00;0;-,004;60,995;0;"F=|-,004| <=,05 * 60,995";"F=|SBIL(q+1)| <= Ksoglia1 * ENE PRG(q+1)"
D2;UP_SERMIDE_4;05/12/2019 00:00:00;1;05/12/2019 03:30:00;05/12/2019 05:00:00;0;,206;45,051;0;"F=0 <= 2,5 AND ,206 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;11/12/2019 00:00:00;1;11/12/2019 03:15:00;11/12/2019 08:00:00;0;,427;45;0;"F=0 <= 2,5 AND ,427 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;13/12/2019 00:00:00;1;13/12/2019 04:45:00;13/12/2019 06:30:00;0;,403;63,619;0;"F=0 <= 2,5 AND ,403 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;17/12/2019 00:00:00;1;17/12/2019 05:45:00;17/12/2019 08:00:00;0;,075;60,98;0;"F=0 <= 2,5 AND ,075 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;20/12/2019 00:00:00;1;20/12/2019 05:45:00;20/12/2019 08:00:00;0;,263;57,61;0;"F=0 <= 2,5 AND ,263 >= 0";"F=SBIL(q'-1) <= Ksogliaspegnimento AND SBIL(q+1) >= 0"

