﻿TESTATA/DETTAGLIO;<PERSON><PERSON><PERSON><PERSON> SOCIALE;PARTITA IVA;CODICE CONTRATTO;ANNO DI RIFERIMENTO;MESE DI RIFERIMENTO;IDENTIFICATIVO NATURA ECONOMICA;FLAG A/R/S;IMPOR<PERSON> BENESTARE;QUANTITA';FLAG ATTIVO PASSIVO (A/P);IDENTIFICATIVO BENESTARE;IDENT. BENEST. DI RIFERIMENTO;MODALITA' REGOLAZ. ECONOMICA;N° FATTURA/N° NOTA DI CREDITO;DATA SCADENZA;N° FATTURA/ N° NOTA DI CREDITO DI RIFERIMENTO
T;A2A SPA;P.IVA ="11957540153";DI002603;2019;12;CORR_MROCA;A;31675;1;A;20191415716A;;Utente riceve fattura;0000002002001721;24/02/2020;
TESTATA/DETTAGLIO;CODICE UP;DATA RIFERIMENTO CORRISPETTIVO;ULTIMO CALCOLO [EURO];<PERSON><PERSON><PERSON><PERSON> PRECEDENTE [EURO];DELTA DA REGOLARE [EURO];PREZZO VALIDO CAMBIO ASSETTO [EURO];NMROCA [#];FORMULA NUMERICA;FORMULA TESTUALE;FLG_STO
D;UP_CASSANO_2;01/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;02/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;03/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;04/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;05/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;06/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;07/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;08/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;09/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;10/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;11/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;12/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;13/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;14/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;15/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;16/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;17/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;18/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;19/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;20/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;21/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;22/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;23/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;24/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;25/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;26/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;27/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;28/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;29/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;30/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CASSANO_2;31/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;01/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;02/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;03/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;04/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;05/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;06/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;07/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;08/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;09/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;10/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;11/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;12/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;13/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;14/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;15/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;16/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;17/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;18/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;19/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;20/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;21/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;22/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;23/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;24/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;25/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;26/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;27/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;28/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;29/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;30/12/2019 ;31675;;31675;31675;1;"F=31675 * min(3 ; [1]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_1;31/12/2019 ;0;;0;31675;0;"F=31675 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;01/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;02/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;03/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;04/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;05/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;06/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;07/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;08/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;09/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;10/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;11/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;12/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;13/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;14/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;15/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;16/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;17/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;18/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;19/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;20/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;21/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;22/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;23/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;24/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;25/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;26/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;27/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;28/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;29/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;30/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CHIVASSO_2;31/12/2019 ;0;;0;1;0;"F=1 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;01/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;02/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;03/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;04/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;05/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;06/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;07/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;08/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;09/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;10/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;11/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;12/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;13/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;14/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;15/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;16/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;17/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;18/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;19/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;20/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;21/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;22/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;23/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;24/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;25/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;26/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;27/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;28/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;29/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;30/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_1;31/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;01/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;02/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;03/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;04/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;05/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;06/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;07/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;08/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;09/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;10/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;11/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;12/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;13/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;14/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;15/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;16/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;17/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;18/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;19/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;20/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;21/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;22/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;23/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;24/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;25/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;26/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;27/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;28/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;29/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;30/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CNTRLDSCND_2;31/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;01/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;02/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;03/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;04/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;05/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;06/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;07/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;08/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;09/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;10/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;11/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;12/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;13/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;14/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;15/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;16/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;17/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;18/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;19/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;20/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;21/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;22/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;23/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;24/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;25/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;26/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;27/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;28/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;29/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;30/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_CTE_DEL_M_2;31/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;01/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;02/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;03/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;04/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;05/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;06/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;07/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;08/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;09/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;10/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;11/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;12/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;13/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;14/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;15/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;16/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;17/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;18/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;19/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;20/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;21/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;22/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;23/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;24/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;25/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;26/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;27/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;28/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;29/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;30/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_1;31/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;01/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;02/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;03/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;04/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;05/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;06/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;07/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;08/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;09/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;10/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;11/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;12/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;13/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;14/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;15/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;16/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;17/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;18/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;19/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;20/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;21/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;22/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;23/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;24/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;25/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;26/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;27/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;28/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;29/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;30/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_GISSI_2;31/12/2019 ;0;;0;0;0;"F=0 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;01/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;02/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;03/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;04/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;05/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;06/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;07/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;08/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;09/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;10/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;11/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;12/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;13/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;14/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;15/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;16/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;17/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;18/12/2019 ;0;;0;32500;0;"F=32500 * min(1 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;19/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;20/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;21/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;22/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;23/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;24/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;25/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;26/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;27/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;28/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;29/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;30/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_PIACENZA_4;31/12/2019 ;0;;0;32500;0;"F=32500 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;01/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;02/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;03/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;04/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;05/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;06/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;07/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;08/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;09/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;10/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;11/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;12/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;13/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;14/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;15/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;16/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;17/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;18/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;19/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;20/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;21/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;22/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;23/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;24/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;25/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;26/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;27/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;28/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;29/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;30/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_3;31/12/2019 ;0;;0;5;0;"F=5 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;01/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;02/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;03/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [1]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;04/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;05/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [1]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;06/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;07/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;08/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;09/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;10/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;11/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;12/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;13/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;14/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;15/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;16/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;17/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;18/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;19/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;20/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;21/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;22/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;23/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;24/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;25/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;26/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;27/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;28/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;29/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;30/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
D;UP_SERMIDE_4;31/12/2019 ;0;;0;32580;0;"F=32580 * min(0 ; [0]+)";"F=GA* min(SUM NMROCA;[NcaMB-NcaMI]+)";N
 
TESTATA/DETTAGLIO;CODICE_UP;ID CAMBIO ASSETTO MB;Indice di macroassetto;DATA RIFERIMENTO CONTROLLO q';DATA RIFERIMENTO CONTROLLO q;SBILANCIAMENTO FISICO q'-1 [MWh];ENERGIA PROGRAMMATA q'-1 [MWh];SBILANCIAMENTO FISICO q+1 [MWh];ENERGIA PROGRAMMATA q+1 [MWh];Nmroca CA [#];CONDIZIONE FORMULA NUMERICA;CONDIZIONE FORMULA TESTUALE
D2;UP_CASSANO_2;1;2;03/12/2019 06:30:00;03/12/2019 06:45:00;,985;77,891;,216;146,86;0;"F=,985 <= ,05*77,891 AND ,216 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CASSANO_2;1;2;04/12/2019 06:15:00;04/12/2019 06:45:00;-,398;68,438;-,152;147,656;0;"F=|-,152| <= ,05*147,656";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CASSANO_2;1;2;06/12/2019 06:15:00;06/12/2019 06:45:00;,33;68,438;-,715;148,879;0;"F=|-,715| <= ,05*148,879";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CASSANO_2;1;2;09/12/2019 06:15:00;09/12/2019 06:45:00;,298;68,438;-,715;147,363;0;"F=|-,715| <= ,05*147,363";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CASSANO_2;1;2;10/12/2019 06:30:00;10/12/2019 06:45:00;-,257;77,813;,372;147,068;0;"F=-,257 <= ,05*77,813 AND ,372 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CASSANO_2;1;2;16/12/2019 06:15:00;16/12/2019 06:45:00;,198;68,438;1,39;143,67;0;"F=,198 <= ,05*68,438 AND 1,39 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;02/12/2019 06:30:00;02/12/2019 06:45:00;,533;77,656;-1,53;152,344;0;"F=|-1,53| <= ,05*152,344";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;1;2;03/12/2019 06:30:00;03/12/2019 06:45:00;,565;79,813;,185;150,325;0;"F=,565 <= ,05*79,813 AND ,185 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;04/12/2019 06:15:00;04/12/2019 06:30:00;,215;90,468;,345;122,927;0;"F=,215 <= ,05*90,468 AND ,345 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;05/12/2019 06:30:00;05/12/2019 06:45:00;-,663;65,476;,252;194,156;0;"F=-,663 <= ,05*65,476 AND ,252 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;06/12/2019 06:30:00;06/12/2019 06:45:00;,656;76,834;,009;145,333;0;"F=,656 <= ,05*76,834 AND ,009 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;09/12/2019 06:30:00;09/12/2019 06:45:00;-,258;77,656;-,029;151,056;0;"F=|-,029| <= ,05*151,056";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;1;2;10/12/2019 06:30:00;10/12/2019 06:45:00;-,197;77,656;,007;151,02;0;"F=-,197 <= ,05*77,656 AND ,007 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;11/12/2019 06:30:00;11/12/2019 06:45:00;1,253;79,976;13,662;132,684;0;"F=1,253 <= ,05*79,976 AND 13,662 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;12/12/2019 06:30:00;12/12/2019 06:45:00;-,197;77,656;-,085;169,17;0;"F=|-,085| <= ,05*169,17";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;1;2;16/12/2019 06:30:00;16/12/2019 06:45:00;-,014;77,656;-,945;151,638;0;"F=|-,945| <= ,05*151,638";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;1;2;17/12/2019 06:30:00;17/12/2019 06:45:00;-,035;79,531;,215;150,387;0;"F=-,035 <= ,05*79,531 AND ,215 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;18/12/2019 06:30:00;18/12/2019 06:45:00;,396;66,362;,085;151,337;0;"F=,396 <= ,05*66,362 AND ,085 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_CHIVASSO_1;1;2;19/12/2019 06:30:00;19/12/2019 06:45:00;,937;68,892;-1,448;137,792;0;"F=|-1,448| <= ,05*137,792";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;1;2;20/12/2019 06:30:00;20/12/2019 06:45:00;-,248;79,531;-,585;150,335;0;"F=|-,585| <= ,05*150,335";"F=|SBIL(q+1)| <= KAsoglia2 * Eprog(q+1)"
D2;UP_CHIVASSO_1;3;2;30/12/2019 15:15:00;30/12/2019 15:30:00;17,051;72,507;-,517;119,442;1;"F=17,051 > ,05*72,507";"F=SBIL(q'-1) > KAsoglia1 * Eprog(q'-1)"
D2;UP_CHIVASSO_1;1;2;30/12/2019 07:30:00;30/12/2019 07:45:00;2,605;49,531;-,715;126,875;1;"F=2,605 > ,05*49,531";"F=SBIL(q'-1) > KAsoglia1 * Eprog(q'-1)"
D2;UP_CHIVASSO_1;2;2;30/12/2019 14:15:00;30/12/2019 14:45:00;14,829;74;17,051;72,507;1;"F=14,829 > ,05*74";"F=SBIL(q'-1) > KAsoglia1 * Eprog(q'-1)"
D2;UP_PIACENZA_4;1;2;05/12/2019 06:30:00;05/12/2019 06:45:00;3,59;78,125;,064;151,875;0;"F=3,59 <= ,05*78,125 AND ,064 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;1;2;17/12/2019 06:15:00;17/12/2019 06:45:00;1,092;69,375;2,625;150,469;0;"F=1,092 <= ,05*69,375 AND 2,625 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_PIACENZA_4;1;2;18/12/2019 06:15:00;18/12/2019 07:00:00;14,332;64,282;2,093;177,875;1;"F=14,332 > ,05*64,282";"F=SBIL(q'-1) > KAsoglia1 * Eprog(q'-1)"
D2;UP_SERMIDE_4;1;2;03/12/2019 10:00:00;03/12/2019 10:15:00;1,418;65,249;,029;112,36;0;"F=1,418 <= ,05*65,249 AND ,029 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;1;2;05/12/2019 10:00:00;05/12/2019 10:30:00;,221;67,51;,219;114,915;0;"F=,221 <= ,05*67,51 AND ,219 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;1;2;11/12/2019 14:15:00;11/12/2019 14:45:00;,47;68,438;,164;150,155;0;"F=,47 <= ,05*68,438 AND ,164 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"
D2;UP_SERMIDE_4;1;2;13/12/2019 07:15:00;13/12/2019 07:45:00;-,072;75,625;,36;174,875;0;"F=-,072 <= ,05*75,625 AND ,36 >= 0";"F=SBIL(q'-1) <= KAsoglia1 * Eprog(q'-1) and SBIL(q+1) >= 0"

