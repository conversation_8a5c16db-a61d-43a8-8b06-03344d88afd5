Instructions

1. Put the Microsoft command line transformation utility (msxsl.exe) file in the same folder as the BizTalkMapDocumenterHTML.xslt
2. In the command prompt, navigate to the folder containing the msxsl.exe file
3. Run and a command like the following:

      msxsl "..\..\..\Source\Acme\Integration\Maps\SouceSystemA\MyMap.btm" BizTalkMapDocumenterHTML.xslt -o "MyMap.btm.html"

   Or create an html file for each map file in a specified folder:

      for %%f in ("Source\*.btm") do call msxsl "%%f" BizTalkMapDocumenterHTML.xslt -o "%%~nf.btm.html"