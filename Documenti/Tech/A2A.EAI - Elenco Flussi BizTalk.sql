WITH baseQuery AS
(
SELECT DISTINCT
	PRC.ApplicationName,
	ProcessName = PRC.OrchestrationName,
	PortType = PRC.LogicalPortType,
	PRC.LogicalPortBindingType,
	PRC.LogicalPortName,
	PortName = ISNULL(SND.SendPortName, RPT.ReceivePortName),
	Adapter = COALESCE(SND.SendPortAdapterDetail, RPT.ReceiveLocationAdapterDetail, SND.SendPortAdapter, RPT.ReceiveLocationAdapter),
	PortAddress = ISNULL(SND.SendPortAddress, RPT.ReceiveLocationAddress)
					
FROM
	[dbo].[Orchestration] PRC WITH (NOLOCK)

	LEFT JOIN [dbo].[SendPort] SND WITH (NOLOCK)
	ON PRC.SendPortId = SND.SendPortId

	LEFT JOIN [dbo].[ReceivePort] RPT WITH (NOLOCK)
	ON PRC.ReceivePortId = RPT.ReceivePortId
WHERE
	PRC.ApplicationName LIKE 'A2A%'
	AND PRC.OrchestrationName NOT LIKE '%ObjectDef%'
	AND PRC.LogicalPortName NOT LIKE '%Notification%'
)
SELECT
	*,
	Port = PortName + ' (' + Adapter + ')',
	SystemName = CASE
					WHEN PortAddress LIKE '%DCPRVW118N2%' THEN 'N2'
					WHEN PortAddress LIKE '%nbdo%' THEN 'N2'
					WHEN PortAddress LIKE '%ndue%' THEN 'N2'
					WHEN PortName LIKE '%rptTernaBde%' THEN 'Terna'
					WHEN PortAddress LIKE '%DWH_EZEKE%' THEN 'BI (TABULAR)'
					WHEN PortName LIKE '%ezeke%' THEN 'Ezeke'
					WHEN PortName LIKE '%rpa%' THEN 'RPA'
					WHEN PortName LIKE '%gedi%' THEN 'GEDI'
					WHEN PortName LIKE '%etrm%' THEN 'ETRM'
					WHEN PortName LIKE '%gme%' THEN 'GME'
					WHEN PortName LIKE '%scandale%' THEN 'Scandale'
					WHEN PortName LIKE '%virtus%' THEN 'Virtus'
					WHEN PortName LIKE '%DataPlatform%' THEN 'Data Platform (Google)'
					WHEN PortName LIKE '%terna%' THEN 'Terna'
					WHEN PortName LIKE 'Pi%' THEN 'PI'
					WHEN PortAddress LIKE '%a2a-prd-cruscottosapp-sqlsrv%' THEN  'BI (PowerBI)'
					WHEN PortName LIKE '%BiCmi%' THEN 'BI (PowerBI)'
					WHEN PortName LIKE '%ERGPO%' THEN 'ERG'
					WHEN PortName LIKE '%ProtezioneCivile%' THEN 'Protezione Civile'
					WHEN PortName LIKE '%quake%' THEN 'Sito INGV (Terremoti)'
					WHEN PortName LIKE 'ipez%' THEN 'IPEX'
					WHEN PortAddress LIKE '%\ESO%' THEN 'Eso Terna Link'
					WHEN PortName LIKE '%Snam%' THEN 'SNAM'
					WHEN PortName LIKE '%SAPP%' THEN 'SAPP'

					ELSE ''
					END + ' (' + Adapter + ')'
FROM
	baseQuery

ORDER BY 1,2,3