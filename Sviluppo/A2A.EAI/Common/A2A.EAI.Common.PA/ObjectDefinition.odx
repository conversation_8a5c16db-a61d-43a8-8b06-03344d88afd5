﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="dd62b34e-9162-4127-ac57-eb85a5d6989f" LowerBound="1.1" HigherBound="34.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.Common.PA" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="MultipartMessageType" OID="35f25bb9-2cea-4358-9143-77766cd354dc" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FatturaPa_1_2_Type" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="32b2dae3-f3f6-4449-af35-65657558e521" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.Common.PA.Schemas.FatturaPa_v1_2" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="8dd225ba-0085-453e-a740-fb38334107e5" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FatturaPa_1_2_1_Type" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="b6ee75b6-7474-4818-b8f7-d41a69a7b32f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.Common.PA.Schemas.FatturaPa_v1_2_1" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="f5293d59-5163-47ab-a350-ed11eee93b80" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FatturaPa_1_2_InType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="ba2bfea3-7110-439b-aefc-05294e4c4a0e" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="1dd0756f-52e3-4434-8fa5-44d37579cbc8" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.31">
                    <om:Property Name="Ref" Value="A2A.EAI.Common.PA.FatturaPa_1_2_Type" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="94bfa684-9441-4f8b-beb5-1d1dfff0e7ad" ParentLink="Module_PortType" LowerBound="19.1" HigherBound="26.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FatturaPa_1_2_1_InType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="7e3247bd-6ecd-432a-9822-cae08567a9e8" ParentLink="PortType_OperationDeclaration" LowerBound="21.1" HigherBound="25.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="21c7182e-47c9-4a7b-8b81-58872ce3887b" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="23.13" HigherBound="23.33">
                    <om:Property Name="Ref" Value="A2A.EAI.Common.PA.FatturaPa_1_2_1_Type" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="31a88e21-b33e-4241-b135-96ef759b9d52" ParentLink="Module_ServiceDeclaration" LowerBound="26.1" HigherBound="33.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="305102f7-4585-4227-888d-4fb7d90fb24b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.Common.PA
{
    public messagetype FatturaPa_1_2_Type
    {
        body Schemas.FatturaPa_v1_2 parameters;
    };
    public messagetype FatturaPa_1_2_1_Type
    {
        body Schemas.FatturaPa_v1_2_1 parameters;
    };
    public porttype FatturaPa_1_2_InType
    {
        oneway parameters
        {
            FatturaPa_1_2_Type
        };
    };
    public porttype FatturaPa_1_2_1_InType
    {
        oneway parameters
        {
            FatturaPa_1_2_1_Type
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    public service ObjectDefinition
    {
        body ()
        {
        }
    }
}

