﻿using System;
using System.IO;
using Azure.Messaging.ServiceBus;
using Microsys.EAI.Framework.Azure.Services;

namespace A2A.EAI.Common.SB
{
    [Serializable]
    public class SbServices
    {

        ServiceBusClient client;
        ServiceBusReceiver receiver;

        private string _destinationFolder;
        private string _filePrefix;
        private string _fileExtension;
        private string _connectionString;
        private string _queueName;
        private int _timeToLiveInSeconds;

        public SbServices(string destinationFolder, string filePrefix, string fileExtension, string connectionString, string queueName, int timeToLiveInSeconds)
        {
            _destinationFolder = destinationFolder;
            _filePrefix = filePrefix;
            _fileExtension = fileExtension;
            _connectionString = connectionString;
            _queueName = queueName;
            _timeToLiveInSeconds = timeToLiveInSeconds;
        }

        public void Dequeue()
        {

            try
            {

                LoggingServices.TraceDebugInfo($"IN - Starting listening to '{_queueName}'");

                var clientOptions = new ServiceBusClientOptions()
                {
                    TransportType = ServiceBusTransportType.AmqpTcp
                };

                client = new ServiceBusClient(_connectionString, clientOptions);

                var receiverOptions = new ServiceBusReceiverOptions()
                {
                    ReceiveMode = ServiceBusReceiveMode.ReceiveAndDelete
                };

                receiver = client.CreateReceiver(_queueName, receiverOptions);

                DateTime startAt = DateTime.Now;

                TimeSpan maxWaitTime = new TimeSpan(0, 0, 5);

                while ((DateTime.Now - startAt).TotalSeconds < _timeToLiveInSeconds)
                {

                    var message = receiver.ReceiveMessageAsync(maxWaitTime).Result;

                    if (message != null)
                    {
                        var filePath = Path.Combine(_destinationFolder, string.Concat(_filePrefix, message.EnqueuedTime.ToString("yyyyMMddHHmmssfff"), "_", message.MessageId, ".", _fileExtension));

                        LoggingServices.TraceDebugInfo($"Message {message.MessageId} will save on {filePath}");

                        using (FileStream outputFileStream = new FileStream(filePath, FileMode.Create))
                        {
                            message.Body.ToStream().CopyTo(outputFileStream);
                        }
                    }

                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                receiver.DisposeAsync();
                client.DisposeAsync();

                LoggingServices.TraceDebugInfo("OUT");
            }

        }


    }
}
