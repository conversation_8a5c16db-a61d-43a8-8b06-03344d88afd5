﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.25.0" targetFramework="net48" />
  <package id="Azure.Core.Amqp" version="1.2.0" targetFramework="net48" />
  <package id="Azure.Messaging.ServiceBus" version="7.11.1" targetFramework="net48" />
  <package id="Microsoft.Azure.Amqp" version="2.5.12" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="1.1.1" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="4.6.0" targetFramework="net48" />
  <package id="System.IO" version="4.3.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.4" targetFramework="net48" />
  <package id="System.Memory.Data" version="1.0.2" targetFramework="net48" />
  <package id="System.Net.Http" version="4.3.4" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime" version="4.3.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.7.1" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Algorithms" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.Cryptography.X509Certificates" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="4.7.2" targetFramework="net48" />
  <package id="System.Text.Json" version="4.7.2" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>