﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.Data;
using System.Data.SqlClient;

namespace A2A.EAI.Common.Services
{
    [Serializable]
    public static class MonitoringServices
    {

        public static void MonitorQueueInsert(string activityName, string activityId, string n2FileName, DateTime dateFrom, DateTime dateTo)
        {

            try
            {

                LoggingServices.TraceDebugInfo($"IN - Activity Name: '{activityName}' - Activity Id: '{activityId}'");

                string connectionString = ConfigurationHelper.GetValue(ProcessServices.ApplicationName, "EAIConnectionString");

                using (SqlConnection connection = new SqlConnection(connectionString))
                {

                    connection.Open();

                    SqlCommand command = connection.CreateCommand();
                    command.CommandTimeout = 600;
                    command.CommandText = "MonitorQueueInsert";
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.Add(new SqlParameter("@activityName", activityName));
                    command.Parameters.Add(new SqlParameter("@activityId", activityId));
                    command.Parameters.Add(new SqlParameter("@n2FileName", n2FileName));
                    command.Parameters.Add(new SqlParameter("@dateFrom", dateFrom));
                    command.Parameters.Add(new SqlParameter("@dateTo", dateTo));

                    command.ExecuteNonQuery();
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

        }

    }
}
