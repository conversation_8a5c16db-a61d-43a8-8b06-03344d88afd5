﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace A2A.EAI.Common.Services
{
    [Serializable]
    public static class ProcessServices
    {
        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "Common";

        public const string FlowNameBiCollector = "BI Collector";

        public static int CalculateDuration(DateTimeOffset dateFrom, DateTimeOffset dateTo)
        {
            int returnValue = 0;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                if (dateFrom != null && dateTo != null)
                {
                    TimeSpan ts = (dateTo - dateFrom);
                    returnValue = (int)ts.TotalMilliseconds;
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;

        }

        public static string Left(this string value, int maxLength)
        {
            if (string.IsNullOrEmpty(value)) return value;
            
            maxLength = Math.Abs(maxLength);

            return (value.Length <= maxLength
                   ? value
                   : value.Substring(0, maxLength)
                   );
        }

        public static string CompressStringToBase64(string text)
        {
            byte[] buffer = Encoding.UTF8.GetBytes(text);
            var memoryStream = new MemoryStream();
            using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Compress, true))
            {
                gZipStream.Write(buffer, 0, buffer.Length);
            }

            memoryStream.Position = 0;

            var compressedData = new byte[memoryStream.Length];
            memoryStream.Read(compressedData, 0, compressedData.Length);

            var gZipBuffer = new byte[compressedData.Length + 4];
            Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
            Buffer.BlockCopy(BitConverter.GetBytes(buffer.Length), 0, gZipBuffer, 0, 4);
            return Convert.ToBase64String(gZipBuffer);
        }

        public static string CompressStringToBase64(XmlDocument inputMessage)
        {
            return CompressStringToBase64(inputMessage.InnerXml);
        }

        public static bool SendableDetails (XmlDocument message, string tagName)
        {
            bool returnValue = false;

            try
            {

                LoggingServices.TraceDebugInfo($"IN");

                var numOfItems = message.GetElementsByTagName(tagName).Count;

                LoggingServices.TraceDebugInfo($"Items: {numOfItems}");

                returnValue = (numOfItems > 0);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo($"OUT - Return {returnValue}");
            }

            return returnValue;
        }


        public static string EncodeToBase64(string input)
        {
            byte[] bytes = Encoding.UTF8.GetBytes(input);
            return Convert.ToBase64String(bytes);
        }

        public static string EncodeToBase64(XmlDocument inputMessage)
        {
            return EncodeToBase64(inputMessage.InnerText);
        }

        public static string DecodeFromBase64(string base64Input)
        {
            byte[] bytes = Convert.FromBase64String(base64Input);
            return Encoding.UTF8.GetString(bytes);
        }

    }
}
