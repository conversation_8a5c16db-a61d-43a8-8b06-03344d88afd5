﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.Xml;
using System.Xml.Linq;
using System.Globalization;
using System.Linq;
using System.IO;

namespace A2A.EAI.Common.Services
{

    [Serializable]
    public class VirtusServices
    {

        private const string VirtusDateTimeFormat = "yyyy-MM-ddTHH:mm:ss.fffZ";

        public string ComposeDate(string inDate, string inHour, string inDateFormat, string inMinutes)
        {

            string returnValue = string.Empty;

            try
            {

                //LoggingServices.TraceDebugInfo($"IN - Input Date: '{inDate}' - Input Hour: '{inHour}'");

                DateTimeOffset dateLocal = DateTimeOffset.ParseExact(inDate, inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal);

                DateTimeOffset dateTimeUtc = dateLocal.ToUniversalTime().AddHours(int.Parse(inHour) - 1);

                if (!string.IsNullOrEmpty(inMinutes))
                {
                    dateTimeUtc = dateTimeUtc.AddMinutes(int.Parse(inMinutes));
                }

                returnValue = dateTimeUtc.ToString(VirtusDateTimeFormat);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                //LoggingServices.TraceDebugInfo($"OUT - Return: '{returnValue}'");
            }

            return returnValue;
        }

        public string ComposeDateWithQuarters(string inDate, string inQuarter, string inDateFormat)
        {

            string returnValue = string.Empty;

            try
            {

                //LoggingServices.TraceDebugInfo($"IN - Input Date: '{inDate}' - Input Quarter: '{inQuarter}'");

                DateTimeOffset dateLocal = DateTimeOffset.ParseExact(inDate, inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal);

                DateTimeOffset dateTimeUtc = dateLocal.ToUniversalTime().AddMinutes((int.Parse(inQuarter) - 1)*15);

                returnValue = dateTimeUtc.ToString(VirtusDateTimeFormat);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                //LoggingServices.TraceDebugInfo($"OUT - Return: '{returnValue}'");
            }

            return returnValue;
        }


        public string DateTimeLocalToUniversal(string inDate, string inDateFormat)
        {
            string returnValue = string.Empty;

            try
            {

                //LoggingServices.TraceDebugInfo($"IN - Input Date: '{inDate}'");

                DateTimeOffset dateLocal = DateTimeOffset.ParseExact(inDate, inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal);

                DateTimeOffset dateTimeUtc = dateLocal.ToUniversalTime();

                returnValue = dateTimeUtc.ToString(VirtusDateTimeFormat);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                //LoggingServices.TraceDebugInfo($"OUT - Return: '{returnValue}'");
            }

            return returnValue;
        }

        /// <summary>
        /// ottobre
        //2022-10-29T22:00:00.000Z --> 2022-10-30, hour: 1
        //2022-10-29T23:00:00.000Z --> 2022-10-30, hour: 2
        //2022-10-30T00:00:00.000Z --> 2022-10-30, hour: 3
        //2022-10-30T01:00:00.000Z --> 2022-10-30, hour: 4
        //2022-10-30T02:00:00.000Z --> 2022-10-30, hour: 5
        //...
        //2022-10-30T21:00:00.000Z --> 2022-10-30, hour: 24
        //2022-10-30T22:00:00.000Z --> 2022-10-30, hour: 25
        //2022-10-30T23:00:00.000Z --> 2022-10-31, hour: 1
        //
        // marzo
        //2022-03-27T19:00:00.000Z --> 2022-03-27, hour: 21
        //2022-03-27T20:00:00.000Z --> 2022-03-27, hour: 22
        //2022-03-27T21:00:00.000Z --> 2022-03-27, hour: 23
        //2022-03-27T22:00:00.000Z --> 2022-03-28, hour: 1
        //
        // normale
        //2022-08-03T22:00:00.000Z --> 2022-08-04, hour: 1
        //...
        //2022-08-04T21:00:00.000Z --> 2022-08-04, hour: 24
        //2022-08-04T22:00:00.000Z --> 2022-08-05, hour: 1

        /// </summary>
        /// <param name="inDateTimeString"></param>
        /// <param name="inDateFormat"></param>
        /// <param name="outDateFormat"></param>
        /// <returns></returns>
        public string VirtusUTCToTernaProgressive(string inDateTimeString, string inDateFormat, string outDateFormat)
        {

            string returnValue = string.Empty;

            try
            {
                DateTimeOffset inputDate_ = DateTime.ParseExact(inDateTimeString.Trim(), inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                TimeZoneInfo hwZone = TimeZoneInfo.FindSystemTimeZoneById("W. Europe Standard Time");
                DateTimeOffset inputDate = TimeZoneInfo.ConvertTime(inputDate_, hwZone);

                int hour = inputDate.Hour;
                int returnHour = hour;

                if (inputDate.Month == 3 || inputDate.Month == 10)
                {
                    // Calcolo il giorno del cambio ora dell'anno della data
                    DateTime date = new DateTime(inputDate.Year, inputDate.Month, 31);
                    date = date.AddDays(-(int)date.DayOfWeek);

                    // Se è l'ultima domenica di ottobre
                    if (inputDate.Month == 10 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        if (hour == 2 && inputDate.Offset == TimeSpan.FromHours(1))
                        {
                            returnHour = hour + 2;
                        }
                        else if (hour < 3)
                        {
                            returnHour = hour + 1;
                        }
                        else
                        {
                            returnHour = hour + 2;
                        }
                    }
                    // Se è l'ultima domenica di marzo
                    else if (inputDate.Month == 3 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        if (hour <= 1)
                        {
                            returnHour = hour + 1;
                        }
                        else
                        {
                            returnHour = hour;
                        }
                    }
                    else
                    {
                        returnHour = hour + 1;
                    }
                }
                else
                {
                    returnHour = hour + 1;
                }
                returnValue = string.Concat(inputDate.ToString(outDateFormat), ";", returnHour);
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            return returnValue;

        }

        /// <summary>
        /// Same as VirtusUTCToTernaProgressive but calculates also the progressive of the 15-minute interval
        /// </summary>
        /// <param name="inDateTimeString"></param>
        /// <param name="inDateFormat"></param>
        /// <param name="outDateFormat"></param>
        /// <returns></returns>
        public string VirtusUTCToTernaProgressiveQuarter(string inDateTimeString, string inDateFormat, string outDateFormat)
        {

            string returnValue = string.Empty;
            try
            {

                DateTimeOffset inputDate_ = DateTime.ParseExact(inDateTimeString.Trim(), inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                TimeZoneInfo hwZone = TimeZoneInfo.FindSystemTimeZoneById("W. Europe Standard Time");
                DateTimeOffset inputDate = TimeZoneInfo.ConvertTime(inputDate_, hwZone);

                int hour = inputDate.Hour;
                int returnHour = hour;

                if (inputDate.Month == 3 || inputDate.Month == 10)
                {
                    // Calcolo il giorno del cambio ora dell'anno della data
                    DateTime date = new DateTime(inputDate.Year, inputDate.Month, 31);
                    date = date.AddDays(-(int)date.DayOfWeek);

                    // Se è l'ultima domenica di ottobre
                    if (inputDate.Month == 10 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        if (hour == 2 && inputDate.Offset == TimeSpan.FromHours(1))
                        {
                            returnHour = hour + 2;
                        }
                        else if (hour < 3)
                        {
                            returnHour = hour + 1;
                        }
                        else
                        {
                            returnHour = hour + 2;
                        }
                    }
                    // Se è l'ultima domenica di marzo
                    else if (inputDate.Month == 3 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        if (hour <= 1)
                        {
                            returnHour = hour + 1;
                        }
                        else
                        {
                            returnHour = hour;
                        }
                    }
                    else
                    {
                        returnHour = hour + 1;
                    }
                }
                else
                {
                    returnHour = hour + 1;
                }

                int minOut = 0;
                if (inputDate.Minute < 15)
                {
                    minOut = 1;
                }
                else if (inputDate.Minute < 30)
                {
                    minOut = 2;
                }
                else if (inputDate.Minute < 45)
                {
                    minOut = 3;
                }
                else if (inputDate.Minute < 60)
                {
                    minOut = 4;
                }
                

                int progressiveQuarter = (returnHour - 1) * 4 + minOut;

                returnValue = string.Concat(inputDate.ToString(outDateFormat), ";", returnHour, ";", progressiveQuarter);
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            return returnValue;
        }

        public string DateTimeUniversalToLocal(string inDate, string inDateFormat, string outDateFormat)
        {

            string returnValue = string.Empty;

            try
            {

                //LoggingServices.TraceDebugInfo($"IN - Input Date: '{inDate}'");

                DateTimeOffset dateTimeUtc = DateTimeOffset.ParseExact(inDate, inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                TimeZoneInfo hwZone = TimeZoneInfo.FindSystemTimeZoneById("W. Europe Standard Time");
                DateTimeOffset dateLocal = TimeZoneInfo.ConvertTime(dateTimeUtc, hwZone);

                returnValue = dateLocal.ToString(outDateFormat);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                //LoggingServices.TraceDebugInfo($"OUT - Return: '{returnValue}'");
            }

            return returnValue;
        }

        public string GetDate()
        {
            return DateTime.UtcNow.ToString(VirtusDateTimeFormat);
        }

        public string FormatVirtusValue(string input)
        {
            string returnValue = input;

            try
            {
                returnValue = input.Replace(".", "").Replace(",", ".");
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }

            return returnValue;
        }


        /// <summary>
        /// Calculates and sets the correct Start and End for Virtus Request Timeseries Segments
        /// </summary>
        /// <param name="input"></param>
        /// <param name="timedelta"></param>
        /// <returns></returns>
        public static XmlDocument FixVirtusRequestSegments(XmlDocument input, int deltaMinutes)
        {
            try
            {
                LoggingServices.TraceDebugInfo($"IN - deltaMinutes: '{deltaMinutes}'");
                var xmlns = new XmlNamespaceManager(input.NameTable);
                xmlns.AddNamespace("ns0", "http://www.powel.com/SmE/AMQPmessageTypes/v1.0");

                var segments = input.SelectNodes("//ns0:Segment", xmlns);
                foreach (XmlNode segment in segments)
                {
                    var allpoints = segment.SelectNodes("ns0:Point", xmlns);
                    if (allpoints.Count == 0)
                    {
                        continue;
                    }
                    var orderedTimestamps = allpoints.Cast<XmlNode>().Select(x => x.Attributes["Timestamp"].Value);
                    var min = orderedTimestamps.Min();
                    var max = orderedTimestamps.Max();
                    DateTimeOffset maxDate = DateTimeOffset.ParseExact(max, VirtusDateTimeFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                    maxDate = maxDate.AddMinutes(deltaMinutes);
                    segment.Attributes["Start"].Value = min;
                    segment.Attributes["End"].Value = maxDate.ToString(VirtusDateTimeFormat);
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }


            return input;
        }

        /// <summary>
        /// Gets the activityId from the filename, the second part after "___"
        /// </summary>
        /// <param name="fileName"></param>
        /// <param name="activityInstanceId"></param>
        /// <returns></returns>
        public static string GetRelatedActivityByFileName(string fileName, string activityInstanceId)
        {
            string returnValue = activityInstanceId;

            try
            {
                LoggingServices.TraceDebugInfo("IN - fileName: {fileName}", fileName);

                fileName = Path.GetFileNameWithoutExtension(fileName);

                returnValue = fileName.Split(new string[] { "___" }, StringSplitOptions.None)[1];
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                // May occur if manually resubmitted
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT - Related Activity Name: {returnValue}", returnValue);
            }

            return returnValue;
        }


        /// <summary>
        /// 2022-10-30 il numero di ore è 25
        /// 2022-09-09 il numero di ore è 24
        /// 2023-03-26 il numero di ore è 23
        /// </summary>
        /// <param name="inDate"></param>
        /// <param name="inDateFormat"></param>
        /// <returns></returns>
        public static int GetHourNumberOfDate(string inDate, string inDateFormat)
        {
            int returnValue = 24;
            try
            {
                DateTimeOffset inputDate = DateTime.ParseExact(inDate.Trim(), inDateFormat, CultureInfo.InvariantCulture, DateTimeStyles.AssumeUniversal);
                if (inputDate.Month == 3 || inputDate.Month == 10)
                {
                    // Calcolo il giorno del cambio ora dell'anno della data
                    DateTime date = new DateTime(inputDate.Year, inputDate.Month, 31);
                    date = date.AddDays(-(int)date.DayOfWeek);

                    // Se è l'ultima domenica di ottobre
                    if (inputDate.Month == 10 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        returnValue = 25;
                    }
                    // Se è l'ultima domenica di marzo
                    else if (inputDate.Month == 3 && inputDate.Month == date.Month && inputDate.Day == date.Day)
                    {
                        returnValue = 23;
                    }
                }
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }


            return returnValue;
        }

        /// <summary>
        /// Split string on given character and return selected part
        /// </summary>
        /// <param name="name"></param>
        /// <param name="splitOn"></param>
        /// <param name="part"></param>
        /// <returns></returns>
        public string SplitName(string name, string splitOn, int part)
        {
            string returnValue = string.Empty;

            try
            {
                returnValue = name.Split(splitOn.ToCharArray())[part];
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);

                throw;
            }


            return returnValue;

        }
    }
}
