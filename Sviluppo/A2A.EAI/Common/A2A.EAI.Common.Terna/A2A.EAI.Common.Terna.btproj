﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CB59B97C-6B70-4B53-AB01-D0A9BFECBE41}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.Common.Terna</RootNamespace>
    <AssemblyName>A2A.EAI.Common.Terna</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Schemas\RupMIB.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>RupMIB.xsd</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <None Include="Schemas\RupMIB.xsc">
      <DependentUpon>RupMIB.xsd</DependentUpon>
    </None>
    <None Include="Schemas\RupMIB.xss">
      <DependentUpon>RupMIB.xsd</DependentUpon>
    </None>
    <Schema Include="Schemas\RupVDT.xsd">
      <SubType>Task</SubType>
      <TypeName>RupVDT</TypeName>
      <Namespace>A2A.EAI.Common.Terna.Schemas</Namespace>
    </Schema>
    <Schema Include="Schemas\RupMIB.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>RupMIB.Designer.cs</LastGenOutput>
      <SubType>Task</SubType>
      <TypeName>RupMIB</TypeName>
      <Namespace>A2A.EAI.Common.Terna.Schemas</Namespace>
    </Schema>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>