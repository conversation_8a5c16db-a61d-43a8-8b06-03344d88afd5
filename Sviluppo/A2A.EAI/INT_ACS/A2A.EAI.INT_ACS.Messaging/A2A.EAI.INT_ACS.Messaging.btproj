﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CBCAF53D-4B0B-4E9F-900F-3CEBC2A570BA}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_ACS.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_ACS.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreBresciaPolling.Type.xsd">
      <SubType>Task</SubType>
      <TypeName>ACSVenditaCaloreBresciaPolling_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreBresciaPolling.xsd">
      <SubType>Task</SubType>
      <TypeName>ACSVenditaCaloreBresciaPolling</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\N2VenditaCalore.Type.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2VenditaCalore_Type</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\N2VenditaCalore.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2VenditaCalore</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreMilanoPolling.xsd">
      <TypeName>ACSVenditaCaloreMilanoPolling</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreMilanoPolling.Type.xsd">
      <TypeName>ACSVenditaCaloreMilanoPolling_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreBresciaInsertTypedProcedure.Type.xsd">
      <TypeName>ACSVenditaCaloreBresciaInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreBresciaInsertTypedProcedure.xsd">
      <TypeName>ACSVenditaCaloreBresciaInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\VenditaCaloreBrescia.xsd">
      <TypeName>VenditaCaloreBrescia</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\VenditaCaloreBrescia.Type.xsd">
      <TypeName>VenditaCaloreBrescia_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\N2ConsumiRetragas.Type.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2ConsumiRetragas_Type</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\N2ConsumiRetragas.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2ConsumiRetragas</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreMilanoInsertTypedProcedure.Type.xsd">
      <TypeName>ACSVenditaCaloreMilanoInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\ACSVenditaCaloreMilanoInsertTypedProcedure.xsd">
      <TypeName>ACSVenditaCaloreMilanoInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\VenditaCaloreMilano.Type.xsd">
      <TypeName>VenditaCaloreMilano_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\VenditaCalore\VenditaCaloreMilano.xsd">
      <TypeName>VenditaCaloreMilano</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\N2ConsumiUnareti.Type.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2ConsumiUnareti_Type</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\N2ConsumiUnareti.xsd">
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <TypeName>N2ConsumiUnareti</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseUnaretiPolling.xsd">
      <TypeName>ACSCO2EmesseUnaretiPolling</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseUnaretiPolling.Type.xsd">
      <TypeName>ACSCO2EmesseUnaretiPolling_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSUpdateStatusTypedProcedure.xsd">
      <TypeName>ACSUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseRetragasInsertTypedProcedure.Type.xsd">
      <TypeName>ACSCO2EmesseRetragasInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseRetragasInsertTypedProcedure.xsd">
      <TypeName>ACSCO2EmesseRetragasInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\CO2EmesseRetragas.Type.xsd">
      <TypeName>CO2EmesseRetragas_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\CO2EmesseRetragas.xsd">
      <TypeName>CO2EmesseRetragas</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseUnaretiInsertTypedProcedure.Type.xsd">
      <TypeName>ACSCO2EmesseUnaretiInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseUnaretiInsertTypedProcedure.xsd">
      <TypeName>ACSCO2EmesseUnaretiInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\CO2EmesseUnareti.Type.xsd">
      <TypeName>CO2EmesseUnareti_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\CO2EmesseUnareti.xsd">
      <TypeName>CO2EmesseUnareti</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipelines\In\CO2EmesseUnareti.btp">
      <TypeName>CO2EmesseUnareti</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\ACSCO2EmesseRetragasInsert.bindinginfo.xml" />
    <Content Include="Bindings\ACSCO2EmesseUnaretiUpdateStatus.bindinginfo.xml" />
    <Content Include="Bindings\N2ConsumiUnareti.BindingInfo.xml" />
    <Content Include="Bindings\ACSVenditaCaloreMilanoInsert.bindinginfo.xml" />
    <Content Include="Bindings\N2ConsumiRetragas.BindingInfo.xml" />
    <Content Include="Bindings\N2VenditaCalore.BindingInfo.xml" />
    <Map Include="Maps\VenditaCalore\ACSVenditaCaloreBresciaPollingToN2VenditaCalore.btm">
      <TypeName>ACSVenditaCaloreBresciaPollingToN2VenditaCalore</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\VenditaCalore\ACSVenditaCaloreBresciaPollingToACSUpdateStatus.btm">
      <TypeName>ACSVenditaCaloreBresciaPollingToACSUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\VenditaCalore\ACSVenditaCaloreMilanoPollingToN2VenditaCalore.btm">
      <TypeName>ACSVenditaCaloreMilanoPollingToN2VenditaCalore</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\VenditaCalore\ACSVenditaCaloreMilanoPollingToACSUpdateStatus.btm">
      <TypeName>ACSVenditaCaloreMilanoPollingToACSUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\VenditaCalore\VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert.btm">
      <TypeName>VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Content Include="Bindings\ACSVenditaCaloreBresciaInsert.bindinginfo.xml" />
    <Pipeline Include="Pipelines\In\VenditaCaloreBrescia.btp">
      <TypeName>VenditaCaloreBrescia</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\CO2Emesse\ACSCO2EmesseRetragasPollingToN2ConsumiRetragas.btm">
      <TypeName>ACSCO2EmesseRetragasPollingToN2ConsumiRetragas</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\VenditaCalore\VenditaCaloreMilanoToACSVenditaCaloreMilanoInsert.btm">
      <TypeName>VenditaCaloreMilanoToACSVenditaCaloreMilanoInsert</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\In\VenditaCaloreMilano.btp">
      <TypeName>VenditaCaloreMilano</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\CO2Emesse\ACSCO2EmesseRetragasPollingToACSUpdateStatus.btm">
      <TypeName>ACSCO2EmesseRetragasPollingToACSUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\CO2Emesse\ACSCO2EmesseUnaretiPollingToACSUpdateStatus.btm">
      <TypeName>ACSCO2EmesseUnaretiPollingToACSUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\CO2Emesse\ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti.btm">
      <TypeName>ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseRetragasPolling.xsd">
      <TypeName>ACSCO2EmesseRetragasPolling</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\CO2Emesse\ACSCO2EmesseRetragasPolling.Type.xsd">
      <TypeName>ACSCO2EmesseRetragasPolling_Type</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Map Include="Maps\CO2Emesse\CO2EmesseRetragasToACSCO2EmesseRetragasInsert.btm">
      <TypeName>CO2EmesseRetragasToACSCO2EmesseRetragasInsert</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\In\CO2EmesseRetragas.btp">
      <TypeName>CO2EmesseRetragas</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\CO2Emesse\CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert.btm">
      <TypeName>CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert</TypeName>
      <Namespace>A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Content Include="Bindings\ACSCO2EmesseUnaretiInsert.bindinginfo.xml" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>