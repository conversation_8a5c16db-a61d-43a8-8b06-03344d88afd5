﻿<?xml version="1.0" encoding="utf-8"?>
<BindingInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" Assembly="Microsoft.BizTalk.Adapter.Wcf.Consuming, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Version="*******">
  <Timestamp>2021-03-24T16:42:10.2786684+01:00</Timestamp>
  <SendPortCollection>
    <SendPort Name="WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap" IsStatic="true" IsTwoWay="true" BindingOption="0">
      <Description>service "ConsumiRetragas" port "ConsumiRetragasSoap"</Description>
      <TransmitPipeline Name="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="2" />
      <PrimaryTransport>
        <Address>http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx</Address>
        <TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
        <TransportTypeData>&lt;CustomProps&gt;
  &lt;MaxReceivedMessageSize vt="3"&gt;65536&lt;/MaxReceivedMessageSize&gt;
  &lt;MessageEncoding vt="8"&gt;Text&lt;/MessageEncoding&gt;
  &lt;TextEncoding vt="8"&gt;utf-8&lt;/TextEncoding&gt;
  &lt;SecurityMode vt="8"&gt;None&lt;/SecurityMode&gt;
  &lt;MessageClientCredentialType vt="8"&gt;UserName&lt;/MessageClientCredentialType&gt;
  &lt;AlgorithmSuite vt="8"&gt;Basic256&lt;/AlgorithmSuite&gt;
  &lt;TransportClientCredentialType vt="8"&gt;None&lt;/TransportClientCredentialType&gt;
  &lt;UseAcsAuthentication vt="11"&gt;0&lt;/UseAcsAuthentication&gt;
  &lt;UseSasAuthentication vt="11"&gt;0&lt;/UseSasAuthentication&gt;
  &lt;UseSSO vt="11"&gt;0&lt;/UseSSO&gt;
  &lt;ProxyToUse vt="8"&gt;Default&lt;/ProxyToUse&gt;
  &lt;StaticAction vt="8"&gt;&amp;lt;BtsActionMapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&amp;gt;
  &amp;lt;Operation Name="ImportazioneRetragas" Action="http://nbdo.org/ImportazioneRetragas" /&amp;gt;
&amp;lt;/BtsActionMapping&amp;gt;&lt;/StaticAction&gt;
  &lt;InboundBodyLocation vt="8"&gt;UseBodyElement&lt;/InboundBodyLocation&gt;
  &lt;InboundNodeEncoding vt="8"&gt;Xml&lt;/InboundNodeEncoding&gt;
  &lt;OutboundBodyLocation vt="8"&gt;UseBodyElement&lt;/OutboundBodyLocation&gt;
  &lt;OutboundXmlTemplate vt="8"&gt;&amp;lt;bts-msg-body xmlns="http://www.microsoft.com/schemas/bts2007" encoding="xml"/&amp;gt;&lt;/OutboundXmlTemplate&gt;
  &lt;PropagateFaultMessage vt="11"&gt;-1&lt;/PropagateFaultMessage&gt;
  &lt;OpenTimeout vt="8"&gt;00:01:00&lt;/OpenTimeout&gt;
  &lt;SendTimeout vt="8"&gt;00:01:00&lt;/SendTimeout&gt;
  &lt;CloseTimeout vt="8"&gt;00:01:00&lt;/CloseTimeout&gt;
&lt;/CustomProps&gt;</TransportTypeData>
        <RetryCount>3</RetryCount>
        <RetryInterval>5</RetryInterval>
        <ServiceWindowEnabled>false</ServiceWindowEnabled>
        <FromTime>2000-01-01T00:00:00</FromTime>
        <ToTime>2000-01-01T23:59:59</ToTime>
        <Primary>true</Primary>
        <OrderedDelivery>false</OrderedDelivery>
        <DeliveryNotification>1</DeliveryNotification>
        <SendHandler xsi:nil="true" />
      </PrimaryTransport>
      <ReceivePipeline Name="Microsoft.BizTalk.DefaultPipelines.XMLReceive" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.XMLReceive, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="1" />
      <ReceivePipelineData xsi:nil="true" />
      <Tracking>0</Tracking>
      <Filter />
      <OrderedDelivery>false</OrderedDelivery>
      <Priority>5</Priority>
      <StopSendingOnFailure>false</StopSendingOnFailure>
      <RouteFailedMessage>false</RouteFailedMessage>
      <ApplicationName xsi:nil="true" />
    </SendPort>
    <SendPort Name="WcfSendPort_ConsumiRetragas_ConsumiRetragasSoap12" IsStatic="true" IsTwoWay="true" BindingOption="0">
      <Description>service "ConsumiRetragas" port "ConsumiRetragasSoap12"</Description>
      <TransmitPipeline Name="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="2" />
      <PrimaryTransport>
        <Address>http://dctsvw034.group.local/NBDOws/BDM/Gas/ConsumiRetragas.asmx</Address>
        <TransportType Name="WCF-Custom" Capabilities="907" ConfigurationClsid="af081f69-38ca-4d5b-87df-f0344b12557a" />
        <TransportTypeData>&lt;CustomProps&gt;
  &lt;BindingType vt="8"&gt;customBinding&lt;/BindingType&gt;
  &lt;BindingConfiguration vt="8"&gt;&amp;lt;binding name="ConsumiRetragasSoap12"&amp;gt;&amp;lt;textMessageEncoding messageVersion="Soap12" /&amp;gt;&amp;lt;httpTransport /&amp;gt;&amp;lt;/binding&amp;gt;&lt;/BindingConfiguration&gt;
  &lt;StaticAction vt="8"&gt;&amp;lt;BtsActionMapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&amp;gt;
  &amp;lt;Operation Name="ImportazioneRetragas" Action="http://nbdo.org/ImportazioneRetragas" /&amp;gt;
&amp;lt;/BtsActionMapping&amp;gt;&lt;/StaticAction&gt;
  &lt;UseSSO vt="11"&gt;0&lt;/UseSSO&gt;
  &lt;InboundBodyLocation vt="8"&gt;UseBodyElement&lt;/InboundBodyLocation&gt;
  &lt;InboundNodeEncoding vt="8"&gt;Xml&lt;/InboundNodeEncoding&gt;
  &lt;OutboundBodyLocation vt="8"&gt;UseBodyElement&lt;/OutboundBodyLocation&gt;
  &lt;OutboundXmlTemplate vt="8"&gt;&amp;lt;bts-msg-body xmlns="http://www.microsoft.com/schemas/bts2007" encoding="xml"/&amp;gt;&lt;/OutboundXmlTemplate&gt;
  &lt;PropagateFaultMessage vt="11"&gt;-1&lt;/PropagateFaultMessage&gt;
  &lt;EnableTransaction vt="11"&gt;0&lt;/EnableTransaction&gt;
  &lt;IsolationLevel vt="8"&gt;Serializable&lt;/IsolationLevel&gt;
  &lt;Identity vt="8" /&gt;
&lt;/CustomProps&gt;</TransportTypeData>
        <RetryCount>3</RetryCount>
        <RetryInterval>5</RetryInterval>
        <ServiceWindowEnabled>false</ServiceWindowEnabled>
        <FromTime>2000-01-01T00:00:00</FromTime>
        <ToTime>2000-01-01T23:59:59</ToTime>
        <Primary>true</Primary>
        <OrderedDelivery>false</OrderedDelivery>
        <DeliveryNotification>1</DeliveryNotification>
        <SendHandler xsi:nil="true" />
      </PrimaryTransport>
      <ReceivePipeline Name="Microsoft.BizTalk.DefaultPipelines.XMLReceive" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.XMLReceive, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="1" />
      <ReceivePipelineData xsi:nil="true" />
      <Tracking>0</Tracking>
      <Filter />
      <OrderedDelivery>false</OrderedDelivery>
      <Priority>5</Priority>
      <StopSendingOnFailure>false</StopSendingOnFailure>
      <RouteFailedMessage>false</RouteFailedMessage>
      <ApplicationName xsi:nil="true" />
    </SendPort>
  </SendPortCollection>
</BindingInfo>