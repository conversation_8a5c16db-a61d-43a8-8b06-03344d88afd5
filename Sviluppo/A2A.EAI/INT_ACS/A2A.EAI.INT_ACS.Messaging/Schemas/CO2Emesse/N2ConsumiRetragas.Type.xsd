<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Schema.ISKRA_ContatoriConsumoGas.ConsumiRetragas" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Schema.ISKRA_ContatoriConsumoGas.ConsumiRetragas" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="import" type="tns:ConsumiRetragasDati" />
  <xs:complexType name="ConsumiRetragasDati">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Header" type="tns:Header" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Header">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="CodicePuntoRiconsegna" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="SegmentoRete" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Punto" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="AOP" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="TipoMisura" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Rows" type="tns:Rows" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Rows">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Row" type="tns:Row" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Row">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Data" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" form="unqualified" name="Valore" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>