<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Schema.ISKRA_ContatoriConsumoGas.Ricavi" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Schema.ISKRA_ContatoriConsumoGas.Ricavi" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="import" type="tns:RicaviDati" />
  <xs:complexType name="RicaviDati">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Nomefile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Anno" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Header" type="tns:RicaviHeader" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RicaviHeader">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Misura" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Unitamisura" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Impianto" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Rows" type="tns:RicaviRow" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RicaviRow">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Mese" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" form="unqualified" name="Valore" type="xs:decimal" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>