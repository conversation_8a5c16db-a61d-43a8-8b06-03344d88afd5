﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="e9c8c1cb-88cc-468d-8705-4ffa08fa6444" LowerBound="1.1" HigherBound="298.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="57d17978-8705-4242-bbec-95b659a8b412" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="297.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="0391ae26-f08f-41a0-ad09-045278b99ebe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponseContent" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="420ad363-1d2f-4534-a571-c5d3bd1d308c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ebbd733e-21a9-44e7-8285-baffc380276d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="fbd37468-7856-4765-bdb0-28d5fedcd05c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9376bcf6-3f1c-4b70-b88c-0e978d94e491" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="948eeaed-f7e0-4051-a97e-0924fe196c6a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5dfac57a-5cc0-47b9-8af9-ab164e83d9db" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="99974a81-732b-4c2b-8c97-f333e4ce3545" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="318960c1-7000-4c43-9703-693be6d21bb2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5e67f942-7bd6-47ec-a2f7-dfc740773967" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4e82fa21-064e-454c-8efc-8c72a2015a80" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2b1d6882-33e7-4230-ab49-7eb5ec4765b7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c181e60f-2b22-48ba-b87f-9d7f6a1fe83f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7b7be7bd-bec0-4e70-b456-addae00b36a7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e20e8685-0ff8-4337-aab7-0cb8a7249f22" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseRetragasType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseRetragas" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d695e4f9-1c4e-49f8-8b21-ba14e863965b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiRetragasRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiRetragasRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="9ac1efec-fa04-4d68-994e-461e22d6a6d8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiRetragasResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiRetragasResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="1e260a49-6328-42c6-a150-8ddcc984544b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="4359eef9-0d9c-4bd5-b75c-b964f54b5422" ParentLink="ServiceBody_Statement" LowerBound="61.1" HigherBound="68.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ACSCO2EmesseRetragasPollingIn" />
                    <om:Property Name="MessageName" Value="ACSCO2EmesseRetragas" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="95dbf4f6-49aa-43fe-97dc-5c427f3dc8d8" ParentLink="ServiceBody_Statement" LowerBound="68.1" HigherBound="80.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseRetragasProcess;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;WSResponseContent = System.String.Empty;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="94aea775-3a10-4278-8453-7f2da61f368c" ParentLink="ServiceBody_Statement" LowerBound="80.1" HigherBound="92.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;CO2EmesseRetragas&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSCO2EmesseRetragas.parameters.activityIdStaging, &quot;&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="3a379734-0d9c-48a8-bfa6-e63572d5ad28" ParentLink="ServiceBody_Statement" LowerBound="92.1" HigherBound="193.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="b3512015-0db4-4e04-89a7-350d483fa924" ParentLink="ComplexStatement_Statement" LowerBound="97.1" HigherBound="105.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create WS" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="7df9b06a-fbf1-4ed6-996f-9a9b320e2885" ParentLink="Construct_MessageRef" LowerBound="98.31" HigherBound="98.55">
                            <om:Property Name="Ref" Value="N2ConsumiRetragasRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="bd57ffa3-8107-4bb1-b4a1-40252d379ead" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="102.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseRetragasPollingToN2ConsumiRetragas" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup WS" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="519d4e6b-c90e-40ff-8612-33d22a5fdba8" ParentLink="Transform_InputMessagePartRef" LowerBound="101.164" HigherBound="101.195">
                                <om:Property Name="MessageRef" Value="ACSCO2EmesseRetragas" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="cda9452c-352c-4737-a1d6-85cd9d282006" ParentLink="Transform_OutputMessagePartRef" LowerBound="101.36" HigherBound="101.71">
                                <om:Property Name="MessageRef" Value="N2ConsumiRetragasRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="0e734dd8-6a3d-4fee-9876-e09beb345ad9" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                            <om:Property Name="Expression" Value="N2ConsumiRetragasRequest.parameters.import.NomeFile = System.IO.Path.GetFileName(ACSCO2EmesseRetragas.parameters.NomeFile);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="NomeFile" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="21737510-5d30-4900-b1ea-ff2cd1387682" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="107.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="63a2f5c8-1b71-4fac-b6a9-934bd03b205e" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                        <om:Property Name="PortName" Value="N2ConsumiRetragasOut" />
                        <om:Property Name="MessageName" Value="N2ConsumiRetragasRequest" />
                        <om:Property Name="OperationName" Value="ImportazioneRetragas" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="863938d5-9705-4c70-9287-66d0e6835554" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="111.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="N2ConsumiRetragasOut" />
                        <om:Property Name="MessageName" Value="N2ConsumiRetragasResponse" />
                        <om:Property Name="OperationName" Value="ImportazioneRetragas" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="77dbc04e-e8e1-4456-b0c2-85317d123328" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="115.1">
                        <om:Property Name="Expression" Value="WSResponse = N2ConsumiRetragasResponse.parameters;&#xD;&#xA;WSResponseContent = WSResponse.InnerXml.ToString();&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Assign Response" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Decision" OID="b3ac1cc2-7acb-4036-a894-3609286e2a21" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="128.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Response OK" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="f6d9ef1c-c12e-4a53-b4e1-c276c54c89d9" ParentLink="ReallyComplexStatement_Branch" LowerBound="116.21" HigherBound="128.1">
                            <om:Property Name="Expression" Value="WSResponseContent.Contains(&quot;KO&quot;) == true" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="NOT OK" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="205ee3d7-f7b0-46ca-9ee0-c7f4f4d01e62" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="127.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(WSResponse.InnerText);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Error" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="7b54ae1a-1b77-4ce4-bf69-9115f70c4a4f" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="64f8d136-4451-4f41-9f64-1ed3793706a8" ParentLink="ComplexStatement_Statement" LowerBound="128.1" HigherBound="135.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Esito WS&quot;, WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="c2f5ecca-21ee-4bf8-aa13-6407ca025db0" ParentLink="Scope_Catch" LowerBound="138.1" HigherBound="152.1">
                        <om:Property Name="ExceptionName" Value="validationExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Message Type Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="52c4b5b0-4ab0-4275-9dde-b902e77d5873" ParentLink="Catch_Statement" LowerBound="141.1" HigherBound="151.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(validationExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="add6b65a-f2bb-439f-9534-a59e433ee5d1" ParentLink="Scope_Catch" LowerBound="152.1" HigherBound="166.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="N2ConsumiRetragasOut.ImportazioneRetragas.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="dabf8a74-2c63-479f-bffa-7ac38cf490fd" ParentLink="Catch_Statement" LowerBound="155.1" HigherBound="165.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="149fd7f7-a58c-4215-b4a8-f855fe582567" ParentLink="Scope_Catch" LowerBound="166.1" HigherBound="179.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f695fb28-a8b6-4a4e-b625-9b3aad9e024a" ParentLink="Catch_Statement" LowerBound="169.1" HigherBound="178.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="47721a92-8bb5-483d-8851-ab1493a93929" ParentLink="Scope_Catch" LowerBound="179.1" HigherBound="191.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="460525e0-f61b-4e25-8f1a-bb98d6fbb514" ParentLink="Catch_Statement" LowerBound="182.1" HigherBound="190.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="c4ff5938-bc3a-4441-ab3a-a2bbd53c9198" ParentLink="ServiceBody_Statement" LowerBound="193.1" HigherBound="249.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="ebc4bc39-92f4-4aad-97cb-94e5bc7c9ebf" ParentLink="ComplexStatement_Statement" LowerBound="198.1" HigherBound="208.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="df3bf92e-1395-4721-8f1d-c3b86e46040a" ParentLink="ComplexStatement_Statement" LowerBound="201.1" HigherBound="203.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseRetragasPollingToACSUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="0ede42a3-0a50-41aa-9659-dddae890b476" ParentLink="Transform_InputMessagePartRef" LowerBound="202.160" HigherBound="202.191">
                                <om:Property Name="MessageRef" Value="ACSCO2EmesseRetragas" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="602acfa6-dc91-4de3-a35d-8a8928ccc91a" ParentLink="Transform_OutputMessagePartRef" LowerBound="202.36" HigherBound="202.69">
                                <om:Property Name="MessageRef" Value="ACSUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="86b1ff9a-fd87-4b5f-8117-8af2a72c8fc6" ParentLink="ComplexStatement_Statement" LowerBound="203.1" HigherBound="207.1">
                            <om:Property Name="Expression" Value="ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="7eb30720-fb64-4d5c-b506-72bdb1817614" ParentLink="Construct_MessageRef" LowerBound="199.31" HigherBound="199.53">
                            <om:Property Name="Ref" Value="ACSUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="371eb17d-623e-452c-a437-a261eeabc380" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="210.1">
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="51985985-97b7-4536-a74e-eb3a9c64fa71" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="212.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="30977dc6-bb38-437c-9f07-3849257305ac" ParentLink="ComplexStatement_Statement" LowerBound="212.1" HigherBound="216.1">
                        <om:Property Name="Expression" Value="sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="b67b852b-38e0-4dd5-afe3-8e1f4061cb71" ParentLink="Scope_Catch" LowerBound="219.1" HigherBound="233.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="db2461fb-2ab2-4014-a124-9caaf0b53198" ParentLink="Catch_Statement" LowerBound="222.1" HigherBound="232.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="38d7810d-8309-4c5d-89c3-d86360dccc43" ParentLink="Scope_Catch" LowerBound="233.1" HigherBound="247.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="552eb66d-97cc-4a89-a05c-4bb2c2e31109" ParentLink="Catch_Statement" LowerBound="236.1" HigherBound="246.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="73b09abd-ac47-4fd2-bc5b-5c174de75d6b" ParentLink="ServiceBody_Statement" LowerBound="249.1" HigherBound="286.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="9fe56a10-7e8f-4bcc-8488-965bf9afcec5" ParentLink="ReallyComplexStatement_Branch" LowerBound="250.13" HigherBound="253.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="27caf607-0066-44c5-9f50-cd6ed2b92a89" ParentLink="ReallyComplexStatement_Branch" LowerBound="253.18" HigherBound="256.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="754db42a-98e7-4274-a394-8822f66d9eac" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="10ea3c98-7178-4b78-9f10-4555dc3c4d9c" ParentLink="ComplexStatement_Statement" LowerBound="258.1" HigherBound="285.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="bbea34f4-b046-4d56-9718-2f3f5bce07ea" ParentLink="ComplexStatement_Statement" LowerBound="263.1" HigherBound="281.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="8621a7ff-b17e-436d-8bc7-ce66ef73bc21" ParentLink="ComplexStatement_Statement" LowerBound="266.1" HigherBound="280.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameters.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="9b911afb-ff61-4986-9096-8e990c882c77" ParentLink="Construct_MessageRef" LowerBound="264.35" HigherBound="264.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="f7e48eda-b427-4c7a-badc-1ed67679fdb1" ParentLink="ComplexStatement_Statement" LowerBound="281.1" HigherBound="283.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="9151675a-8070-4605-8235-3f8cf1076b0f" ParentLink="ServiceBody_Statement" LowerBound="286.1" HigherBound="295.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9555ae63-a363-41c1-9d3e-b92aed31f3bb" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseRetragasPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseRetragasPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="68388d0f-66ef-4112-9cc6-b512716d53ed" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4917530b-670f-4c6f-b3c9-a3cf4a32b6b9" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="115" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaRetiUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="84963af2-c532-4b90-9c5b-4ef1833d8470" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="94b88cf7-6090-4266-9aea-635ff8d83576" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="174" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="c55aa5d7-a496-4338-875a-66d93520e8da" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3fb735ba-08fb-4bae-8b76-1a51065dd62a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="40.1" HigherBound="42.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="37" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiRetragasOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiRetragasOut" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="7a0924e9-6ff4-41e7-bfd5-1326fcdb9447" ParentLink="PortDeclaration_CLRAttribute" LowerBound="40.1" HigherBound="41.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="ccee098c-76ae-49f7-80f8-5ba6ea32fb69" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseRetragasType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="02d07f5d-363c-422e-a6cb-56d14ea328a1" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasPolling_Type" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="39eb1ef2-fb17-44d1-b47f-290ecf6ffd66" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiRetragasRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a40661a0-f8ee-49eb-a748-7225c0c3fd67" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiRetragas.ImportazioneRetragas" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c86e69d2-f3f2-4488-aef7-1d625f868332" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiRetragasResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e4009098-9034-4afa-a716-d844a298b9d5" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiRetragas.ImportazioneRetragasResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="cb403588-dcc0-4702-8ac5-e2407fcad1b5" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseRetragasPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3e611fd9-9985-43e1-bebc-d1a4dd71fe09" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="5c6beee1-d294-4f3f-ae93-b7f810e12860" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.37">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseRetragasType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="8db27dd4-cd44-4b20-9f5f-a5e8c1130b6b" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiRetragasOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="dfaf3789-56d3-4eb3-b215-e5e6e5bfe31f" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ImportazioneRetragas" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="0b42d50e-8a95-4f78-874d-eb820788295e" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.41">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiRetragasRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="538191f3-360c-4763-9cda-0c4d1ba1ac11" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="27.74" HigherBound="27.91">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.FaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="0fe0a595-3be8-42ac-ad23-4c10a4c96ea7" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.43" HigherBound="27.72">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiRetragasResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype ACSCO2EmesseRetragasType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasPolling_Type parameters;
    };
    internal messagetype N2ConsumiRetragasRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiRetragas.ImportazioneRetragas parameters;
    };
    internal messagetype N2ConsumiRetragasResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiRetragas.ImportazioneRetragasResponse parameters;
    };
    internal porttype ACSCO2EmesseRetragasPollingInType
    {
        oneway Receive
        {
            ACSCO2EmesseRetragasType
        };
    };
    internal porttype N2ConsumiRetragasOutType
    {
        requestresponse ImportazioneRetragas
        {
            N2ConsumiRetragasRequestType, N2ConsumiRetragasResponseType, Fault = FaultType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service CO2EmesseRetragasProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ACSCO2EmesseRetragasPollingInType ACSCO2EmesseRetragasPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseUnaRetiUpdateStatusOutType ACSUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses N2ConsumiRetragasOutType N2ConsumiRetragasOut;
        message ACSUpdateStatusResponseType ACSUpdateStatusResponse;
        message NotificationType Notification;
        message ACSUpdateStatusRequestType ACSUpdateStatusRequest;
        message ACSCO2EmesseRetragasType ACSCO2EmesseRetragas;
        message N2ConsumiRetragasRequestType N2ConsumiRetragasRequest;
        message N2ConsumiRetragasResponseType N2ConsumiRetragasResponse;
        System.String WSResponseContent;
        System.Xml.XmlDocument WSResponse;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4359eef9-0d9c-4bd5-b75c-b964f54b5422")]
            activate receive (ACSCO2EmesseRetragasPollingIn.Receive, ACSCO2EmesseRetragas);
            WSResponseContent = "";
            WSResponse = new System.Xml.XmlDocument();
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("95dbf4f6-49aa-43fe-97dc-5c427f3dc8d8")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseRetragasProcess;
            sendNotification = 0;
            
            WSResponseContent = System.String.Empty;
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("94aea775-3a10-4278-8453-7f2da61f368c")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "CO2EmesseRetragas",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSCO2EmesseRetragas.parameters.activityIdStaging, "");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a379734-0d9c-48a8-bfa6-e63572d5ad28")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b3512015-0db4-4e04-89a7-350d483fa924")]
                    construct N2ConsumiRetragasRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bd57ffa3-8107-4bb1-b4a1-40252d379ead")]
                        transform (N2ConsumiRetragasRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseRetragasPollingToN2ConsumiRetragas (ACSCO2EmesseRetragas.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e734dd8-6a3d-4fee-9876-e09beb345ad9")]
                        N2ConsumiRetragasRequest.parameters.import.NomeFile = System.IO.Path.GetFileName(ACSCO2EmesseRetragas.parameters.NomeFile);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("21737510-5d30-4900-b1ea-ff2cd1387682")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("63a2f5c8-1b71-4fac-b6a9-934bd03b205e")]
                    send (N2ConsumiRetragasOut.ImportazioneRetragas, N2ConsumiRetragasRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("863938d5-9705-4c70-9287-66d0e6835554")]
                    receive (N2ConsumiRetragasOut.ImportazioneRetragas, N2ConsumiRetragasResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("77dbc04e-e8e1-4456-b0c2-85317d123328")]
                    WSResponse = N2ConsumiRetragasResponse.parameters;
                    WSResponseContent = WSResponse.InnerXml.ToString();
                    
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b3ac1cc2-7acb-4036-a894-3609286e2a21")]
                    if (WSResponseContent.Contains("KO") == true)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("205ee3d7-f7b0-46ca-9ee0-c7f4f4d01e62")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(WSResponse.InnerText);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("64f8d136-4451-4f41-9f64-1ed3793706a8")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Esito WS", WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c2f5ecca-21ee-4bf8-aa13-6407ca025db0")]
                    catch (Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException validationExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("52c4b5b0-4ab0-4275-9dde-b902e77d5873")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(validationExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("add6b65a-f2bb-439f-9534-a59e433ee5d1")]
                    catch (N2ConsumiRetragasOut.ImportazioneRetragas.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dabf8a74-2c63-479f-bffa-7ac38cf490fd")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("149fd7f7-a58c-4215-b4a8-f855fe582567")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f695fb28-a8b6-4a4e-b625-9b3aad9e024a")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("47721a92-8bb5-483d-8851-ab1493a93929")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("460525e0-f61b-4e25-8f1a-bb98d6fbb514")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c4ff5938-bc3a-4441-ab3a-a2bbd53c9198")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebc4bc39-92f4-4aad-97cb-94e5bc7c9ebf")]
                    construct ACSUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("df3bf92e-1395-4721-8f1d-c3b86e46040a")]
                        transform (ACSUpdateStatusRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseRetragasPollingToACSUpdateStatus (ACSCO2EmesseRetragas.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("86b1ff9a-fd87-4b5f-8117-8af2a72c8fc6")]
                        ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("371eb17d-623e-452c-a437-a261eeabc380")]
                    send (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51985985-97b7-4536-a74e-eb3a9c64fa71")]
                    receive (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("30977dc6-bb38-437c-9f07-3849257305ac")]
                    sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b67b852b-38e0-4dd5-afe3-8e1f4061cb71")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("db2461fb-2ab2-4014-a124-9caaf0b53198")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("38d7810d-8309-4c5d-89c3-d86360dccc43")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("552eb66d-97cc-4a89-a05c-4bb2c2e31109")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("73b09abd-ac47-4fd2-bc5b-5c174de75d6b")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("10ea3c98-7178-4b78-9f10-4555dc3c4d9c")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bbea34f4-b046-4d56-9718-2f3f5bce07ea")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8621a7ff-b17e-436d-8bc7-ce66ef73bc21")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameters.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f7e48eda-b427-4c7a-badc-1ed67679fdb1")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9151675a-8070-4605-8235-3f8cf1076b0f")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

