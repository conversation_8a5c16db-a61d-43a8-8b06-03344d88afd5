﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="5718d977-690b-410c-afc3-fd4bd76e153d" LowerBound="1.1" HigherBound="190.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="ec4bd00b-3f57-4d91-b4ce-272014dac991" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="56e405c9-1b50-4b5a-84ed-f48ab4494a35" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="1bc7155d-b496-48d6-93dc-ceeb30aafd49" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.34">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="f217220c-8409-449f-aa73-474e50b1f24a" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseRetragasInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3d65df90-8845-4b34-b7fe-8eda469b07cf" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseRetragasInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="3767af2b-363a-447e-a7af-81ea22553ab4" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.47">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="d61348c9-1519-463e-83bc-255028181ccf" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.49" HigherBound="27.84">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="5fb9c410-f05d-4029-b68f-b2fc2cd204cc" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="307eda17-3f11-428f-9ccb-e48205cb3771" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.CO2EmesseRetragas" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="5c4c1f96-cd0b-4180-a0d7-6ac6fa3bce23" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c2752f65-63c9-4719-980e-5efc47afce12" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasInsertTypedProcedure.ACSCO2EmesseRetragasInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="28a97ccc-f375-47e8-8dbe-65e9797e873e" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f2d3730d-84f5-4f5b-a4f7-50e913ff12dd" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasInsertTypedProcedure.ACSCO2EmesseRetragasInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="5155de17-e4ea-49e9-b9fd-c4ef320d13ba" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="189.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseRetragasStaging" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="33267398-affc-49b1-b343-b00de298ac7f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e5d0bb10-a01b-4092-8af6-56f90f377cc1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="36b824d0-53d6-4189-8994-8c46577556b6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="553c8e41-5a96-42b9-b4ef-769f1333be6c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c4d71fd6-9b2f-491a-bd20-c0d374da25e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="67a81746-5bd1-4f8f-bdeb-71ac53870226" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="38ec38b7-37ae-4f3c-8a99-e845cea242c6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2ecc0834-a171-417d-b8bc-d8a7ef9560ba" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2ad0ca9b-bf0d-4a64-9581-9f76489d5f87" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d1ca4dbe-8dc9-4f21-8a62-3d1bfb6433cb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3df3f55f-9de4-443d-a995-b1c2bc0d0fce" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1ba45d14-79b8-42b8-ab7a-1659fe00f4d1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseRetragas" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="61e5e64d-d2c0-4251-9430-0e902aedc6cd" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseRetragasInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="9d8d8ff1-634a-4ea3-a941-9313e5858b1d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseRetragasInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="a06eb203-19df-404a-9fd2-f0c39bd3c41f" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="f8f5a7d8-aee9-4e51-a7e3-25bb918d8261" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="63.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="CO2EmesseRetragasIn" />
                    <om:Property Name="MessageName" Value="CO2EmesseRetragas" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="c35f9e8d-2090-4ef3-a859-7ebc25249a8c" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="77.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseRetragasStaging;&#xD;&#xA;&#xD;&#xA;dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(CO2EmesseRetragas);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(CO2EmesseRetragas);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5b6ebc47-b7f4-4dc1-b8f9-bae324609533" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="88.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;CO2EmesseRetragas&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;File Name&quot;, originalFileName&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="77e0b5f7-7959-4f09-98a7-d9b75012b514" ParentLink="ServiceBody_Statement" LowerBound="88.1" HigherBound="146.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="063beed0-e56e-4acb-bcf1-c5a24822c4fa" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="102.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="d6c05b0c-7d47-4970-ad02-f2a3f9595e7f" ParentLink="Construct_MessageRef" LowerBound="94.31" HigherBound="94.61">
                            <om:Property Name="Ref" Value="CO2EmesseRetragasInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="d17ce996-d480-4532-99ed-a4df00381b42" ParentLink="ComplexStatement_Statement" LowerBound="96.1" HigherBound="98.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.CO2EmesseRetragasToACSCO2EmesseRetragasInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="d465b352-f24e-48bd-83c3-1d4d6d7433b8" ParentLink="Transform_OutputMessagePartRef" LowerBound="97.36" HigherBound="97.77">
                                <om:Property Name="MessageRef" Value="CO2EmesseRetragasInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="2dc1449c-2f3a-4c8a-8f58-c05dc4f60c1e" ParentLink="Transform_InputMessagePartRef" LowerBound="97.169" HigherBound="97.197">
                                <om:Property Name="MessageRef" Value="CO2EmesseRetragas" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="d7e87ed2-8029-4565-9fac-c1db47077b80" ParentLink="ComplexStatement_Statement" LowerBound="98.1" HigherBound="101.1">
                            <om:Property Name="Expression" Value="CO2EmesseRetragasInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;CO2EmesseRetragasInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="90a76edf-9aa9-4d0c-8ff8-c9aa977de7b1" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="62fe7400-8f88-4f7d-a69c-27ee1f6902cb" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                        <om:Property Name="PortName" Value="ACSCO2EmesseRetragasInsertOut" />
                        <om:Property Name="MessageName" Value="CO2EmesseRetragasInsertRequest" />
                        <om:Property Name="OperationName" Value="ACSCO2EmesseRetragasInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="0151fcb4-d64b-4ef6-a8f6-bd88a1a2ab3b" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="108.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSCO2EmesseRetragasInsertOut" />
                        <om:Property Name="MessageName" Value="CO2EmesseRetragasInsertResponse" />
                        <om:Property Name="OperationName" Value="ACSCO2EmesseRetragasInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="dfb04cb4-2110-4373-a824-70249d6d6107" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="114.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="d8ee1913-b1ad-4d24-b2f6-1708a7e4e45c" ParentLink="Scope_Catch" LowerBound="117.1" HigherBound="131.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="52dddb0a-bbb9-4887-bbb1-10ff02717056" ParentLink="Catch_Statement" LowerBound="120.1" HigherBound="130.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="16601b6e-3b9e-4fdd-95b0-4ea08f23dd10" ParentLink="Scope_Catch" LowerBound="131.1" HigherBound="144.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e102a54a-8d57-4a2c-a545-59f16a16b964" ParentLink="Catch_Statement" LowerBound="134.1" HigherBound="143.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="d26b17a7-1c9d-4b80-8964-c4cea0080808" ParentLink="ServiceBody_Statement" LowerBound="146.1" HigherBound="179.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="e95843bd-377c-43d4-8711-d0766fbd9913" ParentLink="ReallyComplexStatement_Branch" LowerBound="147.13" HigherBound="150.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="fcabb962-04ec-4c6a-9114-f786a1e5db42" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="278b80de-5b2b-4da1-a8b5-a81db8ee7d40" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="178.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="72377104-c5c2-469a-927f-cb98116b3bf3" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="174.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="94669701-099f-45e1-adb1-da20e1d0bd7e" ParentLink="Construct_MessageRef" LowerBound="158.35" HigherBound="158.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="da250090-b14e-481d-a652-4f68a4ceb861" ParentLink="ComplexStatement_Statement" LowerBound="160.1" HigherBound="173.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="2c586ca6-bd1d-4aa9-acc1-c9559ec33cd8" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="176.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="eba7b4a6-4a58-4aff-8c25-4de7fd3eb5de" ParentLink="ServiceBody_Statement" LowerBound="179.1" HigherBound="187.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3153cb5d-ad29-4c1e-bc10-2302b78e7c2d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseRetragasInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseRetragasIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="f50bfbd2-91ed-4151-932f-b0c8471a9afa" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7f44ea49-2f20-4d1f-90ad-ce13ca33daa0" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="36" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseRetragasInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseRetragasInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="a67482dd-4c13-4145-8c54-104070fbe3ad" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="bfe5dd06-4ffc-465f-beaa-e87237ff379d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="103" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="2cfa570f-daf2-4f3b-9b83-5d1d8863a52f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype CO2EmesseRetragasType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.CO2EmesseRetragas parameters;
    };
    internal messagetype CO2EmesseRetragasInsertRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasInsertTypedProcedure.ACSCO2EmesseRetragasInsert parameters;
    };
    internal messagetype CO2EmesseRetragasInsertResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseRetragasInsertTypedProcedure.ACSCO2EmesseRetragasInsertResponse parameters;
    };
    internal porttype CO2EmesseRetragasInType
    {
        oneway Receive
        {
            CO2EmesseRetragasType
        };
    };
    internal porttype ACSCO2EmesseRetragasInsertOutType
    {
        requestresponse ACSCO2EmesseRetragasInsert
        {
            CO2EmesseRetragasInsertRequestType, CO2EmesseRetragasInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service CO2EmesseRetragasStaging
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements CO2EmesseRetragasInType CO2EmesseRetragasIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseRetragasInsertOutType ACSCO2EmesseRetragasInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message CO2EmesseRetragasType CO2EmesseRetragas;
        message CO2EmesseRetragasInsertRequestType CO2EmesseRetragasInsertRequest;
        message CO2EmesseRetragasInsertResponseType CO2EmesseRetragasInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String flowDescription;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f8f5a7d8-aee9-4e51-a7e3-25bb918d8261")]
            activate receive (CO2EmesseRetragasIn.Receive, CO2EmesseRetragas);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            flowDescription = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c35f9e8d-2090-4ef3-a859-7ebc25249a8c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseRetragasStaging;
            
            dataInizio = System.DateTimeOffset.Now;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(CO2EmesseRetragas);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(CO2EmesseRetragas);
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b6ebc47-b7f4-4dc1-b8f9-bae324609533")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "CO2EmesseRetragas",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "File Name", originalFileName
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("77e0b5f7-7959-4f09-98a7-d9b75012b514")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("063beed0-e56e-4acb-bcf1-c5a24822c4fa")]
                    construct CO2EmesseRetragasInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d17ce996-d480-4532-99ed-a4df00381b42")]
                        transform (CO2EmesseRetragasInsertRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.CO2EmesseRetragasToACSCO2EmesseRetragasInsert (CO2EmesseRetragas.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d7e87ed2-8029-4565-9fac-c1db47077b80")]
                        CO2EmesseRetragasInsertRequest.parameters.fileName = originalFileName;
                        CO2EmesseRetragasInsertRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("90a76edf-9aa9-4d0c-8ff8-c9aa977de7b1")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("62fe7400-8f88-4f7d-a69c-27ee1f6902cb")]
                    send (ACSCO2EmesseRetragasInsertOut.ACSCO2EmesseRetragasInsert, CO2EmesseRetragasInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0151fcb4-d64b-4ef6-a8f6-bd88a1a2ab3b")]
                    receive (ACSCO2EmesseRetragasInsertOut.ACSCO2EmesseRetragasInsert, CO2EmesseRetragasInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("dfb04cb4-2110-4373-a824-70249d6d6107")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8ee1913-b1ad-4d24-b2f6-1708a7e4e45c")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("52dddb0a-bbb9-4887-bbb1-10ff02717056")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("16601b6e-3b9e-4fdd-95b0-4ea08f23dd10")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e102a54a-8d57-4a2c-a545-59f16a16b964")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d26b17a7-1c9d-4b80-8964-c4cea0080808")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("278b80de-5b2b-4da1-a8b5-a81db8ee7d40")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("72377104-c5c2-469a-927f-cb98116b3bf3")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("da250090-b14e-481d-a652-4f68a4ceb861")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2c586ca6-bd1d-4aa9-acc1-c9559ec33cd8")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("eba7b4a6-4a58-4aff-8c25-4de7fd3eb5de")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

