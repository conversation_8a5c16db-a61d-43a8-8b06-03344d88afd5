﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="64ebd142-95dc-4962-ab71-cba9c2848435" LowerBound="1.1" HigherBound="305.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="68aa1d59-eeca-493e-a0a9-e461467d556b" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiUnaretiOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="7531ba18-b14b-41e0-938e-dee960424237" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ImportazioneUnareti" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="69e43731-6768-4348-9d4c-307ff346fba0" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="20.42" HigherBound="20.70">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiUnaretiResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="cb6c39c7-1baf-4c27-8954-4154ce013f9b" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.40">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiUnaretiRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="c376f2bc-7ed6-412a-9602-74d8c055e97a" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="20.72" HigherBound="20.89">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.FaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="3014b5f9-5b58-49eb-8fa8-0dcb04314e91" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseUnaretiPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="fde017c3-6402-42c0-8d67-929f5fe471ad" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="cfcc9037-561e-494e-9142-207bc0544692" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.36">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaretiType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="3575df16-8c37-494b-b028-e6273e6100c0" ParentLink="Module_PortType" LowerBound="30.1" HigherBound="37.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseUnaRetiUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="49249671-0862-4e74-8962-75fa9541b208" ParentLink="PortType_OperationDeclaration" LowerBound="32.1" HigherBound="36.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8372a6fc-3134-40f7-bd0b-8e42f4dd725f" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="34.13" HigherBound="34.39">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="c1505705-471a-46e0-8866-9f0ff26bc495" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="34.41" HigherBound="34.68">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bed70870-91a7-415c-9344-a331d661c03f" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiUnaretiRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="46328882-b59b-47fd-8347-d17b2effe96f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiUnareti.ImportazioneUnareti" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a72f5779-3f13-48d6-bf8e-fbd196e531ed" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2ConsumiUnaretiResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5d9f7773-9b57-4be5-a133-9bd955a1a09a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiUnareti.ImportazioneUnaretiResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="96056624-37fd-4bff-9c8c-75a5a869dcb1" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseUnaretiType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d025881e-7f14-477e-abf7-ce96357b0670" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiPolling_Type" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="10bad0cc-e648-47cb-a042-9fd75d2da786" ParentLink="Module_ServiceDeclaration" LowerBound="37.1" HigherBound="304.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseUnaretiProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="5f7eb7d9-ca39-43d8-855e-db072e3aa8ad" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9a55dbbe-dd4c-4bed-840c-38a001d15c5c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="dfb3e765-454e-41a2-9388-6c21c89c73de" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponseContent" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="aa2d171f-2862-4da9-a1a2-372d7105b239" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="760667c9-b66e-460c-be38-813be046176f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="03a976bc-178d-4ba4-95ff-92078a597c2e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9eb8de1c-9e2b-4f7c-bf4e-4ab4ba6494ee" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e047951b-8edf-47a4-b41c-43edd669beef" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e9939f89-1570-4856-9867-9cdde1d2738f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9ea79222-daa3-4e97-9a8d-4478a629f24a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1c226085-fc4a-4594-9408-37088e735060" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b7807862-0b46-464f-8878-efc89ddb7faa" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b1f4913a-c1b5-4c05-80f8-db08c27fa97c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiUnaretiRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiUnaretiRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="333dc1d6-6561-4d00-8c63-578fe151aadb" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiUnaretiResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiUnaretiResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="505e7f9a-e6ce-4442-ae11-48e1eef5d506" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaretiType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseUnareti" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="0bd85e90-e8f3-46ce-80d2-14226f0b1e3f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bfafcd76-d894-4200-baea-857363be5023" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="6fd639a2-4896-4e70-a87c-21f59635bec8" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="d04eb921-17e3-43b8-81cf-9cf110b1d391" ParentLink="ServiceBody_Statement" LowerBound="69.1" HigherBound="76.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ACSCO2EmesseUnaretiPollingIn" />
                    <om:Property Name="MessageName" Value="ACSCO2EmesseUnareti" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d074d690-aa6b-4a38-a39a-8567fff435db" ParentLink="ServiceBody_Statement" LowerBound="76.1" HigherBound="88.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseUnaretiProcess;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;WSResponseContent = System.String.Empty;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="7a9ab47d-05a0-41a9-ac54-b957f3df3c5d" ParentLink="ServiceBody_Statement" LowerBound="88.1" HigherBound="100.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;CO2EmesseUnareti&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSCO2EmesseUnareti.parameters.activityIdStaging, &quot;&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="476a5f7d-2eb9-4fdd-bfd3-f3ea69913e2d" ParentLink="ServiceBody_Statement" LowerBound="100.1" HigherBound="200.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="9e316df4-de8b-4684-9ee6-0eba001d4427" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="113.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create WS" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="7f26a7fb-10d4-45a8-946d-8a169a9e7102" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="110.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup WS" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="6328af88-0e8c-4a86-9537-45c95e7de678" ParentLink="Transform_OutputMessagePartRef" LowerBound="109.36" HigherBound="109.70">
                                <om:Property Name="MessageRef" Value="N2ConsumiUnaretiRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="917b6889-6df7-4428-b071-72a25787da43" ParentLink="Transform_InputMessagePartRef" LowerBound="109.161" HigherBound="109.191">
                                <om:Property Name="MessageRef" Value="ACSCO2EmesseUnareti" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="91494a33-f6d1-4795-9899-1e2240b2ad34" ParentLink="ComplexStatement_Statement" LowerBound="110.1" HigherBound="112.1">
                            <om:Property Name="Expression" Value="N2ConsumiUnaretiRequest.parameters.import.NomeFile = System.IO.Path.GetFileName(ACSCO2EmesseUnareti.parameters.NomeFile);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="NomeFile" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="239017e4-995f-49d8-af94-55e9a79949b6" ParentLink="Construct_MessageRef" LowerBound="106.31" HigherBound="106.54">
                            <om:Property Name="Ref" Value="N2ConsumiUnaretiRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="68b6c46a-8dc2-438c-ab61-a5765a6439dd" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="115.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="9894e5ce-2105-448e-b33b-475ce578d4b4" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="117.1">
                        <om:Property Name="PortName" Value="N2ConsumiUnaretiOut" />
                        <om:Property Name="MessageName" Value="N2ConsumiUnaretiRequest" />
                        <om:Property Name="OperationName" Value="ImportazioneUnareti" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="09af8f79-28f3-47b2-806d-74f737a73dfb" ParentLink="ComplexStatement_Statement" LowerBound="117.1" HigherBound="119.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="N2ConsumiUnaretiOut" />
                        <om:Property Name="MessageName" Value="N2ConsumiUnaretiResponse" />
                        <om:Property Name="OperationName" Value="ImportazioneUnareti" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="2ec1c2de-2768-43f0-9b68-47db74dfb965" ParentLink="ComplexStatement_Statement" LowerBound="119.1" HigherBound="122.1">
                        <om:Property Name="Expression" Value="WSResponse = N2ConsumiUnaretiResponse.parameters;&#xD;&#xA;WSResponseContent = WSResponse.InnerXml.ToString();" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Assign Response" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="6f9328d5-080c-4432-b00b-385d7cc71486" ParentLink="ComplexStatement_Statement" LowerBound="122.1" HigherBound="135.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Response OK" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="d5aeda99-d535-47bb-bd52-49fe55cd525b" ParentLink="ReallyComplexStatement_Branch" LowerBound="123.21" HigherBound="135.1">
                            <om:Property Name="Expression" Value="WSResponseContent.Contains(&quot;KO&quot;) == true" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="NOT OK" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="926fe1c3-2ce7-481b-ac4e-7cdaf4e27d4f" ParentLink="ComplexStatement_Statement" LowerBound="125.1" HigherBound="134.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(WSResponse.InnerText);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Error" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="cf393ba0-57cc-49f1-b2e5-67563c9286e2" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="c6dcbbbb-6275-47cd-bf7a-61d344d37b72" ParentLink="ComplexStatement_Statement" LowerBound="135.1" HigherBound="142.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Esito WS&quot;, WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="59f3ceaf-a72c-42b0-856d-a02a2e2fbd71" ParentLink="Scope_Catch" LowerBound="145.1" HigherBound="159.1">
                        <om:Property Name="ExceptionName" Value="validationExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Message Type Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="6d1dc855-a335-41af-99c5-6760689d94d8" ParentLink="Catch_Statement" LowerBound="148.1" HigherBound="158.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(validationExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="0b5b3979-ca49-4421-b959-b28c7694b5f3" ParentLink="Scope_Catch" LowerBound="159.1" HigherBound="173.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="N2ConsumiUnaretiOut.ImportazioneUnareti.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="ff6cace4-2557-4798-8a62-2443db2d6479" ParentLink="Catch_Statement" LowerBound="162.1" HigherBound="172.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="c37965ba-f2df-468e-a508-91613173c423" ParentLink="Scope_Catch" LowerBound="173.1" HigherBound="186.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="3319d156-f0f0-4544-a330-a7bae922faa5" ParentLink="Catch_Statement" LowerBound="176.1" HigherBound="185.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f9bd5726-a8f8-45b0-b3d9-f9ad94e0bb83" ParentLink="Scope_Catch" LowerBound="186.1" HigherBound="198.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="3a66c943-08e7-49ef-81d6-6f18684806fe" ParentLink="Catch_Statement" LowerBound="189.1" HigherBound="197.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="b164afcb-1a0b-44d8-aa51-f60a10b3da1b" ParentLink="ServiceBody_Statement" LowerBound="200.1" HigherBound="256.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="3b6c9f79-8eff-496d-90eb-27964ae6db76" ParentLink="ComplexStatement_Statement" LowerBound="205.1" HigherBound="215.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="2cfe79c8-57d0-443d-a606-04cfc78896f7" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="210.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseUnaretiPollingToACSUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="fa30fb05-5108-41d7-8e92-17ab9c88e629" ParentLink="Transform_InputMessagePartRef" LowerBound="209.159" HigherBound="209.189">
                                <om:Property Name="MessageRef" Value="ACSCO2EmesseUnareti" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="9d5a260d-72c9-4688-b75d-a043495b7e6f" ParentLink="Transform_OutputMessagePartRef" LowerBound="209.36" HigherBound="209.69">
                                <om:Property Name="MessageRef" Value="ACSUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="ae8baf05-5a13-447b-82be-7c0bcc8bed30" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="214.1">
                            <om:Property Name="Expression" Value="ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="768d2342-40ab-45a0-a483-022ee7b8022b" ParentLink="Construct_MessageRef" LowerBound="206.31" HigherBound="206.53">
                            <om:Property Name="Ref" Value="ACSUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="203b5a74-cd2b-4e3e-8f28-b481b0e306da" ParentLink="ComplexStatement_Statement" LowerBound="215.1" HigherBound="217.1">
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="ea52994a-6e91-4b37-8308-21a4a4b52fc8" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="219.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="c19b135d-af82-49f2-b47c-6a6ea250bdc1" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="223.1">
                        <om:Property Name="Expression" Value="sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="51b057eb-29a9-4c85-acea-97a689799281" ParentLink="Scope_Catch" LowerBound="226.1" HigherBound="240.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="afa5c8d6-ed01-4518-ad2e-b1ab72157c92" ParentLink="Catch_Statement" LowerBound="229.1" HigherBound="239.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f0665c55-79ac-4f81-ad94-17d981e7c6c4" ParentLink="Scope_Catch" LowerBound="240.1" HigherBound="254.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c91d15c0-f997-46bf-94a7-7226acd9ff51" ParentLink="Catch_Statement" LowerBound="243.1" HigherBound="253.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="52cecd71-38d1-4e71-988c-15709ed412c9" ParentLink="ServiceBody_Statement" LowerBound="256.1" HigherBound="293.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="7b0549d6-8718-4141-a793-65c8f2b5135b" ParentLink="ReallyComplexStatement_Branch" LowerBound="257.13" HigherBound="260.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="d9b3a3bd-aaad-4fca-9241-30ce9a3f4417" ParentLink="ReallyComplexStatement_Branch" LowerBound="260.18" HigherBound="263.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="560b275c-eb1a-4f57-9c6a-1511b62073d8" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="b779488f-31b6-4539-8d41-76bba51bdd86" ParentLink="ComplexStatement_Statement" LowerBound="265.1" HigherBound="292.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="edd3593d-542f-4beb-8eaa-dcc38e85a81f" ParentLink="ComplexStatement_Statement" LowerBound="270.1" HigherBound="288.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="e92f7742-0dfa-4ea3-896f-8f3c8f0f1e04" ParentLink="Construct_MessageRef" LowerBound="271.35" HigherBound="271.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="30b50744-ad7f-4ffb-ab1c-4edc65175069" ParentLink="ComplexStatement_Statement" LowerBound="273.1" HigherBound="287.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameters.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="99cccf66-e184-4c8b-8ab2-f8a116eeff56" ParentLink="ComplexStatement_Statement" LowerBound="288.1" HigherBound="290.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5a9d945f-24df-428a-8a5b-5dec77e970bc" ParentLink="ServiceBody_Statement" LowerBound="293.1" HigherBound="302.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="6c1b0d06-7fe0-4f76-bc6d-d9c055c0ac2b" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="40.1" HigherBound="42.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaretiPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseUnaretiPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="e364464f-f87f-4d5a-83bf-5f35e92a8764" ParentLink="PortDeclaration_CLRAttribute" LowerBound="40.1" HigherBound="41.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ee43f8df-0b19-48c6-a9e5-7d09a2196d16" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="42.1" HigherBound="45.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="36" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2ConsumiUnaretiOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2ConsumiUnaretiOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="16e66727-0ee6-4d9f-aba1-e063932ba3ef" ParentLink="PortDeclaration_CLRAttribute" LowerBound="42.1" HigherBound="43.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8228d6c0-97ff-4c4a-be7c-0bbe388c1161" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="45.1" HigherBound="48.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="115" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaRetiUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="a5e5ef6e-ec70-4bcc-92aa-31862c1de557" ParentLink="PortDeclaration_CLRAttribute" LowerBound="45.1" HigherBound="46.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b1e04e8b-1741-4306-90a3-948e445e1bbc" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="174" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="d313e28c-ddbd-4cc8-bfa9-82719370314b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype N2ConsumiUnaretiRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiUnareti.ImportazioneUnareti parameters;
    };
    internal messagetype N2ConsumiUnaretiResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2ConsumiUnareti.ImportazioneUnaretiResponse parameters;
    };
    internal messagetype ACSCO2EmesseUnaretiType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiPolling_Type parameters;
    };
    internal porttype N2ConsumiUnaretiOutType
    {
        requestresponse ImportazioneUnareti
        {
            N2ConsumiUnaretiRequestType, N2ConsumiUnaretiResponseType, Fault = FaultType
        };
    };
    internal porttype ACSCO2EmesseUnaretiPollingInType
    {
        oneway Receive
        {
            ACSCO2EmesseUnaretiType
        };
    };
    internal porttype ACSCO2EmesseUnaRetiUpdateStatusOutType
    {
        requestresponse ACSUpdateStatus
        {
            ACSUpdateStatusRequestType, ACSUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service CO2EmesseUnaretiProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ACSCO2EmesseUnaretiPollingInType ACSCO2EmesseUnaretiPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses N2ConsumiUnaretiOutType N2ConsumiUnaretiOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseUnaRetiUpdateStatusOutType ACSUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message N2ConsumiUnaretiRequestType N2ConsumiUnaretiRequest;
        message N2ConsumiUnaretiResponseType N2ConsumiUnaretiResponse;
        message ACSCO2EmesseUnaretiType ACSCO2EmesseUnareti;
        message ACSUpdateStatusRequestType ACSUpdateStatusRequest;
        message ACSUpdateStatusResponseType ACSUpdateStatusResponse;
        System.Int32 sendNotification;
        System.String flowDescription;
        System.String WSResponseContent;
        System.Xml.XmlDocument WSResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d04eb921-17e3-43b8-81cf-9cf110b1d391")]
            activate receive (ACSCO2EmesseUnaretiPollingIn.Receive, ACSCO2EmesseUnareti);
            flowDescription = "";
            WSResponseContent = "";
            WSResponse = new System.Xml.XmlDocument();
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d074d690-aa6b-4a38-a39a-8567fff435db")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseUnaretiProcess;
            sendNotification = 0;
            
            WSResponseContent = System.String.Empty;
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7a9ab47d-05a0-41a9-ac54-b957f3df3c5d")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "CO2EmesseUnareti",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSCO2EmesseUnareti.parameters.activityIdStaging, "");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("476a5f7d-2eb9-4fdd-bfd3-f3ea69913e2d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9e316df4-de8b-4684-9ee6-0eba001d4427")]
                    construct N2ConsumiUnaretiRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f26a7fb-10d4-45a8-946d-8a169a9e7102")]
                        transform (N2ConsumiUnaretiRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseUnaretiPollingToN2ConsumiUnareti (ACSCO2EmesseUnareti.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("91494a33-f6d1-4795-9899-1e2240b2ad34")]
                        N2ConsumiUnaretiRequest.parameters.import.NomeFile = System.IO.Path.GetFileName(ACSCO2EmesseUnareti.parameters.NomeFile);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("68b6c46a-8dc2-438c-ab61-a5765a6439dd")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9894e5ce-2105-448e-b33b-475ce578d4b4")]
                    send (N2ConsumiUnaretiOut.ImportazioneUnareti, N2ConsumiUnaretiRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("09af8f79-28f3-47b2-806d-74f737a73dfb")]
                    receive (N2ConsumiUnaretiOut.ImportazioneUnareti, N2ConsumiUnaretiResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2ec1c2de-2768-43f0-9b68-47db74dfb965")]
                    WSResponse = N2ConsumiUnaretiResponse.parameters;
                    WSResponseContent = WSResponse.InnerXml.ToString();
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6f9328d5-080c-4432-b00b-385d7cc71486")]
                    if (WSResponseContent.Contains("KO") == true)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("926fe1c3-2ce7-481b-ac4e-7cdaf4e27d4f")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(WSResponse.InnerText);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c6dcbbbb-6275-47cd-bf7a-61d344d37b72")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Esito WS", WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("59f3ceaf-a72c-42b0-856d-a02a2e2fbd71")]
                    catch (Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException validationExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d1dc855-a335-41af-99c5-6760689d94d8")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(validationExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0b5b3979-ca49-4421-b959-b28c7694b5f3")]
                    catch (N2ConsumiUnaretiOut.ImportazioneUnareti.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ff6cace4-2557-4798-8a62-2443db2d6479")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c37965ba-f2df-468e-a508-91613173c423")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3319d156-f0f0-4544-a330-a7bae922faa5")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f9bd5726-a8f8-45b0-b3d9-f9ad94e0bb83")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a66c943-08e7-49ef-81d6-6f18684806fe")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b164afcb-1a0b-44d8-aa51-f60a10b3da1b")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b6c9f79-8eff-496d-90eb-27964ae6db76")]
                    construct ACSUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2cfe79c8-57d0-443d-a606-04cfc78896f7")]
                        transform (ACSUpdateStatusRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.ACSCO2EmesseUnaretiPollingToACSUpdateStatus (ACSCO2EmesseUnareti.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae8baf05-5a13-447b-82be-7c0bcc8bed30")]
                        ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("203b5a74-cd2b-4e3e-8f28-b481b0e306da")]
                    send (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea52994a-6e91-4b37-8308-21a4a4b52fc8")]
                    receive (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c19b135d-af82-49f2-b47c-6a6ea250bdc1")]
                    sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51b057eb-29a9-4c85-acea-97a689799281")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("afa5c8d6-ed01-4518-ad2e-b1ab72157c92")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f0665c55-79ac-4f81-ad94-17d981e7c6c4")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c91d15c0-f997-46bf-94a7-7226acd9ff51")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("52cecd71-38d1-4e71-988c-15709ed412c9")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("b779488f-31b6-4539-8d41-76bba51bdd86")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("edd3593d-542f-4beb-8eaa-dcc38e85a81f")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("30b50744-ad7f-4ffb-ab1c-4edc65175069")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameters.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("99cccf66-e184-4c8b-8ab2-f8a116eeff56")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5a9d945f-24df-428a-8a5b-5dec77e970bc")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

