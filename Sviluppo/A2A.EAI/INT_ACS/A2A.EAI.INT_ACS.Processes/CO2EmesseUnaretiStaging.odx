﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="f3193096-554f-411e-8011-d54f08ec7cf4" LowerBound="1.1" HigherBound="191.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="8d42bd23-6bd9-4174-a887-e58959a31b5a" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseUnaretiInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="df2e629a-7e7e-48b6-8d30-ba55ee75da2d" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="48d93352-69fc-41ff-a028-f90e1b3cd118" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.33">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseunaretiType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="7ac76f3b-2897-45b1-ae3b-4a9f7fc257f4" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSCO2EmesseUnaretiInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3c748b05-ba54-4457-b88c-5da3e0abffd1" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseUnaretiInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="82f21064-56c6-49f4-8e0c-1fd916f05fcf" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.46">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseUnaretiInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b0d25e71-6aef-4762-a4d2-fac35f9645f4" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.48" HigherBound="27.82">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseUnaretiInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="cf6720b5-0f98-487e-913f-2e71ddaaf28d" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseunaretiType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7b79483e-7215-4e5b-8aa2-e44d3ab1c4bb" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.CO2EmesseUnareti" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="2c98e02e-1d51-459d-93fa-ec372659acfd" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseUnaretiInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="8de2f1e2-53b8-4072-8ecb-df09613128d2" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiInsertTypedProcedure.ACSCO2EmesseUnaretiInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="43216ac5-041e-4f19-b82f-b812acd5d41d" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseUnaretiInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f6f3c42b-9c51-4a45-9a76-326379ac1dbc" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiInsertTypedProcedure.ACSCO2EmesseUnaretiInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="c44d600a-7c42-4f75-92b9-9ecb5449693e" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="190.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="CO2EmesseUnaretiStaging" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="0cc65792-8be9-4533-9d53-2f0f233a1888" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="07b24f01-2401-489e-a645-ab2cd6b18824" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ab0b1e6b-836b-4806-b60d-9dff3185f493" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1ef15b52-c7e4-45e8-9639-c617d7d64054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ce924f33-4e83-4d31-9c48-2f48f5dfc261" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="67e49570-7940-4b79-8ab6-9f5fa4537872" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8075be3c-0fbe-40b8-82c6-d07e468da50c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8958a767-3e15-45c0-861d-881c6e621acb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7fa087a7-18ae-48ff-a323-1a2a49750cd3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="787eb07c-98e1-441a-9a41-61fec5bfa9e8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8b9403fe-8931-434a-96a9-199574746c72" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseunaretiType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2Emessenareti" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4d50f1a1-2e1b-4ee3-9799-c2d162847d51" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6bc1fe92-9c1a-4c38-8c08-8fb6f8af60c2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseUnaretiInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseUnaretiInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e1d8262f-1760-45a2-862c-a702d5735fc2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseUnaretiInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseUnaretiInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="bf60d153-9b6d-465c-902f-777a1167972d" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="aae19bff-9f8b-42c3-bddd-4f8fa5c0d417" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="63.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="CO2EmesseUnaretiIn" />
                    <om:Property Name="MessageName" Value="CO2Emessenareti" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a3e29b28-3255-4ecc-af0d-90f19bcf130c" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="78.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseUnaretiStaging;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(CO2Emessenareti);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(CO2Emessenareti);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="28016372-7f04-448e-a496-89fdd64149b0" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="89.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;CO2EmesseUnareti&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;File Name&quot;, originalFileName&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="27d77db6-dccb-4704-bb9e-db4b409de141" ParentLink="ServiceBody_Statement" LowerBound="89.1" HigherBound="147.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="0652515f-356d-4437-b89e-984c39b8601e" ParentLink="ComplexStatement_Statement" LowerBound="94.1" HigherBound="103.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="1219c172-bdf2-443c-b3a3-362b7f4c4ad6" ParentLink="ComplexStatement_Statement" LowerBound="97.1" HigherBound="99.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="69ffeb50-aef0-4ee3-9731-902b7834bddc" ParentLink="Transform_InputMessagePartRef" LowerBound="98.166" HigherBound="98.192">
                                <om:Property Name="MessageRef" Value="CO2Emessenareti" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="7cc1f4d5-9f6f-49d4-bd45-8587104356df" ParentLink="Transform_OutputMessagePartRef" LowerBound="98.36" HigherBound="98.76">
                                <om:Property Name="MessageRef" Value="CO2EmesseUnaretiInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="b852ebd3-34af-4ea8-9658-0e61377a83d1" ParentLink="ComplexStatement_Statement" LowerBound="99.1" HigherBound="102.1">
                            <om:Property Name="Expression" Value="CO2EmesseUnaretiInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;CO2EmesseUnaretiInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="e8ea131f-617c-4a2c-964e-d10d99d84cb4" ParentLink="Construct_MessageRef" LowerBound="95.31" HigherBound="95.60">
                            <om:Property Name="Ref" Value="CO2EmesseUnaretiInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="874a84dc-7879-4f8a-bf70-9d84fb15e64e" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="105.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="0cc10aa2-413f-4da7-91cf-56eef989d2bd" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="107.1">
                        <om:Property Name="PortName" Value="ACSCO2EmesseUnaretiInsertOut" />
                        <om:Property Name="MessageName" Value="CO2EmesseUnaretiInsertRequest" />
                        <om:Property Name="OperationName" Value="ACSCO2EmesseUnaretiInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="8bb28d7c-4d54-4769-aba2-5a6011e23a47" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSCO2EmesseUnaretiInsertOut" />
                        <om:Property Name="MessageName" Value="CO2EmesseUnaretiInsertResponse" />
                        <om:Property Name="OperationName" Value="ACSCO2EmesseUnaretiInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="572fb7da-0f90-4830-ac4d-090b8343153c" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="115.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="87655756-505e-439b-aec0-02bae3a17347" ParentLink="Scope_Catch" LowerBound="118.1" HigherBound="132.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="078ae7ec-d572-4eb0-9863-b556caf92091" ParentLink="Catch_Statement" LowerBound="121.1" HigherBound="131.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="911f20dc-8968-4890-b0a9-dfd049fc8881" ParentLink="Scope_Catch" LowerBound="132.1" HigherBound="145.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="6ee8e665-bb79-4db3-8433-bbe069d17feb" ParentLink="Catch_Statement" LowerBound="135.1" HigherBound="144.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="8bfea81a-7f0e-46dc-ab87-fb9bc865ed75" ParentLink="ServiceBody_Statement" LowerBound="147.1" HigherBound="180.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="5d0cdd7e-a5cd-462b-85b1-39675d7939a5" ParentLink="ReallyComplexStatement_Branch" LowerBound="148.13" HigherBound="151.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="b5268db6-da14-4503-90e5-fab988f9c19b" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="c45b4bd2-0add-4828-afc9-f2869cbb032c" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="179.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="4f2ddee9-9b12-4a1f-9b96-24e7917c62aa" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="175.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="f6397b7d-9305-4f7c-9418-d8626e8ad4e8" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="174.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="241fa348-774e-421c-bccd-791c5c3aaabb" ParentLink="Construct_MessageRef" LowerBound="159.35" HigherBound="159.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="7ecf16b7-7229-4b99-8c43-28622a84990b" ParentLink="ComplexStatement_Statement" LowerBound="175.1" HigherBound="177.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="48871d5f-a78e-4343-b904-07fdb2a174c4" ParentLink="ServiceBody_Statement" LowerBound="180.1" HigherBound="188.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="99ba4fa6-99bf-49ce-95d1-e5a6445697a3" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.CO2EmesseUnaretiInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="CO2EmesseUnaretiIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="9c04385d-26fd-4e27-a2b5-af855c51fef4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5e92ca4a-2c1a-4b9f-aae5-316f1bcdc088" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="30" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaretiInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSCO2EmesseUnaretiInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="e3b721e4-8037-4374-b145-a0b8cf268efc" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c5364f3f-5ce7-422a-b33b-cc8143a65404" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="103" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="259e2542-39aa-4c05-b533-e416921385a4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype CO2EmesseunaretiType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.CO2EmesseUnareti parameters;
    };
    internal messagetype CO2EmesseUnaretiInsertRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiInsertTypedProcedure.ACSCO2EmesseUnaretiInsert parameters;
    };
    internal messagetype CO2EmesseUnaretiInsertResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSCO2EmesseUnaretiInsertTypedProcedure.ACSCO2EmesseUnaretiInsertResponse parameters;
    };
    internal porttype CO2EmesseUnaretiInType
    {
        oneway Receive
        {
            CO2EmesseunaretiType
        };
    };
    internal porttype ACSCO2EmesseUnaretiInsertOutType
    {
        requestresponse ACSCO2EmesseUnaretiInsert
        {
            CO2EmesseUnaretiInsertRequestType, CO2EmesseUnaretiInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service CO2EmesseUnaretiStaging
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements CO2EmesseUnaretiInType CO2EmesseUnaretiIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseUnaretiInsertOutType ACSCO2EmesseUnaretiInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message CO2EmesseunaretiType CO2Emessenareti;
        message NotificationType Notification;
        message CO2EmesseUnaretiInsertRequestType CO2EmesseUnaretiInsertRequest;
        message CO2EmesseUnaretiInsertResponseType CO2EmesseUnaretiInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String flowDescription;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("aae19bff-9f8b-42c3-bddd-4f8fa5c0d417")]
            activate receive (CO2EmesseUnaretiIn.Receive, CO2Emessenareti);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            flowDescription = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a3e29b28-3255-4ecc-af0d-90f19bcf130c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionCO2EmesseUnaretiStaging;
            
            
            dataInizio = System.DateTimeOffset.Now;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(CO2Emessenareti);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(CO2Emessenareti);
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("28016372-7f04-448e-a496-89fdd64149b0")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "CO2EmesseUnareti",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "File Name", originalFileName
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("27d77db6-dccb-4704-bb9e-db4b409de141")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0652515f-356d-4437-b89e-984c39b8601e")]
                    construct CO2EmesseUnaretiInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1219c172-bdf2-443c-b3a3-362b7f4c4ad6")]
                        transform (CO2EmesseUnaretiInsertRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.CO2Emesse.CO2EmesseUnaretiToACSCO2EmesseUnaretiInsert (CO2Emessenareti.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b852ebd3-34af-4ea8-9658-0e61377a83d1")]
                        CO2EmesseUnaretiInsertRequest.parameters.fileName = originalFileName;
                        CO2EmesseUnaretiInsertRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("874a84dc-7879-4f8a-bf70-9d84fb15e64e")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0cc10aa2-413f-4da7-91cf-56eef989d2bd")]
                    send (ACSCO2EmesseUnaretiInsertOut.ACSCO2EmesseUnaretiInsert, CO2EmesseUnaretiInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8bb28d7c-4d54-4769-aba2-5a6011e23a47")]
                    receive (ACSCO2EmesseUnaretiInsertOut.ACSCO2EmesseUnaretiInsert, CO2EmesseUnaretiInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("572fb7da-0f90-4830-ac4d-090b8343153c")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("87655756-505e-439b-aec0-02bae3a17347")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("078ae7ec-d572-4eb0-9863-b556caf92091")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("911f20dc-8968-4890-b0a9-dfd049fc8881")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6ee8e665-bb79-4db3-8433-bbe069d17feb")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8bfea81a-7f0e-46dc-ab87-fb9bc865ed75")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c45b4bd2-0add-4828-afc9-f2869cbb032c")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4f2ddee9-9b12-4a1f-9b96-24e7917c62aa")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f6397b7d-9305-4f7c-9418-d8626e8ad4e8")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7ecf16b7-7229-4b99-8c43-28622a84990b")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("48871d5f-a78e-4343-b904-07fdb2a174c4")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

