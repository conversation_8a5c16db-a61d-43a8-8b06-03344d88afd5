﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="3bfb3b40-8a5a-4c49-a36e-704b70473056" LowerBound="1.1" HigherBound="50.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="ff7be1f2-735e-4354-850d-06a5054e6032" ParentLink="Module_ServiceDeclaration" LowerBound="42.1" HigherBound="49.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="d8792a0e-42a6-448f-a714-933e988ed0aa" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d191d1ce-d5a6-4533-a0c5-f68754764cb3" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2VenditaCaloreResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="fed1254c-cbd8-466d-a958-b4afc84e48af" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2VenditaCalore.ImportazioneRicaviResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="e7f57f3a-f576-49e2-85f2-8f9980c593e7" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2VenditaCaloreRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="0ab5a69e-a712-449e-b4de-4b0035ec67ad" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.N2VenditaCalore.ImportazioneRicavi" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="8017a83a-02a9-45fa-abea-808121851d1f" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="038f3737-7782-4917-b086-6a132573f7ce" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSUpdateStatusTypedProcedure.ACSUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="cd607e83-0f9f-4b65-9141-7ca6932aa293" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a4d9e6a5-ecce-4265-bff6-99f097437919" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSUpdateStatusTypedProcedure.ACSUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bfae71c5-f9ae-470d-9dd4-7959dc54abbc" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FaultType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="51c077c2-340c-4141-918e-d869f92006f2" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="BTS.soap_envelope_1__1.Fault" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultString" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bf933035-c15d-4b6f-8f70-87fdbe7b05b9" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5e15a694-fa5c-4f64-a882-715030958fb0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="80e3b1cc-19a0-40b9-9103-78a5c3d97643" ParentLink="Module_PortType" LowerBound="28.1" HigherBound="35.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2VenditaCaloreOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="1cb07bf3-ab99-49d5-b7f8-9441a4085834" ParentLink="PortType_OperationDeclaration" LowerBound="30.1" HigherBound="34.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ImportazioneRicavi" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="1cb13179-2bab-4365-bc03-148f16ee3568" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="32.41" HigherBound="32.68">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="f3f9f79e-8a9a-41e4-993f-bf6f8f567372" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="32.70" HigherBound="32.87">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.FaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="c996e003-849c-4101-9b3f-19b4d3330115" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="32.13" HigherBound="32.39">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="a9f6983c-3f33-4092-b75f-4cadb88b5fd9" ParentLink="Module_PortType" LowerBound="35.1" HigherBound="42.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="7a097180-1748-4907-8f7a-e4dc7a1d6b2c" ParentLink="PortType_OperationDeclaration" LowerBound="37.1" HigherBound="41.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="6c2a6d4f-bae8-46a3-9d91-7699f7ccfca7" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="39.13" HigherBound="39.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype N2VenditaCaloreResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2VenditaCalore.ImportazioneRicaviResponse parameters;
    };
    internal messagetype N2VenditaCaloreRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.N2VenditaCalore.ImportazioneRicavi parameters;
    };
    internal messagetype ACSUpdateStatusResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSUpdateStatusTypedProcedure.ACSUpdateStatusResponse parameters;
    };
    internal messagetype ACSUpdateStatusRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSUpdateStatusTypedProcedure.ACSUpdateStatus parameters;
    };
    internal messagetype FaultType
    {
        body BTS.soap_envelope_1__1.Fault faultString;
    };
    internal messagetype NotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameters;
    };
    internal porttype N2VenditaCaloreOutType
    {
        requestresponse ImportazioneRicavi
        {
            N2VenditaCaloreRequestType, N2VenditaCaloreResponseType, Fault = FaultType
        };
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            NotificationType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service ObjectDefinition
    {
        body ()
        {
        }
    }
}

