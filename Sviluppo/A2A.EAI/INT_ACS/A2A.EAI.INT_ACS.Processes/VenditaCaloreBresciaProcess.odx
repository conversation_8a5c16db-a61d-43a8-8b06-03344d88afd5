﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="019e5bbf-01ee-4416-b3c8-48059a2f7832" LowerBound="1.1" HigherBound="283.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="3d1a27f7-4a77-41f6-8657-a247d0f324ef" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSVenditaCaloreBresciaPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="477f309e-ea9f-4d7d-ab7b-c80052cfff22" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="49717ade-9f17-4c74-8cec-4cdda86d6003" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.40">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreBresciaType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="4ccf9c60-eb19-4768-a8d6-6e75d25cc11b" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSVenditaCaloreBresciaType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="9168b4cd-a48b-45bf-84f6-fe09fdefb851" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaPolling_Type" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="344f5e81-e342-4286-9d8a-9ba3ed818f5e" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="282.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="e4a6aa1f-852d-4a8e-a5e7-0d0bb90f0b5f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponseContent" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e212f6ad-f321-4975-a0ed-055fd964110d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f799b652-f99f-4c9d-8b41-4f8fcd3abcac" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c9bd4885-8f7e-4682-98bd-45515aead299" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f06973b7-e5cd-4022-b297-036a4bc28a14" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c01e57cb-d30b-4d97-96f7-0d7bff689618" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9265ec0f-40a6-4a61-98f8-3efa253d6059" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ea99847f-79b5-48d2-bf62-58d8fd084eb4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="82530747-15f2-4914-9afa-0c1e7b896c92" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2598ccfb-3c12-464c-8bba-89013ae6cf8f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6164fea6-7f0b-4a51-8a3b-919435e8703b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c8999b16-dbf5-453f-ad34-fdd74730da8a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="57d73a20-304b-4048-b883-d7d9898707f2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="70a19189-9bbf-49a7-8a83-41b074aaf7ec" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="30bf2540-185d-42aa-a4dd-77e6a3e23076" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e66d76af-da03-4f0c-a383-99538cecb49b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6d0364a6-67a3-4db6-b857-4765a2d0addf" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreBresciaType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreBrescia" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="b44c7fa2-06f9-4cf3-827b-0cb1a3519922" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="dc80e05a-8442-4097-a215-dad8fe2fca1b" ParentLink="ServiceBody_Statement" LowerBound="47.1" HigherBound="54.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ACSVenditaCaloreBresciaPollingIn" />
                    <om:Property Name="MessageName" Value="ACSVenditaCaloreBrescia" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="c57b3522-d0c5-490d-ac48-c52e02821b2d" ParentLink="ServiceBody_Statement" LowerBound="54.1" HigherBound="66.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreBresciaProcess;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;WSResponseContent = System.String.Empty;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="105c04ff-7dc1-431c-8caf-559b55136fdb" ParentLink="ServiceBody_Statement" LowerBound="66.1" HigherBound="78.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;VenditaCaloreBrescia&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSVenditaCaloreBrescia.parameters.activityIdStaging, &quot;&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="ebf97a98-761c-421f-a908-9316de61ae09" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="178.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Catch" OID="9d5a7be6-be43-4ca1-b34b-460d8395a6ce" ParentLink="Scope_Catch" LowerBound="123.1" HigherBound="137.1">
                        <om:Property Name="ExceptionName" Value="validationExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Message Type Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="db44796a-f86a-4b93-8e11-ce67bdc6df89" ParentLink="Catch_Statement" LowerBound="126.1" HigherBound="136.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(validationExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="eb078ef6-34e9-41c8-b024-ab94f3528cb9" ParentLink="Scope_Catch" LowerBound="137.1" HigherBound="151.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="N2VenditaCaloreOut.ImportazioneRicavi.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="abe42893-a19f-4089-9bf0-e229886ddb44" ParentLink="Catch_Statement" LowerBound="140.1" HigherBound="150.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="53d4c9c1-4aef-4a6b-941b-5710f63c2617" ParentLink="Scope_Catch" LowerBound="151.1" HigherBound="164.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="acebb9e2-722f-4af8-9ce1-aa590bb397bc" ParentLink="Catch_Statement" LowerBound="154.1" HigherBound="163.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="8848dee8-ebb4-47ba-8837-b93fe7b982ff" ParentLink="Scope_Catch" LowerBound="164.1" HigherBound="176.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c5f689aa-3521-4207-8a91-6c81c7148cf7" ParentLink="Catch_Statement" LowerBound="167.1" HigherBound="175.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="9151cb4a-eb6a-4f90-8e3e-9d51dcba3328" ParentLink="ComplexStatement_Statement" LowerBound="83.1" HigherBound="91.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create WS" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="99722626-fdff-4ca3-890f-c9e206f910b4" ParentLink="Construct_MessageRef" LowerBound="84.31" HigherBound="84.53">
                            <om:Property Name="Ref" Value="N2VenditaCaloreRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="9cf7a5f4-afc8-498c-98ec-2a1a770f88c8" ParentLink="ComplexStatement_Statement" LowerBound="86.1" HigherBound="88.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreBresciaPollingToN2VenditaCalore" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup WS" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="bb625489-2c5c-4854-bc03-76e48dde5567" ParentLink="Transform_OutputMessagePartRef" LowerBound="87.36" HigherBound="87.69">
                                <om:Property Name="MessageRef" Value="N2VenditaCaloreRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="9be3d449-38fc-4b46-b728-074e749a48d8" ParentLink="Transform_InputMessagePartRef" LowerBound="87.167" HigherBound="87.201">
                                <om:Property Name="MessageRef" Value="ACSVenditaCaloreBrescia" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="84335967-720a-484f-8768-2f38ada0f8d6" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                            <om:Property Name="Expression" Value="N2VenditaCaloreRequest.parameters.import.Nomefile = System.IO.Path.GetFileName(ACSVenditaCaloreBrescia.parameters.NomeFile);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="NomeFile" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="87135b4a-2894-4079-9397-c3d8124b285c" ParentLink="ComplexStatement_Statement" LowerBound="91.1" HigherBound="93.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="f08a7a0c-b774-4da2-a212-3164143ea4e8" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="95.1">
                        <om:Property Name="PortName" Value="N2VenditaCaloreOut" />
                        <om:Property Name="MessageName" Value="N2VenditaCaloreRequest" />
                        <om:Property Name="OperationName" Value="ImportazioneRicavi" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="13482542-6a5b-4b9b-a720-ea8bc882af97" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="97.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="N2VenditaCaloreOut" />
                        <om:Property Name="MessageName" Value="N2VenditaCaloreResponse" />
                        <om:Property Name="OperationName" Value="ImportazioneRicavi" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="9fd97bf4-50c1-4d8e-abd4-914e98350125" ParentLink="ComplexStatement_Statement" LowerBound="97.1" HigherBound="100.1">
                        <om:Property Name="Expression" Value="WSResponse = N2VenditaCaloreResponse.parameters;&#xD;&#xA;WSResponseContent = WSResponse.InnerXml.ToString();&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Assign Response" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Decision" OID="750b7a44-121b-4b0c-8d90-e21e27b815e2" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="113.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Response OK" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="a9e816d7-85fc-4f70-838a-4597a0040e41" ParentLink="ReallyComplexStatement_Branch" LowerBound="101.21" HigherBound="113.1">
                            <om:Property Name="Expression" Value="WSResponseContent.Contains(&quot;KO&quot;) == true" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="NOT OK" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="3d998cfe-1af8-48d2-9472-ff9b7bc3759c" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="112.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(WSResponse.InnerText);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Error" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="9d073b99-5073-4fcc-9589-800d122dbd15" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="94a7d3a7-787a-4c4c-a5e7-8ebf406f3d24" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="120.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Esito WS&quot;, WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="f849976d-840d-417c-a143-e9ea190fcf6c" ParentLink="ServiceBody_Statement" LowerBound="178.1" HigherBound="234.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Catch" OID="d5f5012a-f95a-4ba2-9611-1ea86222bb7a" ParentLink="Scope_Catch" LowerBound="204.1" HigherBound="218.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="45dfb914-673b-4df8-9944-5c2eab82a6c1" ParentLink="Catch_Statement" LowerBound="207.1" HigherBound="217.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="0156b0dd-65be-47ba-907e-0f6d5bc66bc5" ParentLink="Scope_Catch" LowerBound="218.1" HigherBound="232.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="9de98978-d192-4648-b9b0-1782e2e1fa80" ParentLink="Catch_Statement" LowerBound="221.1" HigherBound="231.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="40710cdb-3998-4e7b-b34a-4eb7f8da7f31" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="193.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="76c7515c-6dbc-4d23-ae2a-3a46fa54bf3e" ParentLink="ComplexStatement_Statement" LowerBound="186.1" HigherBound="188.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreBresciaPollingToACSUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="208e6ca4-4c88-4e63-bd38-4f3c1d441268" ParentLink="Transform_OutputMessagePartRef" LowerBound="187.36" HigherBound="187.69">
                                <om:Property Name="MessageRef" Value="ACSUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="bdfe3a80-b3e5-4975-9403-14a7afc4d43a" ParentLink="Transform_InputMessagePartRef" LowerBound="187.167" HigherBound="187.201">
                                <om:Property Name="MessageRef" Value="ACSVenditaCaloreBrescia" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="f239a534-db52-4a30-8828-7f4efc5d9f8f" ParentLink="ComplexStatement_Statement" LowerBound="188.1" HigherBound="192.1">
                            <om:Property Name="Expression" Value="ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="0a1b0544-9091-4eb3-a852-811bc68cd477" ParentLink="Construct_MessageRef" LowerBound="184.31" HigherBound="184.53">
                            <om:Property Name="Ref" Value="ACSUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="ac4951ae-83f0-4513-9e43-ae6c8b05950e" ParentLink="ComplexStatement_Statement" LowerBound="193.1" HigherBound="195.1">
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="59d215c0-0300-4115-a5ac-458f6b12f734" ParentLink="ComplexStatement_Statement" LowerBound="195.1" HigherBound="197.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="5d124678-8788-4072-8a92-18552a972f4c" ParentLink="ComplexStatement_Statement" LowerBound="197.1" HigherBound="201.1">
                        <om:Property Name="Expression" Value="sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="531ac69a-a44e-41bb-9325-3a3b25b06389" ParentLink="ServiceBody_Statement" LowerBound="234.1" HigherBound="271.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="53820cf1-2d58-4d6f-98a8-9fb8def23fc5" ParentLink="ReallyComplexStatement_Branch" LowerBound="235.13" HigherBound="238.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="f1687339-7299-42b5-991b-15adbcc86243" ParentLink="ReallyComplexStatement_Branch" LowerBound="238.18" HigherBound="241.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="e0aca705-4ee3-40d8-a572-15cdc835b2ab" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="aa57a0da-31b7-47ff-b433-6d0507c811aa" ParentLink="ComplexStatement_Statement" LowerBound="243.1" HigherBound="270.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="59e36172-0656-4714-8b57-2e31dcd27098" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="266.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="680f96f7-5d56-41b0-aed2-19eb783b4abd" ParentLink="Construct_MessageRef" LowerBound="249.35" HigherBound="249.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="16471cb9-497e-4cb4-b8ab-3077c73ec4d5" ParentLink="ComplexStatement_Statement" LowerBound="251.1" HigherBound="265.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameters.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="28c5be47-aa90-4c5a-82f3-23f15866197c" ParentLink="ComplexStatement_Statement" LowerBound="266.1" HigherBound="268.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="22968175-64f3-49ef-9de4-448d9a7f871b" ParentLink="ServiceBody_Statement" LowerBound="271.1" HigherBound="280.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b3e9569f-b28b-42e9-a6ff-45fe0b70c63d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreBresciaPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreBresciaPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="0f2d214b-32b0-4f0f-aae5-5a7188adcc1a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="0b92d5b3-df26-4e64-a201-34feef21dd1a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="23.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="41" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="2ee2110d-2f02-44ee-b7e1-712cbf1f7815" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9f6f7d63-6373-49b0-a3fe-7ff70e138d51" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="23.1" HigherBound="26.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="143" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaRetiUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="f9b9b6e2-45aa-4227-a5a7-822696a2cdc4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="23.1" HigherBound="24.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="d82ab480-9b34-4502-80cd-66820b2fa873" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="26.1" HigherBound="28.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="199" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="77c386b6-817e-47e3-88c5-e75f5da18c5f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="26.1" HigherBound="27.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype ACSVenditaCaloreBresciaType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaPolling_Type parameters;
    };
    internal porttype ACSVenditaCaloreBresciaPollingInType
    {
        oneway Receive
        {
            ACSVenditaCaloreBresciaType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service VenditaCaloreBresciaProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ACSVenditaCaloreBresciaPollingInType ACSVenditaCaloreBresciaPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses N2VenditaCaloreOutType N2VenditaCaloreOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseUnaRetiUpdateStatusOutType ACSUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message N2VenditaCaloreResponseType N2VenditaCaloreResponse;
        message N2VenditaCaloreRequestType N2VenditaCaloreRequest;
        message ACSUpdateStatusResponseType ACSUpdateStatusResponse;
        message ACSUpdateStatusRequestType ACSUpdateStatusRequest;
        message ACSVenditaCaloreBresciaType ACSVenditaCaloreBrescia;
        System.String WSResponseContent;
        System.Xml.XmlDocument WSResponse;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("dc80e05a-8442-4097-a215-dad8fe2fca1b")]
            activate receive (ACSVenditaCaloreBresciaPollingIn.Receive, ACSVenditaCaloreBrescia);
            WSResponseContent = "";
            WSResponse = new System.Xml.XmlDocument();
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c57b3522-d0c5-490d-ac48-c52e02821b2d")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreBresciaProcess;
            sendNotification = 0;
            
            WSResponseContent = System.String.Empty;
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("105c04ff-7dc1-431c-8caf-559b55136fdb")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "VenditaCaloreBrescia",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSVenditaCaloreBrescia.parameters.activityIdStaging, "");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebf97a98-761c-421f-a908-9316de61ae09")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9151cb4a-eb6a-4f90-8e3e-9d51dcba3328")]
                    construct N2VenditaCaloreRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9cf7a5f4-afc8-498c-98ec-2a1a770f88c8")]
                        transform (N2VenditaCaloreRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreBresciaPollingToN2VenditaCalore (ACSVenditaCaloreBrescia.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("84335967-720a-484f-8768-2f38ada0f8d6")]
                        N2VenditaCaloreRequest.parameters.import.Nomefile = System.IO.Path.GetFileName(ACSVenditaCaloreBrescia.parameters.NomeFile);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("87135b4a-2894-4079-9397-c3d8124b285c")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f08a7a0c-b774-4da2-a212-3164143ea4e8")]
                    send (N2VenditaCaloreOut.ImportazioneRicavi, N2VenditaCaloreRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("13482542-6a5b-4b9b-a720-ea8bc882af97")]
                    receive (N2VenditaCaloreOut.ImportazioneRicavi, N2VenditaCaloreResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9fd97bf4-50c1-4d8e-abd4-914e98350125")]
                    WSResponse = N2VenditaCaloreResponse.parameters;
                    WSResponseContent = WSResponse.InnerXml.ToString();
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("750b7a44-121b-4b0c-8d90-e21e27b815e2")]
                    if (WSResponseContent.Contains("KO") == true)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3d998cfe-1af8-48d2-9472-ff9b7bc3759c")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(WSResponse.InnerText);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("94a7d3a7-787a-4c4c-a5e7-8ebf406f3d24")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Esito WS", WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d5a7be6-be43-4ca1-b34b-460d8395a6ce")]
                    catch (Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException validationExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("db44796a-f86a-4b93-8e11-ce67bdc6df89")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(validationExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb078ef6-34e9-41c8-b024-ab94f3528cb9")]
                    catch (N2VenditaCaloreOut.ImportazioneRicavi.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("abe42893-a19f-4089-9bf0-e229886ddb44")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("53d4c9c1-4aef-4a6b-941b-5710f63c2617")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("acebb9e2-722f-4af8-9ce1-aa590bb397bc")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8848dee8-ebb4-47ba-8837-b93fe7b982ff")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c5f689aa-3521-4207-8a91-6c81c7148cf7")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f849976d-840d-417c-a143-e9ea190fcf6c")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("40710cdb-3998-4e7b-b34a-4eb7f8da7f31")]
                    construct ACSUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("76c7515c-6dbc-4d23-ae2a-3a46fa54bf3e")]
                        transform (ACSUpdateStatusRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreBresciaPollingToACSUpdateStatus (ACSVenditaCaloreBrescia.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f239a534-db52-4a30-8828-7f4efc5d9f8f")]
                        ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ac4951ae-83f0-4513-9e43-ae6c8b05950e")]
                    send (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("59d215c0-0300-4115-a5ac-458f6b12f734")]
                    receive (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5d124678-8788-4072-8a92-18552a972f4c")]
                    sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d5f5012a-f95a-4ba2-9611-1ea86222bb7a")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("45dfb914-673b-4df8-9944-5c2eab82a6c1")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0156b0dd-65be-47ba-907e-0f6d5bc66bc5")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9de98978-d192-4648-b9b0-1782e2e1fa80")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("531ac69a-a44e-41bb-9325-3a3b25b06389")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("aa57a0da-31b7-47ff-b433-6d0507c811aa")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("59e36172-0656-4714-8b57-2e31dcd27098")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("16471cb9-497e-4cb4-b8ab-3077c73ec4d5")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameters.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("28c5be47-aa90-4c5a-82f3-23f15866197c")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("22968175-64f3-49ef-9de4-448d9a7f871b")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

