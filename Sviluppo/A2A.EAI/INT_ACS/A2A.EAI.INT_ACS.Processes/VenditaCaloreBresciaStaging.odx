﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="e367ab7e-b619-48ec-adcf-39e329b01695" LowerBound="1.1" HigherBound="190.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="cf4ae035-16a0-4dee-b7d2-2c90b9114bea" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="fb821f45-f8a8-4dd5-9580-a86618a0698d" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="3370f484-ab7c-4616-aa45-fc0567de1689" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.37">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="eb359245-36fc-42cc-b95d-3a34729748fa" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSVenditaCaloreBresciaInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="709de6ae-af8b-4b4b-9cb1-775d76d0e68f" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreBresciaInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="ce955456-76fd-45f4-9da7-40fec88a6433" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.50">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="abae4dee-f6d4-4ae7-9547-0b02a0ecbc2a" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.52" HigherBound="27.90">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7d80e250-ba9a-4d1e-938c-64d519b55299" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4e3b3f0a-b850-46cd-8ca5-d71a77cd5e28" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.VenditaCaloreBrescia" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bf846f5e-f8d3-4f1d-b0f8-f41c97086e6c" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="2559f04e-8036-4556-a7d7-e7d3bcaf5e90" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaInsertTypedProcedure.ACSVenditaCaloreBresciaInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="3e0ccc31-c856-4491-b2f3-dd6a6ef36feb" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d7f8ebab-6a77-4817-b73b-96c719d4a845" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaInsertTypedProcedure.ACSVenditaCaloreBresciaInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="a5384d7d-af92-4aa6-b12e-56784ef86467" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="189.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreBresciaStaging" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="3984a74c-6cf5-487a-9449-5ae330b87d6f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="fda30b50-0ebe-4c39-a4ea-1fe19f6758a0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ae57a15e-a3f6-4487-9210-ea4b18f2ce6f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="669d7c7b-8cd2-45a5-bf62-79dccef8bedb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5371e18b-4ddc-4960-835e-12e0535dfba1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="163d5d18-9ef0-49a1-a307-9d866072fd2d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9b88d440-cb0c-4b73-98e6-f93f9e68f0b5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2e55c019-3fdd-4399-8163-0def31dcbc3d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b72ed95c-9b32-48c6-9789-0626d8996874" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e1009eb4-fa09-4cca-905a-1420fe237bb9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b010a121-dc88-4229-a85f-238caf6c4034" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="73b6bbc5-5efe-4797-a99b-314667ed14ef" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VenditaCaloreBrescia" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e4019872-2e4d-4996-898f-d003bdc76209" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VenditaCaloreBresciaInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e45ed7db-4e97-44ba-9b3d-6ed9d6bba722" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VenditaCaloreBresciaInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="a37871bc-aede-42ca-bed9-d6a613f62b59" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="e3f8ee75-f4a6-4bb5-8172-94173c4c08c1" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="63.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="VenditaCaloreBresciaIn" />
                    <om:Property Name="MessageName" Value="VenditaCaloreBrescia" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="6aef3bd9-6520-4d87-9bc1-862eae86fc44" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="77.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreBresciaStaging;&#xD;&#xA;&#xD;&#xA;dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VenditaCaloreBrescia);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VenditaCaloreBrescia);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0aae7f5d-82d9-4669-9af0-c4506151fe21" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="88.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;VenditaCaloreBrescia&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;File Name&quot;, originalFileName&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="c535c819-9e58-4e14-bc66-0422196aded1" ParentLink="ServiceBody_Statement" LowerBound="88.1" HigherBound="146.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="ebcc684c-91ae-4b8a-a8bd-4c398e13e142" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="102.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="ed2d5515-81d6-48e7-a585-916cf0f3af8d" ParentLink="ComplexStatement_Statement" LowerBound="96.1" HigherBound="98.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="40f1733f-9be0-48d2-aa85-121ee7547eb3" ParentLink="Transform_InputMessagePartRef" LowerBound="97.182" HigherBound="97.213">
                                <om:Property Name="MessageRef" Value="VenditaCaloreBrescia" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="4197dea8-93a6-44ac-964e-9a4179f72f63" ParentLink="Transform_OutputMessagePartRef" LowerBound="97.36" HigherBound="97.80">
                                <om:Property Name="MessageRef" Value="VenditaCaloreBresciaInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="e96933a9-0e41-4214-9a32-62defc9a25c2" ParentLink="ComplexStatement_Statement" LowerBound="98.1" HigherBound="101.1">
                            <om:Property Name="Expression" Value="VenditaCaloreBresciaInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;VenditaCaloreBresciaInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="d640859a-3232-4964-89a6-126d181324fb" ParentLink="Construct_MessageRef" LowerBound="94.31" HigherBound="94.64">
                            <om:Property Name="Ref" Value="VenditaCaloreBresciaInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="158b68a4-4e61-4835-a0e8-fd7128b9cd2e" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="bc04fb8e-f227-48a2-87c0-8bc1a48c17cc" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                        <om:Property Name="PortName" Value="ACSVenditaCaloreBresciaInsertOut" />
                        <om:Property Name="MessageName" Value="VenditaCaloreBresciaInsertRequest" />
                        <om:Property Name="OperationName" Value="ACSVenditaCaloreBresciaInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="62f2afbc-55b8-4047-adf9-ecdb63665408" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="108.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSVenditaCaloreBresciaInsertOut" />
                        <om:Property Name="MessageName" Value="VenditaCaloreBresciaInsertResponse" />
                        <om:Property Name="OperationName" Value="ACSVenditaCaloreBresciaInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="f7595572-0b30-49db-ba92-d92a5be8441f" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="114.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="e8d81caa-7895-4512-a071-c8234209418b" ParentLink="Scope_Catch" LowerBound="117.1" HigherBound="131.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="cc21af7d-6be5-4e0c-a0e4-046b2ace3c73" ParentLink="Catch_Statement" LowerBound="120.1" HigherBound="130.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="562cd139-99bf-4593-abad-a58b3cfa306f" ParentLink="Scope_Catch" LowerBound="131.1" HigherBound="144.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="db92fa52-17b2-401d-99d2-80ee824c5c23" ParentLink="Catch_Statement" LowerBound="134.1" HigherBound="143.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="6f3abdcc-196f-428d-b69e-27f4339aefd1" ParentLink="ServiceBody_Statement" LowerBound="146.1" HigherBound="179.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="ae82dbe4-b54c-40f4-b704-4d38828103cd" ParentLink="ReallyComplexStatement_Branch" LowerBound="147.13" HigherBound="150.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="eef3446e-49f6-49d5-872d-89b1bbb2c5d7" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="ffe48861-fe76-4d02-82ce-13479607cd03" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="178.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="d1ae44cb-5469-4149-8a95-178cdea88205" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="174.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="cb31e7b7-983a-462d-889e-19a5cf2e161e" ParentLink="Construct_MessageRef" LowerBound="158.35" HigherBound="158.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="73326d9c-badc-45dd-a2ca-68c30b8c0215" ParentLink="ComplexStatement_Statement" LowerBound="160.1" HigherBound="173.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="c163a4a3-c7e2-4f01-a8a7-cadbbfc2bf51" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="176.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="4f6c47f9-8fd1-4498-b370-91cf483a6bc4" ParentLink="ServiceBody_Statement" LowerBound="179.1" HigherBound="187.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="bfe69cc0-9320-4d76-940c-358cc12f86e2" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.VenditaCaloreBresciaInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VenditaCaloreBresciaIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="60a577f1-fbff-4b5b-b28c-416750c5aafb" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="f111a058-645e-4e14-b5e8-db1ae17a7da3" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="33" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreBresciaInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreBresciaInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="9d806bdb-6032-459a-a363-5d7b546a5a5a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="01c8fe8d-660f-444b-8fff-8e253e3b219a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="103" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="260d2124-ae50-4350-8cb6-545170c4f4e7" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype VenditaCaloreBresciaType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.VenditaCaloreBrescia parameters;
    };
    internal messagetype VenditaCaloreBresciaInsertRequestType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaInsertTypedProcedure.ACSVenditaCaloreBresciaInsert parameters;
    };
    internal messagetype VenditaCaloreBresciaInsertResponseType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreBresciaInsertTypedProcedure.ACSVenditaCaloreBresciaInsertResponse parameters;
    };
    internal porttype VenditaCaloreBresciaInType
    {
        oneway Receive
        {
            VenditaCaloreBresciaType
        };
    };
    internal porttype ACSVenditaCaloreBresciaInsertOutType
    {
        requestresponse ACSVenditaCaloreBresciaInsert
        {
            VenditaCaloreBresciaInsertRequestType, VenditaCaloreBresciaInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service VenditaCaloreBresciaStaging
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements VenditaCaloreBresciaInType VenditaCaloreBresciaIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSVenditaCaloreBresciaInsertOutType ACSVenditaCaloreBresciaInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message VenditaCaloreBresciaType VenditaCaloreBrescia;
        message VenditaCaloreBresciaInsertRequestType VenditaCaloreBresciaInsertRequest;
        message VenditaCaloreBresciaInsertResponseType VenditaCaloreBresciaInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3f8ee75-f4a6-4bb5-8172-94173c4c08c1")]
            activate receive (VenditaCaloreBresciaIn.Receive, VenditaCaloreBrescia);
            originalFileName = "";
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6aef3bd9-6520-4d87-9bc1-862eae86fc44")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreBresciaStaging;
            
            dataInizio = System.DateTimeOffset.Now;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VenditaCaloreBrescia);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VenditaCaloreBrescia);
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0aae7f5d-82d9-4669-9af0-c4506151fe21")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "VenditaCaloreBrescia",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "File Name", originalFileName
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c535c819-9e58-4e14-bc66-0422196aded1")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebcc684c-91ae-4b8a-a8bd-4c398e13e142")]
                    construct VenditaCaloreBresciaInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ed2d5515-81d6-48e7-a585-916cf0f3af8d")]
                        transform (VenditaCaloreBresciaInsertRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.VenditaCaloreBresciaToACSVenditaCaloreBresciaInsert (VenditaCaloreBrescia.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e96933a9-0e41-4214-9a32-62defc9a25c2")]
                        VenditaCaloreBresciaInsertRequest.parameters.fileName = originalFileName;
                        VenditaCaloreBresciaInsertRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("158b68a4-4e61-4835-a0e8-fd7128b9cd2e")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("bc04fb8e-f227-48a2-87c0-8bc1a48c17cc")]
                    send (ACSVenditaCaloreBresciaInsertOut.ACSVenditaCaloreBresciaInsert, VenditaCaloreBresciaInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("62f2afbc-55b8-4047-adf9-ecdb63665408")]
                    receive (ACSVenditaCaloreBresciaInsertOut.ACSVenditaCaloreBresciaInsert, VenditaCaloreBresciaInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f7595572-0b30-49db-ba92-d92a5be8441f")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e8d81caa-7895-4512-a071-c8234209418b")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc21af7d-6be5-4e0c-a0e4-046b2ace3c73")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("562cd139-99bf-4593-abad-a58b3cfa306f")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("db92fa52-17b2-401d-99d2-80ee824c5c23")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6f3abdcc-196f-428d-b69e-27f4339aefd1")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("ffe48861-fe76-4d02-82ce-13479607cd03")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1ae44cb-5469-4149-8a95-178cdea88205")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("73326d9c-badc-45dd-a2ca-68c30b8c0215")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c163a4a3-c7e2-4f01-a8a7-cadbbfc2bf51")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4f6c47f9-8fd1-4498-b370-91cf483a6bc4")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

