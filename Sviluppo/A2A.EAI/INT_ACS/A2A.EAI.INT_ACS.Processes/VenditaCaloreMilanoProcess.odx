﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="e3905c42-6649-4fd1-87c2-4da60a0f2a53" LowerBound="1.1" HigherBound="283.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ACS.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="c0f7093c-c7a0-4b08-9333-9bbb35762864" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSVenditaCaloreMilanoPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="c8ef83dc-f031-4afb-b6c0-2470a088aa5e" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="ad946974-72bb-4f27-9568-9a3c3100221e" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.39">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreMilanoType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0f8028eb-7398-4e89-b0eb-7db711377fda" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ACSVenditaCaloreMilanoType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7a4d9831-7bf0-48ef-a6bc-31f5a391ad2a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreMilanoPolling_Type" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="e76be6ef-3d61-4f86-9854-5777af90815c" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="282.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VenditaCaloreMilanoProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="7a847d6a-2417-4ffb-afa8-cf8e2b6d9319" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponseContent" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="59cf2ef3-244e-44b7-875b-41673788942c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="WSResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b6aaa083-ba3c-4aa0-8b5f-60c21d9f3ef8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8c53cf4a-352f-4ffc-b8bd-0602404c8fa5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c6af7b68-fbbb-4ad4-90ae-be0a092cf657" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="28af152e-0cfc-4d91-8b14-c8de2de81522" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e2563c02-f35d-4cc7-9d48-bdc14795bf7d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f3d34dd-2b06-4b38-b600-b764290e4a1f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed7100d3-2431-4bb3-a5f4-98903bbb2029" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="73372def-d02a-4c57-ae1a-6e07dfad94be" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="867ac69e-ab21-4ec6-95a6-3891941f9c20" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="688ec4f2-f0b2-4c6b-9029-0fca0eb669b1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="759dda6e-23ed-4411-8661-19d5000a495b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5ac97b07-391d-4d8e-b956-ea23cd792e4c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="41fd7588-1527-48df-bd48-493062ef4b04" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="06744d88-e6a6-45b9-9bf6-841b736b1fba" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1b6c1083-a987-4209-bd21-74b274f0172f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreMilanoType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreMilano" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="bbc78c26-5858-432f-8c0d-a81b4ce55d3c" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="2f5ff4a9-a1af-47ff-96d3-bf9105cd9709" ParentLink="ServiceBody_Statement" LowerBound="47.1" HigherBound="54.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ACSVenditaCaloreMilanoPollingIn" />
                    <om:Property Name="MessageName" Value="ACSVenditaCaloreMilano" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="77954aa6-b63d-455f-accf-06368b7a417e" ParentLink="ServiceBody_Statement" LowerBound="54.1" HigherBound="66.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreMilanoProcess;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;WSResponseContent = System.String.Empty;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="6cff2eed-0baa-4207-813f-968572423e10" ParentLink="ServiceBody_Statement" LowerBound="66.1" HigherBound="78.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;VenditaCaloreMilano&quot;,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSVenditaCaloreMilano.parameters.activityIdStaging, &quot;&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="acf6f1a8-b517-41d6-9ba5-a5b467e5a7b9" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="178.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Catch" OID="63ba77b9-5fce-40b3-9242-d5788e9b6d3b" ParentLink="Scope_Catch" LowerBound="123.1" HigherBound="137.1">
                        <om:Property Name="ExceptionName" Value="validationExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Message Type Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0fb7ecc3-b650-418b-81f9-64409e811c21" ParentLink="Catch_Statement" LowerBound="126.1" HigherBound="136.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(validationExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f9dff920-3e0c-4fd1-aaee-9f2c59bed372" ParentLink="Scope_Catch" LowerBound="137.1" HigherBound="151.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="N2VenditaCaloreOut.ImportazioneRicavi.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e4ec0ce5-366b-4071-b53e-82425cc79b4f" ParentLink="Catch_Statement" LowerBound="140.1" HigherBound="150.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="80197aab-d3d9-4029-b553-a257099b9410" ParentLink="Scope_Catch" LowerBound="151.1" HigherBound="164.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="51af0154-ae8f-4c3b-a51c-9a6358598796" ParentLink="Catch_Statement" LowerBound="154.1" HigherBound="163.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f99e5e75-d120-4c97-afa4-09c463c907f4" ParentLink="Scope_Catch" LowerBound="164.1" HigherBound="176.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f9bd94ca-0814-4d63-a9ec-f62f07cb7968" ParentLink="Catch_Statement" LowerBound="167.1" HigherBound="175.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="cf8b739f-9612-4561-8e91-c9821e3c6a70" ParentLink="ComplexStatement_Statement" LowerBound="83.1" HigherBound="91.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create WS" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="fed4e5fd-8135-427e-87b5-6980a334ab0f" ParentLink="Construct_MessageRef" LowerBound="84.31" HigherBound="84.53">
                            <om:Property Name="Ref" Value="N2VenditaCaloreRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="1ca09024-e5ab-4a96-83f5-0b5740a26e67" ParentLink="ComplexStatement_Statement" LowerBound="86.1" HigherBound="88.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreMilanoPollingToN2VenditaCalore" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup WS" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="7685eb4c-ba38-4175-89b1-023b2e75f38c" ParentLink="Transform_InputMessagePartRef" LowerBound="87.166" HigherBound="87.199">
                                <om:Property Name="MessageRef" Value="ACSVenditaCaloreMilano" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="a191735a-5c4b-4c25-8a8c-2c2a3bdd31ae" ParentLink="Transform_OutputMessagePartRef" LowerBound="87.36" HigherBound="87.69">
                                <om:Property Name="MessageRef" Value="N2VenditaCaloreRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="bb34ab6e-1507-42cb-83a6-c21408076f6d" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                            <om:Property Name="Expression" Value="N2VenditaCaloreRequest.parameters.import.Nomefile = System.IO.Path.GetFileName(ACSVenditaCaloreMilano.parameters.NomeFile);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="NomeFile" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="fda3d072-6610-4825-b416-b49e5b1aaefd" ParentLink="ComplexStatement_Statement" LowerBound="91.1" HigherBound="93.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="d8cb8123-d6ad-4b08-9b18-e9dee4668810" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="95.1">
                        <om:Property Name="PortName" Value="N2VenditaCaloreOut" />
                        <om:Property Name="MessageName" Value="N2VenditaCaloreRequest" />
                        <om:Property Name="OperationName" Value="ImportazioneRicavi" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="72c7cd9f-1329-4f7c-8bdf-6c2bf47e9a80" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="97.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="N2VenditaCaloreOut" />
                        <om:Property Name="MessageName" Value="N2VenditaCaloreResponse" />
                        <om:Property Name="OperationName" Value="ImportazioneRicavi" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="ff1a003e-711b-4e11-a45e-91d4ecb018b2" ParentLink="ComplexStatement_Statement" LowerBound="97.1" HigherBound="100.1">
                        <om:Property Name="Expression" Value="WSResponse = N2VenditaCaloreResponse.parameters;&#xD;&#xA;WSResponseContent = WSResponse.InnerXml.ToString();&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Assign Response" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Decision" OID="3a2e60d0-4980-4010-bf59-f3ca2b08b43d" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="113.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Response OK" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="cf77ca6d-b76e-45ab-a822-1f5a29d27602" ParentLink="ReallyComplexStatement_Branch" LowerBound="101.21" HigherBound="113.1">
                            <om:Property Name="Expression" Value="WSResponseContent.Contains(&quot;KO&quot;) == true" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="NOT OK" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="ced583fe-1839-41c9-bb18-d1177ec3dd94" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="112.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(WSResponse.InnerText);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Error" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="80e66ccb-1517-4481-a07f-e79ab448b5a7" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="a5c8e1d4-3a57-402d-bad9-561e43aedaf7" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="120.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Esito WS&quot;, WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="cda7a03d-e13c-45c7-9f34-afbfce558e2a" ParentLink="ServiceBody_Statement" LowerBound="178.1" HigherBound="234.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="c0e5bc9e-2067-4cc4-9421-19eb9b86a76f" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="193.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="d4035d31-cc83-4ef8-b463-c31aeac5f1f3" ParentLink="Construct_MessageRef" LowerBound="184.31" HigherBound="184.53">
                            <om:Property Name="Ref" Value="ACSUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="9c483bac-3be0-40e6-9fe3-57d335939a6f" ParentLink="ComplexStatement_Statement" LowerBound="186.1" HigherBound="188.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreMilanoPollingToACSUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="36edaaec-407d-4e1c-974b-678490c2b519" ParentLink="Transform_InputMessagePartRef" LowerBound="187.166" HigherBound="187.199">
                                <om:Property Name="MessageRef" Value="ACSVenditaCaloreMilano" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="a799a783-1db3-4aff-8678-430b38e46047" ParentLink="Transform_OutputMessagePartRef" LowerBound="187.36" HigherBound="187.69">
                                <om:Property Name="MessageRef" Value="ACSUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="3b4ac797-7d9e-45c3-8a88-dd9396e7f9a1" ParentLink="ComplexStatement_Statement" LowerBound="188.1" HigherBound="192.1">
                            <om:Property Name="Expression" Value="ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="dc427616-2ee2-42cd-af65-4105bc43d72c" ParentLink="ComplexStatement_Statement" LowerBound="193.1" HigherBound="195.1">
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="cc2a7eba-fa28-46be-9457-794acbb9db31" ParentLink="ComplexStatement_Statement" LowerBound="195.1" HigherBound="197.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ACSUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ACSUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ACSUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="c1b4ac79-49bd-4114-9873-03bdda8e89c7" ParentLink="ComplexStatement_Statement" LowerBound="197.1" HigherBound="201.1">
                        <om:Property Name="Expression" Value="sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="fb1b7164-c416-4404-a1ed-0a5b704c8d4e" ParentLink="Scope_Catch" LowerBound="204.1" HigherBound="218.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f5df6d87-7537-42b7-ac1f-cd3b97f2a73d" ParentLink="Catch_Statement" LowerBound="207.1" HigherBound="217.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="7d2a288a-f48b-430f-b3c2-19a6db3d8dfd" ParentLink="Scope_Catch" LowerBound="218.1" HigherBound="232.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="afcd3375-d37c-4501-ab21-b460b56b7143" ParentLink="Catch_Statement" LowerBound="221.1" HigherBound="231.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="003a7bd0-f2d3-4888-bb79-1bf6a6bf08f4" ParentLink="ServiceBody_Statement" LowerBound="234.1" HigherBound="271.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="69eecacc-c002-484d-91b5-4e880c6d0e00" ParentLink="ReallyComplexStatement_Branch" LowerBound="235.13" HigherBound="238.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="a7129ec9-db44-4fb2-b8c4-efa4f2396566" ParentLink="ReallyComplexStatement_Branch" LowerBound="238.18" HigherBound="241.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="6ef4a83b-59af-45e3-9e05-1f7403850ebc" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="7cfa9a67-4aeb-4d99-82dd-5b51f643252f" ParentLink="ComplexStatement_Statement" LowerBound="243.1" HigherBound="270.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="1dba8ad6-2679-4ead-963d-054199152587" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="266.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="a855ff2a-38c4-4946-957b-665ac257cc67" ParentLink="Construct_MessageRef" LowerBound="249.35" HigherBound="249.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="b4f40d5f-7209-4c1f-9274-a9a971e185f7" ParentLink="ComplexStatement_Statement" LowerBound="251.1" HigherBound="265.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameters.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e2e5aa76-5531-42d6-a199-bcb0738a9b0a" ParentLink="ComplexStatement_Statement" LowerBound="266.1" HigherBound="268.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="14a44be4-d477-4b2e-a742-4558564f1fd0" ParentLink="ServiceBody_Statement" LowerBound="271.1" HigherBound="280.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="bdf12047-fc44-4394-869e-9737262cc6bb" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSVenditaCaloreMilanoPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSVenditaCaloreMilanoPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="95d61650-2a53-46b2-9a45-00c5809e4d39" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="580beeb5-74a2-4c5b-95bd-e5dd33d5bfbf" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="23.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="41" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.N2VenditaCaloreOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2VenditaCaloreOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="5180bb42-5e7c-47ea-8f4c-0ba292838f73" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="92e60670-6a67-4b33-b59e-265f2084c0df" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="23.1" HigherBound="26.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="143" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.ACSCO2EmesseUnaRetiUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ACSUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="63154abe-524c-4586-b794-37240c1c72d8" ParentLink="PortDeclaration_CLRAttribute" LowerBound="23.1" HigherBound="24.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5fb134b6-a5d2-4037-a722-8ad48fdabb89" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="26.1" HigherBound="28.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="199" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ACS.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="656c8452-5366-465e-866e-8b977a86aa97" ParentLink="PortDeclaration_CLRAttribute" LowerBound="26.1" HigherBound="27.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ACS.Processes
{
    internal messagetype ACSVenditaCaloreMilanoType
    {
        body A2A.EAI.INT_ACS.Messaging.Schemas.ACSVenditaCaloreMilanoPolling_Type parameters;
    };
    internal porttype ACSVenditaCaloreMilanoPollingInType
    {
        oneway Receive
        {
            ACSVenditaCaloreMilanoType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service VenditaCaloreMilanoProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ACSVenditaCaloreMilanoPollingInType ACSVenditaCaloreMilanoPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses N2VenditaCaloreOutType N2VenditaCaloreOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ACSCO2EmesseUnaRetiUpdateStatusOutType ACSUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message ACSUpdateStatusResponseType ACSUpdateStatusResponse;
        message ACSUpdateStatusRequestType ACSUpdateStatusRequest;
        message NotificationType Notification;
        message N2VenditaCaloreRequestType N2VenditaCaloreRequest;
        message N2VenditaCaloreResponseType N2VenditaCaloreResponse;
        message ACSVenditaCaloreMilanoType ACSVenditaCaloreMilano;
        System.String WSResponseContent;
        System.Xml.XmlDocument WSResponse;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2f5ff4a9-a1af-47ff-96d3-bf9105cd9709")]
            activate receive (ACSVenditaCaloreMilanoPollingIn.Receive, ACSVenditaCaloreMilano);
            WSResponseContent = "";
            WSResponse = new System.Xml.XmlDocument();
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("77954aa6-b63d-455f-accf-06368b7a417e")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ACS.Services.ProcessServices.FlowDescriptionVenditaCaloreMilanoProcess;
            sendNotification = 0;
            
            WSResponseContent = System.String.Empty;
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6cff2eed-0baa-4207-813f-968572423e10")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "VenditaCaloreMilano",
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameStaging, ACSVenditaCaloreMilano.parameters.activityIdStaging, "");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("acf6f1a8-b517-41d6-9ba5-a5b467e5a7b9")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("cf8b739f-9612-4561-8e91-c9821e3c6a70")]
                    construct N2VenditaCaloreRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1ca09024-e5ab-4a96-83f5-0b5740a26e67")]
                        transform (N2VenditaCaloreRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreMilanoPollingToN2VenditaCalore (ACSVenditaCaloreMilano.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bb34ab6e-1507-42cb-83a6-c21408076f6d")]
                        N2VenditaCaloreRequest.parameters.import.Nomefile = System.IO.Path.GetFileName(ACSVenditaCaloreMilano.parameters.NomeFile);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fda3d072-6610-4825-b416-b49e5b1aaefd")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8cb8123-d6ad-4b08-9b18-e9dee4668810")]
                    send (N2VenditaCaloreOut.ImportazioneRicavi, N2VenditaCaloreRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("72c7cd9f-1329-4f7c-8bdf-6c2bf47e9a80")]
                    receive (N2VenditaCaloreOut.ImportazioneRicavi, N2VenditaCaloreResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ff1a003e-711b-4e11-a45e-91d4ecb018b2")]
                    WSResponse = N2VenditaCaloreResponse.parameters;
                    WSResponseContent = WSResponse.InnerXml.ToString();
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a2e60d0-4980-4010-bf59-f3ca2b08b43d")]
                    if (WSResponseContent.Contains("KO") == true)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ced583fe-1839-41c9-bb18-d1177ec3dd94")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(WSResponse.InnerText);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a5c8e1d4-3a57-402d-bad9-561e43aedaf7")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Esito WS", WSResponseContent.Substring(0, System.Math.Min(WSResponseContent.Length,300)),
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("63ba77b9-5fce-40b3-9242-d5788e9b6d3b")]
                    catch (Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException validationExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0fb7ecc3-b650-418b-81f9-64409e811c21")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(validationExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f9dff920-3e0c-4fd1-aaee-9f2c59bed372")]
                    catch (N2VenditaCaloreOut.ImportazioneRicavi.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e4ec0ce5-366b-4071-b53e-82425cc79b4f")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("80197aab-d3d9-4029-b553-a257099b9410")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("51af0154-ae8f-4c3b-a51c-9a6358598796")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f99e5e75-d120-4c97-afa4-09c463c907f4")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f9bd94ca-0814-4d63-a9ec-f62f07cb7968")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cda7a03d-e13c-45c7-9f34-afbfce558e2a")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0e5bc9e-2067-4cc4-9421-19eb9b86a76f")]
                    construct ACSUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9c483bac-3be0-40e6-9fe3-57d335939a6f")]
                        transform (ACSUpdateStatusRequest.parameters) = A2A.EAI.INT_ACS.Messaging.Maps.VenditaCalore.ACSVenditaCaloreMilanoPollingToACSUpdateStatus (ACSVenditaCaloreMilano.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b4ac797-7d9e-45c3-8a88-dd9396e7f9a1")]
                        ACSUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        ACSUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        ACSUpdateStatusRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("dc427616-2ee2-42cd-af65-4105bc43d72c")]
                    send (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc2a7eba-fa28-46be-9457-794acbb9db31")]
                    receive (ACSUpdateStatusOut.ACSUpdateStatus, ACSUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c1b4ac79-49bd-4114-9873-03bdda8e89c7")]
                    sendNotification = ACSUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fb1b7164-c416-4404-a1ed-0a5b704c8d4e")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5df6d87-7537-42b7-ac1f-cd3b97f2a73d")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7d2a288a-f48b-430f-b3c2-19a6db3d8dfd")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("afcd3375-d37c-4501-ab21-b460b56b7143")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("003a7bd0-f2d3-4888-bb79-1bf6a6bf08f4")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("7cfa9a67-4aeb-4d99-82dd-5b51f643252f")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1dba8ad6-2679-4ead-963d-054199152587")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b4f40d5f-7209-4c1f-9274-a9a971e185f7")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ACS.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ACS.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameters.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e2e5aa76-5531-42d6-a199-bcb0738a9b0a")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("14a44be4-d477-4b2e-a742-4558564f1fd0")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ACS.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

