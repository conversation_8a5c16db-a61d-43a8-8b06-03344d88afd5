﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace A2A.EAI.INT_ACS.Services
{
    [Serializable]
    public class ProcessServices
    {
        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_ACS";

        /// <summary> Activity Name (for BAM purpose)</summary>
        public const string ActivityNameStaging = "ACS Staging";


        public const string ActivityNameProcess = "ACS Process";
        /// <summary>
        /// Flowdescription
        /// </summary>
        public const string FlowDescriptionCO2EmesseUnaretiStaging = "CO2 Emesse Unareti Staging";
        public const string FlowDescriptionCO2EmesseRetragasStaging = "CO2 Emesse Retragas Staging";

        public const string FlowDescriptionCO2EmesseUnaretiProcess = "CO2 Emesse Unareti Process";
        public const string FlowDescriptionCO2EmesseRetragasProcess = "CO2 Emesse Retragas Process";

        public const string FlowDescriptionVenditaCaloreMilanoStaging = "Vendita Calore Milano Staging";
        public const string FlowDescriptionVenditaCaloreMilanoProcess = "Vendita Calore Milano Process";

        public const string FlowDescriptionVenditaCaloreBresciaStaging = "Vendita Calore Brescia Staging";
        public const string FlowDescriptionVenditaCaloreBresciaProcess = "Vendita Calore Brescia Process";


    }
}
