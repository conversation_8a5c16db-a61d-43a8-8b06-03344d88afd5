﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21107</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{07808627-97ED-48B7-9FB4-7E15D342D2C4}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_AMBIENTE.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_AMBIENTE.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'INT_AMBIENTE|AnyCPU'">
    <OutputPath>bin\INT_AMBIENTE\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisLogFile>bin\Release\A2A.EAI.INT_AMBIENTE.Messaging.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\Microsoft BizTalk Server 2013\Pipeline Components\Microsys.EAI.Framework.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\schDatiLaboratorioCheckTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schDatiLaboratorioInsertTypedProcedure.bindinginfo.xml" />
    <Map Include="Maps\mapDatiLaboratorioToCheck.btm">
      <TypeName>mapDatiLaboratorioToCheck</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapDatiLaboratorioToInsert.btm">
      <TypeName>mapDatiLaboratorioToInsert</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\rppDatiLaboratorio.btp">
      <TypeName>rppDatiLaboratorio</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\DatiLaboratorio\schDatiLaboratorio.xsd">
      <TypeName>schDatiLaboratorio</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DatiLaboratorio\schDatiLaboratorioInsertTypedProcedure.Type.xsd">
      <TypeName>schDatiLaboratorioInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DatiLaboratorio\schDatiLaboratorioInsertTypedProcedure.Table.xsd">
      <TypeName>schDatiLaboratorioInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DatiLaboratorio\schDatiLaboratorioInsertTypedProcedure.xsd">
      <TypeName>schDatiLaboratorioInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DatiLaboratorio\schDatiLaboratorioCheckTypedProcedure.xsd">
      <TypeName>schDatiLaboratorioCheckTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\A2A.EAI.INT_AMBIENTE.Services\A2A.EAI.INT_AMBIENTE.Services.csproj">
      <Project>{85448cc7-2f78-45e1-bdba-2aaa0f208840}</Project>
      <Name>A2A.EAI.INT_AMBIENTE.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>