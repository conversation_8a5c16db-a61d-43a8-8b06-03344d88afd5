﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="d3a3f706-653e-4898-bdab-b7a06e1043b2" LowerBound="1.1" HigherBound="282.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_AMBIENTE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="80beced7-e50c-4e79-9b7c-2cca32984c57" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typDatiLaboratorio" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="22b30872-917e-42b2-a4df-b9fe998cd186" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8632fe38-8a31-470b-bdca-a36778d9fb0a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.86">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorio" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="bd51c9ef-e02a-4368-832f-2393a46982b5" ParentLink="Module_PortType" LowerBound="15.1" HigherBound="22.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typNbdoDatiLaboratorio" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="b8edbb33-4682-44d4-90b8-3a98ba9769c8" ParentLink="PortType_OperationDeclaration" LowerBound="17.1" HigherBound="21.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="DatiLaboratorioInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="96c098c9-9530-43c7-97b4-b2f4d0ac6821" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="19.13" HigherBound="19.128">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsert" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="bacb9533-bdb6-44b7-9355-eafa15a5ce0b" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="19.130" HigherBound="19.253">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsertResponse" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="646dcb37-0e09-4db9-abd3-2920308c4ae6" ParentLink="Module_PortType" LowerBound="22.1" HigherBound="29.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typNbdoDatiLaboratorioCheck" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="99c98c23-55b5-4613-9732-046ccd7613fa" ParentLink="PortType_OperationDeclaration" LowerBound="24.1" HigherBound="28.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="DatiLaboratorioCheck" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="bce8c5ae-9e2e-47d9-b23f-1cc48a800ed8" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="26.13" HigherBound="26.126">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheck" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="9cb49d42-e775-4268-9bdc-5957230b69cc" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="26.128" HigherBound="26.249">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheckResponse" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="e58e047c-d8f2-4a32-86e1-53a78f3141d5" ParentLink="Module_PortType" LowerBound="29.1" HigherBound="36.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typNotification" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="40f7b4ee-a5c4-4640-a649-7e29de5322db" ParentLink="PortType_OperationDeclaration" LowerBound="31.1" HigherBound="35.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="66270eb5-c037-4cef-be19-6ca1cac3da65" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="33.13" HigherBound="33.56">
                    <om:Property Name="Ref" Value="Microsys.EAI.Framework.Schemas.Notification" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="44b609d7-b218-4ff0-ba95-84f7a25c8680" ParentLink="Module_ServiceDeclaration" LowerBound="36.1" HigherBound="281.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcDatiLaboratorio" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="faf10d5b-1677-4925-8d5c-e395188d5d13" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c5d9ee98-15a5-4e36-b274-c91d07793d5d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f44cf43e-1ec5-4375-af24-8c75ab9fb0ef" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="05dee5a8-59e7-4f65-9b87-64d9767346c3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1aec6902-e389-4852-91fc-cf3e260a8ac0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e0d8c1cc-5f9b-45bb-b59f-ceb5d23cd2f7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7a10c437-0c24-4a46-a677-74c52b255fab" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4c2bb8c2-64f6-4c44-bf65-bcc7e5b5ac1c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="34d4d4c1-4aea-417d-9dd2-66a99592aa7a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessServices" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="commonServices" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f7f55fba-e356-46a9-afbf-d5068a96cdf6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="45910bd6-3307-4e80-a9ea-326545c06acd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="responseInsert" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="56465f16-af78-4736-a4c2-c861b6c51553" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d415061a-f071-4778-ae4c-ed1d0f5eedf5" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorio" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDatiLaboratorioInput" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c087d56f-4d1b-4313-a095-c9786af0a272" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsert" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDatiLaboratorioInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="585901a6-fff8-48c0-900b-6b233391995b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsertResponse" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDatiLaboratorioInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="cc6b5088-5c81-4b00-b2fc-3fb562cb0527" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheck" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDatiLaboratorioCheckRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7fbbbaf1-d9d5-4e46-98ce-542f441383c3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheckResponse" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDatiLaboratorioCheckResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="c2493b80-c489-4e51-9ee7-9b948bbf6bda" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="d4f59b86-c5f8-49f2-a461-7eb3a376f121" ParentLink="ServiceBody_Statement" LowerBound="68.1" HigherBound="76.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptDatiLaboratorio" />
                    <om:Property Name="MessageName" Value="msgDatiLaboratorioInput" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e11b4c1a-582f-4488-a078-5f43c8b3b58a" ParentLink="ServiceBody_Statement" LowerBound="76.1" HigherBound="94.1">
                    <om:Property Name="Expression" Value="commonServices = new Microsys.EAI.Framework.Services.ProcessServices();&#xD;&#xA;&#xD;&#xA;activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgDatiLaboratorioInput);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgDatiLaboratorioInput);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="439ba31b-d81d-4776-a454-80f3d986e8f0" ParentLink="ServiceBody_Statement" LowerBound="94.1" HigherBound="103.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="3e696116-69b3-4a97-b603-b08e0fc6884d" ParentLink="ServiceBody_Statement" LowerBound="103.1" HigherBound="211.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Send To Nbdo PMC" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Decision" OID="24df690d-9b7d-4d01-87c3-b12d42b030a4" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="161.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Is Excluded Artefact For PMC" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="e34f9d15-fce0-4a5a-a492-1320a7cc7307" ParentLink="ReallyComplexStatement_Branch" LowerBound="109.21" HigherBound="116.1">
                            <om:Property Name="Expression" Value="!A2A.EAI.INT_AMBIENTE.Services.ProcessServices.IsToProcess(msgDatiLaboratorioInput.IdCliente.idCliente)&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="8ede9298-3fe5-47d6-9d11-a3433a1fe19e" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="115.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Esito N2&quot;, &quot;Excluded from PMC&quot;,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="7f211053-3831-498b-8837-f46b14699e1c" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="4815e852-1c83-4a5e-a121-630d294e6606" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="129.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Msg for Nbdo" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="f9c620de-8bec-4db6-a0e4-cc13f3116e60" ParentLink="Construct_MessageRef" LowerBound="119.35" HigherBound="119.66">
                                    <om:Property Name="Ref" Value="msgDatiLaboratorioInsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="6652c3fe-0330-4fb8-a824-7f6941a9ee3b" ParentLink="Construct_MessageRef" LowerBound="119.68" HigherBound="119.98">
                                    <om:Property Name="Ref" Value="msgDatiLaboratorioCheckRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="26c67f76-1202-45eb-9061-1880f0b65c87" ParentLink="ComplexStatement_Statement" LowerBound="121.1" HigherBound="123.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_AMBIENTE.Messaging.Maps.mapDatiLaboratorioToCheck" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Check" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="5124779c-08f5-4972-b79f-7fb140752c34" ParentLink="Transform_InputMessagePartRef" LowerBound="122.137" HigherBound="122.160">
                                        <om:Property Name="MessageRef" Value="msgDatiLaboratorioInput" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_5" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="dade16c8-f512-4160-9bff-1941d13734d5" ParentLink="Transform_OutputMessagePartRef" LowerBound="122.40" HigherBound="122.70">
                                        <om:Property Name="MessageRef" Value="msgDatiLaboratorioCheckRequest" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_6" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Transform" OID="335a54d8-e294-4037-becd-18bb25a770de" ParentLink="ComplexStatement_Statement" LowerBound="123.1" HigherBound="125.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_AMBIENTE.Messaging.Maps.mapDatiLaboratorioToInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Insert" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="4c60d965-5cf9-4f35-98cf-f7d7560d35e2" ParentLink="Transform_InputMessagePartRef" LowerBound="124.139" HigherBound="124.162">
                                        <om:Property Name="MessageRef" Value="msgDatiLaboratorioInput" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="5ba7f5af-9744-4baf-8044-fef59774c4d6" ParentLink="Transform_OutputMessagePartRef" LowerBound="124.40" HigherBound="124.71">
                                        <om:Property Name="MessageRef" Value="msgDatiLaboratorioInsertRequest" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="d5a50283-4bbe-4c44-af43-9098c4473ec6" ParentLink="ComplexStatement_Statement" LowerBound="125.1" HigherBound="128.1">
                                    <om:Property Name="Expression" Value="msgDatiLaboratorioInsertRequest.fileName = System.IO.Path.GetFileName(originalFileName);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Assign Filename and Impianto Id" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="2524f882-f577-4514-b060-20c01e106259" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="131.1">
                                <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Take Time" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Send" OID="693df335-2a94-4551-8e9d-5eb98cbe3e28" ParentLink="ComplexStatement_Statement" LowerBound="131.1" HigherBound="133.1">
                                <om:Property Name="PortName" Value="sptDatiLaboratorioCheck" />
                                <om:Property Name="MessageName" Value="msgDatiLaboratorioCheckRequest" />
                                <om:Property Name="OperationName" Value="DatiLaboratorioCheck" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Check" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="71ae7c76-58e4-4b58-944d-c906d2b8c098" ParentLink="ComplexStatement_Statement" LowerBound="133.1" HigherBound="135.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptDatiLaboratorioCheck" />
                                <om:Property Name="MessageName" Value="msgDatiLaboratorioCheckResponse" />
                                <om:Property Name="OperationName" Value="DatiLaboratorioCheck" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive Check" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Send" OID="e011087a-8b15-48d1-9ff8-6aca98ade62a" ParentLink="ComplexStatement_Statement" LowerBound="135.1" HigherBound="137.1">
                                <om:Property Name="PortName" Value="sptDatiLaboratorioInsert" />
                                <om:Property Name="MessageName" Value="msgDatiLaboratorioInsertRequest" />
                                <om:Property Name="OperationName" Value="DatiLaboratorioInsert" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send To Nbdo" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="aecc9ca9-1317-47d4-ae34-48c4b4c9c5e2" ParentLink="ComplexStatement_Statement" LowerBound="137.1" HigherBound="139.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptDatiLaboratorioInsert" />
                                <om:Property Name="MessageName" Value="msgDatiLaboratorioInsertResponse" />
                                <om:Property Name="OperationName" Value="DatiLaboratorioInsert" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive from Ndbo" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="90f16c06-650b-46e6-852f-0c8bff2e63b5" ParentLink="ComplexStatement_Statement" LowerBound="139.1" HigherBound="147.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Esito N2&quot;, responseInsert,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Decision" OID="92b4a9f6-c9c9-4237-9c71-0e585b83f35a" ParentLink="ComplexStatement_Statement" LowerBound="147.1" HigherBound="160.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="There are messages" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="4ee2ac75-d5ed-4fa5-8413-839254f2f7ca" ParentLink="ReallyComplexStatement_Branch" LowerBound="148.25" HigherBound="155.1">
                                    <om:Property Name="Expression" Value="msgDatiLaboratorioInsertResponse.StoredProcedureResultSet0.StoredProcedureResultSet0.returnMessage != &quot;&quot;" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Yes" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="f00e8d1a-4c0a-4484-9197-8464a15f8d2b" ParentLink="ComplexStatement_Statement" LowerBound="150.1" HigherBound="154.1">
                                        <om:Property Name="Expression" Value="responseInsert = msgDatiLaboratorioInsertResponse.StoredProcedureResultSet0.StoredProcedureResultSet0.returnMessage;&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Response Message" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="6bbe21e2-85bb-4074-a675-c0f7098a39e6" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="VariableAssignment" OID="9a8d89b9-13c1-4a58-842c-d7a9a88a0e85" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="159.1">
                                        <om:Property Name="Expression" Value="responseInsert = &quot;Operazione eseguita senza segnalazioni&quot;;&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Response Message" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="a56deb4f-7bc0-48ed-9831-13c0ce1cc81f" ParentLink="Scope_Catch" LowerBound="164.1" HigherBound="179.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="25ff3ec0-c738-4522-aae4-a19c1094e4e6" ParentLink="Catch_Statement" LowerBound="167.1" HigherBound="178.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'inserimento in Nbdo. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="56b982d0-f40d-44ce-a6e6-ab64eb8cd16c" ParentLink="Scope_Catch" LowerBound="179.1" HigherBound="194.1">
                        <om:Property Name="ExceptionName" Value="tranfExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.TransformationFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Transformation Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="70b67095-8f4a-47a7-9ee5-0f91a2bc28d9" ParentLink="Catch_Statement" LowerBound="182.1" HigherBound="193.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la preparazione della richiesta Nbdo. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(tranfExc));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="abf1348d-c268-420b-8b85-48ea86e2e44d" ParentLink="Scope_Catch" LowerBound="194.1" HigherBound="209.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="a2019ab0-eddd-4e70-875b-88bf3c350638" ParentLink="Catch_Statement" LowerBound="197.1" HigherBound="208.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'inserimento in Nbdo.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="003cf23d-c1f4-4131-9b9c-0a3bbf0eab25" ParentLink="ServiceBody_Statement" LowerBound="211.1" HigherBound="273.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="4514c2d1-9234-4af7-b734-d4d2bb1e448c" ParentLink="ReallyComplexStatement_Branch" LowerBound="212.13" HigherBound="215.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="86adf3ea-ed30-4e0e-8e6f-1438fcf02e8d" ParentLink="ReallyComplexStatement_Branch" LowerBound="215.18" HigherBound="244.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Warning" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="60e586bb-f857-4eff-a04a-3a7c8537589d" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="243.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="eaad5783-8ed6-4f7e-beed-db4094d15736" ParentLink="ComplexStatement_Statement" LowerBound="222.1" HigherBound="239.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="e6c3bf74-8f93-4f2b-850e-c760df090a38" ParentLink="Construct_MessageRef" LowerBound="223.35" HigherBound="223.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="bcb17a6a-90c4-407a-8cd3-524b47b28d0e" ParentLink="ComplexStatement_Statement" LowerBound="225.1" HigherBound="238.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.messageText = responseInsert;&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;msgNotification.sourceIdentifier = &quot;n.a.&quot;;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="1930da81-2997-4730-a35b-356743bb08f7" ParentLink="ComplexStatement_Statement" LowerBound="239.1" HigherBound="241.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="2a1b1c97-028b-43a5-9f39-115f7fa2b78b" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="7e96d035-313a-4ae5-9019-f7a68e57ea7f" ParentLink="ComplexStatement_Statement" LowerBound="246.1" HigherBound="272.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="6039ca2f-a0cf-4b12-8612-b01ba9290d14" ParentLink="ComplexStatement_Statement" LowerBound="251.1" HigherBound="268.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="9f875c01-56e6-4a3c-aec9-988d97655bec" ParentLink="Construct_MessageRef" LowerBound="252.35" HigherBound="252.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="6454b51c-3456-4b5b-8472-116959e52381" ParentLink="ComplexStatement_Statement" LowerBound="254.1" HigherBound="267.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="be6d9ec8-9b2e-4376-9795-54f68a7df9a3" ParentLink="ComplexStatement_Statement" LowerBound="268.1" HigherBound="270.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="47bfe693-4b14-4668-8e42-ee27ad002be9" ParentLink="ServiceBody_Statement" LowerBound="273.1" HigherBound="279.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="85f22455-dcf9-4bcd-840a-5387678feeed" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="39.1" HigherBound="41.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Processes.typDatiLaboratorio" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptDatiLaboratorio" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="765f2729-b125-4c25-b816-a507720de190" ParentLink="PortDeclaration_CLRAttribute" LowerBound="39.1" HigherBound="40.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b1b7492e-709a-4f00-82b1-2c5f51a17c91" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="41.1" HigherBound="43.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="150" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="428ead83-d573-48e9-99d1-b5c55abd2b9b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="41.1" HigherBound="42.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="03ea20d9-ec53-4a18-a489-1956de7ed8f0" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="43.1" HigherBound="46.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="59" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Processes.typNbdoDatiLaboratorioCheck" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptDatiLaboratorioCheck" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="4f9aaad5-cb50-4dc3-a2f5-f857e8632569" ParentLink="PortDeclaration_CLRAttribute" LowerBound="43.1" HigherBound="44.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7550dd06-e8fc-44d2-b8d9-55e9b81b7fe7" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="46.1" HigherBound="49.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="64" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_AMBIENTE.Processes.typNbdoDatiLaboratorio" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptDatiLaboratorioInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="fe7a694f-f5f5-40df-90d5-4cb72d7dd75f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="46.1" HigherBound="47.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="f7909064-832c-43f7-9323-b32c843bb0a4" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgFault" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="182f2870-9be9-4d14-bc12-200ee6110619" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="BTS.soap_envelope_1__1.Fault" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultString" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_AMBIENTE.Processes
{
    public messagetype msgFault
    {
        body BTS.soap_envelope_1__1.Fault faultString;
    };
    internal porttype typDatiLaboratorio
    {
        oneway Receive
        {
            A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorio
        };
    };
    internal porttype typNbdoDatiLaboratorio
    {
        requestresponse DatiLaboratorioInsert
        {
            A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsert, A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsertResponse
        };
    };
    internal porttype typNbdoDatiLaboratorioCheck
    {
        requestresponse DatiLaboratorioCheck
        {
            A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheck, A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheckResponse
        };
    };
    internal porttype typNotification
    {
        oneway Send
        {
            Microsys.EAI.Framework.Schemas.Notification
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcDatiLaboratorio
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typDatiLaboratorio rptDatiLaboratorio;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNbdoDatiLaboratorioCheck sptDatiLaboratorioCheck;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNbdoDatiLaboratorio sptDatiLaboratorioInsert;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorio msgDatiLaboratorioInput;
        message A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsert msgDatiLaboratorioInsertRequest;
        message A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioInsertTypedProcedure.DatiLaboratorioInsertResponse msgDatiLaboratorioInsertResponse;
        message A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheck msgDatiLaboratorioCheckRequest;
        message A2A.EAI.INT_AMBIENTE.Messaging.Schemas.DatiLaboratorio.schDatiLaboratorioCheckTypedProcedure.DatiLaboratorioCheckResponse msgDatiLaboratorioCheckResponse;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.String archiveFilePath;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        Microsys.EAI.Framework.Services.ProcessServices commonServices;
        System.String activityInstanceId;
        System.String responseInsert;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d4f59b86-c5f8-49f2-a461-7eb3a376f121")]
            activate receive (rptDatiLaboratorio.Receive, msgDatiLaboratorioInput);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            commonServices = new Microsys.EAI.Framework.Services.ProcessServices();
            activityInstanceId = "";
            responseInsert = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e11b4c1a-582f-4488-a078-5f43c8b3b58a")]
            commonServices = new Microsys.EAI.Framework.Services.ProcessServices();
            
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgDatiLaboratorioInput);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgDatiLaboratorioInput);
            
            
            
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("439ba31b-d81d-4776-a454-80f3d986e8f0")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "instanceId", activityInstanceId,
            "Flusso", A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3e696116-69b3-4a97-b603-b08e0fc6884d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("24df690d-9b7d-4d01-87c3-b12d42b030a4")]
                    if (!A2A.EAI.INT_AMBIENTE.Services.ProcessServices.IsToProcess(msgDatiLaboratorioInput.IdCliente.idCliente))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8ede9298-3fe5-47d6-9d11-a3433a1fe19e")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,
                        "Esito N2", "Excluded from PMC",
                        "Inviato a N2", "NO");
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4815e852-1c83-4a5e-a121-630d294e6606")]
                        construct msgDatiLaboratorioInsertRequest, msgDatiLaboratorioCheckRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("26c67f76-1202-45eb-9061-1880f0b65c87")]
                            transform (msgDatiLaboratorioCheckRequest) = A2A.EAI.INT_AMBIENTE.Messaging.Maps.mapDatiLaboratorioToCheck (msgDatiLaboratorioInput);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("335a54d8-e294-4037-becd-18bb25a770de")]
                            transform (msgDatiLaboratorioInsertRequest) = A2A.EAI.INT_AMBIENTE.Messaging.Maps.mapDatiLaboratorioToInsert (msgDatiLaboratorioInput);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d5a50283-4bbe-4c44-af43-9098c4473ec6")]
                            msgDatiLaboratorioInsertRequest.fileName = System.IO.Path.GetFileName(originalFileName);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2524f882-f577-4514-b060-20c01e106259")]
                        dataInizioDb = System.DateTimeOffset.Now;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("693df335-2a94-4551-8e9d-5eb98cbe3e28")]
                        send (sptDatiLaboratorioCheck.DatiLaboratorioCheck, msgDatiLaboratorioCheckRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("71ae7c76-58e4-4b58-944d-c906d2b8c098")]
                        receive (sptDatiLaboratorioCheck.DatiLaboratorioCheck, msgDatiLaboratorioCheckResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e011087a-8b15-48d1-9ff8-6aca98ade62a")]
                        send (sptDatiLaboratorioInsert.DatiLaboratorioInsert, msgDatiLaboratorioInsertRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("aecc9ca9-1317-47d4-ae34-48c4b4c9c5e2")]
                        receive (sptDatiLaboratorioInsert.DatiLaboratorioInsert, msgDatiLaboratorioInsertResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("90f16c06-650b-46e6-852f-0c8bff2e63b5")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,
                        "Esito N2", responseInsert,
                        "Inviato a N2", "SI",
                        "Data Inizio DB", dataInizioDb,
                        "Data Fine DB", System.DateTimeOffset.Now,
                        "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("92b4a9f6-c9c9-4237-9c71-0e585b83f35a")]
                        if (msgDatiLaboratorioInsertResponse.StoredProcedureResultSet0.StoredProcedureResultSet0.returnMessage != "")
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f00e8d1a-4c0a-4484-9197-8464a15f8d2b")]
                            responseInsert = msgDatiLaboratorioInsertResponse.StoredProcedureResultSet0.StoredProcedureResultSet0.returnMessage;
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9a8d89b9-13c1-4a58-842c-d7a9a88a0e85")]
                            responseInsert = "Operazione eseguita senza segnalazioni";
                        }
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a56deb4f-7bc0-48ed-9831-13c0ce1cc81f")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("25ff3ec0-c738-4522-aae4-a19c1094e4e6")]
                        errorMessage.Append("Errore durante l'inserimento in Nbdo. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("56b982d0-f40d-44ce-a6e6-ab64eb8cd16c")]
                    catch (Microsoft.XLANGs.BaseTypes.TransformationFailureException tranfExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("70b67095-8f4a-47a7-9ee5-0f91a2bc28d9")]
                        errorMessage.Append("Errore durante la preparazione della richiesta Nbdo. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(tranfExc));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("abf1348d-c268-420b-8b85-48ea86e2e44d")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a2019ab0-eddd-4e70-875b-88bf3c350638")]
                        errorMessage.Append("Errore durante l'inserimento in Nbdo.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("003cf23d-c1f4-4131-9b9c-0a3bbf0eab25")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("60e586bb-f857-4eff-a04a-3a7c8537589d")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("eaad5783-8ed6-4f7e-beed-db4094d15736")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bcb17a6a-90c4-407a-8cd3-524b47b28d0e")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.messageText = responseInsert;
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                            msgNotification.sourceIdentifier = "n.a.";
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1930da81-2997-4730-a35b-356743bb08f7")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("7e96d035-313a-4ae5-9019-f7a68e57ea7f")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6039ca2f-a0cf-4b12-8612-b01ba9290d14")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6454b51c-3456-4b5b-8472-116959e52381")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_AMBIENTE.Services.ProcessServices.FlowDescriptionDatiLaboratorio;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("be6d9ec8-9b2e-4376-9795-54f68a7df9a3")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("47bfe693-4b14-4668-8e42-ee27ad002be9")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_AMBIENTE.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));
        }
    }
}

