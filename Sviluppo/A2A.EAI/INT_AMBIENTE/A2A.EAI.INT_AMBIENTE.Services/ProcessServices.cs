﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Microsys.EAI.Framework.Azure.Services;

namespace A2A.EAI.INT_AMBIENTE.Services
{
    [Serializable]
    public class ProcessServices
    {

        /// <summary>NBDO Settlement Activity Name (for BAM purpose)</summary>
        public const string ActivityName = "AMBIENTE";

        /// <summary>NBDO Settlement Activity Name (for BAM purpose)</summary>
        public const string FlowGroup = "INT_AMBIENTE";

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Dati laboratorio Flow Description</summary>
        public const string FlowDescriptionDatiLaboratorio = "Dati laboratorio";

        /// <summary>
        /// Returns if specified Artefact is escluded for sending to PMC
        /// </summary>
        /// <param name="artefact"></param>
        /// <returns></returns>
        public static bool IsToProcess(string artefact)
        {

            bool returnValue = false;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                returnValue = artefact.ToUpper().Contains("VASCA TRAPPOLA");

                LoggingServices.TraceDebugInfo("UP: {0} - {1}", artefact, returnValue.ToString());

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        public string FormatDate(string inputDate)
        {

            string returnValue = string.Empty;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                DateTime date = DateTime.ParseExact(inputDate, "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture);

                returnValue = date.ToString("yyyy-MM-dd");
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw new Exception(String.Format("#*Data Emissione non valida '{0}'. Il formato atteso è 'dd/MM/yyyy'.*#", inputDate));
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        
        }
    }
}