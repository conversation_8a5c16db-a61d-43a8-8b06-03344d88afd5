﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{9063F894-294D-479E-89D9-55313BADD660}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_BDE.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_BDE.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="A2A.EAI.INT_BDE.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=3e560864e91bc2e0, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\schEaiBdeGetEmailAddrTypedProcedure.xsd">
      <TypeName>schEaiBdeGetEmailAddrTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schEaiBdeSrTypedProcedure.xsd">
      <TypeName>schEaiBdeSrTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schEaiBdeRcTypedProcedure.xsd">
      <TypeName>schEaiBdeRcTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schEaiBdeLbTypedProcedure.xsd">
      <TypeName>schEaiBdeLbTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schEaiBdeCbTypedProcedure.xsd">
      <TypeName>schEaiBdeCbTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schEaiBdeNotification.xsd">
      <TypeName>schEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schN2BdeCalcoloPVM.xsd">
      <TypeName>schN2BdeCalcoloPVM</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schN2BdeValidazione.xsd">
      <TypeName>schN2BdeValidazione</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schN2BdeAnnullamento.xsd">
      <TypeName>schN2BdeAnnullamento</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\schTernaBde.xsd">
      <TypeName>schTernaBde</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="PropertySchema.xsd">
      <TypeName>PropertySchema</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Shared\A2A.EAI.Common.Shared.btproj">
      <Project>{fe830fb0-b65e-459d-b592-53001be3d7b6}</Project>
      <Name>A2A.EAI.Common.Shared</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_BDE.Services\A2A.EAI.INT_BDE.Services.csproj">
      <Project>{d56ffb78-67be-4ec6-bfc9-4fa3187911a7}</Project>
      <Name>A2A.EAI.INT_BDE.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipeline\rppTernaBde.btp">
      <TypeName>rppTernaBde</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Pipeline</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipeline\sppTernaBde.btp">
      <TypeName>sppTernaBde</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Pipeline</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\schEaiBdeCbTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schEaiBdeLbTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schEaiBdeRcTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schEaiBdeSrTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schEaiBdeGetEmailAddrTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\schN2WebServices.bindings.xml" />
    <Map Include="Maps\mapEaiBdeNotificationToMailSend.btm">
      <SubType>Task</SubType>
      <TypeName>mapEaiBdeNotificationToMailSend</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
    </Map>
    <Pipeline Include="Pipeline\sppMailBde.btp">
      <TypeName>sppMailBde</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Pipeline</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\mapTernaBdeCbToEaiBdeCb.btm">
      <TypeName>mapTernaBdeCbToEaiBdeCb</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Designer</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeLbToEaiBdeLb.btm">
      <TypeName>mapTernaBdeLbToEaiBdeLb</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeRcToEaiBdeRc.btm">
      <TypeName>mapTernaBdeRcToEaiBdeRc</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeSrToEaiBdeSr.btm">
      <TypeName>mapTernaBdeSrToEaiBdeSr</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapEaiBdeCbToN2BdeCalcoloPVM.btm">
      <TypeName>mapEaiBdeCbToN2BdeCalcoloPVM</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapEaiBdeLbToN2BdeValidazione.btm">
      <TypeName>mapEaiBdeLbToN2BdeValidazione</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapEaiBdeNotificationToEaiBdeGetEmailAddr.btm">
      <TypeName>mapEaiBdeNotificationToEaiBdeGetEmailAddr</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapEaiBdeRcToN2BdeAnnullamento.btm">
      <TypeName>mapEaiBdeRcToN2BdeAnnullamento</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapEaiBdeSrToN2BdeValidazione.btm">
      <TypeName>mapEaiBdeSrToN2BdeValidazione</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeCbToEaiBdeNotification.btm">
      <TypeName>mapTernaBdeCbToEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeLbToEaiBdeNotification.btm">
      <TypeName>mapTernaBdeLbToEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeRcToEaiBdeNotification.btm">
      <TypeName>mapTernaBdeRcToEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeSrToEaiBdeNotification.btm">
      <TypeName>mapTernaBdeSrToEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\mapTernaBdeVqToEaiBdeNotification.btm">
      <TypeName>mapTernaBdeVqToEaiBdeNotification</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipeline\rppTernaBdeRcRetry.btp">
      <TypeName>rppTernaBdeRcRetry</TypeName>
      <Namespace>A2A.EAI.INT_BDE.Messaging.Pipeline</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>