<?xml version="1.0" encoding="utf-8"?>
<BindingInfo xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" Assembly="Microsoft.BizTalk.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Version="*******" BindingStatus="FullyBound">
	<SendPortCollection>
		<SendPort Name="sptN2BdeValidazione" IsStatic="true" IsTwoWay="true" BindingOption="1" AnalyticsEnabled="false">
			<Description>service "ValidazioneBDE" port "ValidazioneBDESoap"</Description>
			<TransmitPipeline Name="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="2" TrackingOption="None" Description="" />
			<PrimaryTransport>
				<Address>http://dctsvw034/nbdows/validazionebde.asmx</Address>
				<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				<TransportTypeData>&lt;CustomProps&gt;&lt;InboundNodeEncoding vt="8"&gt;Xml&lt;/InboundNodeEncoding&gt;&lt;MessageEncoding vt="8"&gt;Text&lt;/MessageEncoding&gt;&lt;ServiceCertificate vt="8" /&gt;&lt;InboundBodyPathExpression vt="8" /&gt;&lt;OutboundBodyLocation vt="8"&gt;UseBodyElement&lt;/OutboundBodyLocation&gt;&lt;StaticAction vt="8"&gt;&amp;lt;BtsActionMapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&amp;gt;
  &amp;lt;Operation Name="ValidaBDE" Action="http://nbdo/nbdows/ValidaBDE" /&amp;gt;
&amp;lt;/BtsActionMapping&amp;gt;&lt;/StaticAction&gt;&lt;InboundBodyLocation vt="8"&gt;UseBodyElement&lt;/InboundBodyLocation&gt;&lt;ProxyAddress vt="8" /&gt;&lt;MaxReceivedMessageSize vt="3"&gt;65536&lt;/MaxReceivedMessageSize&gt;&lt;ClientCertificate vt="8" /&gt;&lt;AlgorithmSuite vt="8"&gt;Basic256&lt;/AlgorithmSuite&gt;&lt;ProxyUserName vt="8" /&gt;&lt;OutboundXmlTemplate vt="8"&gt;&amp;lt;bts-msg-body xmlns="http://www.microsoft.com/schemas/bts2007" encoding="xml"/&amp;gt;&lt;/OutboundXmlTemplate&gt;&lt;TextEncoding vt="8"&gt;utf-8&lt;/TextEncoding&gt;&lt;PropagateFaultMessage vt="11"&gt;-1&lt;/PropagateFaultMessage&gt;&lt;SecurityMode vt="8"&gt;None&lt;/SecurityMode&gt;&lt;ProxyToUse vt="8"&gt;Default&lt;/ProxyToUse&gt;&lt;TransportClientCredentialType vt="8"&gt;None&lt;/TransportClientCredentialType&gt;&lt;OpenTimeout vt="8"&gt;00:02:00&lt;/OpenTimeout&gt;&lt;UseAcsAuthentication vt="11"&gt;0&lt;/UseAcsAuthentication&gt;&lt;MessageClientCredentialType vt="8"&gt;UserName&lt;/MessageClientCredentialType&gt;&lt;UseSSO vt="11"&gt;0&lt;/UseSSO&gt;&lt;UseSasAuthentication vt="11"&gt;0&lt;/UseSasAuthentication&gt;&lt;CloseTimeout vt="8"&gt;00:01:00&lt;/CloseTimeout&gt;&lt;SendTimeout vt="8"&gt;00:10:00&lt;/SendTimeout&gt;&lt;/CustomProps&gt;</TransportTypeData>
				<RetryCount>1</RetryCount>
				<RetryInterval>1</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T00:00:00</FromTime>
				<ToTime>2000-01-01T23:59:59</ToTime>
				<Primary>true</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler Name="BizTalkServerNbdoSend" HostTrusted="false">
					<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				</SendHandler>
			</PrimaryTransport>
			<SecondaryTransport>
				<Address />
				<RetryCount>3</RetryCount>
				<RetryInterval>5</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T23:00:00</FromTime>
				<ToTime>2000-01-01T22:59:59</ToTime>
				<Primary>false</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			</SecondaryTransport>
			<ReceivePipeline Name="Microsoft.BizTalk.DefaultPipelines.XMLReceive" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.XMLReceive, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="1" TrackingOption="None" Description="" />
			<ReceivePipelineData xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			<Tracking>240</Tracking>
			<Filter />
			<Transforms />
			<InboundTransforms />
			<OrderedDelivery>false</OrderedDelivery>
			<Priority>5</Priority>
			<StopSendingOnFailure>false</StopSendingOnFailure>
			<RouteFailedMessage>true</RouteFailedMessage>
			<ApplicationName>A2A.EAI.INT_BDE</ApplicationName>
		</SendPort>
		<SendPort Name="sptN2BdeAnnullamento" IsStatic="true" IsTwoWay="true" BindingOption="1" AnalyticsEnabled="false">
			<Description>service "AnnullamentoBDE" port "AnnullamentoBDESoap"</Description>
			<TransmitPipeline Name="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="2" TrackingOption="None" Description="" />
			<PrimaryTransport>
				<Address>http://dctsvw034/nbdows/annullamentoBDE.asmx</Address>
				<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				<TransportTypeData>&lt;CustomProps&gt;&lt;InboundNodeEncoding vt="8"&gt;Xml&lt;/InboundNodeEncoding&gt;&lt;MessageEncoding vt="8"&gt;Text&lt;/MessageEncoding&gt;&lt;ServiceCertificate vt="8" /&gt;&lt;InboundBodyPathExpression vt="8" /&gt;&lt;OutboundBodyLocation vt="8"&gt;UseBodyElement&lt;/OutboundBodyLocation&gt;&lt;StaticAction vt="8"&gt;&amp;lt;BtsActionMapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&amp;gt;
  &amp;lt;Operation Name="AnnullaBDE" Action="http://nbdo/nbdows/AnnullaBDE" /&amp;gt;
&amp;lt;/BtsActionMapping&amp;gt;&lt;/StaticAction&gt;&lt;InboundBodyLocation vt="8"&gt;UseBodyElement&lt;/InboundBodyLocation&gt;&lt;ProxyAddress vt="8" /&gt;&lt;MaxReceivedMessageSize vt="3"&gt;65536&lt;/MaxReceivedMessageSize&gt;&lt;ClientCertificate vt="8" /&gt;&lt;AlgorithmSuite vt="8"&gt;Basic256&lt;/AlgorithmSuite&gt;&lt;ProxyUserName vt="8" /&gt;&lt;OutboundXmlTemplate vt="8"&gt;&amp;lt;bts-msg-body xmlns="http://www.microsoft.com/schemas/bts2007" encoding="xml"/&amp;gt;&lt;/OutboundXmlTemplate&gt;&lt;TextEncoding vt="8"&gt;utf-8&lt;/TextEncoding&gt;&lt;PropagateFaultMessage vt="11"&gt;-1&lt;/PropagateFaultMessage&gt;&lt;SecurityMode vt="8"&gt;None&lt;/SecurityMode&gt;&lt;ProxyToUse vt="8"&gt;Default&lt;/ProxyToUse&gt;&lt;TransportClientCredentialType vt="8"&gt;None&lt;/TransportClientCredentialType&gt;&lt;OpenTimeout vt="8"&gt;00:01:00&lt;/OpenTimeout&gt;&lt;UseAcsAuthentication vt="11"&gt;0&lt;/UseAcsAuthentication&gt;&lt;MessageClientCredentialType vt="8"&gt;UserName&lt;/MessageClientCredentialType&gt;&lt;UseSSO vt="11"&gt;0&lt;/UseSSO&gt;&lt;UseSasAuthentication vt="11"&gt;0&lt;/UseSasAuthentication&gt;&lt;CloseTimeout vt="8"&gt;00:01:00&lt;/CloseTimeout&gt;&lt;SendTimeout vt="8"&gt;00:05:00&lt;/SendTimeout&gt;&lt;/CustomProps&gt;</TransportTypeData>
				<RetryCount>0</RetryCount>
				<RetryInterval>0</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T00:00:00</FromTime>
				<ToTime>2000-01-01T23:59:59</ToTime>
				<Primary>true</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler Name="BizTalkServerNbdoSend" HostTrusted="false">
					<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				</SendHandler>
			</PrimaryTransport>
			<SecondaryTransport>
				<Address />
				<RetryCount>3</RetryCount>
				<RetryInterval>5</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T22:00:00</FromTime>
				<ToTime>2000-01-01T21:59:59</ToTime>
				<Primary>false</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			</SecondaryTransport>
			<ReceivePipeline Name="Microsoft.BizTalk.DefaultPipelines.XMLReceive" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.XMLReceive, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="1" TrackingOption="None" Description="" />
			<ReceivePipelineData xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			<Tracking>240</Tracking>
			<Filter />
			<Transforms />
			<InboundTransforms />
			<OrderedDelivery>false</OrderedDelivery>
			<Priority>5</Priority>
			<StopSendingOnFailure>false</StopSendingOnFailure>
			<RouteFailedMessage>true</RouteFailedMessage>
			<ApplicationName>A2A.EAI.INT_BDE</ApplicationName>
		</SendPort>
		<SendPort Name="sptN2BdeCalcoloPvm" IsStatic="true" IsTwoWay="true" BindingOption="1" AnalyticsEnabled="false">
			<Description>service "CalcoloPVM" port "CalcoloPVMSoap"</Description>
			<TransmitPipeline Name="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="2" TrackingOption="None" Description="" />
			<PrimaryTransport>
				<Address>http://dctsvw034/nbdows/calcolopvm.asmx</Address>
				<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				<TransportTypeData>&lt;CustomProps&gt;&lt;InboundNodeEncoding vt="8"&gt;Xml&lt;/InboundNodeEncoding&gt;&lt;MessageEncoding vt="8"&gt;Text&lt;/MessageEncoding&gt;&lt;ServiceCertificate vt="8" /&gt;&lt;InboundBodyPathExpression vt="8" /&gt;&lt;OutboundBodyLocation vt="8"&gt;UseBodyElement&lt;/OutboundBodyLocation&gt;&lt;StaticAction vt="8"&gt;&amp;lt;BtsActionMapping xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"&amp;gt;
  &amp;lt;Operation Name="LanciaTestCalcoloPVMC" Action="http://nbdo/nbdows/LanciaTestCalcoloPVMC" /&amp;gt;
&amp;lt;/BtsActionMapping&amp;gt;&lt;/StaticAction&gt;&lt;InboundBodyLocation vt="8"&gt;UseBodyElement&lt;/InboundBodyLocation&gt;&lt;ProxyAddress vt="8" /&gt;&lt;MaxReceivedMessageSize vt="3"&gt;65536&lt;/MaxReceivedMessageSize&gt;&lt;ClientCertificate vt="8" /&gt;&lt;AlgorithmSuite vt="8"&gt;Basic256&lt;/AlgorithmSuite&gt;&lt;ProxyUserName vt="8" /&gt;&lt;OutboundXmlTemplate vt="8"&gt;&amp;lt;bts-msg-body xmlns="http://www.microsoft.com/schemas/bts2007" encoding="xml"/&amp;gt;&lt;/OutboundXmlTemplate&gt;&lt;TextEncoding vt="8"&gt;utf-8&lt;/TextEncoding&gt;&lt;PropagateFaultMessage vt="11"&gt;-1&lt;/PropagateFaultMessage&gt;&lt;SecurityMode vt="8"&gt;None&lt;/SecurityMode&gt;&lt;ProxyToUse vt="8"&gt;Default&lt;/ProxyToUse&gt;&lt;TransportClientCredentialType vt="8"&gt;None&lt;/TransportClientCredentialType&gt;&lt;OpenTimeout vt="8"&gt;00:10:00&lt;/OpenTimeout&gt;&lt;UseAcsAuthentication vt="11"&gt;0&lt;/UseAcsAuthentication&gt;&lt;MessageClientCredentialType vt="8"&gt;UserName&lt;/MessageClientCredentialType&gt;&lt;UseSSO vt="11"&gt;0&lt;/UseSSO&gt;&lt;UseSasAuthentication vt="11"&gt;0&lt;/UseSasAuthentication&gt;&lt;CloseTimeout vt="8"&gt;00:10:00&lt;/CloseTimeout&gt;&lt;SendTimeout vt="8"&gt;00:10:00&lt;/SendTimeout&gt;&lt;/CustomProps&gt;</TransportTypeData>
				<RetryCount>0</RetryCount>
				<RetryInterval>0</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T00:00:00</FromTime>
				<ToTime>2000-01-01T23:59:59</ToTime>
				<Primary>true</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler Name="BizTalkServerNbdoSend" HostTrusted="false">
					<TransportType Name="WCF-BasicHttp" Capabilities="899" ConfigurationClsid="467c1a52-373f-4f09-9008-27af6b985f14" />
				</SendHandler>
			</PrimaryTransport>
			<SecondaryTransport>
				<Address />
				<RetryCount>3</RetryCount>
				<RetryInterval>5</RetryInterval>
				<ServiceWindowEnabled>false</ServiceWindowEnabled>
				<FromTime>2000-01-01T22:00:00</FromTime>
				<ToTime>2000-01-01T21:59:59</ToTime>
				<Primary>false</Primary>
				<OrderedDelivery>false</OrderedDelivery>
				<DeliveryNotification>1</DeliveryNotification>
				<SendHandler xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			</SecondaryTransport>
			<ReceivePipeline Name="Microsoft.BizTalk.DefaultPipelines.XMLReceive" FullyQualifiedName="Microsoft.BizTalk.DefaultPipelines.XMLReceive, Microsoft.BizTalk.DefaultPipelines, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" Type="1" TrackingOption="None" Description="" />
			<ReceivePipelineData xsi:nil="true" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" />
			<Tracking>240</Tracking>
			<Filter />
			<Transforms />
			<InboundTransforms />
			<OrderedDelivery>false</OrderedDelivery>
			<Priority>5</Priority>
			<StopSendingOnFailure>false</StopSendingOnFailure>
			<RouteFailedMessage>true</RouteFailedMessage>
			<ApplicationName>A2A.EAI.INT_BDE</ApplicationName>
		</SendPort>
	</SendPortCollection>
	<ReceivePortCollection/>
</BindingInfo>