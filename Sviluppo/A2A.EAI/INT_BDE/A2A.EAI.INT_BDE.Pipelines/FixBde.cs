﻿using System;
using System.IO;
using System.Text;
using System.ComponentModel;
using Microsoft.BizTalk.Message.Interop;
using Microsoft.BizTalk.Component.Interop;
using Microsys.EAI.Framework.Azure.Services;

namespace A2A.EAI.INT_BDE.Pipelines
{
    [ComponentCategory(CategoryTypes.CATID_PipelineComponent)]
    [System.Runtime.InteropServices.Guid("E8569314-705D-4F0F-9DD0-FA05711529D4")]
    [ComponentCategory(CategoryTypes.CATID_Any)]
    public class FixBde : Microsoft.BizTalk.Component.Interop.IComponent, IBaseComponent, IPersistPropertyBag, IComponentUI
    {

        /// <summary>
        /// Enable component
        /// </summary>
        public bool Enabled { get; set; }

        #region IBaseComponent members
        /// <summary>
        /// Name of the component
        /// </summary>
        [Browsable(false)]
        public string Name
        {
            get
            {
                return "FixBde";
            }
        }

        /// <summary>
        /// Version of the component
        /// </summary>
        [Browsable(false)]
        public string Version
        {
            get
            {
                return "*******";
            }
        }

        /// <summary>
        /// Description of the component
        /// </summary>
        [Browsable(false)]
        public string Description
        {
            get
            {
                return "A2A - Fix Terna BDE";
            }
        }
        #endregion

        #region IPersistPropertyBag members
        /// <summary>
        /// Gets class ID of component for usage from unmanaged code.
        /// </summary>
        /// <param name="classid">
        /// Class ID of the component
        /// </param>
        public void GetClassID(out System.Guid classid)
        {
            classid = new System.Guid("E8569314-705D-4F0F-9DD0-FA05711529D4");
        }

        /// <summary>
        /// not implemented
        /// </summary>
        public void InitNew()
        {
        }

        /// <summary>
        /// Loads configuration properties for the component
        /// </summary>
        /// <param name="pb">Configuration property bag</param>
        /// <param name="errlog">Error status</param>
        public virtual void Load(Microsoft.BizTalk.Component.Interop.IPropertyBag pb, int errlog)
        {

            object val = null;

            val = this.ReadPropertyBag(pb, "Enabled");
            if ((val != null))
            {
                Enabled = ((bool)(val));
            }

        }

        /// <summary>
        /// Saves the current component configuration into the property bag
        /// </summary>
        /// <param name="pb">Configuration property bag</param>
        /// <param name="fClearDirty">not used</param>
        /// <param name="fSaveAllProperties">not used</param>
        public virtual void Save(Microsoft.BizTalk.Component.Interop.IPropertyBag pb, bool fClearDirty, bool fSaveAllProperties)
        {
            this.WritePropertyBag(pb, "Enabled", Enabled);
        }

        #region utility functionality
        /// <summary>
        /// Reads property value from property bag
        /// </summary>
        /// <param name="pb">Property bag</param>
        /// <param name="propName">Name of property</param>
        /// <returns>Value of the property</returns>
        private object ReadPropertyBag(Microsoft.BizTalk.Component.Interop.IPropertyBag pb, string propName)
        {
            object val = null;
            try
            {
                pb.Read(propName, out val, 0);
            }
            catch (System.ArgumentException)
            {
                return val;
            }
            catch (System.Exception e)
            {
                throw new System.ApplicationException(e.Message);
            }
            return val;
        }

        /// <summary>
        /// Writes property values into a property bag.
        /// </summary>
        /// <param name="pb">Property bag.</param>
        /// <param name="propName">Name of property.</param>
        /// <param name="val">Value of property.</param>
        private void WritePropertyBag(Microsoft.BizTalk.Component.Interop.IPropertyBag pb, string propName, object val)
        {
            try
            {
                pb.Write(propName, ref val);
            }
            catch (System.Exception e)
            {
                throw new System.ApplicationException(e.Message);
            }
        }
        #endregion
        #endregion

        #region IComponentUI members
        /// <summary>
        /// Component icon to use in BizTalk Editor
        /// </summary>
        [Browsable(false)]
        public IntPtr Icon
        {
            get
            {
                return new IntPtr();
            }
        }

        /// <summary>
        /// The Validate method is called by the BizTalk Editor during the build 
        /// of a BizTalk project.
        /// </summary>
        /// <param name="obj">An Object containing the configuration properties.</param>
        /// <returns>The IEnumerator enables the caller to enumerate through a collection of strings containing error messages. These error messages appear as compiler error messages. To report successful property validation, the method should return an empty enumerator.</returns>
        public System.Collections.IEnumerator Validate(object obj)
        {
            // example implementation:
            // ArrayList errorList = new ArrayList();
            // errorList.Add("This is a compiler error");
            // return errorList.GetEnumerator();
            return null;
        }
        #endregion

        #region IComponent members
        /// <summary>
        /// Implements IComponent.Execute method.
        /// </summary>
        /// <param name="pc">Pipeline context</param>
        /// <param name="inmsg">Input message</param>
        /// <returns>Original input message</returns>
        /// <remarks>
        /// IComponent.Execute method is used to initiate
        /// the processing of the message in this pipeline component.
        /// </remarks>
        public Microsoft.BizTalk.Message.Interop.IBaseMessage Execute(Microsoft.BizTalk.Component.Interop.IPipelineContext pContext, Microsoft.BizTalk.Message.Interop.IBaseMessage pInMsg)
        {

            if (!Enabled)
            {
                return pInMsg;
            }

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                IBaseMessagePart bodyPart = pInMsg.BodyPart;

                if (bodyPart != null)
                {

                    StreamReader reader = new StreamReader(pInMsg.BodyPart.Data);

                    string messageContent = reader.ReadToEnd();

                    // Alcuni file hanno teminatore CRLF altri LF altri ancora sono misti
                    messageContent = messageContent.Replace("\r\n", "\n");

                    // Tolgo le righe doppie inserite in modo casuale
                    messageContent = messageContent.Replace("\n\n", "\n");

                    // Tolgo i tab inseriti a caso
                    messageContent = messageContent.Replace("\t", "");

                    // alcune righe hanno lo spazio dopo l'uguale altre no,
                    // in questo modo inserisco lo spazio per tutti
                    messageContent = messageContent.Replace("= ", "=");
                    messageContent = messageContent.Replace("=", "= ");

                    // tolgo i doppi spazi anche questi inseriti a caso
                    while (messageContent.Contains("  "))
                    {
                        messageContent = messageContent.Replace("  ", " ");
                    }

                    // se il file non termina con un a capo lo inserisco
                    if (messageContent.Substring(messageContent.Length - 1, 1) != "\n")
                    {
                        messageContent = messageContent + "\n";
                    }

                    MemoryStream inStream = new MemoryStream();
                    UTF8Encoding utf8Encoding = new UTF8Encoding();

                    byte[] stringByte = utf8Encoding.GetBytes(messageContent);
                    inStream.Write(stringByte, 0, stringByte.Length);

                    inStream.Position = 0;
                    pInMsg.BodyPart.Data = inStream;

                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return pInMsg;
        }



        #endregion
    }
}
