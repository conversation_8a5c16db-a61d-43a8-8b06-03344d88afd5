﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="f6ab619e-a67b-473e-ba05-e22c679e4f98" LowerBound="1.1" HigherBound="448.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="27941cc0-2074-4ebb-89d1-aa36615c02b0" ParentLink="Module_PortType" LowerBound="20.1" HigherBound="27.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiBdeCb" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="467b474c-67c3-42d3-9e4c-351d42494ac0" ParentLink="PortType_OperationDeclaration" LowerBound="22.1" HigherBound="26.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BdeCb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8a1ad884-5b44-484b-a8b1-c6d19461036a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="24.13" HigherBound="24.35">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeCbRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="5111b609-b844-4809-a2d8-7bfcd0d21057" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="24.37" HigherBound="24.60">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeCbResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="6e5b79c5-4b48-4eab-85be-1750a14bfa64" ParentLink="Module_PortType" LowerBound="27.1" HigherBound="34.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typN2BdeCalcoloPvm" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="60e89ccd-1794-46b0-9906-6648723af09f" ParentLink="PortType_OperationDeclaration" LowerBound="29.1" HigherBound="33.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="LanciaTestCalcoloPVMC" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="53f097a4-44db-4421-ba12-74077a4e5439" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="31.13" HigherBound="31.42">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeCalcoloPvmRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="9d164b5e-d87a-478a-866f-52015ea048d2" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="31.76" HigherBound="31.96">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgFaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="60d9184a-2556-4db6-9490-87f491b40829" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="31.44" HigherBound="31.74">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeCalcoloPvmResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="ca68eafd-d909-4a7c-88a8-e249494584a2" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeCbRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="57fbf239-3b59-47d6-9e97-7c3b93050ac7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure.BdeCb" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="aa9e94ee-4ef5-49dd-ba76-d3ae7ff79c3e" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeCbResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7016aab7-9d16-47ed-8478-b0d22879879a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure.BdeCbResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="63b7cead-8d8c-4046-9305-c50845a184ed" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeCalcoloPvmRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="257b0d33-0e80-4d88-9984-fde02be93241" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeCalcoloPVM.LanciaTestCalcoloPVMC" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="79849cc1-2e4a-428a-bce0-c16ee960d4f1" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeCalcoloPvmResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="73bc3576-a6aa-4f21-b332-70cffb69ff7a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeCalcoloPVM.LanciaTestCalcoloPVMCResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="edd6063d-2bb2-443c-a445-bfd7d0050e80" ParentLink="Module_ServiceDeclaration" LowerBound="34.1" HigherBound="447.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeCb" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="368fec14-1cb8-4335-8de1-5fdfb3113d41" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="06af886e-0fbe-4d14-b755-063975bd2c95" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="54735679-6948-4d87-a477-379f3355135c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="35ab5d14-e286-4acf-ae6e-11ee643b7dd3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6ac1fda9-0632-44e6-b519-b1690c00d7fe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="41797c45-a2fe-40e6-ae0a-519725faf658" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="410cf9a9-58d4-4b24-9fcd-4d0c7af630e1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1c4ab7db-9474-46b6-963b-c730fc4f9cc9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5174d801-9880-4fbf-a062-f9b7936a5c76" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="51ecf29e-1fe7-4fa7-9634-f192fc919bdf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426b6a7c-17fb-4945-96dc-0026769a61d7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="UP" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8e2d43ae-f2eb-4465-8fe1-c6a3f2a2f4f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="tipoBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f1a0428c-d1cb-43cd-832b-a1aac6653c6d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="298bd104-a85c-43ad-8af3-d8dc0c35e8e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f07553f4-9218-43d4-8d73-cf265ad6a122" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b475c2-d1e1-461d-a3ce-31a06c9b5123" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7658a2c6-aee5-4d67-a981-fdb73792e87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="71.1" HigherBound="72.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f8905c1-6bb0-411d-b62e-1dcbbdb2167a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="72.1" HigherBound="73.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9a95c256-fb02-4c88-ae27-e37142bac6d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="73.1" HigherBound="74.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bd76a109-4266-43ae-b3db-a023402f0d2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="74.1" HigherBound="75.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78ad8ea2-d6b7-43a5-9099-5213a82f91dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="75.1" HigherBound="76.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f58e4c85-4768-415d-8808-3efbed7d576b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="backupFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b3f46dd-785b-4520-af09-a5765d883054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3d742c18-539c-495c-92fd-fccdc5f1bd01" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="796648e1-54dc-492c-87e9-b0a642f2e08c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgTernaBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="160b8573-cc65-4f40-9ba6-9364d20d2340" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeCbRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4884cc52-01c0-4473-ab72-b2446928c8d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeCbResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5fb41824-91cd-41f5-b752-6d49033f5642" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeCalcoloPvmRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeCalcoloPvmRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7d00a780-c838-437f-8518-a01a6625836d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeCalcoloPvmResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeCalcoloPvmResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6ae76a8b-e47a-4683-bdcd-3c7755173a80" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="b17fc5a7-016f-493f-b82d-71fc7e6240d9" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="26bc6b4a-8f3b-4972-87a2-e87d20377552" ParentLink="ServiceBody_Statement" LowerBound="80.1" HigherBound="93.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptTernaBdeCb" />
                    <om:Property Name="MessageName" Value="msgTernaBde" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5b70b7e9-866c-4e11-901e-a3f333e64502" ParentLink="ServiceBody_Statement" LowerBound="93.1" HigherBound="117.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); &#xD;&#xA;originalFileName = System.IO.Path.GetFileName(originalFilePath);&#xD;&#xA;backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);&#xD;&#xA;&#xD;&#xA;validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationMaxRetryCount&quot;);&#xD;&#xA;validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationRetryInterval&quot;);&#xD;&#xA;validationRetryCount = 0;&#xD;&#xA;validationRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertMaxRetryCount&quot;);&#xD;&#xA;insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertRetryInterval&quot;);&#xD;&#xA;insertRetryCount = 0;&#xD;&#xA;insertRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;&#xD;&#xA;tipoBde = &quot;Comando di Bilanciamento&quot;;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="538b548f-eba1-48eb-b57f-bd65d222d5c6" ParentLink="ServiceBody_Statement" LowerBound="117.1" HigherBound="131.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;BDE Codice&quot;, originalFileName,&#xD;&#xA;&quot;BDE Data Inizio&quot;, msgTernaBde.parameter.DataOraInizioComando.DataOraInizioComando,&#xD;&#xA;&quot;BDE Data Fine&quot;, msgTernaBde.parameter.DataOraFineComando.DataOraFineComando,&#xD;&#xA;&quot;BDE Stato Comando&quot;, msgTernaBde.parameter.StatoContinuazioneComando.StatoContinuazioneComando,&#xD;&#xA;&quot;BDE Tipo&quot;, A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb,&#xD;&#xA;&quot;UP&quot;, UP,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="d52039a1-c897-444d-9fde-65962613f110" ParentLink="ServiceBody_Statement" LowerBound="131.1" HigherBound="373.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="139d18c6-681c-4aa0-b12e-7c5962c73438" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="152.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Messages" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="0759c1b6-30d2-4674-b6a9-611219d07d2a" ParentLink="ComplexStatement_Statement" LowerBound="139.1" HigherBound="141.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeCbToEaiBdeCb" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Nbdo Request" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="de583a11-a777-465e-9e32-cc9f173c408b" ParentLink="Transform_OutputMessagePartRef" LowerBound="140.36" HigherBound="140.62">
                                <om:Property Name="MessageRef" Value="msgEaiBdeRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="4dd27bc9-f11e-4067-b827-71389e26932f" ParentLink="Transform_InputMessagePartRef" LowerBound="140.122" HigherBound="140.143">
                                <om:Property Name="MessageRef" Value="msgTernaBde" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="ed64fd28-1179-4eeb-82f8-c243d65bea65" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="145.1">
                            <om:Property Name="Expression" Value="msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;&#xD;&#xA;msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Codice BDE" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="a7743daa-4994-4d09-ad5b-8f6c9e6dd57f" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="151.1">
                            <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter = &#xD;&#xA;Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0&quot;,&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure+BdeCbResponse&quot;&#xD;&#xA;);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="CreateResponse" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="c53eaaef-1469-411c-a8e2-ec6b7f6233e6" ParentLink="Construct_MessageRef" LowerBound="137.31" HigherBound="137.47">
                            <om:Property Name="Ref" Value="msgEaiBdeRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="0411afc0-ce13-4f8d-b73a-c04016281711" ParentLink="Construct_MessageRef" LowerBound="137.49" HigherBound="137.66">
                            <om:Property Name="Ref" Value="msgEaiBdeResponse" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="While" OID="d1fab865-9a3a-4a61-bf3d-b25c95d94abf" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="208.1">
                        <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Insert Loop" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="90a81b42-ef52-4b43-924d-9df237d82aad" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="157.1">
                            <om:Property Name="Expression" Value="insertRetryCount = insertRetryCount + 1;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="count++" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="5c7e1201-96ce-4494-8500-e941b0dc1da8" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="159.1">
                            <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Take Time" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Send" OID="f52cc46f-3bd0-4d38-a1e1-b4fbcf226706" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="161.1">
                            <om:Property Name="PortName" Value="sptEaiBdeCb" />
                            <om:Property Name="MessageName" Value="msgEaiBdeRequest" />
                            <om:Property Name="OperationName" Value="BdeCb" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Receive" OID="8568cf42-8d46-47a9-8fdf-1f1378185e1e" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="163.1">
                            <om:Property Name="Activate" Value="False" />
                            <om:Property Name="PortName" Value="sptEaiBdeCb" />
                            <om:Property Name="MessageName" Value="msgEaiBdeResponse" />
                            <om:Property Name="OperationName" Value="BdeCb" />
                            <om:Property Name="OperationMessageName" Value="Response" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Receive" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="273782fe-3950-4530-84be-0e43d91c3fcc" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="169.1">
                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BAM Update" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Decision" OID="9f1ecdcf-b38a-4271-8cfe-3cf1618b49fc" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="207.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Process Result" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="DecisionBranch" OID="763e6f4d-b894-471e-af30-bc7740a8d20e" ParentLink="ReallyComplexStatement_Branch" LowerBound="170.25" HigherBound="177.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage &gt; 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="OK" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="d1d6be81-c21e-4125-b400-8d4ed87fb57f" ParentLink="ComplexStatement_Statement" LowerBound="172.1" HigherBound="176.1">
                                    <om:Property Name="Expression" Value="insertRetryMessage = System.String.Concat(&quot;BDE inserita dopo &quot;, insertRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Status Update" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="f5aac9fc-5f9c-4565-9a63-7241eadb3de7" ParentLink="ReallyComplexStatement_Branch" LowerBound="177.30" HigherBound="189.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage == 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BDE Già Validata" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="c0760140-ab10-4e45-9259-9c77c659bb4f" ParentLink="ComplexStatement_Statement" LowerBound="179.1" HigherBound="188.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(&quot;BDE già validata.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="02d55a64-8ca4-498a-a4e8-e386ccfb87ff" ParentLink="ReallyComplexStatement_Branch" LowerBound="189.30" HigherBound="197.1">
                                <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Retry" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="a63e532b-ce8f-4e2e-8cf8-f50be5e857f3" ParentLink="ComplexStatement_Statement" LowerBound="191.1" HigherBound="194.1">
                                    <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Message" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Delay" OID="dde14248-2b42-43bf-95d9-766e0a73e046" ParentLink="ComplexStatement_Statement" LowerBound="194.1" HigherBound="196.1">
                                    <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, insertRetryInterval);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Delay" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="b91afc35-c07a-4293-a3de-4c0ae2280461" ParentLink="ReallyComplexStatement_Branch">
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Else" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="ae480f85-b4a8-4ae0-a3ec-1f2d28154866" ParentLink="ComplexStatement_Statement" LowerBound="199.1" HigherBound="206.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;UP '{0}' non presente in NDUE.&quot;, msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="b9c9d630-ebef-4207-bbb8-21fb0f130d8a" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="239.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BDE Already Submitted" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="66e46a30-6bbb-49c3-9eaa-d10f670048a8" ParentLink="ReallyComplexStatement_Branch" LowerBound="209.21" HigherBound="212.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="b1beb572-f221-41e8-8922-80f91a90fcf7" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Scope" OID="cc38e491-6576-4f03-b58d-8c6519d848fe" ParentLink="ComplexStatement_Statement" LowerBound="214.1" HigherBound="238.1">
                                <om:Property Name="InitializedTransactionType" Value="True" />
                                <om:Property Name="IsSynchronized" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Mail Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Construct" OID="0e45d51b-00a1-42c4-90a6-bb5676335f99" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="227.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Messages" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Transform" OID="e0abf416-0838-415e-940f-1ad9e1525b9f" ParentLink="ComplexStatement_Statement" LowerBound="222.1" HigherBound="224.1">
                                        <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeCbToEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Create Notification" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="MessagePartRef" OID="e78e6796-3e57-44b1-ab4e-96d2ede614fd" ParentLink="Transform_InputMessagePartRef" LowerBound="223.149" HigherBound="223.170">
                                            <om:Property Name="MessageRef" Value="msgTernaBde" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_5" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessagePartRef" OID="70b43608-4951-46cb-9a50-e76721ed208d" ParentLink="Transform_OutputMessagePartRef" LowerBound="223.48" HigherBound="223.79">
                                            <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_6" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="MessageAssignment" OID="1a958251-4fa0-46d8-b791-00e1ac1440d0" ParentLink="ComplexStatement_Statement" LowerBound="224.1" HigherBound="226.1">
                                        <om:Property Name="Expression" Value="msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="False" />
                                        <om:Property Name="Name" Value="File Name" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="MessageRef" OID="0ac7992e-e650-4c3f-ba32-fbfa2a2124a1" ParentLink="Construct_MessageRef" LowerBound="220.43" HigherBound="220.64">
                                        <om:Property Name="Ref" Value="msgEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Exec" OID="9cd7a518-a42c-4800-8135-5a4dbc1e7a50" ParentLink="ComplexStatement_Statement" LowerBound="227.1" HigherBound="229.1">
                                    <om:Property Name="Invokee" Value="A2A.EAI.INT_BDE.Processes.prcBdeNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Start Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="Parameter" OID="a3980d99-badb-46a5-b649-ae3089933e4c" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="UP" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="c476ae66-a8a5-4216-a351-3d528a2c5246" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="tipoBde" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="906d153a-472a-438c-82a8-47f99da04d26" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="resultCode" />
                                        <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="815b66a8-2ca6-4f2d-af18-7777a4ea839b" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="msgEaiBdeNotification" />
                                        <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="44c471f9-4316-4266-bb89-f297b7e5444e" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="activityInstanceId" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Catch" OID="e091236e-8996-44ca-a2bd-9701ef6f9c93" ParentLink="Scope_Catch" LowerBound="232.1" HigherBound="236.1">
                                    <om:Property Name="ExceptionType" Value="General Exception" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Generic" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="a6aa5057-26ff-44be-a5db-97c010676a55" ParentLink="ComplexStatement_Statement" LowerBound="239.1" HigherBound="340.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check Succeded" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="4d0d4c68-bbc6-4916-abb9-e888a87d7ea9" ParentLink="ReallyComplexStatement_Branch" LowerBound="240.21" HigherBound="340.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="9a538bcc-8b02-470e-9dbc-cb940afdcc14" ParentLink="ComplexStatement_Statement" LowerBound="242.1" HigherBound="248.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup Nbdo WS" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="256e8d4b-3951-41a3-994c-76d9b8a20285" ParentLink="Construct_MessageRef" LowerBound="243.35" HigherBound="243.60">
                                    <om:Property Name="Ref" Value="msgN2BdeCalcoloPvmRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="238b75a5-53e9-42a7-9bfc-1e7f6dd30922" ParentLink="ComplexStatement_Statement" LowerBound="245.1" HigherBound="247.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeCbToN2BdeCalcoloPVM" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Create Nbdo Ws" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="161d27e6-0528-4000-83f4-d97350060e02" ParentLink="Transform_InputMessagePartRef" LowerBound="246.140" HigherBound="246.167">
                                        <om:Property Name="MessageRef" Value="msgEaiBdeResponse" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="273be384-b276-4d94-8cbe-f8bc83bdb873" ParentLink="Transform_OutputMessagePartRef" LowerBound="246.40" HigherBound="246.75">
                                        <om:Property Name="MessageRef" Value="msgN2BdeCalcoloPvmRequest" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="ac681104-30a5-443a-ae17-53ad727e5e11" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="339.1">
                                <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Validation Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="e87e7e59-a799-46f5-bbd1-8323924ff324" ParentLink="ComplexStatement_Statement" LowerBound="251.1" HigherBound="253.1">
                                    <om:Property Name="Expression" Value="validationRetryCount = validationRetryCount + 1;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Inc retryCount" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="08eba4fb-7db0-465c-85c9-71e2f97272e5" ParentLink="ComplexStatement_Statement" LowerBound="253.1" HigherBound="255.1">
                                    <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Take Time" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Scope" OID="eb22eeb8-622a-449a-9724-60d068b58c8b" ParentLink="ComplexStatement_Statement" LowerBound="255.1" HigherBound="310.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Catch" OID="2fb9c527-a94a-496c-b5b1-40a006486fdb" ParentLink="Scope_Catch" LowerBound="278.1" HigherBound="293.1">
                                        <om:Property Name="ExceptionName" Value="valSoapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="20f2e396-6da9-4c2d-8db9-6ca65b64d5f1" ParentLink="Catch_Statement" LowerBound="281.1" HigherBound="292.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="0525b170-b69d-482e-8623-7efc3be8eb1f" ParentLink="Scope_Catch" LowerBound="293.1" HigherBound="308.1">
                                        <om:Property Name="ExceptionName" Value="valFaultExc" />
                                        <om:Property Name="ExceptionType" Value="sptN2BdeCalcoloPvm.LanciaTestCalcoloPVMC.Fault" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Fauld Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="6d72e80c-23ba-4a3c-85a8-55e8d20ab1d9" ParentLink="Catch_Statement" LowerBound="296.1" HigherBound="307.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="6921be18-60eb-451e-b966-06b4be0fa8b7" ParentLink="ComplexStatement_Statement" LowerBound="260.1" HigherBound="262.1">
                                        <om:Property Name="PortName" Value="sptN2BdeCalcoloPvm" />
                                        <om:Property Name="MessageName" Value="msgN2BdeCalcoloPvmRequest" />
                                        <om:Property Name="OperationName" Value="LanciaTestCalcoloPVMC" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="0bf9d879-df74-4efc-b0fc-46b1fde8be19" ParentLink="ComplexStatement_Statement" LowerBound="262.1" HigherBound="264.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptN2BdeCalcoloPvm" />
                                        <om:Property Name="MessageName" Value="msgN2BdeCalcoloPvmResponse" />
                                        <om:Property Name="OperationName" Value="LanciaTestCalcoloPVMC" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="6e1f8546-a164-4e4a-b9c2-9b95356df193" ParentLink="ComplexStatement_Statement" LowerBound="264.1" HigherBound="275.1">
                                        <om:Property Name="Expression" Value="validationResult = msgN2BdeCalcoloPvmResponse.parameter.LanciaTestCalcoloPVMCResult;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="34ced71a-d2e0-43fe-af2e-096c71e005cf" ParentLink="ComplexStatement_Statement" LowerBound="310.1" HigherBound="338.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation Result" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="5f9d8d92-fe5a-46d6-87c8-2791f7645509" ParentLink="ReallyComplexStatement_Branch" LowerBound="311.29" HigherBound="318.1">
                                        <om:Property Name="Expression" Value="validationResult.StartsWith(&quot;OK&quot;, System.StringComparison.OrdinalIgnoreCase)" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="OK" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="0bea21ba-952d-4eab-9b71-4a314b1bdb27" ParentLink="ComplexStatement_Statement" LowerBound="313.1" HigherBound="317.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;BDE Validata dopo &quot;, validationRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;validationRetryCount = 100;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Status Update" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="58508a30-359a-49a6-958b-2a7f8a2125a9" ParentLink="ReallyComplexStatement_Branch" LowerBound="318.34" HigherBound="326.1">
                                        <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Retry" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="0eaec8be-4da8-4477-bae9-65a492317168" ParentLink="ComplexStatement_Statement" LowerBound="320.1" HigherBound="323.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Delay" OID="d3317b14-3e2f-476c-8ad4-53a87675f3b0" ParentLink="ComplexStatement_Statement" LowerBound="323.1" HigherBound="325.1">
                                            <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, validationRetryInterval);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Delay" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="170ab208-fb0d-4535-b5e8-f8f551a97d1e" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="2b0dee97-24d7-4201-8150-dacb81fca1d6" ParentLink="ComplexStatement_Statement" LowerBound="328.1" HigherBound="337.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;Num. di tentativi: &quot;, validationRetryCount.ToString(), &quot;. &quot;);&#xD;&#xA;&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;NBDO non ha validato la BDE ({0}). &quot;, validationResult));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Response Code" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="777e6b70-76a6-4119-a564-af5fef1b55db" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="8de12d7a-2f12-403f-84f3-344a65ed6362" ParentLink="Scope_Catch" LowerBound="343.1" HigherBound="357.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="14f45db2-6f4a-4bbd-a965-cca0ca9d1c4e" ParentLink="Catch_Statement" LowerBound="346.1" HigherBound="356.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="5cda6bfb-9e7e-495c-b064-7de0de0d9294" ParentLink="Scope_Catch" LowerBound="357.1" HigherBound="371.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d23d63f3-8fcc-4537-9502-04d5ef1bf5f2" ParentLink="Catch_Statement" LowerBound="360.1" HigherBound="370.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="3fa6b800-7eac-4d75-933b-aea9a00106cc" ParentLink="ServiceBody_Statement" LowerBound="373.1" HigherBound="438.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="c8894d92-6831-4f0c-ab8a-4ce945ffdce3" ParentLink="ReallyComplexStatement_Branch" LowerBound="374.13" HigherBound="377.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="05ff76e2-e216-401f-be94-8b39051663b5" ParentLink="ReallyComplexStatement_Branch" LowerBound="377.18" HigherBound="408.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded With Warnings" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="706071f9-e12d-4907-b0d6-993d4467734e" ParentLink="ComplexStatement_Statement" LowerBound="379.1" HigherBound="407.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="6b7f36d3-7d85-4338-b1af-f5f78c6b6415" ParentLink="ComplexStatement_Statement" LowerBound="384.1" HigherBound="403.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="97497f2b-1fd1-4b27-9d2b-70f856583eca" ParentLink="Construct_MessageRef" LowerBound="385.35" HigherBound="385.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="959d66e6-6117-43b7-8a73-8965e0371a64" ParentLink="ComplexStatement_Statement" LowerBound="387.1" HigherBound="402.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="f5813757-f378-42b5-b3d0-c9b52bbd1c94" ParentLink="ComplexStatement_Statement" LowerBound="403.1" HigherBound="405.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="693268a8-e2e5-4789-b3eb-cb544b522f9a" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="ba01ce81-3131-4ec9-90f4-6f765ef30ccf" ParentLink="ComplexStatement_Statement" LowerBound="410.1" HigherBound="437.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="0ce90641-68b7-454f-9367-4de30d85f70e" ParentLink="ComplexStatement_Statement" LowerBound="415.1" HigherBound="433.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="7010fcea-6fce-4a76-aaec-cdae23f0481d" ParentLink="Construct_MessageRef" LowerBound="416.35" HigherBound="416.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="81681f5d-fc8e-406d-8fa0-87bfecf31bee" ParentLink="ComplexStatement_Statement" LowerBound="418.1" HigherBound="432.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="28e1c767-132d-4a27-9138-8a820eba34c8" ParentLink="ComplexStatement_Statement" LowerBound="433.1" HigherBound="435.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e2060f21-b773-4da5-a479-bbc0011c07a5" ParentLink="ServiceBody_Statement" LowerBound="438.1" HigherBound="445.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="68528821-ec47-44fa-85ab-3c3b3979d237" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="37.1" HigherBound="39.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typTernaBde" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptTernaBdeCb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="fb72e6d0-e82c-42de-9516-e43e345f2f0a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="37.1" HigherBound="38.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="92fba202-2ddd-42f3-bf75-e478ebb7a48e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="39.1" HigherBound="42.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="299" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="17581804-4e3e-4bab-a9a3-f3de910857c0" ParentLink="PortDeclaration_CLRAttribute" LowerBound="39.1" HigherBound="40.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3bf1d079-059b-4ee0-8f6e-2ec779876f33" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="42.1" HigherBound="45.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="53" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typEaiBdeCb" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiBdeCb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="860fb6d4-2ed4-416e-ab7c-fa0c15dd0469" ParentLink="PortDeclaration_CLRAttribute" LowerBound="42.1" HigherBound="43.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="537bc582-57d5-4f5d-9e8d-0e80567127a2" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="45.1" HigherBound="48.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="175" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typN2BdeCalcoloPvm" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2BdeCalcoloPvm" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8c62cb9b-6310-4e2b-af30-04b197ae4516" ParentLink="PortDeclaration_CLRAttribute" LowerBound="45.1" HigherBound="46.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    internal messagetype msgEaiBdeCbRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure.BdeCb parameter;
    };
    internal messagetype msgEaiBdeCbResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure.BdeCbResponse parameter;
    };
    internal messagetype msgN2BdeCalcoloPvmRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeCalcoloPVM.LanciaTestCalcoloPVMC parameter;
    };
    internal messagetype msgN2BdeCalcoloPvmResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeCalcoloPVM.LanciaTestCalcoloPVMCResponse parameter;
    };
    internal porttype typEaiBdeCb
    {
        requestresponse BdeCb
        {
            msgEaiBdeCbRequestType, msgEaiBdeCbResponseType
        };
    };
    internal porttype typN2BdeCalcoloPvm
    {
        requestresponse LanciaTestCalcoloPVMC
        {
            msgN2BdeCalcoloPvmRequestType, msgN2BdeCalcoloPvmResponseType, Fault = msgFaultType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeCb
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typTernaBde rptTernaBdeCb;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiBdeCb sptEaiBdeCb;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typN2BdeCalcoloPvm sptN2BdeCalcoloPvm;
        message msgEaiBdeNotificationType msgEaiBdeNotification;
        message msgTernaBdeType msgTernaBde;
        message msgEaiBdeCbRequestType msgEaiBdeRequest;
        message msgEaiBdeCbResponseType msgEaiBdeResponse;
        message msgN2BdeCalcoloPvmRequestType msgN2BdeCalcoloPvmRequest;
        message msgN2BdeCalcoloPvmResponseType msgN2BdeCalcoloPvmResponse;
        message msgNotificationType msgNotification;
        System.Int32 insertMaxRetryCount;
        System.String insertRetryMessage;
        System.Int32 insertRetryInterval;
        System.Int32 insertRetryCount;
        System.String insertResult;
        System.String validationRetryMessage;
        System.Int32 validationRetryInterval;
        System.Int32 validationRetryCount;
        System.String validationResult;
        System.Int32 validationMaxRetryCount;
        System.String UP;
        System.String tipoBde;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFilePath;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String backupFileName;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("26bc6b4a-8f3b-4972-87a2-e87d20377552")]
            activate receive (rptTernaBdeCb.Receive, msgTernaBde);
            insertRetryMessage = "";
            insertResult = "";
            validationRetryMessage = "";
            validationResult = "";
            UP = "";
            tipoBde = "";
            originalFilePath = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            backupFileName = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b70b7e9-866c-4e11-901e-a3f333e64502")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); 
            originalFileName = System.IO.Path.GetFileName(originalFilePath);
            backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);
            
            validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationMaxRetryCount");
            validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationRetryInterval");
            validationRetryCount = 0;
            validationRetryMessage = System.String.Empty;
            
            insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertMaxRetryCount");
            insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertRetryInterval");
            insertRetryCount = 0;
            insertRetryMessage = System.String.Empty;
            
            UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;
            tipoBde = "Comando di Bilanciamento";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("538b548f-eba1-48eb-b57f-bd65d222d5c6")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Inviato a N2", "NO",
            "BDE Codice", originalFileName,
            "BDE Data Inizio", msgTernaBde.parameter.DataOraInizioComando.DataOraInizioComando,
            "BDE Data Fine", msgTernaBde.parameter.DataOraFineComando.DataOraFineComando,
            "BDE Stato Comando", msgTernaBde.parameter.StatoContinuazioneComando.StatoContinuazioneComando,
            "BDE Tipo", A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb,
            "UP", UP,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d52039a1-c897-444d-9fde-65962613f110")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("139d18c6-681c-4aa0-b12e-7c5962c73438")]
                    construct msgEaiBdeRequest, msgEaiBdeResponse
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0759c1b6-30d2-4674-b6a9-611219d07d2a")]
                        transform (msgEaiBdeRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeCbToEaiBdeCb (msgTernaBde.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ed64fd28-1179-4eeb-82f8-c243d65bea65")]
                        msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;
                        msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);
                        
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a7743daa-4994-4d09-ad5b-8f6c9e6dd57f")]
                        msgEaiBdeResponse.parameter = 
                        Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(
                        "A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0",
                        "A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeCbTypedProcedure+BdeCbResponse"
                        );
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1fab865-9a3a-4a61-bf3d-b25c95d94abf")]
                    while (insertRetryCount < insertMaxRetryCount)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("90a81b42-ef52-4b43-924d-9df237d82aad")]
                        insertRetryCount = insertRetryCount + 1;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5c7e1201-96ce-4494-8500-e941b0dc1da8")]
                        dataInizioDb = System.DateTimeOffset.Now;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f52cc46f-3bd0-4d38-a1e1-b4fbcf226706")]
                        send (sptEaiBdeCb.BdeCb, msgEaiBdeRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8568cf42-8d46-47a9-8fdf-1f1378185e1e")]
                        receive (sptEaiBdeCb.BdeCb, msgEaiBdeResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("273782fe-3950-4530-84be-0e43d91c3fcc")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                        "Data Inizio DB", dataInizioDb,
                        "Data Fine DB", System.DateTimeOffset.Now,
                        "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9f1ecdcf-b38a-4271-8cfe-3cf1618b49fc")]
                        if (msgEaiBdeResponse.parameter.CodeMessage > 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1d6be81-c21e-4125-b400-8d4ed87fb57f")]
                            insertRetryMessage = System.String.Concat("BDE inserita dopo ", insertRetryCount.ToString(), " numero di tentativi. ");
                            
                            insertRetryCount = 100;
                        }
                        else if (msgEaiBdeResponse.parameter.CodeMessage == 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0760140-ab10-4e45-9259-9c77c659bb4f")]
                            errorMessage.Append("BDE già validata.");
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            
                            insertRetryCount = 100;
                        }
                        else if (insertRetryCount < insertMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a63e532b-ce8f-4e2e-8cf8-f50be5e857f3")]
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("dde14248-2b42-43bf-95d9-766e0a73e046")]
                            delay new System.TimeSpan(0, 0, insertRetryInterval);
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae480f85-b4a8-4ae0-a3ec-1f2d28154866")]
                            errorMessage.Append(System.String.Format("UP '{0}' non presente in NDUE.", msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b9c9d630-ebef-4207-bbb8-21fb0f130d8a")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc38e491-6576-4f03-b58d-8c6519d848fe")]
                        scope
                        {
                            body
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e45d51b-00a1-42c4-90a6-bb5676335f99")]
                                construct msgEaiBdeNotification
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0abf416-0838-415e-940f-1ad9e1525b9f")]
                                    transform (msgEaiBdeNotification.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeCbToEaiBdeNotification (msgTernaBde.parameter);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1a958251-4fa0-46d8-b791-00e1ac1440d0")]
                                    msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("9cd7a518-a42c-4800-8135-5a4dbc1e7a50")]
                                exec A2A.EAI.INT_BDE.Processes.prcBdeNotification (UP, tipoBde, resultCode, msgEaiBdeNotification, activityInstanceId);
                            }
                            exceptions
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("e091236e-8996-44ca-a2bd-9701ef6f9c93")]
                                catch
                                {
                                }
                            }
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6aa5057-26ff-44be-a5db-97c010676a55")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9a538bcc-8b02-470e-9dbc-cb940afdcc14")]
                        construct msgN2BdeCalcoloPvmRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("238b75a5-53e9-42a7-9bfc-1e7f6dd30922")]
                            transform (msgN2BdeCalcoloPvmRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeCbToN2BdeCalcoloPVM (msgEaiBdeResponse.parameter);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ac681104-30a5-443a-ae17-53ad727e5e11")]
                        while (validationRetryCount < validationMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e87e7e59-a799-46f5-bbd1-8323924ff324")]
                            validationRetryCount = validationRetryCount + 1;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("08eba4fb-7db0-465c-85c9-71e2f97272e5")]
                            dataInizioWs = System.DateTimeOffset.Now;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb22eeb8-622a-449a-9724-60d068b58c8b")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6921be18-60eb-451e-b966-06b4be0fa8b7")]
                                    send (sptN2BdeCalcoloPvm.LanciaTestCalcoloPVMC, msgN2BdeCalcoloPvmRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bf9d879-df74-4efc-b0fc-46b1fde8be19")]
                                    receive (sptN2BdeCalcoloPvm.LanciaTestCalcoloPVMC, msgN2BdeCalcoloPvmResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6e1f8546-a164-4e4a-b9c2-9b95356df193")]
                                    validationResult = msgN2BdeCalcoloPvmResponse.parameter.LanciaTestCalcoloPVMCResult;
                                    
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                    "Inviato a N2", "SI",
                                    "Esito WS", validationResult,
                                    "Data Inizio WS", dataInizioWs,
                                    "Data Fine WS", System.DateTimeOffset.Now,
                                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                    "Retry", validationRetryCount
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2fb9c527-a94a-496c-b5b1-40a006486fdb")]
                                    catch (System.Web.Services.Protocols.SoapException valSoapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("20f2e396-6da9-4c2d-8db9-6ca65b64d5f1")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0525b170-b69d-482e-8623-7efc3be8eb1f")]
                                    catch (sptN2BdeCalcoloPvm.LanciaTestCalcoloPVMC.Fault valFaultExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d72e80c-23ba-4a3c-85a8-55e8d20ab1d9")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("34ced71a-d2e0-43fe-af2e-096c71e005cf")]
                            if (validationResult.StartsWith("OK", System.StringComparison.OrdinalIgnoreCase))
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bea21ba-952d-4eab-9b71-4a314b1bdb27")]
                                validationRetryMessage = System.String.Concat("BDE Validata dopo ", validationRetryCount.ToString(), " numero di tentativi. ");
                                
                                validationRetryCount = 100;
                            }
                            else if (validationRetryCount < validationMaxRetryCount)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0eaec8be-4da8-4477-bae9-65a492317168")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("d3317b14-3e2f-476c-8ad4-53a87675f3b0")]
                                delay new System.TimeSpan(0, 0, validationRetryInterval);
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("2b0dee97-24d7-4201-8150-dacb81fca1d6")]
                                validationRetryMessage = System.String.Concat("Num. di tentativi: ", validationRetryCount.ToString(), ". ");
                                
                                errorMessage.Append(System.String.Format("NBDO non ha validato la BDE ({0}). ", validationResult));
                                errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            }
                        }
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8de12d7a-2f12-403f-84f3-344a65ed6362")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("14f45db2-6f4a-4bbd-a965-cca0ca9d1c4e")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5cda6bfb-9e7e-495c-b064-7de0de0d9294")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d23d63f3-8fcc-4537-9502-04d5ef1bf5f2")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3fa6b800-7eac-4d75-933b-aea9a00106cc")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("706071f9-e12d-4907-b0d6-993d4467734e")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6b7f36d3-7d85-4338-b1af-f5f78c6b6415")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("959d66e6-6117-43b7-8a73-8965e0371a64")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5813757-f378-42b5-b3d0-c9b52bbd1c94")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("ba01ce81-3131-4ec9-90f4-6f765ef30ccf")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ce90641-68b7-454f-9367-4de30d85f70e")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("81681f5d-fc8e-406d-8fa0-87bfecf31bee")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionCb;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("28e1c767-132d-4a27-9138-8a820eba34c8")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e2060f21-b773-4da5-a479-bbc0011c07a5")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

