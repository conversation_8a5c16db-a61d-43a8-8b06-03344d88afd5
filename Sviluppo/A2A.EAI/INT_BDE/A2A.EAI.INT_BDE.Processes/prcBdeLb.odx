﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="539ca25e-f7c2-4cdc-bca2-77e8ec172956" LowerBound="1.1" HigherBound="432.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="aa641cf1-84a7-4afd-9725-21e688eaf029" ParentLink="Module_ServiceDeclaration" LowerBound="19.1" HigherBound="431.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeLb" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="c9d9b8b5-d4ad-4fc9-9f50-6f9eea09d4b4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="21411d6b-600f-481f-be6a-91004ec16c8b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a63b4140-221f-4e86-bbfc-57216af701af" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0c507748-fe0b-4789-9518-36d9c823176d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2cdea39f-b092-4b8d-b264-8ac8e8fede7c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="07dbaae2-8375-4d9a-98df-31db8686af3a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d110fa8c-1d4c-453f-a2e6-4e6b40044080" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78c7fd5c-fe60-46c4-afef-5b066915deb1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3482371a-738b-4c95-a829-250559fd5053" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="437ea1e0-d315-4002-b537-64a7d38871c8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426b6a7c-17fb-4945-96dc-0026769a61d7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="UP" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8e2d43ae-f2eb-4465-8fe1-c6a3f2a2f4f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="tipoBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f1a0428c-d1cb-43cd-832b-a1aac6653c6d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="298bd104-a85c-43ad-8af3-d8dc0c35e8e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f07553f4-9218-43d4-8d73-cf265ad6a122" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b475c2-d1e1-461d-a3ce-31a06c9b5123" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7658a2c6-aee5-4d67-a981-fdb73792e87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f8905c1-6bb0-411d-b62e-1dcbbdb2167a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9a95c256-fb02-4c88-ae27-e37142bac6d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bd76a109-4266-43ae-b3db-a023402f0d2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78ad8ea2-d6b7-43a5-9099-5213a82f91dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f58e4c85-4768-415d-8808-3efbed7d576b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="backupFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b3f46dd-785b-4520-af09-a5765d883054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3d742c18-539c-495c-92fd-fccdc5f1bd01" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="796648e1-54dc-492c-87e9-b0a642f2e08c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgTernaBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="160b8573-cc65-4f40-9ba6-9364d20d2340" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeLbRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4884cc52-01c0-4473-ab72-b2446928c8d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeLbResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5fb41824-91cd-41f5-b752-6d49033f5642" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeValidazioneRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7d00a780-c838-437f-8518-a01a6625836d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeValidazioneResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6ae76a8b-e47a-4683-bdcd-3c7755173a80" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="7fe2cab5-ed64-4dc9-94e7-0d37d425a403" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="707953ba-b2a9-432d-a3b0-0f5c5c491f1e" ParentLink="ServiceBody_Statement" LowerBound="65.1" HigherBound="78.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptTernaBdeLb" />
                    <om:Property Name="MessageName" Value="msgTernaBde" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0f7ea042-dc1f-4650-b793-66b201891b9c" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="102.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); &#xD;&#xA;originalFileName = System.IO.Path.GetFileName(originalFilePath);&#xD;&#xA;backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);&#xD;&#xA;&#xD;&#xA;validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationMaxRetryCount&quot;);&#xD;&#xA;validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationRetryInterval&quot;);&#xD;&#xA;validationRetryCount = 0;&#xD;&#xA;validationRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertMaxRetryCount&quot;);&#xD;&#xA;insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertRetryInterval&quot;);&#xD;&#xA;insertRetryCount = 0;&#xD;&#xA;insertRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;&#xD;&#xA;tipoBde = &quot;Limitazione al Bilanciamento&quot;;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="6f17198b-8c27-4d6a-a375-af44a5f0542d" ParentLink="ServiceBody_Statement" LowerBound="102.1" HigherBound="115.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;BDE Codice&quot;, originalFileName,&#xD;&#xA;&quot;BDE Data Inizio&quot;, msgTernaBde.parameter.DataOraInizio.DataOraInizio,&#xD;&#xA;&quot;BDE Data Fine&quot;, msgTernaBde.parameter.DataOraInizio.DataOraInizio,&#xD;&#xA;&quot;BDE Tipo&quot;, A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb,&#xD;&#xA;&quot;UP&quot;, UP,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="4bac3acc-b67e-4a0f-a39e-b0877942c626" ParentLink="ServiceBody_Statement" LowerBound="115.1" HigherBound="357.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="8968f3e3-e19f-46ad-8a5e-d404625ba0eb" ParentLink="ComplexStatement_Statement" LowerBound="120.1" HigherBound="136.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Messages" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="ea43be2a-4585-4e04-9739-36620049429f" ParentLink="ComplexStatement_Statement" LowerBound="123.1" HigherBound="125.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeLbToEaiBdeLb" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Nbdo Request" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="03448e1f-af5c-4b48-9c42-425d0cbb8656" ParentLink="Transform_OutputMessagePartRef" LowerBound="124.36" HigherBound="124.62">
                                <om:Property Name="MessageRef" Value="msgEaiBdeRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="04d2c377-befa-4dad-895c-a4aaa6230454" ParentLink="Transform_InputMessagePartRef" LowerBound="124.122" HigherBound="124.143">
                                <om:Property Name="MessageRef" Value="msgTernaBde" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="ef728685-37ea-483e-836c-6ccec90f82ff" ParentLink="ComplexStatement_Statement" LowerBound="125.1" HigherBound="129.1">
                            <om:Property Name="Expression" Value="msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;&#xD;&#xA;msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Codice BDE" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="dc99fd9c-238a-4394-b0b2-8d7ff14cb865" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="135.1">
                            <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter = &#xD;&#xA;Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0&quot;,&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure+BdeLbResponse&quot;&#xD;&#xA;);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="CreateResponse" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="c22ce931-52d1-4ad1-b449-3fa86e6f8268" ParentLink="Construct_MessageRef" LowerBound="121.31" HigherBound="121.47">
                            <om:Property Name="Ref" Value="msgEaiBdeRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="afe8325f-9d58-4698-b80e-69d479fae800" ParentLink="Construct_MessageRef" LowerBound="121.49" HigherBound="121.66">
                            <om:Property Name="Ref" Value="msgEaiBdeResponse" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="While" OID="e6f10719-ec82-41b5-a4b6-7e3957c0d698" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="192.1">
                        <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Insert Loop" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="VariableAssignment" OID="7b1c570a-f942-4019-8a6e-73bba559acda" ParentLink="ComplexStatement_Statement" LowerBound="139.1" HigherBound="141.1">
                            <om:Property Name="Expression" Value="insertRetryCount = insertRetryCount + 1;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="count++" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="42cfddb0-ec81-4930-a939-ba3884967dfd" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="143.1">
                            <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Take Time" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Send" OID="a3e9fae5-ef04-49a3-9b8f-c676bcb79510" ParentLink="ComplexStatement_Statement" LowerBound="143.1" HigherBound="145.1">
                            <om:Property Name="PortName" Value="sptEaiBdeLb" />
                            <om:Property Name="MessageName" Value="msgEaiBdeRequest" />
                            <om:Property Name="OperationName" Value="BdeLb" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Receive" OID="aeb4beb8-958c-4257-b138-c15c663a3385" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="147.1">
                            <om:Property Name="Activate" Value="False" />
                            <om:Property Name="PortName" Value="sptEaiBdeLb" />
                            <om:Property Name="MessageName" Value="msgEaiBdeResponse" />
                            <om:Property Name="OperationName" Value="BdeLb" />
                            <om:Property Name="OperationMessageName" Value="Response" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Receive" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="0d4ce1f9-a405-45cf-b269-b34035b065b7" ParentLink="ComplexStatement_Statement" LowerBound="147.1" HigherBound="153.1">
                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BAM Update" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Decision" OID="5b29e76b-7e23-4f42-a049-e7b6038985b5" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="191.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Process Result" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="DecisionBranch" OID="8d4ebe42-46aa-4d1a-9cfd-2f8514ad9cde" ParentLink="ReallyComplexStatement_Branch" LowerBound="154.25" HigherBound="161.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage &gt; 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="OK" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="1a5b86b7-3b1e-4f5c-8a02-c5cb33b7d142" ParentLink="ComplexStatement_Statement" LowerBound="156.1" HigherBound="160.1">
                                    <om:Property Name="Expression" Value="insertRetryMessage = System.String.Concat(&quot;BDE inserita dopo &quot;, insertRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Status Update" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="2aa0802b-8344-4fd2-80d8-60d4c19d039d" ParentLink="ReallyComplexStatement_Branch" LowerBound="161.30" HigherBound="173.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage == 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BDE Già Validata" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="3c58eb37-2072-455b-b029-1a2fb5bff621" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="172.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(&quot;BDE già validata.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="1674a2b6-3cd2-4dea-bdd5-61454ac1013e" ParentLink="ReallyComplexStatement_Branch" LowerBound="173.30" HigherBound="181.1">
                                <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Retry" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="72c4e692-fc5c-4394-9573-39d9fe0da107" ParentLink="ComplexStatement_Statement" LowerBound="175.1" HigherBound="178.1">
                                    <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Message" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Delay" OID="90a2ef87-01fc-458e-8df4-0d81e8958934" ParentLink="ComplexStatement_Statement" LowerBound="178.1" HigherBound="180.1">
                                    <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, insertRetryInterval);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Delay" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="a1294e71-eba0-4a76-9d3f-6d250af38f4f" ParentLink="ReallyComplexStatement_Branch">
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Else" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="6cd1333a-3963-40d2-86a6-d20c0f4e71b1" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="190.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;UP '{0}' non presente in NDUE.&quot;, msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="92819a59-4077-4958-834c-e3da5bb7f8fc" ParentLink="ComplexStatement_Statement" LowerBound="192.1" HigherBound="223.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BDE Already Submitted" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="33d2bfbe-0f48-47fe-81e2-78aaef4a5503" ParentLink="ReallyComplexStatement_Branch" LowerBound="193.21" HigherBound="196.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="8613e4f9-8929-4ee0-ba3d-15a6c66a317d" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Scope" OID="968828e1-45a0-4365-8163-b888fdf8545a" ParentLink="ComplexStatement_Statement" LowerBound="198.1" HigherBound="222.1">
                                <om:Property Name="InitializedTransactionType" Value="True" />
                                <om:Property Name="IsSynchronized" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Mail Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Catch" OID="120e8c73-3765-4ec0-b448-c317035a655e" ParentLink="Scope_Catch" LowerBound="216.1" HigherBound="220.1">
                                    <om:Property Name="ExceptionType" Value="General Exception" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Generic" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Construct" OID="fe91a5a7-5e7c-4ec1-ae5b-a3fbe0b1ad08" ParentLink="ComplexStatement_Statement" LowerBound="203.1" HigherBound="211.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Messages" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Transform" OID="71422b8b-8a79-4601-ac35-eca423d3c36d" ParentLink="ComplexStatement_Statement" LowerBound="206.1" HigherBound="208.1">
                                        <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeLbToEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Create Notification" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessagePartRef" OID="b0139205-29ec-45d8-a3f3-8a193e700038" ParentLink="Transform_OutputMessagePartRef" LowerBound="207.48" HigherBound="207.79">
                                            <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_6" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessagePartRef" OID="64d16219-837f-4e43-a40f-7401b2c5d044" ParentLink="Transform_InputMessagePartRef" LowerBound="207.149" HigherBound="207.170">
                                            <om:Property Name="MessageRef" Value="msgTernaBde" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_5" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="MessageAssignment" OID="7eb1dec7-daa8-4bf5-be5b-f34374cf1411" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="210.1">
                                        <om:Property Name="Expression" Value="msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="False" />
                                        <om:Property Name="Name" Value="File Name" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="MessageRef" OID="9285adb9-ef78-4cf6-8b6c-30923efc25f2" ParentLink="Construct_MessageRef" LowerBound="204.43" HigherBound="204.64">
                                        <om:Property Name="Ref" Value="msgEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Exec" OID="2c5a8e75-e298-4524-af31-103ba8092157" ParentLink="ComplexStatement_Statement" LowerBound="211.1" HigherBound="213.1">
                                    <om:Property Name="Invokee" Value="A2A.EAI.INT_BDE.Processes.prcBdeNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Start Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="Parameter" OID="6edfa431-8d04-485d-84d9-592113e8afc5" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="UP" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="40b784ee-8ba0-4887-b1df-4ed305383d46" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="tipoBde" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="711455db-cece-4868-a287-b1131ff9985e" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="resultCode" />
                                        <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="95f860fc-dd0d-4952-b2a6-7d1bc7ad5771" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="msgEaiBdeNotification" />
                                        <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="0d0e6880-d27f-4d77-95ef-eeb64c42ed2e" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="activityInstanceId" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="c2adf19e-8ab9-45bc-ae11-dd7b5d66ff14" ParentLink="ComplexStatement_Statement" LowerBound="223.1" HigherBound="324.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check Succeded" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="c0ceb178-e7cb-4d51-be84-93c960144fc1" ParentLink="ReallyComplexStatement_Branch" LowerBound="224.21" HigherBound="324.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="eed5ce2d-9d9b-41c7-b1a6-9295f357363f" ParentLink="ComplexStatement_Statement" LowerBound="226.1" HigherBound="232.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup Nbdo WS" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="857c52d6-8595-4db4-b03e-57c7a6865e64" ParentLink="ComplexStatement_Statement" LowerBound="229.1" HigherBound="231.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeLbToN2BdeValidazione" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Create Nbdo Ws" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="db54d79e-52f8-4cff-85ef-ef441e300141" ParentLink="Transform_OutputMessagePartRef" LowerBound="230.40" HigherBound="230.76">
                                        <om:Property Name="MessageRef" Value="msgN2BdeValidazioneRequest" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="4e7afa59-4470-47e4-bee1-f41f30734988" ParentLink="Transform_InputMessagePartRef" LowerBound="230.142" HigherBound="230.169">
                                        <om:Property Name="MessageRef" Value="msgEaiBdeResponse" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageRef" OID="6da7cdd8-f6ca-4ab6-810e-0628082e53fb" ParentLink="Construct_MessageRef" LowerBound="227.35" HigherBound="227.61">
                                    <om:Property Name="Ref" Value="msgN2BdeValidazioneRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="ddc0b762-dfd2-4af4-b887-a56312c9507c" ParentLink="ComplexStatement_Statement" LowerBound="232.1" HigherBound="323.1">
                                <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Validation Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="1dd33abf-dc62-4a13-8c15-a9608768df25" ParentLink="ComplexStatement_Statement" LowerBound="235.1" HigherBound="237.1">
                                    <om:Property Name="Expression" Value="validationRetryCount = validationRetryCount + 1;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Inc retryCount" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="32429e60-9d29-4533-bf0d-479e3c113989" ParentLink="ComplexStatement_Statement" LowerBound="237.1" HigherBound="239.1">
                                    <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Take Time" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Scope" OID="f620af85-5cf8-4392-9b30-f902aedaed70" ParentLink="ComplexStatement_Statement" LowerBound="239.1" HigherBound="294.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Catch" OID="7ed65d8b-a817-4240-8cdd-13e63b45a7ba" ParentLink="Scope_Catch" LowerBound="262.1" HigherBound="277.1">
                                        <om:Property Name="ExceptionName" Value="valSoapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="01281d29-eea6-4cda-9ac0-da875b554899" ParentLink="Catch_Statement" LowerBound="265.1" HigherBound="276.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="57ac4583-48e8-4aaa-ab4b-032910b008b5" ParentLink="Scope_Catch" LowerBound="277.1" HigherBound="292.1">
                                        <om:Property Name="ExceptionName" Value="valFaultExc" />
                                        <om:Property Name="ExceptionType" Value="sptN2BdeValidazione.ValidaBDE.Fault" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Fauld Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="bde796d5-9bcd-40c9-9f71-26d81ecea325" ParentLink="Catch_Statement" LowerBound="280.1" HigherBound="291.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="9236ee5e-669d-48aa-98b8-3a97dbd83cfe" ParentLink="ComplexStatement_Statement" LowerBound="244.1" HigherBound="246.1">
                                        <om:Property Name="PortName" Value="sptN2BdeValidazione" />
                                        <om:Property Name="MessageName" Value="msgN2BdeValidazioneRequest" />
                                        <om:Property Name="OperationName" Value="ValidaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="240da979-3f10-4cac-9298-b82a30fcb34c" ParentLink="ComplexStatement_Statement" LowerBound="246.1" HigherBound="248.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptN2BdeValidazione" />
                                        <om:Property Name="MessageName" Value="msgN2BdeValidazioneResponse" />
                                        <om:Property Name="OperationName" Value="ValidaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="2ff2c180-d950-43e7-9bc9-e879ad1b0895" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="259.1">
                                        <om:Property Name="Expression" Value="validationResult = msgN2BdeValidazioneResponse.parameter.ValidaBDEResult;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="fbcdaf23-e982-477d-bbf1-b1b948a6a36c" ParentLink="ComplexStatement_Statement" LowerBound="294.1" HigherBound="322.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation Result" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="b6375c38-f148-4778-8e84-fa158fdf9885" ParentLink="ReallyComplexStatement_Branch" LowerBound="295.29" HigherBound="302.1">
                                        <om:Property Name="Expression" Value="validationResult.StartsWith(&quot;OK&quot;, System.StringComparison.OrdinalIgnoreCase)" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="OK" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="80b8154d-5938-4afa-974d-95536d136cff" ParentLink="ComplexStatement_Statement" LowerBound="297.1" HigherBound="301.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;BDE Validata dopo &quot;, validationRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;validationRetryCount = 100;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Status Update" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="01f0543f-8868-452b-b716-adc0e991afc5" ParentLink="ReallyComplexStatement_Branch" LowerBound="302.34" HigherBound="310.1">
                                        <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Retry" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="30622fd2-7209-43b3-9d71-740ee85213be" ParentLink="ComplexStatement_Statement" LowerBound="304.1" HigherBound="307.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Delay" OID="5f211c07-ebb0-4d9b-b7bd-5611103e57e7" ParentLink="ComplexStatement_Statement" LowerBound="307.1" HigherBound="309.1">
                                            <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, validationRetryInterval);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Delay" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="fff47e88-e1a4-41a7-b5ff-77844442d6e9" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="a015ba9b-9630-481f-b1ad-85bbf5aecc9f" ParentLink="ComplexStatement_Statement" LowerBound="312.1" HigherBound="321.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;Num. di tentativi: &quot;, validationRetryCount.ToString(), &quot;. &quot;);&#xD;&#xA;&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;NBDO non ha validato la BDE ({0}). &quot;, validationResult));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Response Code" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="ce93f173-f2fb-43b4-905e-df8f8c086b75" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="93de2da4-d6ad-4193-ba19-5af348f65bbb" ParentLink="Scope_Catch" LowerBound="327.1" HigherBound="341.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="1f6d74f4-640c-4406-8025-22f14e55313a" ParentLink="Catch_Statement" LowerBound="330.1" HigherBound="340.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="49401b2e-3cd5-4588-9efc-25f589ab22b5" ParentLink="Scope_Catch" LowerBound="341.1" HigherBound="355.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="35fd5782-7f86-4728-b91e-cfbd17d734ad" ParentLink="Catch_Statement" LowerBound="344.1" HigherBound="354.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="2e27789d-f041-4671-be3e-d2386e067463" ParentLink="ServiceBody_Statement" LowerBound="357.1" HigherBound="422.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="aa4fe2b5-a724-484d-a8ad-42b882705966" ParentLink="ReallyComplexStatement_Branch" LowerBound="358.13" HigherBound="361.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="82ea3227-b344-4c7d-bf78-c53e9c69d14f" ParentLink="ReallyComplexStatement_Branch" LowerBound="361.18" HigherBound="392.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded With Warnings" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="8c65d9ca-23c7-440d-ba98-3f429f33b8ba" ParentLink="ComplexStatement_Statement" LowerBound="363.1" HigherBound="391.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="9bab9a95-680b-4e81-a345-3dea22d7fa92" ParentLink="ComplexStatement_Statement" LowerBound="368.1" HigherBound="387.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="f5df16d3-a49e-4eff-86c0-14edf6a2e6a9" ParentLink="Construct_MessageRef" LowerBound="369.35" HigherBound="369.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="e342dbcd-87ee-4e10-844c-a5d6dd15a143" ParentLink="ComplexStatement_Statement" LowerBound="371.1" HigherBound="386.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="9526ab4c-bcb9-43f6-a988-614c542e0fce" ParentLink="ComplexStatement_Statement" LowerBound="387.1" HigherBound="389.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="ebc03c05-9fc8-4e16-9d20-c3c5d29a0414" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="94780e4b-22f7-4072-85c6-365322b04f24" ParentLink="ComplexStatement_Statement" LowerBound="394.1" HigherBound="421.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="025d9cad-7413-4fb4-b8ec-801d2f8792ea" ParentLink="ComplexStatement_Statement" LowerBound="399.1" HigherBound="417.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="f8d44ed6-dffc-4b9a-bab1-363e454cb703" ParentLink="ComplexStatement_Statement" LowerBound="402.1" HigherBound="416.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="4c2b5afc-9bfb-4e53-917c-3a35077faa75" ParentLink="Construct_MessageRef" LowerBound="400.35" HigherBound="400.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="9cb8c775-b36b-4433-b1ff-41c4d2eda357" ParentLink="ComplexStatement_Statement" LowerBound="417.1" HigherBound="419.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="c4557eff-452d-4d0e-afd4-e221b7bcb31d" ParentLink="ServiceBody_Statement" LowerBound="422.1" HigherBound="429.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="90b9bd63-def4-4831-aedb-e54d01c03f43" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="24.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typTernaBde" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptTernaBdeLb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="64d69524-a35c-47f9-ba8d-110e5590cc9c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b2711953-d72c-4c1d-87a4-99875594e8fe" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="24.1" HigherBound="27.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="299" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="6df312a6-b08f-4d0a-be8e-571cc0f9c8cf" ParentLink="PortDeclaration_CLRAttribute" LowerBound="24.1" HigherBound="25.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c647eb90-0bed-4b5b-b920-0b2584547dad" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="27.1" HigherBound="30.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="56" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typEaiBdeLb" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiBdeLb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c348ed2a-1d74-4b08-96b3-805be93d7d3d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="27.1" HigherBound="28.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="75a9cc02-72a6-4134-addb-7509168d5499" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="30.1" HigherBound="33.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="185" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typN2BdeValidazione" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2BdeValidazione" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="6dbe49be-756b-47ec-94d5-77e660dfc680" ParentLink="PortDeclaration_CLRAttribute" LowerBound="30.1" HigherBound="31.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a7b7c412-9a5d-4edd-a718-e0959b73402d" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeLbRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7e1a053f-bb5d-4edd-b41b-7113efbb157b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure.BdeLb" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="497705a1-d79e-452a-8c3e-41e7151ab4c8" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeLbResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f442aa88-0ff9-4652-8848-343fd14f7dea" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure.BdeLbResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="594cc3ca-210f-44f2-a39a-abc45583277b" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiBdeLb" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="79745e1d-c9c3-453f-9070-4fc0595e60b9" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BdeLb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="ee73f02a-3c8e-4016-9ba4-cec4b8a0f499" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.35">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeLbRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="94f30437-02c6-4a5d-9ac0-77d729956622" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="16.37" HigherBound="16.60">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeLbResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    internal messagetype msgEaiBdeLbRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure.BdeLb parameter;
    };
    internal messagetype msgEaiBdeLbResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure.BdeLbResponse parameter;
    };
    internal porttype typEaiBdeLb
    {
        requestresponse BdeLb
        {
            msgEaiBdeLbRequestType, msgEaiBdeLbResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeLb
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typTernaBde rptTernaBdeLb;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiBdeLb sptEaiBdeLb;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typN2BdeValidazione sptN2BdeValidazione;
        message msgEaiBdeNotificationType msgEaiBdeNotification;
        message msgTernaBdeType msgTernaBde;
        message msgEaiBdeLbRequestType msgEaiBdeRequest;
        message msgEaiBdeLbResponseType msgEaiBdeResponse;
        message msgN2BdeValidazioneRequestType msgN2BdeValidazioneRequest;
        message msgN2BdeValidazioneResponseType msgN2BdeValidazioneResponse;
        message msgNotificationType msgNotification;
        System.Int32 insertMaxRetryCount;
        System.String insertRetryMessage;
        System.Int32 insertRetryInterval;
        System.Int32 insertRetryCount;
        System.String insertResult;
        System.String validationRetryMessage;
        System.Int32 validationRetryInterval;
        System.Int32 validationRetryCount;
        System.String validationResult;
        System.Int32 validationMaxRetryCount;
        System.String UP;
        System.String tipoBde;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFilePath;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String backupFileName;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("707953ba-b2a9-432d-a3b0-0f5c5c491f1e")]
            activate receive (rptTernaBdeLb.Receive, msgTernaBde);
            insertRetryMessage = "";
            insertResult = "";
            validationRetryMessage = "";
            validationResult = "";
            UP = "";
            tipoBde = "";
            originalFilePath = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            backupFileName = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0f7ea042-dc1f-4650-b793-66b201891b9c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); 
            originalFileName = System.IO.Path.GetFileName(originalFilePath);
            backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);
            
            validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationMaxRetryCount");
            validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationRetryInterval");
            validationRetryCount = 0;
            validationRetryMessage = System.String.Empty;
            
            insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertMaxRetryCount");
            insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertRetryInterval");
            insertRetryCount = 0;
            insertRetryMessage = System.String.Empty;
            
            UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;
            tipoBde = "Limitazione al Bilanciamento";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6f17198b-8c27-4d6a-a375-af44a5f0542d")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Inviato a N2", "NO",
            "BDE Codice", originalFileName,
            "BDE Data Inizio", msgTernaBde.parameter.DataOraInizio.DataOraInizio,
            "BDE Data Fine", msgTernaBde.parameter.DataOraInizio.DataOraInizio,
            "BDE Tipo", A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb,
            "UP", UP,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4bac3acc-b67e-4a0f-a39e-b0877942c626")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8968f3e3-e19f-46ad-8a5e-d404625ba0eb")]
                    construct msgEaiBdeRequest, msgEaiBdeResponse
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea43be2a-4585-4e04-9739-36620049429f")]
                        transform (msgEaiBdeRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeLbToEaiBdeLb (msgTernaBde.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ef728685-37ea-483e-836c-6ccec90f82ff")]
                        msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;
                        msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);
                        
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dc99fd9c-238a-4394-b0b2-8d7ff14cb865")]
                        msgEaiBdeResponse.parameter = 
                        Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(
                        "A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0",
                        "A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeLbTypedProcedure+BdeLbResponse"
                        );
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6f10719-ec82-41b5-a4b6-7e3957c0d698")]
                    while (insertRetryCount < insertMaxRetryCount)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7b1c570a-f942-4019-8a6e-73bba559acda")]
                        insertRetryCount = insertRetryCount + 1;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("42cfddb0-ec81-4930-a939-ba3884967dfd")]
                        dataInizioDb = System.DateTimeOffset.Now;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a3e9fae5-ef04-49a3-9b8f-c676bcb79510")]
                        send (sptEaiBdeLb.BdeLb, msgEaiBdeRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("aeb4beb8-958c-4257-b138-c15c663a3385")]
                        receive (sptEaiBdeLb.BdeLb, msgEaiBdeResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0d4ce1f9-a405-45cf-b269-b34035b065b7")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                        "Data Inizio DB", dataInizioDb,
                        "Data Fine DB", System.DateTimeOffset.Now,
                        "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b29e76b-7e23-4f42-a049-e7b6038985b5")]
                        if (msgEaiBdeResponse.parameter.CodeMessage > 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1a5b86b7-3b1e-4f5c-8a02-c5cb33b7d142")]
                            insertRetryMessage = System.String.Concat("BDE inserita dopo ", insertRetryCount.ToString(), " numero di tentativi. ");
                            
                            insertRetryCount = 100;
                        }
                        else if (msgEaiBdeResponse.parameter.CodeMessage == 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c58eb37-2072-455b-b029-1a2fb5bff621")]
                            errorMessage.Append("BDE già validata.");
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            
                            insertRetryCount = 100;
                        }
                        else if (insertRetryCount < insertMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("72c4e692-fc5c-4394-9573-39d9fe0da107")]
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("90a2ef87-01fc-458e-8df4-0d81e8958934")]
                            delay new System.TimeSpan(0, 0, insertRetryInterval);
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6cd1333a-3963-40d2-86a6-d20c0f4e71b1")]
                            errorMessage.Append(System.String.Format("UP '{0}' non presente in NDUE.", msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("92819a59-4077-4958-834c-e3da5bb7f8fc")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("968828e1-45a0-4365-8163-b888fdf8545a")]
                        scope
                        {
                            body
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("fe91a5a7-5e7c-4ec1-ae5b-a3fbe0b1ad08")]
                                construct msgEaiBdeNotification
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("71422b8b-8a79-4601-ac35-eca423d3c36d")]
                                    transform (msgEaiBdeNotification.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeLbToEaiBdeNotification (msgTernaBde.parameter);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7eb1dec7-daa8-4bf5-be5b-f34374cf1411")]
                                    msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("2c5a8e75-e298-4524-af31-103ba8092157")]
                                exec A2A.EAI.INT_BDE.Processes.prcBdeNotification (UP, tipoBde, resultCode, msgEaiBdeNotification, activityInstanceId);
                            }
                            exceptions
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("120e8c73-3765-4ec0-b448-c317035a655e")]
                                catch
                                {
                                }
                            }
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c2adf19e-8ab9-45bc-ae11-dd7b5d66ff14")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("eed5ce2d-9d9b-41c7-b1a6-9295f357363f")]
                        construct msgN2BdeValidazioneRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("857c52d6-8595-4db4-b03e-57c7a6865e64")]
                            transform (msgN2BdeValidazioneRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeLbToN2BdeValidazione (msgEaiBdeResponse.parameter);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ddc0b762-dfd2-4af4-b887-a56312c9507c")]
                        while (validationRetryCount < validationMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1dd33abf-dc62-4a13-8c15-a9608768df25")]
                            validationRetryCount = validationRetryCount + 1;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("32429e60-9d29-4533-bf0d-479e3c113989")]
                            dataInizioWs = System.DateTimeOffset.Now;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f620af85-5cf8-4392-9b30-f902aedaed70")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9236ee5e-669d-48aa-98b8-3a97dbd83cfe")]
                                    send (sptN2BdeValidazione.ValidaBDE, msgN2BdeValidazioneRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("240da979-3f10-4cac-9298-b82a30fcb34c")]
                                    receive (sptN2BdeValidazione.ValidaBDE, msgN2BdeValidazioneResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2ff2c180-d950-43e7-9bc9-e879ad1b0895")]
                                    validationResult = msgN2BdeValidazioneResponse.parameter.ValidaBDEResult;
                                    
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                    "Inviato a N2", "SI",
                                    "Esito WS", validationResult,
                                    "Data Inizio WS", dataInizioWs,
                                    "Data Fine WS", System.DateTimeOffset.Now,
                                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                    "Retry", validationRetryCount
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7ed65d8b-a817-4240-8cdd-13e63b45a7ba")]
                                    catch (System.Web.Services.Protocols.SoapException valSoapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("01281d29-eea6-4cda-9ac0-da875b554899")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("57ac4583-48e8-4aaa-ab4b-032910b008b5")]
                                    catch (sptN2BdeValidazione.ValidaBDE.Fault valFaultExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bde796d5-9bcd-40c9-9f71-26d81ecea325")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fbcdaf23-e982-477d-bbf1-b1b948a6a36c")]
                            if (validationResult.StartsWith("OK", System.StringComparison.OrdinalIgnoreCase))
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("80b8154d-5938-4afa-974d-95536d136cff")]
                                validationRetryMessage = System.String.Concat("BDE Validata dopo ", validationRetryCount.ToString(), " numero di tentativi. ");
                                
                                validationRetryCount = 100;
                            }
                            else if (validationRetryCount < validationMaxRetryCount)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("30622fd2-7209-43b3-9d71-740ee85213be")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f211c07-ebb0-4d9b-b7bd-5611103e57e7")]
                                delay new System.TimeSpan(0, 0, validationRetryInterval);
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("a015ba9b-9630-481f-b1ad-85bbf5aecc9f")]
                                validationRetryMessage = System.String.Concat("Num. di tentativi: ", validationRetryCount.ToString(), ". ");
                                
                                errorMessage.Append(System.String.Format("NBDO non ha validato la BDE ({0}). ", validationResult));
                                errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            }
                        }
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("93de2da4-d6ad-4193-ba19-5af348f65bbb")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1f6d74f4-640c-4406-8025-22f14e55313a")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("49401b2e-3cd5-4588-9efc-25f589ab22b5")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("35fd5782-7f86-4728-b91e-cfbd17d734ad")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2e27789d-f041-4671-be3e-d2386e067463")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("8c65d9ca-23c7-440d-ba98-3f429f33b8ba")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9bab9a95-680b-4e81-a345-3dea22d7fa92")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e342dbcd-87ee-4e10-844c-a5d6dd15a143")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9526ab4c-bcb9-43f6-a988-614c542e0fce")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("94780e4b-22f7-4072-85c6-365322b04f24")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("025d9cad-7413-4fb4-b8ec-801d2f8792ea")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f8d44ed6-dffc-4b9a-bab1-363e454cb703")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionLb;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9cb8c775-b36b-4433-b1ff-41c4d2eda357")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c4557eff-452d-4d0e-afd4-e221b7bcb31d")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

