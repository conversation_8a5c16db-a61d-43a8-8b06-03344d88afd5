﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="e71d53d7-21d2-48df-8923-899f43c6b5a8" LowerBound="1.1" HigherBound="167.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="670876cd-ed61-4f2a-8417-84c68e4d6ef5" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEmailBde" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="487fb79b-a298-4382-b8d5-ab03b1b24099" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="37497e64-5a1e-4e48-a0fb-a5a0c7ddd2f7" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.25">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEmailType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="b5794df4-dddd-487c-9738-6739ad78a36e" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiBdeGetEmailAddr" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="4fc0e527-2ed6-42f3-b0d6-6896b8ef0c97" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BdeGetEmailAddr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="00ac56ab-b251-4bf5-9b66-5cde07de02e2" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.45">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeGetEmailAddrRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="db1f8493-f5d2-41dd-ab14-e57ecfc821b3" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.47" HigherBound="35.80">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeGetEmailAddrResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="ff8c5437-5bfb-42e5-9818-ac3eb24b969e" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptMailBdeType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="39deab67-5e1b-4a5a-a5b2-acca11c0c471" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptMailBde" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="843970b0-e908-43fe-b6ac-b9ed117f2d68" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgMailSendType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="70328e70-953c-4d72-8ab5-f4421906fdee" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.30" HigherBound="42.53">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgMailSendResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0d169386-b5f0-4a90-8f2c-6dba979ddfe4" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEmailType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="42a12825-4aad-40f5-b1e3-1fb285ae33fe" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Services.XlangCustomFormatters.RawString" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Body" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="841efb91-291c-4b5a-a928-950325bb4a29" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeGetEmailAddrRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="198c2ff1-84fd-4dc5-b6a2-25dd0aab0ea1" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeGetEmailAddrTypedProcedure.BdeGetEmailAddr" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="665bdc5b-d263-4a24-8fb8-865fa31f2725" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeGetEmailAddrResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a7fc3fd8-d577-4e74-9752-013e4e995672" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeGetEmailAddrTypedProcedure.BdeGetEmailAddrResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b9df54cf-de1f-4d21-943c-b39158e23e86" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgMailSendType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e315c66d-dad7-4e75-a3c5-b315ce8ecda7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.Common.Shared.Schemas.MailSend" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="aab322f5-33e9-4529-92bb-b835da8eff47" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgMailSendResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f7647d88-53ed-45f8-9b27-57365880d6d4" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="System.Xml.XmlDocument" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="5719de2e-1496-4a04-a9e9-db3877a32cb9" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="166.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeNotification" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="21abda28-93a3-452e-853c-81babede1870" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="subject" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3c25b94b-333e-4720-98e3-2969d257432c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="emailBody" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="082df0c9-64f3-44e3-826d-7e062f8facaa" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="emailAddress" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5762f71f-cab1-4334-9f99-6a496248b3ef" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEmailType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEmail" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="ba0efc44-4c05-4405-b18d-fd7f47dd8ff5" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeGetEmailAddrRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeGetEmailAddrRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b5b05185-784a-4830-9b77-ce3c06dbf423" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeGetEmailAddrResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeGetEmailAddrResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="60c0c107-c942-415e-bf66-7a126fdc913c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="86ebe3a1-ff11-4603-be4d-e5be020fb9cb" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgMailSendType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgMailSend" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="640510c6-5bd2-43ba-af26-e7ade6a0216d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgMailSendResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgMailSendResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="6eea017b-97f9-472e-bf4b-4c4f635c4f58" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="VariableDeclaration" OID="057aa1ac-9d8b-469c-b2ac-8775591e0a5e" ParentLink="ServiceBody_Declaration" LowerBound="66.15" HigherBound="66.31">
                    <om:Property Name="UseDefaultConstructor" Value="False" />
                    <om:Property Name="Type" Value="System.String" />
                    <om:Property Name="ParamDirection" Value="In" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="UP" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableDeclaration" OID="a5a9d5b8-205b-43cf-8f2a-21f776524f80" ParentLink="ServiceBody_Declaration" LowerBound="66.33" HigherBound="66.54">
                    <om:Property Name="UseDefaultConstructor" Value="False" />
                    <om:Property Name="Type" Value="System.String" />
                    <om:Property Name="ParamDirection" Value="In" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="TipoBde" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableDeclaration" OID="7d977a42-299a-4908-a7f0-057cb835d20c" ParentLink="ServiceBody_Declaration" LowerBound="66.56" HigherBound="66.117">
                    <om:Property Name="UseDefaultConstructor" Value="False" />
                    <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                    <om:Property Name="ParamDirection" Value="In" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="resultCode" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageDeclaration" OID="dc3d400f-de7d-491b-b5e6-451f5f46602e" ParentLink="ServiceBody_Declaration" LowerBound="66.119" HigherBound="66.174">
                    <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                    <om:Property Name="ParamDirection" Value="In" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="msgEaiBdeNotification" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableDeclaration" OID="33197521-dc69-4aff-860c-5b15b6b74ae8" ParentLink="ServiceBody_Declaration" LowerBound="66.176" HigherBound="66.206">
                    <om:Property Name="UseDefaultConstructor" Value="False" />
                    <om:Property Name="Type" Value="System.String" />
                    <om:Property Name="ParamDirection" Value="In" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="callerInstanceId" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="53713758-26c3-43a6-99f7-3f26820a53c5" ParentLink="ServiceBody_Statement" LowerBound="71.1" HigherBound="164.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="92380718-f79f-490d-bee5-f3bbdb7ba30e" ParentLink="ComplexStatement_Statement" LowerBound="76.1" HigherBound="84.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Email Addr Request" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="ebd9b413-2357-4323-876c-9095627f202f" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="81.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeNotificationToEaiBdeGetEmailAddr" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Email Addr Request" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="55f0d167-8a88-4cbf-8501-1aed0819a57c" ParentLink="Transform_InputMessagePartRef" LowerBound="80.152" HigherBound="80.183">
                                <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="02e9e880-fc1b-45dd-aea1-53df5b986234" ParentLink="Transform_OutputMessagePartRef" LowerBound="80.36" HigherBound="80.74">
                                <om:Property Name="MessageRef" Value="msgEaiBdeGetEmailAddrRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="3af9e94b-b15a-443e-b168-993b996d3924" ParentLink="ComplexStatement_Statement" LowerBound="81.1" HigherBound="83.1">
                            <om:Property Name="Expression" Value="msgEaiBdeGetEmailAddrRequest.parameter.Up_BDE = UP;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="UP" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="783ec90e-a969-4c40-a941-4e1c6e7926b0" ParentLink="Construct_MessageRef" LowerBound="77.31" HigherBound="77.59">
                            <om:Property Name="Ref" Value="msgEaiBdeGetEmailAddrRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="3b310b34-38ff-451c-8e87-68502e5e0c87" ParentLink="ComplexStatement_Statement" LowerBound="84.1" HigherBound="86.1">
                        <om:Property Name="PortName" Value="sptEaiBdeGetEmailAddr" />
                        <om:Property Name="MessageName" Value="msgEaiBdeGetEmailAddrRequest" />
                        <om:Property Name="OperationName" Value="BdeGetEmailAddr" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="29d3650e-2dcd-4aac-880f-4b6ccd25009c" ParentLink="ComplexStatement_Statement" LowerBound="86.1" HigherBound="88.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptEaiBdeGetEmailAddr" />
                        <om:Property Name="MessageName" Value="msgEaiBdeGetEmailAddrResponse" />
                        <om:Property Name="OperationName" Value="BdeGetEmailAddr" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="ebefe31a-6ff8-4c0b-98fe-ccb166cb3405" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                        <om:Property Name="Expression" Value="emailAddress = msgEaiBdeGetEmailAddrResponse.parameter.EmailAddress;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Email Address" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Decision" OID="a6581a3b-cc5b-4e38-aa6a-88cd5f43a2a3" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="115.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BDE Transaction Result" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="036be0ed-5ce0-4d9c-b713-82aad3827677" ParentLink="ReallyComplexStatement_Branch" LowerBound="91.21" HigherBound="98.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;&amp;&amp;&#xD;&#xA;TipoBde != &quot;Comando di Variazione Tensione&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Succeded" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="fa7f39b5-321d-46ff-8f57-c7c5fec99a1f" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="97.1">
                                <om:Property Name="Expression" Value="subject = System.String.Format(&quot;BDE - {0} - {1} - Inoltrata a NDUE&quot;, UP, TipoBde);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Subject" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="89cc3bdc-fb22-47f9-ae08-30cb049a238b" ParentLink="ReallyComplexStatement_Branch" LowerBound="98.26" HigherBound="103.1">
                            <om:Property Name="Expression" Value=" resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Warning" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="7db19106-b8be-4b11-8669-565d1be7a15b" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="102.1">
                                <om:Property Name="Expression" Value="subject = System.String.Format(&quot;BDE - {0} - {1} - In attesa di invio verso NDUE&quot;, UP, TipoBde);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Subject" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="3b50a358-b75a-4caa-99d7-7717e60f9a73" ParentLink="ReallyComplexStatement_Branch" LowerBound="103.26" HigherBound="110.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;&amp;&amp;&#xD;&#xA;TipoBde == &quot;Comando di Variazione Tensione&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BDE VQ" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="678bd852-5e8e-4e0d-ac92-38d9f2b0c499" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                                <om:Property Name="Expression" Value="subject = System.String.Format(&quot;BDE - {0} - {1} &quot;, UP, TipoBde);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Subject" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="d8d9725d-1bfa-4dc4-9144-ee8380fea4c8" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="2db55310-7bf8-4e63-bdf5-97059c45184f" ParentLink="ComplexStatement_Statement" LowerBound="112.1" HigherBound="114.1">
                                <om:Property Name="Expression" Value="subject = System.String.Format(&quot;BDE - {0} - {1} - Non inoltrata a NDUE&quot;, UP, TipoBde);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Subject" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="aefedc87-9df2-4e6e-a292-3cd3d26656d8" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="129.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Mail" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="a40af34d-09a6-4ceb-8613-d880454e4ec7" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="120.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeNotificationToMailSend" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup msgMailSend" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="bf269d0a-e69f-4111-9bae-889b8a9721bb" ParentLink="Transform_InputMessagePartRef" LowerBound="119.125" HigherBound="119.156">
                                <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="1c1b329d-47c6-4683-8643-5d989ba86b2b" ParentLink="Transform_OutputMessagePartRef" LowerBound="119.36" HigherBound="119.57">
                                <om:Property Name="MessageRef" Value="msgMailSend" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="b416256c-90cc-48d4-898a-e4983510a2f4" ParentLink="ComplexStatement_Statement" LowerBound="120.1" HigherBound="128.1">
                            <om:Property Name="Expression" Value="emailBody = msgEaiBdeNotification.parameter;&#xD;&#xA;&#xD;&#xA;msgMailSend.parameter.htmlContent = emailBody.InnerXml;&#xD;&#xA;msgMailSend.parameter.subject = subject;&#xD;&#xA;msgMailSend.parameter.mailFrom = &quot;<EMAIL>&quot;;&#xD;&#xA;msgMailSend.parameter.mailTo = emailAddress;&#xD;&#xA; " />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="MessageAssignment_1" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="0b3e732e-adc3-44d7-ba11-a2e86157ea15" ParentLink="Construct_MessageRef" LowerBound="116.31" HigherBound="116.42">
                            <om:Property Name="Ref" Value="msgMailSend" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="81a03e28-2efe-4db3-91cc-2237d70d31b0" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="131.1">
                        <om:Property Name="PortName" Value="sptMailBde" />
                        <om:Property Name="MessageName" Value="msgMailSend" />
                        <om:Property Name="OperationName" Value="sptMailBde" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="721986df-1c14-4893-a5bb-7389fcddbd4b" ParentLink="ComplexStatement_Statement" LowerBound="131.1" HigherBound="133.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptMailBde" />
                        <om:Property Name="MessageName" Value="msgMailSendResponse" />
                        <om:Property Name="OperationName" Value="sptMailBde" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="ea428b0f-c98d-410b-a2dd-0100473bdaa1" ParentLink="ComplexStatement_Statement" LowerBound="133.1" HigherBound="135.1">
                        <om:Property Name="Expression" Value="A2A.EAI.INT_BDE.Services.ProcessServices.AddBdeRelationShip(msgMailSendResponse.parameter, callerInstanceId);" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Add Relationship" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="0a5072c7-44b9-4a04-8332-f88cab3c3b4c" ParentLink="Scope_Catch" LowerBound="138.1" HigherBound="162.1">
                        <om:Property Name="ExceptionType" Value="General Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="General Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Construct" OID="678ccdd3-6e32-43fa-a34d-2e2f00ab6a00" ParentLink="Catch_Statement" LowerBound="141.1" HigherBound="159.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Failure Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessageAssignment" OID="bf16635f-a1c9-416b-8de2-7a0f608f7769" ParentLink="ComplexStatement_Statement" LowerBound="144.1" HigherBound="158.1">
                                <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = &quot;Notifica BDE&quot;;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(Microsys.EAI.Framework.Services.FaultCategory.Code.System);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageText = &quot;Errore durante la notifica dell'invio della BDE&quot;;&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="False" />
                                <om:Property Name="Name" Value="Setup Notification" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessageRef" OID="f6dea3b0-a2a0-415c-9a21-4f62be5a4748" ParentLink="Construct_MessageRef" LowerBound="142.35" HigherBound="142.50">
                                <om:Property Name="Ref" Value="msgNotification" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Send" OID="4dfa42a4-85d7-466b-b478-5f9d5b34ee5e" ParentLink="Catch_Statement" LowerBound="159.1" HigherBound="161.1">
                            <om:Property Name="PortName" Value="sptNotification" />
                            <om:Property Name="MessageName" Value="msgNotification" />
                            <om:Property Name="OperationName" Value="Send" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send Notification" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="16196afc-a42f-41db-932a-5129b88be064" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="51.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="20" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typEaiBdeGetEmailAddr" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiBdeGetEmailAddr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="f01e1294-c014-4232-bc38-d6ae47c5003a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="eccde04e-49c8-4011-aa13-0902fb0a3ea0" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="51.1" HigherBound="54.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="77" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="e67f866a-e561-4e9f-b352-bec76a2f64c5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="51.1" HigherBound="52.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a4adda4c-a008-4869-8d0a-8fa2c1a9fab2" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="54.1" HigherBound="57.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="58" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.sptMailBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptMailBde" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="b93fd017-e8a5-40d2-b4c9-4f843516e09d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="54.1" HigherBound="55.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    internal messagetype msgEmailType
    {
        body Microsys.EAI.Framework.Services.XlangCustomFormatters.RawString Body;
    };
    internal messagetype msgEaiBdeGetEmailAddrRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeGetEmailAddrTypedProcedure.BdeGetEmailAddr parameter;
    };
    internal messagetype msgEaiBdeGetEmailAddrResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeGetEmailAddrTypedProcedure.BdeGetEmailAddrResponse parameter;
    };
    internal messagetype msgMailSendType
    {
        body A2A.EAI.Common.Shared.Schemas.MailSend parameter;
    };
    internal messagetype msgMailSendResponseType
    {
        body System.Xml.XmlDocument parameter;
    };
    internal porttype typEmailBde
    {
        oneway Send
        {
            msgEmailType
        };
    };
    internal porttype typEaiBdeGetEmailAddr
    {
        requestresponse BdeGetEmailAddr
        {
            msgEaiBdeGetEmailAddrRequestType, msgEaiBdeGetEmailAddrResponseType
        };
    };
    internal porttype sptMailBdeType
    {
        requestresponse sptMailBde
        {
            msgMailSendType, msgMailSendResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeNotification
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiBdeGetEmailAddr sptEaiBdeGetEmailAddr;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptMailBdeType sptMailBde;
        message msgEmailType msgEmail;
        message msgEaiBdeGetEmailAddrRequestType msgEaiBdeGetEmailAddrRequest;
        message msgEaiBdeGetEmailAddrResponseType msgEaiBdeGetEmailAddrResponse;
        message msgNotificationType msgNotification;
        message msgMailSendType msgMailSend;
        message msgMailSendResponseType msgMailSendResponse;
        System.String subject;
        System.Xml.XmlDocument emailBody;
        System.String emailAddress;
        body (System.String UP, System.String TipoBde, Microsys.EAI.Framework.Services.ProcessResult.Code resultCode, message msgEaiBdeNotificationType msgEaiBdeNotification, System.String callerInstanceId)
        {
            subject = "";
            emailBody = new System.Xml.XmlDocument();
            emailAddress = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("53713758-26c3-43a6-99f7-3f26820a53c5")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("92380718-f79f-490d-bee5-f3bbdb7ba30e")]
                    construct msgEaiBdeGetEmailAddrRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebd9b413-2357-4323-876c-9095627f202f")]
                        transform (msgEaiBdeGetEmailAddrRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeNotificationToEaiBdeGetEmailAddr (msgEaiBdeNotification.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3af9e94b-b15a-443e-b168-993b996d3924")]
                        msgEaiBdeGetEmailAddrRequest.parameter.Up_BDE = UP;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b310b34-38ff-451c-8e87-68502e5e0c87")]
                    send (sptEaiBdeGetEmailAddr.BdeGetEmailAddr, msgEaiBdeGetEmailAddrRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("29d3650e-2dcd-4aac-880f-4b6ccd25009c")]
                    receive (sptEaiBdeGetEmailAddr.BdeGetEmailAddr, msgEaiBdeGetEmailAddrResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebefe31a-6ff8-4c0b-98fe-ccb166cb3405")]
                    emailAddress = msgEaiBdeGetEmailAddrResponse.parameter.EmailAddress;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6581a3b-cc5b-4e38-aa6a-88cd5f43a2a3")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded
                        &&
                        TipoBde != "Comando di Variazione Tensione")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fa7f39b5-321d-46ff-8f57-c7c5fec99a1f")]
                        subject = System.String.Format("BDE - {0} - {1} - Inoltrata a NDUE", UP, TipoBde);
                    }
                    else if ( resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7db19106-b8be-4b11-8669-565d1be7a15b")]
                        subject = System.String.Format("BDE - {0} - {1} - In attesa di invio verso NDUE", UP, TipoBde);
                    }
                    else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded
                        &&
                        TipoBde == "Comando di Variazione Tensione")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("678bd852-5e8e-4e0d-ac92-38d9f2b0c499")]
                        subject = System.String.Format("BDE - {0} - {1} ", UP, TipoBde);
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2db55310-7bf8-4e63-bdf5-97059c45184f")]
                        subject = System.String.Format("BDE - {0} - {1} - Non inoltrata a NDUE", UP, TipoBde);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("aefedc87-9df2-4e6e-a292-3cd3d26656d8")]
                    construct msgMailSend
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a40af34d-09a6-4ceb-8613-d880454e4ec7")]
                        transform (msgMailSend.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeNotificationToMailSend (msgEaiBdeNotification.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b416256c-90cc-48d4-898a-e4983510a2f4")]
                        emailBody = msgEaiBdeNotification.parameter;
                        
                        msgMailSend.parameter.htmlContent = emailBody.InnerXml;
                        msgMailSend.parameter.subject = subject;
                        msgMailSend.parameter.mailFrom = "<EMAIL>";
                        msgMailSend.parameter.mailTo = emailAddress;
                         
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("81a03e28-2efe-4db3-91cc-2237d70d31b0")]
                    send (sptMailBde.sptMailBde, msgMailSend);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("721986df-1c14-4893-a5bb-7389fcddbd4b")]
                    receive (sptMailBde.sptMailBde, msgMailSendResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea428b0f-c98d-410b-a2dd-0100473bdaa1")]
                    A2A.EAI.INT_BDE.Services.ProcessServices.AddBdeRelationShip(msgMailSendResponse.parameter, callerInstanceId);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0a5072c7-44b9-4a04-8332-f88cab3c3b4c")]
                    catch
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("678ccdd3-6e32-43fa-a34d-2e2f00ab6a00")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bf16635f-a1c9-416b-8de2-7a0f608f7769")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = "Notifica BDE";
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(Microsys.EAI.Framework.Services.FaultCategory.Code.System);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageText = "Errore durante la notifica dell'invio della BDE";
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4dfa42a4-85d7-466b-b478-5f9d5b34ee5e")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
        }
    }
}

