﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="bdca075d-1170-463e-b9c9-dbc37f80809d" LowerBound="1.1" HigherBound="419.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="4ec9f937-9a70-4efa-aa01-244b1bf83588" ParentLink="Module_ServiceDeclaration" LowerBound="46.1" HigherBound="418.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeRc" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="07dbaae2-8375-4d9a-98df-31db8686af3a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d110fa8c-1d4c-453f-a2e6-4e6b40044080" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="71.1" HigherBound="72.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78c7fd5c-fe60-46c4-afef-5b066915deb1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="72.1" HigherBound="73.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3482371a-738b-4c95-a829-250559fd5053" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="73.1" HigherBound="74.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="437ea1e0-d315-4002-b537-64a7d38871c8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="74.1" HigherBound="75.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426b6a7c-17fb-4945-96dc-0026769a61d7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="75.1" HigherBound="76.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="UP" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8e2d43ae-f2eb-4465-8fe1-c6a3f2a2f4f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="tipoBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f1a0428c-d1cb-43cd-832b-a1aac6653c6d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="298bd104-a85c-43ad-8af3-d8dc0c35e8e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="78.1" HigherBound="79.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f07553f4-9218-43d4-8d73-cf265ad6a122" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="79.1" HigherBound="80.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b475c2-d1e1-461d-a3ce-31a06c9b5123" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="80.1" HigherBound="81.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7658a2c6-aee5-4d67-a981-fdb73792e87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="81.1" HigherBound="82.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f8905c1-6bb0-411d-b62e-1dcbbdb2167a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="82.1" HigherBound="83.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9a95c256-fb02-4c88-ae27-e37142bac6d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="83.1" HigherBound="84.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bd76a109-4266-43ae-b3db-a023402f0d2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="84.1" HigherBound="85.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78ad8ea2-d6b7-43a5-9099-5213a82f91dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="85.1" HigherBound="86.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f58e4c85-4768-415d-8808-3efbed7d576b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="86.1" HigherBound="87.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="backupFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b3f46dd-785b-4520-af09-a5765d883054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="87.1" HigherBound="88.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3d742c18-539c-495c-92fd-fccdc5f1bd01" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="796648e1-54dc-492c-87e9-b0a642f2e08c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgTernaBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="160b8573-cc65-4f40-9ba6-9364d20d2340" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeRcRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4884cc52-01c0-4473-ab72-b2446928c8d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeRcResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5fb41824-91cd-41f5-b752-6d49033f5642" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeAnnullamentoRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeAnnullamentoRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7d00a780-c838-437f-8518-a01a6625836d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeAnnullamentoResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeAnnullamentoResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6ae76a8b-e47a-4683-bdcd-3c7755173a80" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="39299ac9-37e3-42d4-ac92-c17454c66efd" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgGenericRetryCollectorType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgGenericRetryCollector" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="caf8e529-2bf8-4ac4-b199-76c2ae66c46d" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="13726b48-9a6d-4388-9771-cc844c289103" ParentLink="ServiceBody_Statement" LowerBound="90.1" HigherBound="101.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptTernaBdeRc" />
                    <om:Property Name="MessageName" Value="msgTernaBde" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d1615d42-6c8c-401d-a3ea-2d88979ca0b8" ParentLink="ServiceBody_Statement" LowerBound="101.1" HigherBound="124.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); &#xD;&#xA;originalFileName = System.IO.Path.GetFileName(originalFilePath);&#xD;&#xA;backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);&#xD;&#xA;&#xD;&#xA;validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationMaxRetryCount&quot;);&#xD;&#xA;validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationRetryInterval&quot;);&#xD;&#xA;validationRetryCount = 0;&#xD;&#xA;validationRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;&#xD;&#xA;tipoBde = &quot;Revoca Comando&quot;;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="98a5f27c-285b-4b43-aaa4-11e51073cb03" ParentLink="ServiceBody_Statement" LowerBound="124.1" HigherBound="138.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;BDE Codice&quot;, originalFileName,&#xD;&#xA;&quot;BDE Data Inizio&quot;, msgTernaBde.parameter.DataOraInizioRevocaComando.DataOraInizioRevocaComando,&#xD;&#xA;&quot;BDE Data Fine&quot;, msgTernaBde.parameter.DataOraFineRevocaComando.DataOraFineRevocaComando,&#xD;&#xA;&quot;BDE Stato Comando&quot;, msgTernaBde.parameter.SequenzaComando.SequenzaComando,&#xD;&#xA;&quot;BDE Tipo&quot;, A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc,&#xD;&#xA;&quot;UP&quot;, UP,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="737b4e3f-10a0-4666-a1f3-b4750299feac" ParentLink="ServiceBody_Statement" LowerBound="138.1" HigherBound="375.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="5bb311ba-db5c-4bd9-a614-88f3a41666a4" ParentLink="ComplexStatement_Statement" LowerBound="143.1" HigherBound="153.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Messages" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="a5ddb911-961d-4479-a670-fe0799d80e8b" ParentLink="Construct_MessageRef" LowerBound="144.31" HigherBound="144.47">
                            <om:Property Name="Ref" Value="msgEaiBdeRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="008bc12a-1c53-4017-ac3e-ae23f1771c35" ParentLink="ComplexStatement_Statement" LowerBound="146.1" HigherBound="148.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeRcToEaiBdeRc" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Nbdo Request" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="f861716d-3e74-40e6-8a24-6c788e339ab0" ParentLink="Transform_OutputMessagePartRef" LowerBound="147.36" HigherBound="147.62">
                                <om:Property Name="MessageRef" Value="msgEaiBdeRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="c040b869-890d-444c-b1b8-bc648d2a18ed" ParentLink="Transform_InputMessagePartRef" LowerBound="147.122" HigherBound="147.143">
                                <om:Property Name="MessageRef" Value="msgTernaBde" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="83018498-d61c-4f3a-986d-c59d1ce3fe99" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="152.1">
                            <om:Property Name="Expression" Value="msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;&#xD;&#xA;msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Codice BDE" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="e6daa650-598e-4cf0-9489-ddec874e0ee4" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="155.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="91015275-1e66-41c7-91ad-3c8ad68871ad" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="157.1">
                        <om:Property Name="PortName" Value="sptEaiBdeRc" />
                        <om:Property Name="MessageName" Value="msgEaiBdeRequest" />
                        <om:Property Name="OperationName" Value="BdeRc" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="5c8a4185-a866-4411-9483-ac80be35aeb8" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="159.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptEaiBdeRc" />
                        <om:Property Name="MessageName" Value="msgEaiBdeResponse" />
                        <om:Property Name="OperationName" Value="BdeRc" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="0bc8121b-0511-4351-a3e9-022f8729f120" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="165.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="192b3422-407c-474a-ab19-4defaa7e0e0c" ParentLink="ComplexStatement_Statement" LowerBound="165.1" HigherBound="189.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Process Result" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="648b4754-b5d9-44eb-9080-67958648ed3b" ParentLink="ReallyComplexStatement_Branch" LowerBound="166.21" HigherBound="169.1">
                            <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage &gt; 0" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="OK" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="755b5ae9-ecbe-48b7-b1d8-46c7b0dd1c1d" ParentLink="ReallyComplexStatement_Branch" LowerBound="169.26" HigherBound="179.1">
                            <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage == -1" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BDE non Presente" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="97d43d6c-3ef3-4eee-8c1b-b8e597470c5a" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="178.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;BDE di revoca in attesa.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Response Code" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="2e09e81d-20da-4e7a-a60a-827d70f12947" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="b283565e-7ed6-4bae-a672-da5a0db78679" ParentLink="ComplexStatement_Statement" LowerBound="181.1" HigherBound="188.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(msgEaiBdeResponse.parameter.ErrorMessage);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Response Code" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="e087eb22-11b0-40e4-a9c6-56977dc8051d" ParentLink="ComplexStatement_Statement" LowerBound="189.1" HigherBound="220.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BDE Already Submitted" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="be2583f9-64c5-409e-ac3c-b634907220ca" ParentLink="ReallyComplexStatement_Branch" LowerBound="190.21" HigherBound="193.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="704dde28-d7cb-4fa9-a0f2-9dec0023525f" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Scope" OID="3c3e7352-8d94-486e-a040-cb951a586045" ParentLink="ComplexStatement_Statement" LowerBound="195.1" HigherBound="219.1">
                                <om:Property Name="InitializedTransactionType" Value="True" />
                                <om:Property Name="IsSynchronized" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Mail Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Construct" OID="4ca591dc-0e92-4a18-a327-309b569173f3" ParentLink="ComplexStatement_Statement" LowerBound="200.1" HigherBound="208.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Messages" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessageRef" OID="40da7403-adba-4924-84c4-751eb3977bd9" ParentLink="Construct_MessageRef" LowerBound="201.43" HigherBound="201.64">
                                        <om:Property Name="Ref" Value="msgEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Transform" OID="466cbf8d-cd27-4b24-9ac8-9f33d362fca8" ParentLink="ComplexStatement_Statement" LowerBound="203.1" HigherBound="205.1">
                                        <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeRcToEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Create Notification" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessagePartRef" OID="9d4b21d3-db76-4204-adbb-b07317ef862c" ParentLink="Transform_InputMessagePartRef" LowerBound="204.149" HigherBound="204.170">
                                            <om:Property Name="MessageRef" Value="msgTernaBde" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_5" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessagePartRef" OID="c9b9db39-a934-4b9c-a4c3-9684df84813e" ParentLink="Transform_OutputMessagePartRef" LowerBound="204.48" HigherBound="204.79">
                                            <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_6" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="MessageAssignment" OID="b94178d7-46f4-48cd-b46c-55b694aeef94" ParentLink="ComplexStatement_Statement" LowerBound="205.1" HigherBound="207.1">
                                        <om:Property Name="Expression" Value="msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="False" />
                                        <om:Property Name="Name" Value="File Name" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Exec" OID="7edaf03b-ec62-40f8-b0a8-65d75f7ca7cb" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="210.1">
                                    <om:Property Name="Invokee" Value="A2A.EAI.INT_BDE.Processes.prcBdeNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Start Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="Parameter" OID="501e8605-03d7-49fa-96b4-d4920288f0fa" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="UP" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="3adcabe0-2827-4c62-83fb-3c609211c176" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="tipoBde" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="d27aae9a-1b7d-4abb-8382-d89b2e52a772" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="resultCode" />
                                        <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="74282299-1123-427f-90d3-d95b2ba405b7" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="msgEaiBdeNotification" />
                                        <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="249a5693-aa01-4542-9c32-0aa8055e9a46" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="activityInstanceId" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Catch" OID="12fcfff4-7ca8-4cf6-94dc-44224867e58f" ParentLink="Scope_Catch" LowerBound="213.1" HigherBound="217.1">
                                    <om:Property Name="ExceptionType" Value="General Exception" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Generic" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="8fae6438-0e23-4330-86dc-8846d8d9d2b0" ParentLink="ComplexStatement_Statement" LowerBound="220.1" HigherBound="342.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="43d1dcd7-3bd0-41b8-b994-9a375987d0d0" ParentLink="ReallyComplexStatement_Branch" LowerBound="221.21" HigherBound="321.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Succeded" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="5b67acc7-4db3-4bae-ab1b-ee3496a01433" ParentLink="ComplexStatement_Statement" LowerBound="223.1" HigherBound="229.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup Nbdo WS" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="e0233ddd-c4ca-48d5-b55e-6a843d90da39" ParentLink="Construct_MessageRef" LowerBound="224.35" HigherBound="224.62">
                                    <om:Property Name="Ref" Value="msgN2BdeAnnullamentoRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="bfc86ae3-57b4-4916-82e0-76a9451bef99" ParentLink="ComplexStatement_Statement" LowerBound="226.1" HigherBound="228.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeRcToN2BdeAnnullamento" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Create Nbdo Ws" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="6aa69053-3a72-443b-a15c-24aa426dae0c" ParentLink="Transform_OutputMessagePartRef" LowerBound="227.40" HigherBound="227.77">
                                        <om:Property Name="MessageRef" Value="msgN2BdeAnnullamentoRequest" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="8a966d76-036b-46c1-afc3-627cc9c507a4" ParentLink="Transform_InputMessagePartRef" LowerBound="227.144" HigherBound="227.171">
                                        <om:Property Name="MessageRef" Value="msgEaiBdeResponse" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="d9e299bc-5452-4338-a371-b5bda106bf90" ParentLink="ComplexStatement_Statement" LowerBound="229.1" HigherBound="320.1">
                                <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Validation Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="a92ff62e-fd52-4cec-8181-a942d6587004" ParentLink="ComplexStatement_Statement" LowerBound="232.1" HigherBound="234.1">
                                    <om:Property Name="Expression" Value="validationRetryCount = validationRetryCount + 1;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Inc retryCount" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="35549077-5a0c-46be-bdfd-42e6342cd01c" ParentLink="ComplexStatement_Statement" LowerBound="234.1" HigherBound="236.1">
                                    <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Take Time" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Scope" OID="8328981d-9ff3-4e08-9e3f-ce2e0abd83bb" ParentLink="ComplexStatement_Statement" LowerBound="236.1" HigherBound="291.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Send" OID="54f79244-421e-4527-8286-f60d0630e6ed" ParentLink="ComplexStatement_Statement" LowerBound="241.1" HigherBound="243.1">
                                        <om:Property Name="PortName" Value="sptN2BdeAnnullamento" />
                                        <om:Property Name="MessageName" Value="msgN2BdeAnnullamentoRequest" />
                                        <om:Property Name="OperationName" Value="AnnullaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="b60335b5-cb43-44a6-84e5-a61cb22bccab" ParentLink="ComplexStatement_Statement" LowerBound="243.1" HigherBound="245.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptN2BdeAnnullamento" />
                                        <om:Property Name="MessageName" Value="msgN2BdeAnnullamentoResponse" />
                                        <om:Property Name="OperationName" Value="AnnullaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="56fe99fc-0e26-4c63-a999-d14f5dab065e" ParentLink="ComplexStatement_Statement" LowerBound="245.1" HigherBound="256.1">
                                        <om:Property Name="Expression" Value="validationResult = msgN2BdeAnnullamentoResponse.parameter.AnnullaBDEResult;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="3d1057f0-a6d1-4c21-a4da-603d93e91f66" ParentLink="Scope_Catch" LowerBound="259.1" HigherBound="274.1">
                                        <om:Property Name="ExceptionName" Value="valSoapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="4fa0230b-0d6a-4ebc-81ad-f17f5ae99a77" ParentLink="Catch_Statement" LowerBound="262.1" HigherBound="273.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="52e56760-0aca-4ea8-915f-a02098433021" ParentLink="Scope_Catch" LowerBound="274.1" HigherBound="289.1">
                                        <om:Property Name="ExceptionName" Value="valFaultExc" />
                                        <om:Property Name="ExceptionType" Value="sptN2BdeAnnullamento.AnnullaBDE.Fault" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Fauld Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="472da9a6-a78d-45ec-8b1a-1649816a0cf1" ParentLink="Catch_Statement" LowerBound="277.1" HigherBound="288.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="d8400706-b000-4e0c-bc09-ac5aa87fbbc0" ParentLink="ComplexStatement_Statement" LowerBound="291.1" HigherBound="319.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation Result" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="91098b1d-7b1b-4ed4-a36b-eb2c83508de0" ParentLink="ReallyComplexStatement_Branch" LowerBound="292.29" HigherBound="299.1">
                                        <om:Property Name="Expression" Value="validationResult.StartsWith(&quot;OK&quot;, System.StringComparison.OrdinalIgnoreCase)" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="OK" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="3bea0a19-db35-4ade-8ae3-80c3ad5fde94" ParentLink="ComplexStatement_Statement" LowerBound="294.1" HigherBound="298.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;BDE Validata dopo &quot;, validationRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;validationRetryCount = 100;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Status Update" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="8bfa8cca-1901-4477-a228-edf91f2a5a00" ParentLink="ReallyComplexStatement_Branch" LowerBound="299.34" HigherBound="307.1">
                                        <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Retry" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="c2a2a690-dc2c-48bc-a48b-013d25d2d008" ParentLink="ComplexStatement_Statement" LowerBound="301.1" HigherBound="304.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Delay" OID="e0cacea9-a074-4c51-87b1-4e75d7eb2c1c" ParentLink="ComplexStatement_Statement" LowerBound="304.1" HigherBound="306.1">
                                            <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, validationRetryInterval);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Delay" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="be7947f7-19d9-4426-8a4b-ceb3dfb685f6" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="10663f56-07b2-4799-81f9-383fd8385311" ParentLink="ComplexStatement_Statement" LowerBound="309.1" HigherBound="318.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;Num. di tentativi: &quot;, validationRetryCount.ToString(), &quot;. &quot;);&#xD;&#xA;&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;NBDO non ha validato la BDE ({0}). &quot;, validationResult));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Response Code" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="581ac197-c989-495a-abd5-54598b130d05" ParentLink="ReallyComplexStatement_Branch" LowerBound="321.26" HigherBound="342.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Warning" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="33fc9747-985b-4338-b6ab-c878f633292d" ParentLink="ComplexStatement_Statement" LowerBound="323.1" HigherBound="339.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create Retry Message" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="0bb69937-3746-42c0-a05b-0e2490aa9d2b" ParentLink="ComplexStatement_Statement" LowerBound="326.1" HigherBound="338.1">
                                    <om:Property Name="Expression" Value="msgGenericRetryCollector.Context = new Microsys.EAI.Framework.Schemas.GenericRetryContext();&#xD;&#xA;&#xD;&#xA;msgGenericRetryCollector.Context.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgGenericRetryCollector.Context.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgGenericRetryCollector.Context.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgGenericRetryCollector.Context.flowDescription =  A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc;&#xD;&#xA;msgGenericRetryCollector.Context.fileName = msgTernaBde(FILE.ReceivedFileName);&#xD;&#xA;msgGenericRetryCollector.Context.messageText = &quot;BDE di comando non trovata.&quot;;&#xD;&#xA;msgGenericRetryCollector.Context.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;&#xD;&#xA;msgGenericRetryCollector.Message = msgTernaBde.parameter;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Retry Message" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="7e38cf05-ec38-4326-93ab-e2515aec412c" ParentLink="Construct_MessageRef" LowerBound="324.35" HigherBound="324.59">
                                    <om:Property Name="Ref" Value="msgGenericRetryCollector" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="6ecb2c4d-1184-4b2d-b673-d63d12ddfa5a" ParentLink="ComplexStatement_Statement" LowerBound="339.1" HigherBound="341.1">
                                <om:Property Name="PortName" Value="sptGenericRetryCollector" />
                                <om:Property Name="MessageName" Value="msgGenericRetryCollector" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="3db9ef93-e525-4c1a-bbef-2160fb4d9365" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="1dec4ba6-dc1a-4455-a288-96a48b1a4453" ParentLink="Scope_Catch" LowerBound="345.1" HigherBound="359.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="407f1794-79db-4412-a0e8-9b03b2d1a6d1" ParentLink="Catch_Statement" LowerBound="348.1" HigherBound="358.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="235921cc-7a87-4b69-ae02-d06ad090e00c" ParentLink="Scope_Catch" LowerBound="359.1" HigherBound="373.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d132c8cb-af34-4d9a-8bb3-d08c01f8f587" ParentLink="Catch_Statement" LowerBound="362.1" HigherBound="372.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="5727cb73-ae0a-4a26-9354-2bfbd2b658df" ParentLink="ServiceBody_Statement" LowerBound="375.1" HigherBound="409.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="5bd7a820-4e12-4b81-8e67-19cab0a07a7a" ParentLink="ReallyComplexStatement_Branch" LowerBound="376.13" HigherBound="379.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="7e14a380-bd1c-4605-94c5-04a16fdacaa9" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="2d30abf9-1e88-4f02-bcc3-7d4ccfcee3e1" ParentLink="ComplexStatement_Statement" LowerBound="381.1" HigherBound="408.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="fbd4de47-074d-4c92-a284-684aad7214a2" ParentLink="ComplexStatement_Statement" LowerBound="386.1" HigherBound="404.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="99107f8c-e7ee-4bb1-99ca-9ed12a56c7a6" ParentLink="Construct_MessageRef" LowerBound="387.35" HigherBound="387.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="f563095e-56cc-4ec6-8f1b-225c213126cd" ParentLink="ComplexStatement_Statement" LowerBound="389.1" HigherBound="403.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="719d03ed-a380-477d-8f78-aa6d440c63a8" ParentLink="ComplexStatement_Statement" LowerBound="404.1" HigherBound="406.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="f1fd26ab-c09b-4048-92f4-40ed0af92a9b" ParentLink="ServiceBody_Statement" LowerBound="409.1" HigherBound="416.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="6581865f-ea2a-4a19-8e3b-eb1d258e0184" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="49.1" HigherBound="51.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typTernaBde" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptTernaBdeRc" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="92c9672b-4d25-4226-8aea-4a8ef1f41347" ParentLink="PortDeclaration_CLRAttribute" LowerBound="49.1" HigherBound="50.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9735fd8b-394d-445d-8fcc-a8aa41be69cf" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="51.1" HigherBound="54.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="42" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typEaiBdeRc" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiBdeRc" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="3e6f791f-b3ef-418b-a240-5d8b82886c93" ParentLink="PortDeclaration_CLRAttribute" LowerBound="51.1" HigherBound="52.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="1104a99e-d346-4d05-bfb8-e7276f8495bf" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="54.1" HigherBound="56.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="142" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typGenericRetryCollector" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptGenericRetryCollector" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="1e8c132b-145b-4a2d-862e-658f18a5e2af" ParentLink="PortDeclaration_CLRAttribute" LowerBound="54.1" HigherBound="55.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="202e4a97-c5f9-480b-a795-b29a28cfc803" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="56.1" HigherBound="59.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="165" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typN2BdeAnnullamento" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2BdeAnnullamento" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="473dbd42-037b-463e-ad67-c959a6a357d5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="56.1" HigherBound="57.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="61c04837-6082-4166-ade1-ebe38bfcc840" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="59.1" HigherBound="62.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="281" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="678126c3-829d-4456-b96a-80560651946f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="59.1" HigherBound="60.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b7a5c8de-2e93-4fd2-bdb9-367ec659b10e" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="9.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgGenericRetryCollectorType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c5e17209-3654-4ce4-bec3-2ff9d3a12840" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.GenericRetryContext" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Context" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="PartDeclaration" OID="1294300d-d150-4c75-a6ba-ee28252691bb" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="7.1" HigherBound="8.1">
                <om:Property Name="ClassName" Value="System.Xml.XmlDocument" />
                <om:Property Name="IsBodyPart" Value="False" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Message" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="8d14e1b0-4aec-403c-a737-9e8e3c4c2e62" ParentLink="Module_MessageType" LowerBound="9.1" HigherBound="13.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeRcRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f6a20000-893c-4cae-b25d-6d98deaf0522" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="11.1" HigherBound="12.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeRcTypedProcedure.BdeRc" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="923ea497-e352-4300-89b7-969f17758901" ParentLink="Module_MessageType" LowerBound="13.1" HigherBound="17.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeRcResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a5215b9e-8c1e-4ad5-8612-1d752be07017" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="15.1" HigherBound="16.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeRcTypedProcedure.BdeRcResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d664ca78-8b05-4568-be1b-2bfab1ae53d1" ParentLink="Module_MessageType" LowerBound="17.1" HigherBound="21.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeAnnullamentoRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="bd2e5741-0275-4319-910b-6d2c056d2df8" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="19.1" HigherBound="20.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeAnnullamento.AnnullaBDE" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="251bd132-3e94-4827-a608-f6e65d2dc63a" ParentLink="Module_MessageType" LowerBound="21.1" HigherBound="25.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeAnnullamentoResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a4c64adf-aa6f-4e01-a89e-c88e7fe7ab7c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="23.1" HigherBound="24.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeAnnullamento.AnnullaBDEResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="ee9a8772-77eb-4d12-a7ff-babcc5752a8e" ParentLink="Module_PortType" LowerBound="25.1" HigherBound="32.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiBdeRc" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="90b536e3-df46-4867-8453-665038a34a9f" ParentLink="PortType_OperationDeclaration" LowerBound="27.1" HigherBound="31.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BdeRc" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="08094c55-68ed-486a-ba05-c7b861902592" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="29.13" HigherBound="29.35">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeRcRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="d16dc6bc-40d9-45ba-bfe8-9d4a6e50cb6b" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="29.37" HigherBound="29.60">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeRcResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="5f7da939-5b66-4f6c-a83a-f0ed5d27af05" ParentLink="Module_PortType" LowerBound="32.1" HigherBound="39.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typGenericRetryCollector" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="858b1d66-4efe-4fe7-bde0-27ae16638fd9" ParentLink="PortType_OperationDeclaration" LowerBound="34.1" HigherBound="38.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="c01a9528-b967-4e6d-a7f0-a8e4be4796ad" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="36.13" HigherBound="36.41">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgGenericRetryCollectorType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="7f892815-1870-44d3-b873-8803e23e60f6" ParentLink="Module_PortType" LowerBound="39.1" HigherBound="46.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typN2BdeAnnullamento" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="57e94976-e9d4-4d1c-9532-0bbd8769308b" ParentLink="PortType_OperationDeclaration" LowerBound="41.1" HigherBound="45.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="AnnullaBDE" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b85e39de-842d-42f1-8f1f-4e9dbfed65cd" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="43.13" HigherBound="43.44">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeAnnullamentoRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="a629419e-1828-4f35-912a-c045b3923004" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="43.80" HigherBound="43.100">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgFaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="f86a7386-f4f0-4e0a-b29e-0154a7cc168c" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="43.46" HigherBound="43.78">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeAnnullamentoResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    public messagetype msgGenericRetryCollectorType
    {
        body Microsys.EAI.Framework.Schemas.GenericRetryContext Context;
        System.Xml.XmlDocument Message;
    };
    internal messagetype msgEaiBdeRcRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeRcTypedProcedure.BdeRc parameter;
    };
    internal messagetype msgEaiBdeRcResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeRcTypedProcedure.BdeRcResponse parameter;
    };
    internal messagetype msgN2BdeAnnullamentoRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeAnnullamento.AnnullaBDE parameter;
    };
    internal messagetype msgN2BdeAnnullamentoResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeAnnullamento.AnnullaBDEResponse parameter;
    };
    internal porttype typEaiBdeRc
    {
        requestresponse BdeRc
        {
            msgEaiBdeRcRequestType, msgEaiBdeRcResponseType
        };
    };
    internal porttype typGenericRetryCollector
    {
        oneway Send
        {
            msgGenericRetryCollectorType
        };
    };
    internal porttype typN2BdeAnnullamento
    {
        requestresponse AnnullaBDE
        {
            msgN2BdeAnnullamentoRequestType, msgN2BdeAnnullamentoResponseType, Fault = msgFaultType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeRc
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typTernaBde rptTernaBdeRc;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiBdeRc sptEaiBdeRc;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typGenericRetryCollector sptGenericRetryCollector;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typN2BdeAnnullamento sptN2BdeAnnullamento;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNotification sptNotification;
        message msgEaiBdeNotificationType msgEaiBdeNotification;
        message msgTernaBdeType msgTernaBde;
        message msgEaiBdeRcRequestType msgEaiBdeRequest;
        message msgEaiBdeRcResponseType msgEaiBdeResponse;
        message msgN2BdeAnnullamentoRequestType msgN2BdeAnnullamentoRequest;
        message msgN2BdeAnnullamentoResponseType msgN2BdeAnnullamentoResponse;
        message msgNotificationType msgNotification;
        message msgGenericRetryCollectorType msgGenericRetryCollector;
        System.String validationRetryMessage;
        System.Int32 validationRetryInterval;
        System.Int32 validationRetryCount;
        System.String validationResult;
        System.Int32 validationMaxRetryCount;
        System.String UP;
        System.String tipoBde;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFilePath;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String backupFileName;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("13726b48-9a6d-4388-9771-cc844c289103")]
            activate receive (rptTernaBdeRc.Receive, msgTernaBde);
            validationRetryMessage = "";
            validationResult = "";
            UP = "";
            tipoBde = "";
            originalFilePath = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            backupFileName = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1615d42-6c8c-401d-a3ea-2d88979ca0b8")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); 
            originalFileName = System.IO.Path.GetFileName(originalFilePath);
            backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);
            
            validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationMaxRetryCount");
            validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationRetryInterval");
            validationRetryCount = 0;
            validationRetryMessage = System.String.Empty;
            
            UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;
            tipoBde = "Revoca Comando";
            
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("98a5f27c-285b-4b43-aaa4-11e51073cb03")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Inviato a N2", "NO",
            "BDE Codice", originalFileName,
            "BDE Data Inizio", msgTernaBde.parameter.DataOraInizioRevocaComando.DataOraInizioRevocaComando,
            "BDE Data Fine", msgTernaBde.parameter.DataOraFineRevocaComando.DataOraFineRevocaComando,
            "BDE Stato Comando", msgTernaBde.parameter.SequenzaComando.SequenzaComando,
            "BDE Tipo", A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc,
            "UP", UP,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("737b4e3f-10a0-4666-a1f3-b4750299feac")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5bb311ba-db5c-4bd9-a614-88f3a41666a4")]
                    construct msgEaiBdeRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("008bc12a-1c53-4017-ac3e-ae23f1771c35")]
                        transform (msgEaiBdeRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeRcToEaiBdeRc (msgTernaBde.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("83018498-d61c-4f3a-986d-c59d1ce3fe99")]
                        msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;
                        msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6daa650-598e-4cf0-9489-ddec874e0ee4")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("91015275-1e66-41c7-91ad-3c8ad68871ad")]
                    send (sptEaiBdeRc.BdeRc, msgEaiBdeRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5c8a4185-a866-4411-9483-ac80be35aeb8")]
                    receive (sptEaiBdeRc.BdeRc, msgEaiBdeResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bc8121b-0511-4351-a3e9-022f8729f120")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("192b3422-407c-474a-ab19-4defaa7e0e0c")]
                    if (msgEaiBdeResponse.parameter.CodeMessage > 0)
                    {
                    }
                    else if (msgEaiBdeResponse.parameter.CodeMessage == -1)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("97d43d6c-3ef3-4eee-8c1b-b8e597470c5a")]
                        errorMessage.Append("BDE di revoca in attesa.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b283565e-7ed6-4bae-a672-da5a0db78679")]
                        errorMessage.Append(msgEaiBdeResponse.parameter.ErrorMessage);
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e087eb22-11b0-40e4-a9c6-56977dc8051d")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c3e7352-8d94-486e-a040-cb951a586045")]
                        scope
                        {
                            body
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("4ca591dc-0e92-4a18-a327-309b569173f3")]
                                construct msgEaiBdeNotification
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("466cbf8d-cd27-4b24-9ac8-9f33d362fca8")]
                                    transform (msgEaiBdeNotification.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeRcToEaiBdeNotification (msgTernaBde.parameter);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b94178d7-46f4-48cd-b46c-55b694aeef94")]
                                    msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("7edaf03b-ec62-40f8-b0a8-65d75f7ca7cb")]
                                exec A2A.EAI.INT_BDE.Processes.prcBdeNotification (UP, tipoBde, resultCode, msgEaiBdeNotification, activityInstanceId);
                            }
                            exceptions
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("12fcfff4-7ca8-4cf6-94dc-44224867e58f")]
                                catch
                                {
                                }
                            }
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8fae6438-0e23-4330-86dc-8846d8d9d2b0")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b67acc7-4db3-4bae-ab1b-ee3496a01433")]
                        construct msgN2BdeAnnullamentoRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bfc86ae3-57b4-4916-82e0-76a9451bef99")]
                            transform (msgN2BdeAnnullamentoRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeRcToN2BdeAnnullamento (msgEaiBdeResponse.parameter);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d9e299bc-5452-4338-a371-b5bda106bf90")]
                        while (validationRetryCount < validationMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a92ff62e-fd52-4cec-8181-a942d6587004")]
                            validationRetryCount = validationRetryCount + 1;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("35549077-5a0c-46be-bdfd-42e6342cd01c")]
                            dataInizioWs = System.DateTimeOffset.Now;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8328981d-9ff3-4e08-9e3f-ce2e0abd83bb")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("54f79244-421e-4527-8286-f60d0630e6ed")]
                                    send (sptN2BdeAnnullamento.AnnullaBDE, msgN2BdeAnnullamentoRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b60335b5-cb43-44a6-84e5-a61cb22bccab")]
                                    receive (sptN2BdeAnnullamento.AnnullaBDE, msgN2BdeAnnullamentoResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("56fe99fc-0e26-4c63-a999-d14f5dab065e")]
                                    validationResult = msgN2BdeAnnullamentoResponse.parameter.AnnullaBDEResult;
                                    
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                    "Inviato a N2", "SI",
                                    "Esito WS", validationResult,
                                    "Data Inizio WS", dataInizioWs,
                                    "Data Fine WS", System.DateTimeOffset.Now,
                                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                    "Retry", validationRetryCount
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3d1057f0-a6d1-4c21-a4da-603d93e91f66")]
                                    catch (System.Web.Services.Protocols.SoapException valSoapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4fa0230b-0d6a-4ebc-81ad-f17f5ae99a77")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("52e56760-0aca-4ea8-915f-a02098433021")]
                                    catch (sptN2BdeAnnullamento.AnnullaBDE.Fault valFaultExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("472da9a6-a78d-45ec-8b1a-1649816a0cf1")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8400706-b000-4e0c-bc09-ac5aa87fbbc0")]
                            if (validationResult.StartsWith("OK", System.StringComparison.OrdinalIgnoreCase))
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("3bea0a19-db35-4ade-8ae3-80c3ad5fde94")]
                                validationRetryMessage = System.String.Concat("BDE Validata dopo ", validationRetryCount.ToString(), " numero di tentativi. ");
                                
                                validationRetryCount = 100;
                            }
                            else if (validationRetryCount < validationMaxRetryCount)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c2a2a690-dc2c-48bc-a48b-013d25d2d008")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0cacea9-a074-4c51-87b1-4e75d7eb2c1c")]
                                delay new System.TimeSpan(0, 0, validationRetryInterval);
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("10663f56-07b2-4799-81f9-383fd8385311")]
                                validationRetryMessage = System.String.Concat("Num. di tentativi: ", validationRetryCount.ToString(), ". ");
                                
                                errorMessage.Append(System.String.Format("NBDO non ha validato la BDE ({0}). ", validationResult));
                                errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            }
                        }
                    }
                    else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("33fc9747-985b-4338-b6ab-c878f633292d")]
                        construct msgGenericRetryCollector
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bb69937-3746-42c0-a05b-0e2490aa9d2b")]
                            msgGenericRetryCollector.Context = new Microsys.EAI.Framework.Schemas.GenericRetryContext();
                            
                            msgGenericRetryCollector.Context.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgGenericRetryCollector.Context.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgGenericRetryCollector.Context.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgGenericRetryCollector.Context.flowDescription =  A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc;
                            msgGenericRetryCollector.Context.fileName = msgTernaBde(FILE.ReceivedFileName);
                            msgGenericRetryCollector.Context.messageText = "BDE di comando non trovata.";
                            msgGenericRetryCollector.Context.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            
                            msgGenericRetryCollector.Message = msgTernaBde.parameter;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6ecb2c4d-1184-4b2d-b673-d63d12ddfa5a")]
                        send (sptGenericRetryCollector.Send, msgGenericRetryCollector);
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1dec4ba6-dc1a-4455-a288-96a48b1a4453")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("407f1794-79db-4412-a0e8-9b03b2d1a6d1")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("235921cc-7a87-4b69-ae02-d06ad090e00c")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d132c8cb-af34-4d9a-8bb3-d08c01f8f587")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5727cb73-ae0a-4a26-9354-2bfbd2b658df")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("2d30abf9-1e88-4f02-bcc3-7d4ccfcee3e1")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fbd4de47-074d-4c92-a284-684aad7214a2")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f563095e-56cc-4ec6-8f1b-225c213126cd")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionRc;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("719d03ed-a380-477d-8f78-aa6d440c63a8")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f1fd26ab-c09b-4048-92f4-40ed0af92a9b")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

