﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="10ebc25a-0c25-423f-b115-f5d985ee69fc" LowerBound="1.1" HigherBound="434.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="b5994a84-1cd0-4bfd-85a8-74c26f11b686" ParentLink="Module_ServiceDeclaration" LowerBound="19.1" HigherBound="433.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeSr" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="d7018ced-3c1e-46c2-b63e-72428a6d16f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e961da05-333d-4851-8c6e-f63ba0026c0a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cb5f4313-7396-440c-82a0-88b5e7fb31a3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bbc24d49-6000-451b-8ccf-dbf116407d23" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7cdb69af-6239-4ca8-8f30-ec0d2bdb6e13" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="07dbaae2-8375-4d9a-98df-31db8686af3a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d110fa8c-1d4c-453f-a2e6-4e6b40044080" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryInterval" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78c7fd5c-fe60-46c4-afef-5b066915deb1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3482371a-738b-4c95-a829-250559fd5053" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationResult" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="437ea1e0-d315-4002-b537-64a7d38871c8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="validationMaxRetryCount" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426b6a7c-17fb-4945-96dc-0026769a61d7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="UP" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8e2d43ae-f2eb-4465-8fe1-c6a3f2a2f4f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="tipoBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f1a0428c-d1cb-43cd-832b-a1aac6653c6d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="298bd104-a85c-43ad-8af3-d8dc0c35e8e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f07553f4-9218-43d4-8d73-cf265ad6a122" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b475c2-d1e1-461d-a3ce-31a06c9b5123" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7658a2c6-aee5-4d67-a981-fdb73792e87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f8905c1-6bb0-411d-b62e-1dcbbdb2167a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9a95c256-fb02-4c88-ae27-e37142bac6d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bd76a109-4266-43ae-b3db-a023402f0d2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78ad8ea2-d6b7-43a5-9099-5213a82f91dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f58e4c85-4768-415d-8808-3efbed7d576b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="backupFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b3f46dd-785b-4520-af09-a5765d883054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3d742c18-539c-495c-92fd-fccdc5f1bd01" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="796648e1-54dc-492c-87e9-b0a642f2e08c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgTernaBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="160b8573-cc65-4f40-9ba6-9364d20d2340" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeSrRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4884cc52-01c0-4473-ab72-b2446928c8d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeSrResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5fb41824-91cd-41f5-b752-6d49033f5642" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeValidazioneRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7d00a780-c838-437f-8518-a01a6625836d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgN2BdeValidazioneResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6ae76a8b-e47a-4683-bdcd-3c7755173a80" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="c8ac5b3f-fddf-4130-9833-743791297309" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="b562e619-7aea-4b2c-bb44-c467ffb50626" ParentLink="ServiceBody_Statement" LowerBound="65.1" HigherBound="78.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptTernaBdeSr" />
                    <om:Property Name="MessageName" Value="msgTernaBde" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="2fd536bb-4cc9-495b-876b-7d3098554c81" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="106.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); &#xD;&#xA;originalFileName = System.IO.Path.GetFileName(originalFilePath);&#xD;&#xA;backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);&#xD;&#xA;&#xD;&#xA;validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationMaxRetryCount&quot;);&#xD;&#xA;validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeValidationRetryInterval&quot;);&#xD;&#xA;validationRetryCount = 0;&#xD;&#xA;validationRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertMaxRetryCount&quot;);&#xD;&#xA;insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration(&quot;bdeInsertRetryInterval&quot;);&#xD;&#xA;insertRetryCount = 0;&#xD;&#xA;insertRetryMessage = System.String.Empty;&#xD;&#xA;&#xD;&#xA;UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;&#xD;&#xA;tipoBde = &quot;Servizio Regolazione Secondaria&quot;;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="ef0b4e48-85de-4c7f-9ca9-6e8b8c8b56b4" ParentLink="ServiceBody_Statement" LowerBound="106.1" HigherBound="119.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;BDE Codice&quot;, originalFileName,&#xD;&#xA;&quot;BDE Data Inizio&quot;, msgTernaBde.parameter.DataOraInizio.DataOraInizio,&#xD;&#xA;&quot;BDE Data Fine&quot;, msgTernaBde.parameter.DataOraInizio.DataOraInizio,&#xD;&#xA;&quot;BDE Tipo&quot;, A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr,&#xD;&#xA;&quot;UP&quot;, UP,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="009547f6-f909-4fe0-a09d-60e2e88e592d" ParentLink="ServiceBody_Statement" LowerBound="119.1" HigherBound="359.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="4c8780d5-b6c0-4e5e-a114-a4a9bd988173" ParentLink="ComplexStatement_Statement" LowerBound="124.1" HigherBound="140.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Messages" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="579b72e2-12ba-4295-bb84-c916b0f0698d" ParentLink="Construct_MessageRef" LowerBound="125.31" HigherBound="125.47">
                            <om:Property Name="Ref" Value="msgEaiBdeRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="ec49bffd-5168-47ad-9f92-3885f56808cb" ParentLink="Construct_MessageRef" LowerBound="125.49" HigherBound="125.66">
                            <om:Property Name="Ref" Value="msgEaiBdeResponse" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="308e2f5b-52e0-4e40-8d20-16f28357f008" ParentLink="ComplexStatement_Statement" LowerBound="127.1" HigherBound="129.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeSrToEaiBdeSr" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Nbdo Request" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="87fbc4c0-510c-43f0-b7f5-076c6f25302e" ParentLink="Transform_OutputMessagePartRef" LowerBound="128.36" HigherBound="128.62">
                                <om:Property Name="MessageRef" Value="msgEaiBdeRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="5427e871-7186-40d3-8221-259f63a1312d" ParentLink="Transform_InputMessagePartRef" LowerBound="128.122" HigherBound="128.143">
                                <om:Property Name="MessageRef" Value="msgTernaBde" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="dca47e46-b8b6-4e13-a820-cdfd2942b3a9" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="133.1">
                            <om:Property Name="Expression" Value="msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;&#xD;&#xA;msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Codice BDE" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="9b28e861-c21b-4b7a-97c6-5e85df1643b8" ParentLink="ComplexStatement_Statement" LowerBound="133.1" HigherBound="139.1">
                            <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter = &#xD;&#xA;Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0&quot;,&#xD;&#xA;&quot;A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure+BdeSrResponse&quot;&#xD;&#xA;);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="CreateResponse" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="While" OID="d4f7f4fa-e8a0-4824-861f-f7c08902441c" ParentLink="ComplexStatement_Statement" LowerBound="140.1" HigherBound="194.1">
                        <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Insert Loop" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="VariableAssignment" OID="f70630b2-d913-4650-b722-9a57fd3c032f" ParentLink="ComplexStatement_Statement" LowerBound="143.1" HigherBound="145.1">
                            <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Take Time" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Send" OID="1e0a523e-9513-4ac9-baa0-349efa955f57" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="147.1">
                            <om:Property Name="PortName" Value="sptEaiBdeSr" />
                            <om:Property Name="MessageName" Value="msgEaiBdeRequest" />
                            <om:Property Name="OperationName" Value="BdeSr" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Receive" OID="fa6b059f-0f90-44f2-a5e3-ce6fed4bf48d" ParentLink="ComplexStatement_Statement" LowerBound="147.1" HigherBound="149.1">
                            <om:Property Name="Activate" Value="False" />
                            <om:Property Name="PortName" Value="sptEaiBdeSr" />
                            <om:Property Name="MessageName" Value="msgEaiBdeResponse" />
                            <om:Property Name="OperationName" Value="BdeSr" />
                            <om:Property Name="OperationMessageName" Value="Response" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Receive" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="7087b0b9-8c58-494a-b58c-b00c6372d265" ParentLink="ComplexStatement_Statement" LowerBound="149.1" HigherBound="155.1">
                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BAM Update" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Decision" OID="9d801ce0-5819-4fce-a8a5-b92dff9de44b" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="193.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Process Result" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="DecisionBranch" OID="f0bdd64a-98e0-49a0-88a9-df919f29807a" ParentLink="ReallyComplexStatement_Branch" LowerBound="156.25" HigherBound="163.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage &gt; 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="OK" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="7ba9a94a-6ca6-4371-a2f9-f011a7945415" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="162.1">
                                    <om:Property Name="Expression" Value="insertRetryMessage = System.String.Concat(&quot;BDE inserita dopo &quot;, insertRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Status Update" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="cacd6950-0d69-466d-8505-cd22487b674a" ParentLink="ReallyComplexStatement_Branch" LowerBound="163.30" HigherBound="175.1">
                                <om:Property Name="Expression" Value="msgEaiBdeResponse.parameter.CodeMessage == 0" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BDE Già Validata" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="ce0573ef-ced1-4ba8-8cc8-85867c0f998d" ParentLink="ComplexStatement_Statement" LowerBound="165.1" HigherBound="174.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(&quot;BDE già validata.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;insertRetryCount = 100;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="31810a55-b496-42b9-a200-c24b9c608f74" ParentLink="ReallyComplexStatement_Branch" LowerBound="175.30" HigherBound="183.1">
                                <om:Property Name="Expression" Value="insertRetryCount &lt; insertMaxRetryCount" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Retry" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="bb7ba0ee-bd6c-4113-9ffe-a1f657e508b1" ParentLink="ComplexStatement_Statement" LowerBound="177.1" HigherBound="180.1">
                                    <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Message" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Delay" OID="681b0264-0bee-43cd-8302-5d153910fef0" ParentLink="ComplexStatement_Statement" LowerBound="180.1" HigherBound="182.1">
                                    <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, insertRetryInterval);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Delay" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="47ca6ec0-0f7d-4158-a8b7-e31047b103af" ParentLink="ReallyComplexStatement_Branch">
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Else" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="6194aa23-fe5c-4d66-96c9-94b91b4144a2" ParentLink="ComplexStatement_Statement" LowerBound="185.1" HigherBound="192.1">
                                    <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;UP '{0}' non presente in NDUE.&quot;, msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; &#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Response Code" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="ba3439c5-16d1-4e27-a66a-d8b93be7b2c9" ParentLink="ComplexStatement_Statement" LowerBound="194.1" HigherBound="225.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BDE Already Submitted" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="5102ba5c-d7d2-4630-a4d8-8c6ec24caf0d" ParentLink="ReallyComplexStatement_Branch" LowerBound="195.21" HigherBound="198.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="6a9cb23f-f8fd-4a06-a435-845347c20929" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Scope" OID="8b2928fb-206a-4e8c-be7e-0a49019d4575" ParentLink="ComplexStatement_Statement" LowerBound="200.1" HigherBound="224.1">
                                <om:Property Name="InitializedTransactionType" Value="True" />
                                <om:Property Name="IsSynchronized" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Mail Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Catch" OID="a7c108ef-7e4b-4b38-9c1a-da0c6884d015" ParentLink="Scope_Catch" LowerBound="218.1" HigherBound="222.1">
                                    <om:Property Name="ExceptionType" Value="General Exception" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Generic" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Construct" OID="816463fd-3861-450f-b428-9462be71b5da" ParentLink="ComplexStatement_Statement" LowerBound="205.1" HigherBound="213.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Messages" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessageRef" OID="a37c4306-dc4e-4814-a843-f7d1a4564294" ParentLink="Construct_MessageRef" LowerBound="206.43" HigherBound="206.64">
                                        <om:Property Name="Ref" Value="msgEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Transform" OID="25d35824-23a7-44e7-ace2-96ea288211d0" ParentLink="ComplexStatement_Statement" LowerBound="208.1" HigherBound="210.1">
                                        <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeSrToEaiBdeNotification" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Create Notification" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessagePartRef" OID="808f7f22-3228-42a4-a775-3e0a97aa7af9" ParentLink="Transform_InputMessagePartRef" LowerBound="209.149" HigherBound="209.170">
                                            <om:Property Name="MessageRef" Value="msgTernaBde" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_5" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessagePartRef" OID="18a6800d-154a-4a2a-a623-b87fa4b8aaca" ParentLink="Transform_OutputMessagePartRef" LowerBound="209.48" HigherBound="209.79">
                                            <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_6" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="MessageAssignment" OID="3a477ce6-d551-4cb0-a448-18bdb6cb9aff" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="212.1">
                                        <om:Property Name="Expression" Value="msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="False" />
                                        <om:Property Name="Name" Value="File Name" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Exec" OID="67a8a9f7-105c-49d5-bfcc-18af806fdb07" ParentLink="ComplexStatement_Statement" LowerBound="213.1" HigherBound="215.1">
                                    <om:Property Name="Invokee" Value="A2A.EAI.INT_BDE.Processes.prcBdeNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Start Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="Parameter" OID="e8c62168-9d7a-4402-8588-e181f906bff1" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="UP" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="835d745c-8b98-4179-9166-b716ed9bda2f" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="tipoBde" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="5aa07770-0621-48c1-9992-ac34476bb2c9" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="resultCode" />
                                        <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="f1e3e628-75b1-422c-a3d7-34e300a95633" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="msgEaiBdeNotification" />
                                        <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Parameter" OID="f210aa2d-c371-4ac4-8629-abc402ed5bbb" ParentLink="InvokeStatement_Parameter">
                                        <om:Property Name="Direction" Value="In" />
                                        <om:Property Name="Name" Value="activityInstanceId" />
                                        <om:Property Name="Type" Value="System.String" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="2fda7e91-4553-40ce-9550-3a78aa43d74a" ParentLink="ComplexStatement_Statement" LowerBound="225.1" HigherBound="326.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check Succeded" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="9537bcef-5921-4758-aefb-dc182f4dbf92" ParentLink="ReallyComplexStatement_Branch" LowerBound="226.21" HigherBound="326.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="46c1473d-8277-4040-bfa0-d2c3790dc7da" ParentLink="ComplexStatement_Statement" LowerBound="228.1" HigherBound="234.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup Nbdo WS" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="02692178-9d3a-42f4-bf2e-ffc439ec229d" ParentLink="Construct_MessageRef" LowerBound="229.35" HigherBound="229.61">
                                    <om:Property Name="Ref" Value="msgN2BdeValidazioneRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="4fcd78f1-d7dc-4f95-90f8-33f89ebf110d" ParentLink="ComplexStatement_Statement" LowerBound="231.1" HigherBound="233.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeSrToN2BdeValidazione" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Create Nbdo Ws" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="6ad072c6-51ca-4269-9f94-bba15641ff3a" ParentLink="Transform_InputMessagePartRef" LowerBound="232.142" HigherBound="232.169">
                                        <om:Property Name="MessageRef" Value="msgEaiBdeResponse" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="bf3c4ae4-2b75-4078-b158-79e1e92279a5" ParentLink="Transform_OutputMessagePartRef" LowerBound="232.40" HigherBound="232.76">
                                        <om:Property Name="MessageRef" Value="msgN2BdeValidazioneRequest" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="ba564f23-a2ae-441c-a4b1-6da2ee4461d7" ParentLink="ComplexStatement_Statement" LowerBound="234.1" HigherBound="325.1">
                                <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Validation Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="VariableAssignment" OID="d721b657-4e4e-44b3-b6fa-01340a5b7ab1" ParentLink="ComplexStatement_Statement" LowerBound="237.1" HigherBound="239.1">
                                    <om:Property Name="Expression" Value="validationRetryCount = validationRetryCount + 1;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Inc retryCount" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="62dfae54-3ab1-4725-8056-a9dd7d5269a8" ParentLink="ComplexStatement_Statement" LowerBound="239.1" HigherBound="241.1">
                                    <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Take Time" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Scope" OID="12d6968f-88ae-4d58-813f-7d0c0eb87626" ParentLink="ComplexStatement_Statement" LowerBound="241.1" HigherBound="296.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Send" OID="1768128d-f97b-4498-95fc-81d5a334199f" ParentLink="ComplexStatement_Statement" LowerBound="246.1" HigherBound="248.1">
                                        <om:Property Name="PortName" Value="sptN2BdeValidazione" />
                                        <om:Property Name="MessageName" Value="msgN2BdeValidazioneRequest" />
                                        <om:Property Name="OperationName" Value="ValidaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="63eae19f-f710-4933-94e1-228a1cefa394" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="250.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptN2BdeValidazione" />
                                        <om:Property Name="MessageName" Value="msgN2BdeValidazioneResponse" />
                                        <om:Property Name="OperationName" Value="ValidaBDE" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="63a6256f-a9b2-401d-89f7-18c58efba34c" ParentLink="ComplexStatement_Statement" LowerBound="250.1" HigherBound="261.1">
                                        <om:Property Name="Expression" Value="validationResult = msgN2BdeValidazioneResponse.parameter.ValidaBDEResult;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="eaf14ea0-3fd0-4f5a-96dc-46048e2f4b8e" ParentLink="Scope_Catch" LowerBound="264.1" HigherBound="279.1">
                                        <om:Property Name="ExceptionName" Value="valSoapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="4ee337e5-31b4-4c20-b68b-872b721d16ed" ParentLink="Catch_Statement" LowerBound="267.1" HigherBound="278.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="4bd562c4-dc02-4833-987e-07020a96c0b0" ParentLink="Scope_Catch" LowerBound="279.1" HigherBound="294.1">
                                        <om:Property Name="ExceptionName" Value="valFaultExc" />
                                        <om:Property Name="ExceptionType" Value="sptN2BdeValidazione.ValidaBDE.Fault" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Fauld Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="78e94d87-6bc2-4444-9390-701b54cbd022" ParentLink="Catch_Statement" LowerBound="282.1" HigherBound="293.1">
                                            <om:Property Name="Expression" Value="validationResult = &quot;Errore di comunicazione&quot;;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Esito WS&quot;, validationResult,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Retry&quot;, validationRetryCount&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="a079cf6c-e4c4-4d32-a59b-6ef9e93d06ec" ParentLink="ComplexStatement_Statement" LowerBound="296.1" HigherBound="324.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Validation Result" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="b0712dc7-e4c0-46f7-93bb-36343d26572d" ParentLink="ReallyComplexStatement_Branch" LowerBound="297.29" HigherBound="304.1">
                                        <om:Property Name="Expression" Value="validationResult.StartsWith(&quot;OK&quot;, System.StringComparison.OrdinalIgnoreCase)" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="OK" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="b85812ed-e866-4d96-9eee-9e2925bcd205" ParentLink="ComplexStatement_Statement" LowerBound="299.1" HigherBound="303.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;BDE Validata dopo &quot;, validationRetryCount.ToString(), &quot; numero di tentativi. &quot;);&#xD;&#xA;&#xD;&#xA;validationRetryCount = 100;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Status Update" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="bc10be2b-81c3-4ea1-a937-ecbc868cf4cb" ParentLink="ReallyComplexStatement_Branch" LowerBound="304.34" HigherBound="312.1">
                                        <om:Property Name="Expression" Value="validationRetryCount &lt; validationMaxRetryCount" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Retry" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="e781fba4-8ecd-4e83-9e54-ea76b0670124" ParentLink="ComplexStatement_Statement" LowerBound="306.1" HigherBound="309.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Delay" OID="438794aa-46ce-4590-b262-bfdfc6c50f5d" ParentLink="ComplexStatement_Statement" LowerBound="309.1" HigherBound="311.1">
                                            <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, validationRetryInterval);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Delay" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="f4a86a7d-087b-462f-8a64-bf756e8b1a92" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="305cd0fe-5415-466a-bedb-a873a8dcb0ed" ParentLink="ComplexStatement_Statement" LowerBound="314.1" HigherBound="323.1">
                                            <om:Property Name="Expression" Value="validationRetryMessage = System.String.Concat(&quot;Num. di tentativi: &quot;, validationRetryCount.ToString(), &quot;. &quot;);&#xD;&#xA;&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;NBDO non ha validato la BDE ({0}). &quot;, validationResult));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Response Code" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="1519792d-2448-44d7-8b4e-ee4dcc92df22" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="5242164a-557b-4596-ace5-2b97aaa6d835" ParentLink="Scope_Catch" LowerBound="329.1" HigherBound="343.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="ac7af41f-de4c-41cb-b159-ccfc70626c45" ParentLink="Catch_Statement" LowerBound="332.1" HigherBound="342.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="74f93d34-26ad-4949-94d9-60e776d311d2" ParentLink="Scope_Catch" LowerBound="343.1" HigherBound="357.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="664ea795-bac8-4f91-8d78-97f07d7a89fc" ParentLink="Catch_Statement" LowerBound="346.1" HigherBound="356.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, backupFileName));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="d180be05-a74a-41a1-92e3-120c2a180e2d" ParentLink="ServiceBody_Statement" LowerBound="359.1" HigherBound="424.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="3c6446a8-9709-45c7-9407-e44c479fc229" ParentLink="ReallyComplexStatement_Branch" LowerBound="360.13" HigherBound="363.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="10351fcc-d9f0-43f0-bddc-c29147ca4729" ParentLink="ReallyComplexStatement_Branch" LowerBound="363.18" HigherBound="394.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Succeded With Warnings" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="025d6f8b-adda-4688-8925-61a93c30bc1a" ParentLink="ComplexStatement_Statement" LowerBound="365.1" HigherBound="393.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="3daca9fe-5801-4d83-8280-1ab45cb6b5a7" ParentLink="ComplexStatement_Statement" LowerBound="370.1" HigherBound="389.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="b8917f11-b9fd-4841-af2e-4bf57ff4c484" ParentLink="Construct_MessageRef" LowerBound="371.35" HigherBound="371.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="8aa37017-0861-47f1-a554-102545aa0dab" ParentLink="ComplexStatement_Statement" LowerBound="373.1" HigherBound="388.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="3f98e75d-4565-40ea-b837-6a138a89a27c" ParentLink="ComplexStatement_Statement" LowerBound="389.1" HigherBound="391.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="72584c10-f4cc-47e9-9641-69f4c22ea7ec" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="cd90e7ea-5328-46bd-9b24-5ed627cc934f" ParentLink="ComplexStatement_Statement" LowerBound="396.1" HigherBound="423.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Error Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="4578b3f6-51da-4ddf-b1bd-e27b720ee9f6" ParentLink="ComplexStatement_Statement" LowerBound="401.1" HigherBound="419.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="6de7fd9a-5127-4a2a-b26d-84a3f06b3834" ParentLink="Construct_MessageRef" LowerBound="402.35" HigherBound="402.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="b85e8204-1f3d-4109-bada-b1f150b5e3c1" ParentLink="ComplexStatement_Statement" LowerBound="404.1" HigherBound="418.1">
                                    <om:Property Name="Expression" Value="msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr;&#xD;&#xA;msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());&#xD;&#xA;msgNotification.parameter.messageNotes = System.String.Concat(&quot;UP: &quot;, UP);&#xD;&#xA;msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="139074c9-d5ed-49e2-9d15-84d5ee073d3d" ParentLink="ComplexStatement_Statement" LowerBound="419.1" HigherBound="421.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="713fdad6-4b22-4ea9-bf77-e409c807d00d" ParentLink="ServiceBody_Statement" LowerBound="424.1" HigherBound="431.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b4041ffc-5d6c-4b09-9260-67f23489297d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="24.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typTernaBde" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptTernaBdeSr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="96e47ef5-30c3-404d-a41c-765c160be157" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c3b6481c-f63b-419b-b2d1-e5fc8e482961" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="24.1" HigherBound="27.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="299" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="6be545d3-e309-417d-93d1-0d407570f236" ParentLink="PortDeclaration_CLRAttribute" LowerBound="24.1" HigherBound="25.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a931ad20-3f7e-40ae-b72e-86660c68b836" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="27.1" HigherBound="30.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="47" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typEaiBdeSr" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiBdeSr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="771e2909-16cc-4e16-9404-1c34d308c77c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="27.1" HigherBound="28.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e50b2bec-d61f-4c42-b301-7e9c58cb032b" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="30.1" HigherBound="33.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="179" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typN2BdeValidazione" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2BdeValidazione" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="076295c6-fa44-4a01-a64d-7fe7d8ff2f39" ParentLink="PortDeclaration_CLRAttribute" LowerBound="30.1" HigherBound="31.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0b374027-663e-4308-a54a-9b9dc1210982" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeSrRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="07306b27-50e7-4efe-9b61-30dea159ba45" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure.BdeSr" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0cc71f8f-d9d5-4853-a196-e1af50940cb7" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeSrResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="65afde58-81ab-4d1e-9381-b21693c454d1" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure.BdeSrResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="75662b64-c410-4945-bdb8-82eef6fd95ff" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiBdeSr" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="25a73ce3-22ac-4dea-8fe4-bce329bb1467" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BdeSr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="3cbea4a7-b102-43f8-8b95-741fabf4e072" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.35">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeSrRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b07b1d7a-6719-4ce0-bf9d-2f58c74372b9" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="16.37" HigherBound="16.60">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeSrResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    internal messagetype msgEaiBdeSrRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure.BdeSr parameter;
    };
    internal messagetype msgEaiBdeSrResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure.BdeSrResponse parameter;
    };
    internal porttype typEaiBdeSr
    {
        requestresponse BdeSr
        {
            msgEaiBdeSrRequestType, msgEaiBdeSrResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeSr
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typTernaBde rptTernaBdeSr;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiBdeSr sptEaiBdeSr;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typN2BdeValidazione sptN2BdeValidazione;
        message msgEaiBdeNotificationType msgEaiBdeNotification;
        message msgTernaBdeType msgTernaBde;
        message msgEaiBdeSrRequestType msgEaiBdeRequest;
        message msgEaiBdeSrResponseType msgEaiBdeResponse;
        message msgN2BdeValidazioneRequestType msgN2BdeValidazioneRequest;
        message msgN2BdeValidazioneResponseType msgN2BdeValidazioneResponse;
        message msgNotificationType msgNotification;
        System.String insertRetryMessage;
        System.Int32 insertRetryInterval;
        System.Int32 insertRetryCount;
        System.String insertResult;
        System.Int32 insertMaxRetryCount;
        System.String validationRetryMessage;
        System.Int32 validationRetryInterval;
        System.Int32 validationRetryCount;
        System.String validationResult;
        System.Int32 validationMaxRetryCount;
        System.String UP;
        System.String tipoBde;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFilePath;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String backupFileName;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b562e619-7aea-4b2c-bb44-c467ffb50626")]
            activate receive (rptTernaBdeSr.Receive, msgTernaBde);
            insertRetryMessage = "";
            insertResult = "";
            validationRetryMessage = "";
            validationResult = "";
            UP = "";
            tipoBde = "";
            originalFilePath = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            backupFileName = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2fd536bb-4cc9-495b-876b-7d3098554c81")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); 
            originalFileName = System.IO.Path.GetFileName(originalFilePath);
            backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);
            
            validationMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationMaxRetryCount");
            validationRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeValidationRetryInterval");
            validationRetryCount = 0;
            validationRetryMessage = System.String.Empty;
            
            insertMaxRetryCount = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertMaxRetryCount");
            insertRetryInterval = A2A.EAI.INT_BDE.Services.ProcessServices.GetMaxRetryConfiguration("bdeInsertRetryInterval");
            insertRetryCount = 0;
            insertRetryMessage = System.String.Empty;
            
            UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;
            tipoBde = "Servizio Regolazione Secondaria";
            
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ef0b4e48-85de-4c7f-9ca9-6e8b8c8b56b4")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Inviato a N2", "NO",
            "BDE Codice", originalFileName,
            "BDE Data Inizio", msgTernaBde.parameter.DataOraInizio.DataOraInizio,
            "BDE Data Fine", msgTernaBde.parameter.DataOraInizio.DataOraInizio,
            "BDE Tipo", A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr,
            "UP", UP,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("009547f6-f909-4fe0-a09d-60e2e88e592d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4c8780d5-b6c0-4e5e-a114-a4a9bd988173")]
                    construct msgEaiBdeRequest, msgEaiBdeResponse
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("308e2f5b-52e0-4e40-8d20-16f28357f008")]
                        transform (msgEaiBdeRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeSrToEaiBdeSr (msgTernaBde.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dca47e46-b8b6-4e13-a820-cdfd2942b3a9")]
                        msgEaiBdeRequest.parameter.CodiceBDE = originalFileName;
                        msgEaiBdeRequest.parameter.DataCreazioneFile = A2A.EAI.INT_BDE.Services.ProcessServices.GetDataCreazioneFile(msgTernaBde);
                        
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9b28e861-c21b-4b7a-97c6-5e85df1643b8")]
                        msgEaiBdeResponse.parameter = 
                        Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(
                        "A2A.EAI.INT_BDE.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0",
                        "A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeSrTypedProcedure+BdeSrResponse"
                        );
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d4f7f4fa-e8a0-4824-861f-f7c08902441c")]
                    while (insertRetryCount < insertMaxRetryCount)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f70630b2-d913-4650-b722-9a57fd3c032f")]
                        dataInizioDb = System.DateTimeOffset.Now;
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e0a523e-9513-4ac9-baa0-349efa955f57")]
                        send (sptEaiBdeSr.BdeSr, msgEaiBdeRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fa6b059f-0f90-44f2-a5e3-ce6fed4bf48d")]
                        receive (sptEaiBdeSr.BdeSr, msgEaiBdeResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7087b0b9-8c58-494a-b58c-b00c6372d265")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                        "Data Inizio DB", dataInizioDb,
                        "Data Fine DB", System.DateTimeOffset.Now,
                        "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d801ce0-5819-4fce-a8a5-b92dff9de44b")]
                        if (msgEaiBdeResponse.parameter.CodeMessage > 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7ba9a94a-6ca6-4371-a2f9-f011a7945415")]
                            insertRetryMessage = System.String.Concat("BDE inserita dopo ", insertRetryCount.ToString(), " numero di tentativi. ");
                            
                            insertRetryCount = 100;
                        }
                        else if (msgEaiBdeResponse.parameter.CodeMessage == 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ce0573ef-ced1-4ba8-8cc8-85867c0f998d")]
                            errorMessage.Append("BDE già validata.");
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            
                            insertRetryCount = 100;
                        }
                        else if (insertRetryCount < insertMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bb7ba0ee-bd6c-4113-9ffe-a1f657e508b1")]
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("681b0264-0bee-43cd-8302-5d153910fef0")]
                            delay new System.TimeSpan(0, 0, insertRetryInterval);
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6194aa23-fe5c-4d66-96c9-94b91b4144a2")]
                            errorMessage.Append(System.String.Format("UP '{0}' non presente in NDUE.", msgTernaBde.parameter.NomeUpaUca.NomeUpaUca));
                            errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL; 
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ba3439c5-16d1-4e27-a66a-d8b93be7b2c9")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
                    {
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8b2928fb-206a-4e8c-be7e-0a49019d4575")]
                        scope
                        {
                            body
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("816463fd-3861-450f-b428-9462be71b5da")]
                                construct msgEaiBdeNotification
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("25d35824-23a7-44e7-ace2-96ea288211d0")]
                                    transform (msgEaiBdeNotification.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeSrToEaiBdeNotification (msgTernaBde.parameter);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a477ce6-d551-4cb0-a448-18bdb6cb9aff")]
                                    msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("67a8a9f7-105c-49d5-bfcc-18af806fdb07")]
                                exec A2A.EAI.INT_BDE.Processes.prcBdeNotification (UP, tipoBde, resultCode, msgEaiBdeNotification, activityInstanceId);
                            }
                            exceptions
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("a7c108ef-7e4b-4b38-9c1a-da0c6884d015")]
                                catch
                                {
                                }
                            }
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2fda7e91-4553-40ce-9550-3a78aa43d74a")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("46c1473d-8277-4040-bfa0-d2c3790dc7da")]
                        construct msgN2BdeValidazioneRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4fcd78f1-d7dc-4f95-90f8-33f89ebf110d")]
                            transform (msgN2BdeValidazioneRequest.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapEaiBdeSrToN2BdeValidazione (msgEaiBdeResponse.parameter);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ba564f23-a2ae-441c-a4b1-6da2ee4461d7")]
                        while (validationRetryCount < validationMaxRetryCount)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d721b657-4e4e-44b3-b6fa-01340a5b7ab1")]
                            validationRetryCount = validationRetryCount + 1;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("62dfae54-3ab1-4725-8056-a9dd7d5269a8")]
                            dataInizioWs = System.DateTimeOffset.Now;
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("12d6968f-88ae-4d58-813f-7d0c0eb87626")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1768128d-f97b-4498-95fc-81d5a334199f")]
                                    send (sptN2BdeValidazione.ValidaBDE, msgN2BdeValidazioneRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("63eae19f-f710-4933-94e1-228a1cefa394")]
                                    receive (sptN2BdeValidazione.ValidaBDE, msgN2BdeValidazioneResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("63a6256f-a9b2-401d-89f7-18c58efba34c")]
                                    validationResult = msgN2BdeValidazioneResponse.parameter.ValidaBDEResult;
                                    
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                    "Inviato a N2", "SI",
                                    "Esito WS", validationResult,
                                    "Data Inizio WS", dataInizioWs,
                                    "Data Fine WS", System.DateTimeOffset.Now,
                                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                    "Retry", validationRetryCount
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eaf14ea0-3fd0-4f5a-96dc-46048e2f4b8e")]
                                    catch (System.Web.Services.Protocols.SoapException valSoapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4ee337e5-31b4-4c20-b68b-872b721d16ed")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4bd562c4-dc02-4833-987e-07020a96c0b0")]
                                    catch (sptN2BdeValidazione.ValidaBDE.Fault valFaultExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("78e94d87-6bc2-4444-9390-701b54cbd022")]
                                        validationResult = "Errore di comunicazione";
                                        
                                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
                                        "Inviato a N2", "NO",
                                        "Esito WS", validationResult,
                                        "Data Inizio WS", dataInizioWs,
                                        "Data Fine WS", System.DateTimeOffset.Now,
                                        "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                        "Retry", validationRetryCount
                                        );
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a079cf6c-e4c4-4d32-a59b-6ef9e93d06ec")]
                            if (validationResult.StartsWith("OK", System.StringComparison.OrdinalIgnoreCase))
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("b85812ed-e866-4d96-9eee-9e2925bcd205")]
                                validationRetryMessage = System.String.Concat("BDE Validata dopo ", validationRetryCount.ToString(), " numero di tentativi. ");
                                
                                validationRetryCount = 100;
                            }
                            else if (validationRetryCount < validationMaxRetryCount)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("e781fba4-8ecd-4e83-9e54-ea76b0670124")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("438794aa-46ce-4590-b262-bfdfc6c50f5d")]
                                delay new System.TimeSpan(0, 0, validationRetryInterval);
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("305cd0fe-5415-466a-bedb-a873a8dcb0ed")]
                                validationRetryMessage = System.String.Concat("Num. di tentativi: ", validationRetryCount.ToString(), ". ");
                                
                                errorMessage.Append(System.String.Format("NBDO non ha validato la BDE ({0}). ", validationResult));
                                errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                            }
                        }
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5242164a-557b-4596-ace5-2b97aaa6d835")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ac7af41f-de4c-41cb-b159-ccfc70626c45")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("74f93d34-26ad-4949-94d9-60e776d311d2")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("664ea795-bac8-4f91-8d78-97f07d7a89fc")]
                        errorMessage.Append("Si è verificato un errore. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, backupFileName));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d180be05-a74a-41a1-92e3-120c2a180e2d")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("025d6f8b-adda-4688-8925-61a93c30bc1a")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3daca9fe-5801-4d83-8280-1ab45cb6b5a7")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8aa37017-0861-47f1-a554-102545aa0dab")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Warning.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3f98e75d-4565-40ea-b837-6a138a89a27c")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("cd90e7ea-5328-46bd-9b24-5ed627cc934f")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4578b3f6-51da-4ddf-b1bd-e27b720ee9f6")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b85e8204-1f3d-4109-bada-b1f150b5e3c1")]
                            msgNotification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.parameter.applicationName = A2A.EAI.INT_BDE.Services.ProcessServices.ApplicationName;
                            msgNotification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.parameter.flowGroup = A2A.EAI.INT_BDE.Services.ProcessServices.FlowGroup;
                            msgNotification.parameter.flowDescription = A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionSr;
                            msgNotification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.parameter.messageText = System.String.Concat(insertRetryMessage, validationRetryMessage, errorMessage.ToString());
                            msgNotification.parameter.messageNotes = System.String.Concat("UP: ", UP);
                            msgNotification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("139074c9-d5ed-49e2-9d15-84d5ee073d3d")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("713fdad6-4b22-4ea9-bf77-e409c807d00d")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

