﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="a41f9641-5a93-43ae-ad05-0cdc4fe01f72" LowerBound="1.1" HigherBound="95.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="af7814b9-54d5-4afa-a820-177b8f1c783c" ParentLink="Module_ServiceDeclaration" LowerBound="4.1" HigherBound="94.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcBdeVq" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="426b6a7c-17fb-4945-96dc-0026769a61d7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="11.1" HigherBound="12.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="UP" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8e2d43ae-f2eb-4465-8fe1-c6a3f2a2f4f3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="12.1" HigherBound="13.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="tipoBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f1a0428c-d1cb-43cd-832b-a1aac6653c6d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="13.1" HigherBound="14.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="298bd104-a85c-43ad-8af3-d8dc0c35e8e6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f07553f4-9218-43d4-8d73-cf265ad6a122" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="15.1" HigherBound="16.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b475c2-d1e1-461d-a3ce-31a06c9b5123" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="16.1" HigherBound="17.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7658a2c6-aee5-4d67-a981-fdb73792e87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="17.1" HigherBound="18.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bd76a109-4266-43ae-b3db-a023402f0d2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78ad8ea2-d6b7-43a5-9099-5213a82f91dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="19.1" HigherBound="20.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f58e4c85-4768-415d-8808-3efbed7d576b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="20.1" HigherBound="21.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="backupFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b3f46dd-785b-4520-af09-a5765d883054" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="21.1" HigherBound="22.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3d742c18-539c-495c-92fd-fccdc5f1bd01" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="9.1" HigherBound="10.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiBdeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="796648e1-54dc-492c-87e9-b0a642f2e08c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgTernaBde" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="433a79cb-66aa-450b-ace1-9b00e12098ea" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="83f89d93-8afb-4338-88dd-e847703e16e9" ParentLink="ServiceBody_Statement" LowerBound="24.1" HigherBound="33.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptTernaBdeVq" />
                    <om:Property Name="MessageName" Value="msgTernaBde" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0fcc7f14-4e3b-4d92-a399-7d3193ae675b" ParentLink="ServiceBody_Statement" LowerBound="33.1" HigherBound="48.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); &#xD;&#xA;originalFileName = System.IO.Path.GetFileName(originalFilePath);&#xD;&#xA;backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);&#xD;&#xA;&#xD;&#xA;UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;&#xD;&#xA;tipoBde = &quot;Comando di Variazione Tensione&quot;;&#xD;&#xA;errorMessage.Append(&quot;Nota: il Comando di Variazione Tensione non pevede l'invio dell'informazione ad NDUE.&quot;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="1e596eac-89f0-4609-9c15-b16b8cea71aa" ParentLink="ServiceBody_Statement" LowerBound="48.1" HigherBound="61.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;BDE Codice&quot;, originalFileName,&#xD;&#xA;&quot;BDE Data Inizio&quot;, msgTernaBde.parameter.DataOraInizio.DataOraInizio,&#xD;&#xA;&quot;BDE Data Fine&quot;, msgTernaBde.parameter.DataOraFine.DataOraFine,&#xD;&#xA;&quot;BDE Tipo&quot;, A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionVq,&#xD;&#xA;&quot;UP&quot;, UP,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="ddcd3249-f9e6-430c-810d-635f97dd4576" ParentLink="ServiceBody_Statement" LowerBound="61.1" HigherBound="85.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Mail Notification" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="7ee04f82-3a43-43a6-b27b-91d19e6a96ce" ParentLink="ComplexStatement_Statement" LowerBound="66.1" HigherBound="74.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup Messages" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="4c2bb329-5d5a-4256-8cf0-110d2bdf4eeb" ParentLink="ComplexStatement_Statement" LowerBound="69.1" HigherBound="71.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeVqToEaiBdeNotification" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="fb9bab47-a25f-4882-8a91-b6e1fb09b883" ParentLink="Transform_InputMessagePartRef" LowerBound="70.137" HigherBound="70.158">
                                <om:Property Name="MessageRef" Value="msgTernaBde" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="8f01134f-6a9d-4d22-9842-58aef74f73f7" ParentLink="Transform_OutputMessagePartRef" LowerBound="70.36" HigherBound="70.67">
                                <om:Property Name="MessageRef" Value="msgEaiBdeNotification" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="9044e3e1-9aaa-4f6d-a7f5-ad6a1f675a31" ParentLink="ComplexStatement_Statement" LowerBound="71.1" HigherBound="73.1">
                            <om:Property Name="Expression" Value="msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="File Name" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="991963d8-6d86-441e-a3d7-fe1359068f69" ParentLink="Construct_MessageRef" LowerBound="67.31" HigherBound="67.52">
                            <om:Property Name="Ref" Value="msgEaiBdeNotification" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Exec" OID="a13bc586-8dfe-4340-9340-152c5616d09c" ParentLink="ComplexStatement_Statement" LowerBound="74.1" HigherBound="76.1">
                        <om:Property Name="Invokee" Value="A2A.EAI.INT_BDE.Processes.prcBdeNotification" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Start Notification" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Parameter" OID="08543747-1fcb-4c8d-9678-ac5aa3ce8565" ParentLink="InvokeStatement_Parameter">
                            <om:Property Name="Direction" Value="In" />
                            <om:Property Name="Name" Value="UP" />
                            <om:Property Name="Type" Value="System.String" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Parameter" OID="5eaadc1d-700a-463b-89df-258f14ff9e88" ParentLink="InvokeStatement_Parameter">
                            <om:Property Name="Direction" Value="In" />
                            <om:Property Name="Name" Value="tipoBde" />
                            <om:Property Name="Type" Value="System.String" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Parameter" OID="8d0b70a2-c654-429a-b3f3-59c94db2aa24" ParentLink="InvokeStatement_Parameter">
                            <om:Property Name="Direction" Value="In" />
                            <om:Property Name="Name" Value="resultCode" />
                            <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Parameter" OID="a178eecd-a505-42e2-b356-10c55a44a3bd" ParentLink="InvokeStatement_Parameter">
                            <om:Property Name="Direction" Value="In" />
                            <om:Property Name="Name" Value="msgEaiBdeNotification" />
                            <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.msgEaiBdeNotificationType" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Parameter" OID="eda2fcfe-732c-4536-8b1d-123046a4aa72" ParentLink="InvokeStatement_Parameter">
                            <om:Property Name="Direction" Value="In" />
                            <om:Property Name="Name" Value="activityInstanceId" />
                            <om:Property Name="Type" Value="System.String" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="9f967f12-c322-4829-bd6b-fb6e3fada585" ParentLink="Scope_Catch" LowerBound="79.1" HigherBound="83.1">
                        <om:Property Name="ExceptionType" Value="General Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Generic" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0fed6d50-b6c9-49dd-90f5-b7baf1061770" ParentLink="ServiceBody_Statement" LowerBound="85.1" HigherBound="92.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="f3fce4f6-4f28-4a5e-9abd-148f253686ba" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="7.1" HigherBound="9.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BDE.Processes.typTernaBde" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptTernaBdeVq" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="abf6b62b-e26c-4cde-95a6-c01819142ef3" ParentLink="PortDeclaration_CLRAttribute" LowerBound="7.1" HigherBound="8.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcBdeVq
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typTernaBde rptTernaBdeVq;
        message msgEaiBdeNotificationType msgEaiBdeNotification;
        message msgTernaBdeType msgTernaBde;
        System.String UP;
        System.String tipoBde;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFilePath;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String backupFileName;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("83f89d93-8afb-4338-88dd-e847703e16e9")]
            activate receive (rptTernaBdeVq.Receive, msgTernaBde);
            UP = "";
            tipoBde = "";
            originalFilePath = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            backupFileName = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0fcc7f14-4e3b-4d92-a399-7d3193ae675b")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFilePath = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgTernaBde); 
            originalFileName = System.IO.Path.GetFileName(originalFilePath);
            backupFileName = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgTernaBde);
            
            UP = msgTernaBde.parameter.NomeUpaUca.NomeUpaUca;
            tipoBde = "Comando di Variazione Tensione";
            errorMessage.Append("Nota: il Comando di Variazione Tensione non pevede l'invio dell'informazione ad NDUE.");
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e596eac-89f0-4609-9c15-b16b8cea71aa")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Inviato a N2", "NO",
            "BDE Codice", originalFileName,
            "BDE Data Inizio", msgTernaBde.parameter.DataOraInizio.DataOraInizio,
            "BDE Data Fine", msgTernaBde.parameter.DataOraFine.DataOraFine,
            "BDE Tipo", A2A.EAI.INT_BDE.Services.ProcessServices.FlowDescriptionVq,
            "UP", UP,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ddcd3249-f9e6-430c-810d-635f97dd4576")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7ee04f82-3a43-43a6-b27b-91d19e6a96ce")]
                    construct msgEaiBdeNotification
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4c2bb329-5d5a-4256-8cf0-110d2bdf4eeb")]
                        transform (msgEaiBdeNotification.parameter) = A2A.EAI.INT_BDE.Messaging.Maps.mapTernaBdeVqToEaiBdeNotification (msgTernaBde.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9044e3e1-9aaa-4f6d-a7f5-ad6a1f675a31")]
                        msgEaiBdeNotification.parameter = A2A.EAI.INT_BDE.Services.ProcessServices.FillFileNameIntoNotificationMsg(msgEaiBdeNotification.parameter, originalFileName, backupFileName);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a13bc586-8dfe-4340-9340-152c5616d09c")]
                    exec A2A.EAI.INT_BDE.Processes.prcBdeNotification (UP, tipoBde, resultCode, msgEaiBdeNotification, activityInstanceId);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9f967f12-c322-4829-bd6b-fb6e3fada585")]
                    catch
                    {
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0fed6d50-b6c9-49dd-90f5-b7baf1061770")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BDE.Services.ProcessServices.BdeActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

