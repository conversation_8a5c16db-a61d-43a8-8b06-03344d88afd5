﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="85354681-9670-4e0a-9cac-f6ac31ef63bd" LowerBound="1.1" HigherBound="57.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BDE.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="c5352b31-e830-4020-b92b-0a0e99f9938f" ParentLink="Module_PortType" LowerBound="28.1" HigherBound="35.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typTernaBde" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="3e6285fb-2847-42fe-b6f2-cab4e29947b9" ParentLink="PortType_OperationDeclaration" LowerBound="30.1" HigherBound="34.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="43f84873-2fa1-45cf-9aa4-4c377fc5ca81" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="32.13" HigherBound="32.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgTernaBdeType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="155b93c4-9b30-40c8-9e3f-501e6ea57507" ParentLink="Module_PortType" LowerBound="35.1" HigherBound="42.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typNotification" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ff0fe2fe-6f08-40c7-b90d-01fa4fa08116" ParentLink="PortType_OperationDeclaration" LowerBound="37.1" HigherBound="41.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="770233c6-16bb-409e-92c2-c2a02f13712e" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="39.13" HigherBound="39.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgNotificationType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="4edce45f-900f-4bbd-98f3-76c9ee4833c1" ParentLink="Module_PortType" LowerBound="42.1" HigherBound="49.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typN2BdeValidazione" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="96f1ccd8-79af-4221-b1f3-f134631672f8" ParentLink="PortType_OperationDeclaration" LowerBound="44.1" HigherBound="48.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ValidaBDE" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8e41a0e5-9f61-44de-b1ba-ace4a505ad82" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="46.13" HigherBound="46.43">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="8681e014-eac2-45ae-8ae0-29887f9bdfaf" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="46.78" HigherBound="46.98">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgFaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="4a235e5b-d228-4a5e-86c8-0080f3e89d74" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="46.45" HigherBound="46.76">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BDE.Processes.msgN2BdeValidazioneResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="bf8b00f0-5e67-440c-a0f8-de0ae5fe6738" ParentLink="Module_ServiceDeclaration" LowerBound="49.1" HigherBound="56.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="d34ed97a-966d-4bb6-b445-cf998d0dbad3" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d8d39cf3-d792-476c-9973-c17317318d04" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgFaultType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3c0ac6de-89ba-4944-b25f-3130184c13f9" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="BTS.soap_envelope_1__1.Fault" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultString" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7dd22e44-eccc-4905-9d80-425726b0b235" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgTernaBdeType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="033fd880-174d-4362-86ec-7cfe63bb6731" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schTernaBde" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="1a0b258d-9326-4368-80fa-da4d8f56da24" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgNotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="2acd9c02-58bf-4a5d-9803-44c53141363a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b9ae9c31-bff0-489a-a874-38e2c1a32920" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeValidazioneRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="403031c5-c118-4a42-b17b-996df4ea9046" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeValidazione.ValidaBDE" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9b3d3ac6-633f-4fe8-8fc8-228d80197a67" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgN2BdeValidazioneResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="088ab687-bff5-4b95-a674-04f7b204e758" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeValidazione.ValidaBDEResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="fe058527-685d-49b5-bf5d-aa28ac9d7c51" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiBdeNotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="8f821432-b6cf-4582-b292-d62a7c2e50a8" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeNotification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BDE.Processes
{
    public messagetype msgFaultType
    {
        body BTS.soap_envelope_1__1.Fault faultString;
    };
    internal messagetype msgTernaBdeType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schTernaBde parameter;
    };
    internal messagetype msgNotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameter;
    };
    internal messagetype msgN2BdeValidazioneRequestType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeValidazione.ValidaBDE parameter;
    };
    internal messagetype msgN2BdeValidazioneResponseType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schN2BdeValidazione.ValidaBDEResponse parameter;
    };
    internal messagetype msgEaiBdeNotificationType
    {
        body A2A.EAI.INT_BDE.Messaging.Schemas.schEaiBdeNotification parameter;
    };
    internal porttype typTernaBde
    {
        oneway Receive
        {
            msgTernaBdeType
        };
    };
    internal porttype typNotification
    {
        oneway Send
        {
            msgNotificationType
        };
    };
    internal porttype typN2BdeValidazione
    {
        requestresponse ValidaBDE
        {
            msgN2BdeValidazioneRequestType, msgN2BdeValidazioneResponseType, Fault = msgFaultType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcObjectDefinition
    {
        body ()
        {
        }
    }
}

