﻿using Microsoft.XLANGs.BaseTypes;
using Microsys.EAI.Framework.Azure.Services;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml;

namespace A2A.EAI.INT_BDE.Services
{
    [Serializable]
    public class ProcessServices
    {
        /// <summary>NBDO BDE Activity Name (for BAM purpose)</summary>
        public const string BdeActivityName = "BDE";
        public const string MailSenderActivityName = "Mail Sender";

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_BDE";

        /// <summary>BDE CB Flow Description</summary>
        public const string FlowDescriptionCb = "BDE (Comandi di Bilanciamento)";

        /// <summary>BDE SR Flow Description</summary>
        public const string FlowDescriptionSr = "BDE (Servizio di Regolazione Secondaria)";

        /// <summary>BDE SR Flow Description</summary>
        public const string FlowDescriptionLb = "BDE (Servizio di Limitazione al bilanciamento)";

        /// <summary>BDE RC Flow Description</summary>
        public const string FlowDescriptionRc = "BDE (Servizio di Revoca)";

        /// <summary>BDE VQ Flow Description</summary>
        public const string FlowDescriptionVq = "BDE (Regolazione di Tensione)";

        /// <summary>
        /// 
        /// </summary>
        /// <param name="message"></param>
        /// <returns></returns>
        public static DateTime GetDataCreazioneFile(XLANGMessage message)
        {

            DateTime returnValue = DateTime.UtcNow;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                string inboundTransportType = message.GetPropertyValue(typeof(BTS.InboundTransportType)) as string;

                LoggingServices.TraceDebugInfo("inboundTransportType: {inboundTransportType}", inboundTransportType);

                if (inboundTransportType == "FILE")
                {
                    returnValue = Convert.ToDateTime(message.GetPropertyValue(typeof(FILE.FileCreationTime)), System.Globalization.CultureInfo.InvariantCulture);
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT - returnValue {returnValue}", returnValue);
            }

            return returnValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="notificationMsg"></param>
        /// <param name="fileName"></param>
        /// <param name="url"></param>
        /// <returns></returns>
        public static XmlDocument FillFileNameIntoNotificationMsg(XmlDocument notificationMsg, string fileName, string url)
        {

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                string nodeHtml = string.Format(@"<a href=""{0}"">{1}</a>", url, fileName);

                notificationMsg.InnerXml = notificationMsg.InnerXml.Replace("#FileName#", nodeHtml);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return notificationMsg;

        }

        /// <summary>
        /// </summary>
        /// <param name="inputString"></param>
        /// <param name="splitStringNo"></param>
        /// <returns></returns>
        public static String Split(string inputString, int splitStringNo)
        {
            string returnValue = String.Empty;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                inputString.Trim();

                if (inputString == "" || inputString == ";;;")
                { }
                else
                {
                    returnValue = inputString.Split(new Char[] { ';' })[splitStringNo];
                }
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tIni"></param>
        /// <param name="tFin"></param>
        /// <returns></returns>
        public string GetDurata(string tIni, string tFin)
        {

            string returnValue = String.Empty;

            try
            {

                LoggingServices.TraceDebugInfo("IN");
                DateTime Inizio = DateTime.Parse(tIni.Substring(0, tIni.Length - 2), System.Globalization.CultureInfo.GetCultureInfo("it"));
                DateTime Fine = DateTime.Parse(tFin.Substring(0, tFin.Length - 2), System.Globalization.CultureInfo.GetCultureInfo("it"));

                returnValue = Convert.ToString(Fine.Subtract(Inizio));

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        /// <summary>
        /// Returns retry configuration
        /// </summary>
        /// <param name="retryType">bdeValidationMaxRetryCount - bdeValidationRetryInterval - bdeInsertMaxRetryCount - bdeInsertRetryInterval</param>
        /// <returns></returns>
        public static int GetMaxRetryConfiguration(string retryType)
        {
            int returnValue = 0;

            switch (retryType)
            {
                case "bdeValidationMaxRetryCount":
                    returnValue = 3;
                    break;
                case "bdeValidationRetryInterval":
                    returnValue = 30;
                    break;
                case "bdeInsertMaxRetryCount":
                    returnValue = 3;
                    break;
                case "bdeInsertRetryInterval":
                    returnValue = 5;
                    break;
                default:
                    break;
            }

            return returnValue;
        }


        public static void AddBdeRelationShip(XLANGPart message, string callerOrchestrationId)
        {
            MailSenderResponse returnValue = new MailSenderResponse();
            string responseBody = string.Empty;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                var body = (Stream)message.RetrieveAs(typeof(Stream));

                StreamReader reader = new StreamReader(body);
                responseBody = reader.ReadToEnd();

                dynamic bodyObject = JsonConvert.DeserializeObject<dynamic>(responseBody);

                returnValue.operation_status = bodyObject.operation_status;
                returnValue.errorMessage = bodyObject.errorMessage;
                returnValue.value = bodyObject.value;

                if (!string.IsNullOrEmpty(returnValue.value) && !string.IsNullOrEmpty(callerOrchestrationId))
                BamHelper.AddRelationship(
                    BdeActivityName,
                    callerOrchestrationId,
                    MailSenderActivityName,
                    returnValue.value,
                    ""
                );

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc, responseBody);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT - Return {returnValue}", returnValue);
            }

            
        }


        [Serializable]
        public class MailSenderResponse
        {
            public string operation_status { get; set; }
            public string value { get; set; }
            public string errorMessage { get; set; }
        }

    }

    
}
