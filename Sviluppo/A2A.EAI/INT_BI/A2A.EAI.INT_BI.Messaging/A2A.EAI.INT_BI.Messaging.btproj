﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BDCA928B-A5FE-4DAD-B0EF-275A11979B60}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_BI.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_BI.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ContextAccessor, Version=*******, Culture=neutral, PublicKeyToken=8c24991755142725, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Schema Include="Schemas\Budget\BudgetInsertTypedProcedure.Type.xsd">
      <TypeName>BudgetInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Budget</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Budget\BudgetInsertTypedProcedure.Table.xsd">
      <TypeName>BudgetInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Budget</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Budget\BudgetInsertTypedProcedure.xsd">
      <TypeName>BudgetInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Budget</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Budget\BudgetSbilanciamenti.Type.xsd">
      <TypeName>BudgetSbilanciamenti_Type</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Budget</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Budget\BudgetSbilanciamenti.xsd">
      <TypeName>BudgetSbilanciamenti</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Budget</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnello.xsd">
      <TypeName>EzekeAnello</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfronto.xsd">
      <TypeName>EzekeConfronto</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeSceltaDispaccEvidenze.xsd">
      <TypeName>EzekeSceltaDispaccEvidenze</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeSceltaDispaccModuli.xsd">
      <TypeName>EzekeSceltaDispaccModuli</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tSUC.xsd">
      <TypeName>eZK2B_tSUC</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tQuantita.xsd">
      <TypeName>eZK2B_tQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tParco.xsd">
      <TypeName>eZK2B_tParco</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tMargini.xsd">
      <TypeName>eZK2B_tMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tkeyQuantita.xsd">
      <TypeName>eZK2B_tkeyQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tkeyMargini.xsd">
      <TypeName>eZK2B_tkeyMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\eZK2B_tINFO.xsd">
      <TypeName>eZK2B_tINFO</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Forecast\ForecastInsertTypedProcedure.Type.xsd">
      <TypeName>ForecastInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Forecast</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Forecast\ForecastInsertTypedProcedure.Table.xsd">
      <TypeName>ForecastInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Forecast</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Forecast\ForecastInsertTypedProcedure.xsd">
      <TypeName>ForecastInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Forecast</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\EzekeMatlabZipFileInsertTypedProcedure.Table.xsd">
      <TypeName>EzekeMatlabZipFileInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\EzekeMatlabZipFileInsertTypedProcedure.xsd">
      <TypeName>EzekeMatlabZipFileInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\EzekeMatlabZipFileProcessTypedPolling.xsd">
      <TypeName>EzekeMatlabZipFileProcessTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\EzekeMatlabZipFileProcessTypedProcedure.xsd">
      <TypeName>EzekeMatlabZipFileProcessTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EzekeMatlab\EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.xsd">
      <TypeName>EzekeMatlabZipFileProcessUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccModuliInsertTypedProcedure.Table.xsd">
      <TypeName>EzekeDispaccModuliInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccModuliInsertTypedProcedure.xsd">
      <TypeName>EzekeDispaccModuliInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccEvidenzeInsertTypedProcedure.Table.xsd">
      <TypeName>EzekeDispaccEvidenzeInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccEvidenzeInsertTypedProcedure.xsd">
      <TypeName>EzekeDispaccEvidenzeInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccProcessTypedProcedure.xsd">
      <TypeName>EzekeDispaccProcessTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccProcessUpdateStatusTypedProcedure.xsd">
      <TypeName>EzekeDispaccProcessUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeDispaccProcessTypedPolling.xsd">
      <TypeName>EzekeDispaccProcessTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnelloInsertTypedProcedure.Table.xsd">
      <TypeName>EzekeAnelloInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnelloInsertTypedProcedure.xsd">
      <TypeName>EzekeAnelloInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnelloProcessTypedProcedure.xsd">
      <TypeName>EzekeAnelloProcessTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnelloProcessUpdateStatusTypedProcedure.xsd">
      <TypeName>EzekeAnelloProcessUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfrontoInsertTypedProcedure.Table.xsd">
      <TypeName>EzekeConfrontoInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfrontoInsertTypedProcedure.xsd">
      <TypeName>EzekeConfrontoInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfrontoProcessTypedProcedure.xsd">
      <TypeName>EzekeConfrontoProcessTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfrontoProcessUpdateStatusTypedProcedure.xsd">
      <TypeName>EzekeConfrontoProcessUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeAnelloProcessTypedPolling.xsd">
      <TypeName>EzekeAnelloProcessTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Ezeke\EzekeConfrontoProcessTypedPolling.xsd">
      <TypeName>EzekeConfrontoProcessTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\Forecast\Forecast.xsd">
      <TypeName>Forecast</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Forecast</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Forecast\Forecast.Type.xsd">
      <TypeName>Forecast_Type</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Schemas.Forecast</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <Map Include="Maps\Forecast\ForecastToForecastInsert.btm">
      <TypeName>ForecastToForecastInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Forecast</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\EzekeMatlab\EzekeMatlabZipFileInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\EzekeMatlab\EzekeMatlabZipFileProcessTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\EzekeMatlab\EzekeMatlabZipFileProcessTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\EzekeMatlab\EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeDispaccModuliInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeDispaccEvidenzeInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeDispaccProcessTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeDispaccProcessUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeDispaccProcessTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeAnelloInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeAnelloProcessTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeAnelloProcessUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeConfrontoInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeConfrontoProcessTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeConfrontoProcessUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeAnelloProcessTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\Ezeke\EzekeConfrontoProcessTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\Budget\BudgetInsertTypedProcedure.bindinginfo.xml" />
    <Pipeline Include="Pipeline\In\EzekeSceltaDispaccUnzip.btp">
      <TypeName>EzekeSceltaDispaccUnzip</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Budget\BudgetSbilanciamentiToBudgetInsert.btm">
      <TypeName>BudgetSbilanciamentiToBudgetInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Budget</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipeline\In\BudgetSbilanciamenti.btp">
      <TypeName>BudgetSbilanciamenti</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Ezeke\EzekeConfrontoProcessPollingToUpdateStatus.btm">
      <TypeName>EzekeConfrontoProcessPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeConfrontoProcessPollingToProcess.btm">
      <TypeName>EzekeConfrontoProcessPollingToProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeConfrontoToDbInsert.btm">
      <TypeName>EzekeConfrontoToDbInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeAnelloProcessPollingToUpdateStatus.btm">
      <TypeName>EzekeAnelloProcessPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeAnelloProcessPollingToProcess.btm">
      <TypeName>EzekeAnelloProcessPollingToProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipeline\In\EzekeConfronto.btp">
      <TypeName>EzekeConfronto</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\EzekeAnello.btp">
      <TypeName>EzekeAnello</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Ezeke\EzekeAnelloToDbInsert.btm">
      <TypeName>EzekeAnelloToDbInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeDispaccProcessPollingToUpdateStatus.btm">
      <TypeName>EzekeDispaccProcessPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeDispaccProcessPollingToProcess.btm">
      <TypeName>EzekeDispaccProcessPollingToProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeSceltaDispaccEvidenzeToDbInsert.btm">
      <TypeName>EzekeSceltaDispaccEvidenzeToDbInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Ezeke\EzekeSceltaDispaccModuliToDbInsert.btm">
      <TypeName>EzekeSceltaDispaccModuliToDbInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.Ezeke</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipeline\In\EzekeSceltaDispaccEvidenze.btp">
      <TypeName>EzekeSceltaDispaccEvidenze</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\EzekeSceltaDispaccModuli.btp">
      <TypeName>EzekeSceltaDispaccModuli</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\EzekeMatlab\EzekeMatlabZipFileProcessPollingToUpdateStatus.btm">
      <TypeName>EzekeMatlabZipFileProcessPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\EzekeMatlab\EzekeMatlabZipFileProcessPollingToProcess.btm">
      <TypeName>EzekeMatlabZipFileProcessPollingToProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tSUCToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tSUCToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tkeyQuantitaToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tkeyQuantitaToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tMarginiToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tMarginiToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tParcoToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tParcoToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tQuantitaToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tQuantitaToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tkeyMarginiToEzekeMatlabZipFileInsert.btm">
      <SubType>Task</SubType>
      <TypeName>eZK2B_tkeyMarginiToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
    </Map>
    <Map Include="Maps\EzekeMatlab\eZK2B_tINFOToEzekeMatlabZipFileInsert.btm">
      <TypeName>eZK2B_tINFOToEzekeMatlabZipFileInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tSUC.btp">
      <TypeName>Ezeke_eZK2B_tSUC</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tQuantita.btp">
      <TypeName>Ezeke_eZK2B_tQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tParco.btp">
      <TypeName>Ezeke_eZK2B_tParco</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tMargini.btp">
      <TypeName>Ezeke_eZK2B_tMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tkeyQuantita.btp">
      <TypeName>Ezeke_eZK2B_tkeyQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tkeyMargini.btp">
      <TypeName>Ezeke_eZK2B_tkeyMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Ezeke_eZK2B_tINFO.btp">
      <TypeName>Ezeke_eZK2B_tINFO</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\ExekeMatlabUnzip.btp">
      <TypeName>ExekeMatlabUnzip</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipeline\In\Forecast.btp">
      <TypeName>Forecast</TypeName>
      <Namespace>A2A.EAI.INT_BI.Messaging.Pipeline.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Content Include="Bindings\Forecast\ForecastInsertTypedProcedure.bindinginfo.xml" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>