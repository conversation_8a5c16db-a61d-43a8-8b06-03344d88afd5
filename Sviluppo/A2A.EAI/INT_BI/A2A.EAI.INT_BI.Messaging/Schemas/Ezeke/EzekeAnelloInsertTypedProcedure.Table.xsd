<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.eai</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="EzekeAnelloType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Data" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Ora" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Zona" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="10" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="BloccoOn" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Chiuso" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EzekeAnelloType" nillable="true" type="ns3:EzekeAnelloType" />
  <xs:complexType name="ArrayOfEzekeAnelloType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EzekeAnelloType" type="ns3:EzekeAnelloType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEzekeAnelloType" nillable="true" type="ns3:ArrayOfEzekeAnelloType" />
</xs:schema>