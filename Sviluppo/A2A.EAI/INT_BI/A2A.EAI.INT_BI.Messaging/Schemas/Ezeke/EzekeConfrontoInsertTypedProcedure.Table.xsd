<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.eai</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="EzekeConfrontoType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="idScen_PzReali" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="idScen_PzRisim" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Data" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Ora" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="idZona" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Prezzo_PzReali" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Prezzo_PzRisim" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Zona" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="6" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EzekeConfrontoType" nillable="true" type="ns3:EzekeConfrontoType" />
  <xs:complexType name="ArrayOfEzekeConfrontoType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EzekeConfrontoType" type="ns3:EzekeConfrontoType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEzekeConfrontoType" nillable="true" type="ns3:ArrayOfEzekeConfrontoType" />
</xs:schema>