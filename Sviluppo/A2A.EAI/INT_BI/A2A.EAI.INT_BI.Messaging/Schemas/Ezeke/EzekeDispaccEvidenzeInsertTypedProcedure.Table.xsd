<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.eai</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="EzekeDispaccEvidenzeType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="dataRif" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="oraInizio" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="oraFine" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="zona" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="evidenze" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="valore" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EzekeDispaccEvidenzeType" nillable="true" type="ns3:EzekeDispaccEvidenzeType" />
  <xs:complexType name="ArrayOfEzekeDispaccEvidenzeType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EzekeDispaccEvidenzeType" type="ns3:EzekeDispaccEvidenzeType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEzekeDispaccEvidenzeType" nillable="true" type="ns3:ArrayOfEzekeDispaccEvidenzeType" />
</xs:schema>