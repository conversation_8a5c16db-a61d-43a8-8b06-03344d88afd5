<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/eai" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.eai</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="EzekeDispaccModuliType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="dataRif" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="oraInizio" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="oraFine" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="zona" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="moduli" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="note" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="nrModuliIndisp" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="EzekeDispaccModuliType" nillable="true" type="ns3:EzekeDispaccModuliType" />
  <xs:complexType name="ArrayOfEzekeDispaccModuliType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EzekeDispaccModuliType" type="ns3:EzekeDispaccModuliType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEzekeDispaccModuliType" nillable="true" type="ns3:ArrayOfEzekeDispaccModuliType" />
</xs:schema>