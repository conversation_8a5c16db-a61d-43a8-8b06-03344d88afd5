﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{159C72A0-F5DA-4A8C-A7F9-16BAE84D9132}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_BI.Processes</RootNamespace>
    <AssemblyName>A2A.EAI.INT_BI.Processes</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Bam.XLANGs, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsoft.BizTalk.Bam.XLANGs\v4.0_3.0.1.0__31bf3856ad364e35\Microsoft.BizTalk.Bam.XLANGs.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Azure.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ec334306cc72c98, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Azure.Services\v4.0_1.0.0.0__0ec334306cc72c98\Microsys.EAI.Framework.Azure.Services.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Schemas, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Schemas\v4.0_1.0.0.0__e651f0f96466e9b3\Microsys.EAI.Framework.Schemas.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Services\v4.0_1.0.0.0__e651f0f96466e9b3\Microsys.EAI.Framework.Services.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
  </ItemGroup>
  <ItemGroup>
    <XLang Include="ForecastInsert.odx">
      <TypeName>ForecastInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Services\A2A.EAI.Common.Services.csproj">
      <Project>{16facaa8-43de-45b6-b1a8-bbbc0b8f7bbf}</Project>
      <Name>A2A.EAI.Common.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_BI.Messaging\A2A.EAI.INT_BI.Messaging.btproj">
      <Project>{bdca928b-a5fe-4dad-b0ef-275a11979b60}</Project>
      <Name>A2A.EAI.INT_BI.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_BI.Services\A2A.EAI.INT_BI.Services.csproj">
      <Project>{8c9dffac-7372-4863-907e-f63b05ee0d5f}</Project>
      <Name>A2A.EAI.INT_BI.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="ObjectDefinition.odx">
      <TypeName>ObjectDefinition</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tINFO.odx">
      <TypeName>EzekeMatlab_tINFO</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tkeyMargini.odx">
      <TypeName>EzekeMatlab_tkeyMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tkeyQuantita.odx">
      <TypeName>EzekeMatlab_tkeyQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tMargini.odx">
      <TypeName>EzekeMatlab_tMargini</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tParco.odx">
      <TypeName>EzekeMatlab_tParco</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tQuantita.odx">
      <TypeName>EzekeMatlab_tQuantita</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlab_tSUC.odx">
      <TypeName>EzekeMatlab_tSUC</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeMatlabZipFileProcess.odx">
      <TypeName>EzekeMatlabZipFileProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeDispaccEvidenze.odx">
      <TypeName>EzekeDispaccEvidenze</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeDispaccModuli.odx">
      <TypeName>EzekeDispaccModuli</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeDispaccProcess.odx">
      <TypeName>EzekeDispaccProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeAnello.odx">
      <TypeName>EzekeAnello</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeConfronto.odx">
      <TypeName>EzekeConfronto</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeAnelloProcess.odx">
      <TypeName>EzekeAnelloProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="EzekeConfrontoProcess.odx">
      <TypeName>EzekeConfrontoProcess</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="BudgetSbilanciamentiInsert.odx">
      <TypeName>BudgetSbilanciamentiInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>