﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="40eb02fa-4280-4290-bf89-bd1b7be1b3a3" LowerBound="1.1" HigherBound="188.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="2ff21063-8fd2-414f-81f0-a7f474d8f661" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="187.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnello" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="6b546434-ba61-4a84-b451-6bf1bf5045f2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c8206546-471d-49a8-b06d-881391e87ed9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c6787591-c13f-4d10-819e-55b244702269" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e0e8c33a-dfd2-41e0-af7f-46ff5763c416" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9416796f-0145-4b7d-9bd9-e4735c2e2e8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bf69101e-730b-4200-bbe6-e2599cf59da4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4bab145a-7e55-4bbd-9db3-0ba2eadf2019" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426d7e54-accd-4e49-acbf-f5af50db7dfe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="60abd9cc-50a5-4b2c-a62f-fa5935b48ac6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="14929e2d-9030-44eb-8ad6-8e5693518d3b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="33d2dc37-aca7-43a2-b40f-e13001653b9a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3ae2e0df-35f3-4446-95b6-af2c9a4ac631" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="be79a23c-69f4-4b76-ae6e-e7776422bbc1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloFile" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="0b03a993-2842-4216-a0db-b89c11252a0f" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="08598cec-10cf-44ed-b8d7-e43b7fda5ea2" ParentLink="ServiceBody_Statement" LowerBound="55.1" HigherBound="61.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeAnelloIn" />
                    <om:Property Name="MessageName" Value="EzekeAnelloFile" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="cdb64a9c-5109-4a76-9de7-056bc14e3a45" ParentLink="ServiceBody_Statement" LowerBound="61.1" HigherBound="75.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeAnelloFile));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeAnelloFile);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5ca9418e-8e44-4880-aaf2-76dcfd6038eb" ParentLink="ServiceBody_Statement" LowerBound="75.1" HigherBound="86.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, originalFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloStaging&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="35f2ec36-741e-4e11-8ab3-8f57cc3fb602" ParentLink="ServiceBody_Statement" LowerBound="86.1" HigherBound="144.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="1f4f4a41-b6f2-4c38-9c2e-bcb0fbd343bd" ParentLink="ComplexStatement_Statement" LowerBound="91.1" HigherBound="100.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="c7d651b9-a34d-436d-a30c-fdabe0c53f1d" ParentLink="Construct_MessageRef" LowerBound="92.31" HigherBound="92.55">
                            <om:Property Name="Ref" Value="EzekeAnelloInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="7e396191-19a2-4984-a4ed-b8d05895ee14" ParentLink="ComplexStatement_Statement" LowerBound="94.1" HigherBound="96.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloToDbInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="ff810ed0-fa2c-4a1f-b7c7-ad24a2f10262" ParentLink="Transform_OutputMessagePartRef" LowerBound="95.36" HigherBound="95.70">
                                <om:Property Name="MessageRef" Value="EzekeAnelloInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="7c33c56f-15a3-4d6c-bd90-e50dde5d37f3" ParentLink="Transform_InputMessagePartRef" LowerBound="95.134" HigherBound="95.159">
                                <om:Property Name="MessageRef" Value="EzekeAnelloFile" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="a3900b83-06b6-4a58-bfe4-a6a1375b3786" ParentLink="ComplexStatement_Statement" LowerBound="96.1" HigherBound="99.1">
                            <om:Property Name="Expression" Value="EzekeAnelloInsertRequest.parameter.fileName = originalFileName;&#xD;&#xA;EzekeAnelloInsertRequest.parameter.idEzekeAnelloFile = activityInstanceId;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="666890b8-eabd-4507-9f61-355403f167f8" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="102.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="9fc44998-e515-4cec-a6bd-a2a7262f2b0a" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                        <om:Property Name="PortName" Value="EzekeAnelloInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloInsertRequest" />
                        <om:Property Name="OperationName" Value="EzekeAnelloInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="eba090e4-69fd-402a-aa56-e31e6dc040c0" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeAnelloInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloInsertResponse" />
                        <om:Property Name="OperationName" Value="EzekeAnelloInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="77b5206f-0b37-4819-ade5-d5fc15fe451a" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="112.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="2715e8e2-b6f7-4309-b05f-26ba80fd4de8" ParentLink="Scope_Catch" LowerBound="115.1" HigherBound="129.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="7f8c0a1c-f076-4587-b776-51e8d094dbf3" ParentLink="Catch_Statement" LowerBound="118.1" HigherBound="128.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="fa34e623-3855-43cf-a0f7-10fbac4c3bfa" ParentLink="Scope_Catch" LowerBound="129.1" HigherBound="142.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="eddb584e-66f8-4d56-b025-d67614f7aacc" ParentLink="Catch_Statement" LowerBound="132.1" HigherBound="141.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="aec65b80-a2a8-4f6a-9688-2675f958a36a" ParentLink="ServiceBody_Statement" LowerBound="144.1" HigherBound="177.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="ab86ea99-2297-48f3-8438-6daeffa00a07" ParentLink="ReallyComplexStatement_Branch" LowerBound="145.13" HigherBound="148.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="44f3a34b-a164-4cc0-ba0b-fc687cd87bc0" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="5a7f06e3-3bb8-4dbd-83d9-04247bab8321" ParentLink="ComplexStatement_Statement" LowerBound="150.1" HigherBound="176.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="cc72a62e-6ec5-49bc-beac-06140a9be107" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="172.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="d399aba4-3310-4ed8-bfbc-2900e173335c" ParentLink="Construct_MessageRef" LowerBound="156.35" HigherBound="156.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="33617ff6-e6b9-48f1-89ff-27dcb24942ae" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="171.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloStaging;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="87b09cde-9283-42ca-a257-6e472cebb4d5" ParentLink="ComplexStatement_Statement" LowerBound="172.1" HigherBound="174.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="430cf853-add7-4cdb-8627-c1b57c18b7be" ParentLink="ServiceBody_Statement" LowerBound="177.1" HigherBound="185.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="12a119cc-3663-4627-8171-6b1588c890d7" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="c7ba7247-6e9f-4abf-ade8-2e5a7c55900b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e3caca8b-5b84-420c-8406-8a6e54e417d1" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="45" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="eb47dad3-d691-4eaf-9083-af4754733941" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ab2205d5-3c9b-4684-aac7-4dd14fa257b3" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="107" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="6aca944d-7f99-421c-853c-b261645e9921" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b4af1a59-c864-4495-a55b-1be85af989cf" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="28fad75e-8843-4ae0-b677-fc5926bcfd77" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnello" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bd298121-e37d-4314-a296-273305b4a748" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="b2f966b0-6876-441d-86c7-8431fbca86e0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloInsertTypedProcedure.EzekeAnelloInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="43920eec-cca7-4732-bce0-c6f9dfe85125" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a07602a8-9920-4ab7-928b-b7b283789994" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloInsertTypedProcedure.EzekeAnelloInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="c20790f5-4a57-48ce-9ee9-3863baf6c75c" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="79b4127f-02e1-407d-a7be-516b91e5eb32" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b85fb176-73b8-4a79-91eb-a49af09adafb" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="8dee318a-4bd0-44ad-80fe-43e566b43979" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="8d76979f-08b4-4258-a811-59c1896f3838" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="82ff7fbd-c22b-4af3-a85d-51f65e1c03fc" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.41">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="54df2e1d-ad55-4315-a338-8b88177e66e4" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.43" HigherBound="27.72">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeAnelloType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnello parameter;
    };
    internal messagetype EzekeAnelloInsertRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloInsertTypedProcedure.EzekeAnelloInsert parameter;
    };
    internal messagetype EzekeAnelloInsertResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloInsertTypedProcedure.EzekeAnelloInsertResponse parameter;
    };
    internal porttype EzekeAnelloInType
    {
        oneway Receive
        {
            EzekeAnelloType
        };
    };
    internal porttype EzekeAnelloInsertOutType
    {
        requestresponse EzekeAnelloInsert
        {
            EzekeAnelloInsertRequestType, EzekeAnelloInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeAnello
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeAnelloInType EzekeAnelloIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeAnelloInsertOutType EzekeAnelloInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeAnelloInsertRequestType EzekeAnelloInsertRequest;
        message EzekeAnelloInsertResponseType EzekeAnelloInsertResponse;
        message EzekeAnelloType EzekeAnelloFile;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("08598cec-10cf-44ed-b8d7-e43b7fda5ea2")]
            activate receive (EzekeAnelloIn.Receive, EzekeAnelloFile);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cdb64a9c-5109-4a76-9de7-056bc14e3a45")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeAnelloFile));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeAnelloFile);
            
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ca9418e-8e44-4880-aaf2-76dcfd6038eb")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", originalFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloStaging
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("35f2ec36-741e-4e11-8ab3-8f57cc3fb602")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1f4f4a41-b6f2-4c38-9c2e-bcb0fbd343bd")]
                    construct EzekeAnelloInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7e396191-19a2-4984-a4ed-b8d05895ee14")]
                        transform (EzekeAnelloInsertRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloToDbInsert (EzekeAnelloFile.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a3900b83-06b6-4a58-bfe4-a6a1375b3786")]
                        EzekeAnelloInsertRequest.parameter.fileName = originalFileName;
                        EzekeAnelloInsertRequest.parameter.idEzekeAnelloFile = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("666890b8-eabd-4507-9f61-355403f167f8")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9fc44998-e515-4cec-a6bd-a2a7262f2b0a")]
                    send (EzekeAnelloInsertOut.EzekeAnelloInsert, EzekeAnelloInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eba090e4-69fd-402a-aa56-e31e6dc040c0")]
                    receive (EzekeAnelloInsertOut.EzekeAnelloInsert, EzekeAnelloInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("77b5206f-0b37-4819-ade5-d5fc15fe451a")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2715e8e2-b6f7-4309-b05f-26ba80fd4de8")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f8c0a1c-f076-4587-b776-51e8d094dbf3")]
                        errorMessage.Append("INT_BI - Ezeke Anello - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fa34e623-3855-43cf-a0f7-10fbac4c3bfa")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("eddb584e-66f8-4d56-b025-d67614f7aacc")]
                        errorMessage.Append("INT_BI - Ezeke Anello - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("aec65b80-a2a8-4f6a-9688-2675f958a36a")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("5a7f06e3-3bb8-4dbd-83d9-04247bab8321")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc72a62e-6ec5-49bc-beac-06140a9be107")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("33617ff6-e6b9-48f1-89ff-27dcb24942ae")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloStaging;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("87b09cde-9283-42ca-a257-6e472cebb4d5")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("430cf853-add7-4cdb-8627-c1b57c18b7be")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

