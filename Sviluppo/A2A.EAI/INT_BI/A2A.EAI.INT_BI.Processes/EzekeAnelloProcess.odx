﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="7461e505-e8ac-4b97-9e64-5390d70e0524" LowerBound="1.1" HigherBound="248.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="aa704624-8b8b-4879-b19a-a79c52ef45e0" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="247.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="6278715a-ba92-4786-a27b-15bef684d082" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="acbf5f27-3704-4411-8b8d-41f61bfa3b9c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cf761e5e-f1a4-48eb-997b-2dd083bdfd85" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8cadcd34-9648-4e8e-83b1-ecaf79838757" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ba68a939-2adc-4c93-9e8e-8c750d68490c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9331556f-a41e-42e1-a520-dfb7d43cb307" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="865657ad-062b-4bd1-b235-348b09ac8c2f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="92f04edc-fdae-42e9-b9d2-70c706daa4a7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8dca5514-805f-4fd3-a5b3-8d4bff519276" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="59887e43-ed28-4210-b3f5-b39b1bda103a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e3b39e65-d657-4d3e-a322-25d57db3cf8d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="90d0474d-4149-49d6-8a99-82333e9a104a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="998abb86-c1f0-46d3-96d1-b92d0809c23e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="a79f0634-9521-4c6d-8e5f-fd4d0d2a7cfa" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="cc40b963-95ab-4f2b-a161-d455e76a4087" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="77.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeAnelloProcessPollingIn" />
                    <om:Property Name="MessageName" Value="EzekeAnelloProcessPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="c0b75756-4ed1-4daa-a4be-e2d4a423c687" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="87.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="502c9867-14d3-40dd-8aa0-2f9a8729b447" ParentLink="ServiceBody_Statement" LowerBound="87.1" HigherBound="100.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, EzekeAnelloProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeAnelloFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloProcess&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeAnelloProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeAnelloFile, &quot;Anello&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="8ae9d8e4-cbe7-44fe-978d-c2d93ff7306d" ParentLink="ServiceBody_Statement" LowerBound="100.1" HigherBound="153.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="75b310b6-0f52-4056-a8d5-98d87a81bf2e" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="111.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="00b36a59-854b-45a3-8c80-58df2655e73e" ParentLink="Construct_MessageRef" LowerBound="106.31" HigherBound="106.56">
                            <om:Property Name="Ref" Value="EzekeAnelloProcessRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="c8a9e7f9-468a-4148-96e5-7deac78d4781" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="110.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloProcessPollingToProcess" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="12e85a3c-201d-4c50-a8ab-d00d7bb27519" ParentLink="Transform_InputMessagePartRef" LowerBound="109.148" HigherBound="109.183">
                                <om:Property Name="MessageRef" Value="EzekeAnelloProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="c8404971-c5eb-4479-90e8-94d20a4935c3" ParentLink="Transform_OutputMessagePartRef" LowerBound="109.36" HigherBound="109.71">
                                <om:Property Name="MessageRef" Value="EzekeAnelloProcessRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="f37e8428-3e93-4dde-8e14-46f813c2f919" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="113.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="3abe6d56-7c30-4eda-8406-41980967b1fd" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="115.1">
                        <om:Property Name="PortName" Value="EzekeAnelloProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloProcessRequest" />
                        <om:Property Name="OperationName" Value="EzekeAnelloProcess" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="eb2d6086-8388-4bc8-a497-9e3adf8fa17b" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="117.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeAnelloProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloProcessResponse" />
                        <om:Property Name="OperationName" Value="EzekeAnelloProcess" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="4286ccb4-e1fc-41c4-91e0-624d967bd12f" ParentLink="ComplexStatement_Statement" LowerBound="117.1" HigherBound="123.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="f45769d5-ab45-4431-a09a-d0dfcdc6e895" ParentLink="Scope_Catch" LowerBound="126.1" HigherBound="139.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="184d26e5-666a-4527-84e0-9fc69074f396" ParentLink="Catch_Statement" LowerBound="129.1" HigherBound="138.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="15d7b521-3aae-4b12-9bce-d0e0fee5f172" ParentLink="Scope_Catch" LowerBound="139.1" HigherBound="151.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0305da9b-f4a1-4745-ade8-cf43601bc961" ParentLink="Catch_Statement" LowerBound="142.1" HigherBound="150.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="4b444999-ac2a-4464-9599-e7a6400df25d" ParentLink="ServiceBody_Statement" LowerBound="153.1" HigherBound="204.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="9ed28eee-3eac-4364-b237-22e65b6b1fbd" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="167.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="a11b6e43-55c1-4b66-97b4-29efe54a82c4" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="163.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloProcessPollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="42be460a-0197-4412-b682-9837ff2e111c" ParentLink="Transform_OutputMessagePartRef" LowerBound="162.36" HigherBound="162.83">
                                <om:Property Name="MessageRef" Value="EzekeAnelloProcessUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="ac90eec8-5836-4633-ade3-b137a1d74bfe" ParentLink="Transform_InputMessagePartRef" LowerBound="162.165" HigherBound="162.200">
                                <om:Property Name="MessageRef" Value="EzekeAnelloProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="c9c57d19-3933-4900-ab8e-01ab35688147" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="166.1">
                            <om:Property Name="Expression" Value="EzekeAnelloProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;EzekeAnelloProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="4e3aa890-29f4-4d52-a2af-ee47b2bff9c4" ParentLink="Construct_MessageRef" LowerBound="159.31" HigherBound="159.68">
                            <om:Property Name="Ref" Value="EzekeAnelloProcessUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="56f9a3ad-15d9-46d3-8e91-6c5ff2703c67" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                        <om:Property Name="PortName" Value="EzekeAnelloProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloProcessUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="EzekeAnelloProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="68dad280-29e1-41d9-b859-c10487e79d50" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeAnelloProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeAnelloProcessUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="EzekeAnelloProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="f527d638-a339-4210-9baa-92a42c11ec67" ParentLink="Scope_Catch" LowerBound="174.1" HigherBound="188.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="78729364-2885-4bae-98ad-23b62ca869d4" ParentLink="Catch_Statement" LowerBound="177.1" HigherBound="187.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="440f2de0-ea70-4eed-a5aa-74831141127b" ParentLink="Scope_Catch" LowerBound="188.1" HigherBound="202.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d743ba45-d942-477b-a775-9236b1b82001" ParentLink="Catch_Statement" LowerBound="191.1" HigherBound="201.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Anello Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="ffe58302-e1e4-4381-bc53-11285961229b" ParentLink="ServiceBody_Statement" LowerBound="204.1" HigherBound="237.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="730fcb5d-f548-4f0b-ab51-2d5565e64d95" ParentLink="ReallyComplexStatement_Branch" LowerBound="205.13" HigherBound="208.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="85c6f782-d661-4aa3-9776-0321048d5c95" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="c26d9b7f-a44a-4553-9e88-70c26f9e52ed" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="236.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="ad786cd5-e522-4886-9595-2bcee73d8014" ParentLink="ComplexStatement_Statement" LowerBound="215.1" HigherBound="232.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="d0727c29-0a5d-47f2-9010-07a1761b1d11" ParentLink="Construct_MessageRef" LowerBound="216.35" HigherBound="216.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="8327839a-46ae-484d-87f4-56fe27e51691" ParentLink="ComplexStatement_Statement" LowerBound="218.1" HigherBound="231.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="6093449e-06e3-4aed-8437-24778dccdf3a" ParentLink="ComplexStatement_Statement" LowerBound="232.1" HigherBound="234.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="11cd7f61-49b3-4d89-bd18-75802faea834" ParentLink="ServiceBody_Statement" LowerBound="237.1" HigherBound="245.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="20138e9d-371c-4ab5-9319-a35b10e11e9a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="5fcf0dd0-8e27-4640-87a0-4ad9bd79fb4d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="93eeb7ae-a22d-4563-906e-6bac0a94ef49" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="39" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="0db332e3-a1b3-4cfa-b354-09b5ba87fe42" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9c8621a4-40e3-4988-9e10-80768140f37f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="56.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="97" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="af55561b-272e-4560-9be3-e1159be17a24" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="152994fc-f7e6-405a-888f-91cd1621f71c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="56.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="149" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="d4083039-690c-498c-bd2e-eb266ba63d08" ParentLink="PortDeclaration_CLRAttribute" LowerBound="56.1" HigherBound="57.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9aca8ed5-162c-4027-81b7-7f700093c2ee" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6cf23a06-d144-4344-bcde-014c6cdb8991" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="25ca6580-6e67-4945-ba82-631e63827c91" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="69c35a1d-ca77-419a-92f3-9af7d082f911" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedProcedure.EzekeAnelloProcess" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c3e38506-28ad-4b32-89c0-a41b8e16e2b1" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c3776c06-0e3d-4419-99c8-c6b99803f491" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedProcedure.EzekeAnelloProcessResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="af0b373b-5337-40c9-a166-df2843f85481" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a3902f57-f9bf-4e72-8118-785273d05861" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessUpdateStatusTypedProcedure.EzekeAnelloProcessUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0afb3bf2-f473-4400-ba37-9ad7af3b3c21" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e5014b81-6fa6-4b9d-aa55-90495ca22921" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessUpdateStatusTypedProcedure.EzekeAnelloProcessUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="80db04da-bbd4-472e-b8ad-aa0bea020c79" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="e27d39a7-2b1f-406c-acca-0b7828512ade" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9fc7a42a-ed2a-4f3e-8984-b9fc7a9872cf" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.42">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="81706b66-83e5-40a1-858c-146c682c82b9" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ae7c8348-c457-48a7-8174-e097f429dcf4" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcess" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="cbad2e28-7203-42be-83d7-2cd9efb26657" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.42">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="e61d1db9-e1cc-49dc-84b3-d71266ad8d2f" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.44" HigherBound="35.74">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="a2542923-e55e-4177-a946-d829de6392a3" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="cce6a41a-870b-4075-bd82-b3b2cc854301" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeAnelloProcessUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="a8e0e140-aae5-4685-a8f0-3ab7690be8f7" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.54">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="bb4649b5-d7e1-43ab-9f6b-32590a733e26" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.56" HigherBound="42.98">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeAnelloProcessUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeAnelloProcessPollingType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedPolling.TypedPolling parameter;
    };
    internal messagetype EzekeAnelloProcessRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedProcedure.EzekeAnelloProcess parameter;
    };
    internal messagetype EzekeAnelloProcessResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessTypedProcedure.EzekeAnelloProcessResponse parameter;
    };
    internal messagetype EzekeAnelloProcessUpdateStatusRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessUpdateStatusTypedProcedure.EzekeAnelloProcessUpdateStatus parameter;
    };
    internal messagetype EzekeAnelloProcessUpdateStatusResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeAnelloProcessUpdateStatusTypedProcedure.EzekeAnelloProcessUpdateStatusResponse parameter;
    };
    internal porttype EzekeAnelloProcessPollingInType
    {
        oneway Receive
        {
            EzekeAnelloProcessPollingType
        };
    };
    internal porttype EzekeAnelloProcessOutType
    {
        requestresponse EzekeAnelloProcess
        {
            EzekeAnelloProcessRequestType, EzekeAnelloProcessResponseType
        };
    };
    internal porttype EzekeAnelloProcessUpdateStatusOutType
    {
        requestresponse EzekeAnelloProcessUpdateStatus
        {
            EzekeAnelloProcessUpdateStatusRequestType, EzekeAnelloProcessUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeAnelloProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeAnelloProcessPollingInType EzekeAnelloProcessPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeAnelloProcessOutType EzekeAnelloProcessOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeAnelloProcessUpdateStatusOutType EzekeAnelloProcessUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeAnelloProcessPollingType EzekeAnelloProcessPolling;
        message EzekeAnelloProcessRequestType EzekeAnelloProcessRequest;
        message EzekeAnelloProcessResponseType EzekeAnelloProcessResponse;
        message EzekeAnelloProcessUpdateStatusRequestType EzekeAnelloProcessUpdateStatusRequest;
        message EzekeAnelloProcessUpdateStatusResponseType EzekeAnelloProcessUpdateStatusResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc40b963-95ab-4f2b-a161-d455e76a4087")]
            activate receive (EzekeAnelloProcessPollingIn.Receive, EzekeAnelloProcessPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0b75756-4ed1-4daa-a4be-e2d4a423c687")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("502c9867-14d3-40dd-8aa0-2f9a8729b447")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", EzekeAnelloProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeAnelloFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloProcess
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeAnelloProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeAnelloFile, "Anello");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8ae9d8e4-cbe7-44fe-978d-c2d93ff7306d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("75b310b6-0f52-4056-a8d5-98d87a81bf2e")]
                    construct EzekeAnelloProcessRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c8a9e7f9-468a-4148-96e5-7deac78d4781")]
                        transform (EzekeAnelloProcessRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloProcessPollingToProcess (EzekeAnelloProcessPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f37e8428-3e93-4dde-8e14-46f813c2f919")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3abe6d56-7c30-4eda-8406-41980967b1fd")]
                    send (EzekeAnelloProcessOut.EzekeAnelloProcess, EzekeAnelloProcessRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb2d6086-8388-4bc8-a497-9e3adf8fa17b")]
                    receive (EzekeAnelloProcessOut.EzekeAnelloProcess, EzekeAnelloProcessResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4286ccb4-e1fc-41c4-91e0-624d967bd12f")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f45769d5-ab45-4431-a09a-d0dfcdc6e895")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("184d26e5-666a-4527-84e0-9fc69074f396")]
                        errorMessage.Append("INT_BI - Ezeke Anello Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("15d7b521-3aae-4b12-9bce-d0e0fee5f172")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0305da9b-f4a1-4745-ade8-cf43601bc961")]
                        errorMessage.Append("INT_BI - Ezeke Anello Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4b444999-ac2a-4464-9599-e7a6400df25d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9ed28eee-3eac-4364-b237-22e65b6b1fbd")]
                    construct EzekeAnelloProcessUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a11b6e43-55c1-4b66-97b4-29efe54a82c4")]
                        transform (EzekeAnelloProcessUpdateStatusRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeAnelloProcessPollingToUpdateStatus (EzekeAnelloProcessPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c9c57d19-3933-4900-ab8e-01ab35688147")]
                        EzekeAnelloProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                        EzekeAnelloProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("56f9a3ad-15d9-46d3-8e91-6c5ff2703c67")]
                    send (EzekeAnelloProcessUpdateStatusOut.EzekeAnelloProcessUpdateStatus, EzekeAnelloProcessUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("68dad280-29e1-41d9-b859-c10487e79d50")]
                    receive (EzekeAnelloProcessUpdateStatusOut.EzekeAnelloProcessUpdateStatus, EzekeAnelloProcessUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f527d638-a339-4210-9baa-92a42c11ec67")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("78729364-2885-4bae-98ad-23b62ca869d4")]
                        errorMessage.Append("INT_BI - Ezeke Anello Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("440f2de0-ea70-4eed-a5aa-74831141127b")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d743ba45-d942-477b-a775-9236b1b82001")]
                        errorMessage.Append("INT_BI - Ezeke Anello Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ffe58302-e1e4-4381-bc53-11285961229b")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c26d9b7f-a44a-4553-9e88-70c26f9e52ed")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ad786cd5-e522-4886-9595-2bcee73d8014")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8327839a-46ae-484d-87f4-56fe27e51691")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeAnelloProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6093449e-06e3-4aed-8437-24778dccdf3a")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("11cd7f61-49b3-4d89-bd18-75802faea834")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

