﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="24a1ec3f-576e-4d09-81ea-e46691491a27" LowerBound="1.1" HigherBound="248.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="88512eaf-f969-4510-ad5f-a423b75f3c73" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="247.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="6278715a-ba92-4786-a27b-15bef684d082" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="acbf5f27-3704-4411-8b8d-41f61bfa3b9c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cf761e5e-f1a4-48eb-997b-2dd083bdfd85" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8cadcd34-9648-4e8e-83b1-ecaf79838757" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ba68a939-2adc-4c93-9e8e-8c750d68490c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9331556f-a41e-42e1-a520-dfb7d43cb307" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="865657ad-062b-4bd1-b235-348b09ac8c2f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="92f04edc-fdae-42e9-b9d2-70c706daa4a7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f7b95d40-6128-41ad-8591-372f3e2a58d4" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f7906070-dc08-4141-b7a5-f30185777496" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="41154dfe-977a-4cc0-b0a5-5990ddfde541" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="abbaae91-4c4c-4efc-acfa-0623c79dec2b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b3ca11e5-6bf8-4ae7-9565-8f76af2bb281" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="dbe68c95-1157-4a5a-9a25-d9d0331d669a" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="8e8147ff-57f1-42a5-ad86-aad44b1c090c" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="77.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeConfrontoProcessPollingIn" />
                    <om:Property Name="MessageName" Value="EzekeConfrontoProcessPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5f093369-cafe-4f6c-b190-bd0c3be955ff" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="87.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d92cd0d4-79a0-4a9b-a948-26cf1cba7fbc" ParentLink="ServiceBody_Statement" LowerBound="87.1" HigherBound="100.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, EzekeConfrontoProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeConfrontoFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeConfrontoProcess&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeConfrontoProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeConfrontoFile, &quot;Confronto&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="79a1f17c-c0dc-4e6d-81b9-4f040228cab6" ParentLink="ServiceBody_Statement" LowerBound="100.1" HigherBound="153.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="1623cbb9-4fd9-4c3f-b8ec-3e1d026d3be7" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="111.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="02241a88-a0c3-4668-9d01-fc69704022cf" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="110.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeConfrontoProcessPollingToProcess" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="7ee561f0-8067-416c-81bc-9627649e59ad" ParentLink="Transform_OutputMessagePartRef" LowerBound="109.36" HigherBound="109.74">
                                <om:Property Name="MessageRef" Value="EzekeConfrontoProcessRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="e5068b2a-253f-467f-a86e-0699b41edc7a" ParentLink="Transform_InputMessagePartRef" LowerBound="109.154" HigherBound="109.192">
                                <om:Property Name="MessageRef" Value="EzekeConfrontoProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="875c56d4-7f1d-4d36-895f-80734c264bdb" ParentLink="Construct_MessageRef" LowerBound="106.31" HigherBound="106.59">
                            <om:Property Name="Ref" Value="EzekeConfrontoProcessRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="53ad1df1-78b7-4f14-9c47-4b4f32c98743" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="113.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="a61a26c5-c743-468b-8a2d-8de0f6dd3ad5" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="115.1">
                        <om:Property Name="PortName" Value="EzekeConfrontoProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeConfrontoProcessRequest" />
                        <om:Property Name="OperationName" Value="EzekeConfrontoProcess" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="ae331a88-72db-4b70-bb6a-230875e1d93e" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="117.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeConfrontoProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeConfrontoProcessResponse" />
                        <om:Property Name="OperationName" Value="EzekeConfrontoProcess" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="d4e4f231-fd43-400c-af77-96d495a4e13e" ParentLink="ComplexStatement_Statement" LowerBound="117.1" HigherBound="123.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="0a8cd217-c446-45db-82a5-d1d5e866cd0c" ParentLink="Scope_Catch" LowerBound="126.1" HigherBound="139.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="cf82f394-b40c-466a-a90f-1abc73f5d8ef" ParentLink="Catch_Statement" LowerBound="129.1" HigherBound="138.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Confronto Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="3a0b92ab-d2ef-4252-b582-754b628c4ae7" ParentLink="Scope_Catch" LowerBound="139.1" HigherBound="151.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="ac8b50b9-9840-4b60-afad-651cff299285" ParentLink="Catch_Statement" LowerBound="142.1" HigherBound="150.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Confronto Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="b4e572e2-2cf1-4391-b548-f8d6f5ab2806" ParentLink="ServiceBody_Statement" LowerBound="153.1" HigherBound="204.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="a1fbd97b-ce32-456f-83b7-06cb158c02d4" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="167.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="6e793a1e-46d7-479f-8791-a7dfb78e0e59" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="163.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeConfrontoProcessPollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="2df6e23c-2b36-4388-85ac-05fe01af422d" ParentLink="Transform_InputMessagePartRef" LowerBound="162.171" HigherBound="162.209">
                                <om:Property Name="MessageRef" Value="EzekeConfrontoProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="17abcd6e-71df-43c5-b537-2be457f24373" ParentLink="Transform_OutputMessagePartRef" LowerBound="162.36" HigherBound="162.86">
                                <om:Property Name="MessageRef" Value="EzekeConfrontoProcessUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="22f1ac50-d675-4b21-8507-1bea8644841f" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="166.1">
                            <om:Property Name="Expression" Value="EzekeConfrontoProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;EzekeConfrontoProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="5226b00e-bcbe-4099-8b4c-b56e6ffc0730" ParentLink="Construct_MessageRef" LowerBound="159.31" HigherBound="159.71">
                            <om:Property Name="Ref" Value="EzekeConfrontoProcessUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="7a4cb71c-15d0-4d62-9ad9-605c7b97f89b" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                        <om:Property Name="PortName" Value="EzekeConfrontoProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeConfrontoProcessUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="EzekeConfrontoProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="51518d52-74a6-46d5-b149-f52c2db859e4" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeConfrontoProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeConfrontoProcessUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="EzekeConfrontoProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="85e953e5-c9a9-4aaf-acb8-36ab5ac68229" ParentLink="Scope_Catch" LowerBound="174.1" HigherBound="188.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="8c071157-b440-4be5-925c-be8700bd6314" ParentLink="Catch_Statement" LowerBound="177.1" HigherBound="187.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Confronto Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="582e70b1-f2c0-4665-a55a-4756324df611" ParentLink="Scope_Catch" LowerBound="188.1" HigherBound="202.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="31bf26c3-5bbd-4e57-a0f5-2f6cf606cfcf" ParentLink="Catch_Statement" LowerBound="191.1" HigherBound="201.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Confronto Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="e502e49e-23fb-4c84-8599-f3db797e1caa" ParentLink="ServiceBody_Statement" LowerBound="204.1" HigherBound="237.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="a4a3212f-c6c6-413c-aab6-94c8c6f3bd87" ParentLink="ReallyComplexStatement_Branch" LowerBound="205.13" HigherBound="208.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="bb855168-4b7a-4b86-a6b9-23960dda34be" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="58775a81-9e4e-4fb6-a1b5-24508c899beb" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="236.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="91d7594c-35cc-4ba0-8be5-b9da078b3111" ParentLink="ComplexStatement_Statement" LowerBound="215.1" HigherBound="232.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="34294bde-e8b5-4608-a57f-7a5bfc240b26" ParentLink="Construct_MessageRef" LowerBound="216.35" HigherBound="216.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="36703aa6-a7a0-4353-afbf-788afff74fd0" ParentLink="ComplexStatement_Statement" LowerBound="218.1" HigherBound="231.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeConfrontoProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="c23801ac-430b-42fb-8894-680c8bc797f9" ParentLink="ComplexStatement_Statement" LowerBound="232.1" HigherBound="234.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="b937fb96-2227-4f5c-bd42-6c59f76186a8" ParentLink="ServiceBody_Statement" LowerBound="237.1" HigherBound="245.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3bcb312a-6995-4fe9-bef0-3d68f9ea75b5" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="e8562399-8539-4425-b11a-f127294fe06c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="cc210d2f-cf57-428c-bf78-1b8856309105" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="52.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="163" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="e50cfe6e-ff09-4ee3-9b2d-ca28068d18b2" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="24e7908e-7130-422e-9601-c03ef046800f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="52.1" HigherBound="55.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="43" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="a602cc77-6529-4610-84fb-9216d6ecd1ab" ParentLink="PortDeclaration_CLRAttribute" LowerBound="52.1" HigherBound="53.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="666d5b30-e63f-44b0-8b93-c908c547a163" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="55.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="102" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="44dc1073-483e-47a8-8277-1fa3560b1d41" ParentLink="PortDeclaration_CLRAttribute" LowerBound="55.1" HigherBound="56.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9aca8ed5-162c-4027-81b7-7f700093c2e2" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6cf23a06-d144-4344-bcde-014c6cdb8992" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="25ca6580-6e67-4945-ba82-631e63827c92" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="69c35a1d-ca77-419a-92f3-9af7d082f912" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedProcedure.EzekeConfrontoProcess" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c3e38506-28ad-4b32-89c0-a41b8e16e2b2" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c3776c06-0e3d-4419-99c8-c6b99803f492" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedProcedure.EzekeConfrontoProcessResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="af0b373b-5337-40c9-a166-df2843f85482" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a3902f57-f9bf-4e72-8118-785273d05862" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessUpdateStatusTypedProcedure.EzekeConfrontoProcessUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0afb3bf2-f473-4400-ba37-9ad7af3b3c22" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e5014b81-6fa6-4b9d-aa55-90495ca22922" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessUpdateStatusTypedProcedure.EzekeConfrontoProcessUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="d0b4d4ee-507a-4a76-ac22-859e9e74897e" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="20695766-b0f7-4246-9f5a-c5e2cb613cfc" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="32dab11f-6128-478b-9c97-7c3a9300fb45" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.45">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="0ffc812d-a118-4c0b-9624-d7bde1fa963b" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="37417de2-7a0c-4245-94c5-2a3059837829" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcess" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="59a6f53e-4935-4c9d-8c43-a9b2111e8b1a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.45">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="10452b4d-172a-46ec-b758-3ae230902ed4" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.47" HigherBound="35.80">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="6c8b5989-65cc-48ad-89d7-78b35882a5d8" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="7d603b2f-bf1f-41f4-9753-55140edcea54" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeConfrontoProcessUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2a11b7ca-cf56-480e-a1ac-10383526ae2a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.57">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="0fbdcc24-7026-4280-8d40-8e14a4c16974" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.59" HigherBound="42.104">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeConfrontoProcessUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeConfrontoProcessPollingType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedPolling.TypedPolling parameter;
    };
    internal messagetype EzekeConfrontoProcessRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedProcedure.EzekeConfrontoProcess parameter;
    };
    internal messagetype EzekeConfrontoProcessResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessTypedProcedure.EzekeConfrontoProcessResponse parameter;
    };
    internal messagetype EzekeConfrontoProcessUpdateStatusRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessUpdateStatusTypedProcedure.EzekeConfrontoProcessUpdateStatus parameter;
    };
    internal messagetype EzekeConfrontoProcessUpdateStatusResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeConfrontoProcessUpdateStatusTypedProcedure.EzekeConfrontoProcessUpdateStatusResponse parameter;
    };
    internal porttype EzekeConfrontoProcessPollingInType
    {
        oneway Receive
        {
            EzekeConfrontoProcessPollingType
        };
    };
    internal porttype EzekeConfrontoProcessOutType
    {
        requestresponse EzekeConfrontoProcess
        {
            EzekeConfrontoProcessRequestType, EzekeConfrontoProcessResponseType
        };
    };
    internal porttype EzekeConfrontoProcessUpdateStatusOutType
    {
        requestresponse EzekeConfrontoProcessUpdateStatus
        {
            EzekeConfrontoProcessUpdateStatusRequestType, EzekeConfrontoProcessUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeConfrontoProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeConfrontoProcessPollingInType EzekeConfrontoProcessPollingIn;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeConfrontoProcessOutType EzekeConfrontoProcessOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeConfrontoProcessUpdateStatusOutType EzekeConfrontoProcessUpdateStatusOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeConfrontoProcessPollingType EzekeConfrontoProcessPolling;
        message EzekeConfrontoProcessRequestType EzekeConfrontoProcessRequest;
        message EzekeConfrontoProcessResponseType EzekeConfrontoProcessResponse;
        message EzekeConfrontoProcessUpdateStatusRequestType EzekeConfrontoProcessUpdateStatusRequest;
        message EzekeConfrontoProcessUpdateStatusResponseType EzekeConfrontoProcessUpdateStatusResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8e8147ff-57f1-42a5-ad86-aad44b1c090c")]
            activate receive (EzekeConfrontoProcessPollingIn.Receive, EzekeConfrontoProcessPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f093369-cafe-4f6c-b190-bd0c3be955ff")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d92cd0d4-79a0-4a9b-a948-26cf1cba7fbc")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", EzekeConfrontoProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeConfrontoFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeConfrontoProcess
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeConfrontoProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeConfrontoFile, "Confronto");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("79a1f17c-c0dc-4e6d-81b9-4f040228cab6")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1623cbb9-4fd9-4c3f-b8ec-3e1d026d3be7")]
                    construct EzekeConfrontoProcessRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("02241a88-a0c3-4668-9d01-fc69704022cf")]
                        transform (EzekeConfrontoProcessRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeConfrontoProcessPollingToProcess (EzekeConfrontoProcessPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("53ad1df1-78b7-4f14-9c47-4b4f32c98743")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a61a26c5-c743-468b-8a2d-8de0f6dd3ad5")]
                    send (EzekeConfrontoProcessOut.EzekeConfrontoProcess, EzekeConfrontoProcessRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae331a88-72db-4b70-bb6a-230875e1d93e")]
                    receive (EzekeConfrontoProcessOut.EzekeConfrontoProcess, EzekeConfrontoProcessResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d4e4f231-fd43-400c-af77-96d495a4e13e")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0a8cd217-c446-45db-82a5-d1d5e866cd0c")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cf82f394-b40c-466a-a90f-1abc73f5d8ef")]
                        errorMessage.Append("INT_BI - Ezeke Confronto Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a0b92ab-d2ef-4252-b582-754b628c4ae7")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ac8b50b9-9840-4b60-afad-651cff299285")]
                        errorMessage.Append("INT_BI - Ezeke Confronto Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b4e572e2-2cf1-4391-b548-f8d6f5ab2806")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a1fbd97b-ce32-456f-83b7-06cb158c02d4")]
                    construct EzekeConfrontoProcessUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6e793a1e-46d7-479f-8791-a7dfb78e0e59")]
                        transform (EzekeConfrontoProcessUpdateStatusRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeConfrontoProcessPollingToUpdateStatus (EzekeConfrontoProcessPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("22f1ac50-d675-4b21-8507-1bea8644841f")]
                        EzekeConfrontoProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                        EzekeConfrontoProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7a4cb71c-15d0-4d62-9ad9-605c7b97f89b")]
                    send (EzekeConfrontoProcessUpdateStatusOut.EzekeConfrontoProcessUpdateStatus, EzekeConfrontoProcessUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51518d52-74a6-46d5-b149-f52c2db859e4")]
                    receive (EzekeConfrontoProcessUpdateStatusOut.EzekeConfrontoProcessUpdateStatus, EzekeConfrontoProcessUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("85e953e5-c9a9-4aaf-acb8-36ab5ac68229")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8c071157-b440-4be5-925c-be8700bd6314")]
                        errorMessage.Append("INT_BI - Ezeke Confronto Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("582e70b1-f2c0-4665-a55a-4756324df611")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("31bf26c3-5bbd-4e57-a0f5-2f6cf606cfcf")]
                        errorMessage.Append("INT_BI - Ezeke Confronto Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e502e49e-23fb-4c84-8599-f3db797e1caa")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("58775a81-9e4e-4fb6-a1b5-24508c899beb")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("91d7594c-35cc-4ba0-8be5-b9da078b3111")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("36703aa6-a7a0-4353-afbf-788afff74fd0")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeConfrontoProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c23801ac-430b-42fb-8894-680c8bc797f9")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b937fb96-2227-4f5c-bd42-6c59f76186a8")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

