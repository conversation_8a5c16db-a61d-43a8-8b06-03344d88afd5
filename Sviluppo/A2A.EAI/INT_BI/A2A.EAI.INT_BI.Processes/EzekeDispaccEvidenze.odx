﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="0160b857-2d77-4d0b-a3d6-ba2cc31dac76" LowerBound="1.1" HigherBound="207.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="5cc6b6da-c312-4c0a-b0a8-83e320e76254" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="206.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccEvidenze" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="6b546434-ba61-4a84-b451-6bf1bf5045f2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c8206546-471d-49a8-b06d-881391e87ed9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c6787591-c13f-4d10-819e-55b244702269" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e0e8c33a-dfd2-41e0-af7f-46ff5763c416" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9416796f-0145-4b7d-9bd9-e4735c2e2e8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bf69101e-730b-4200-bbe6-e2599cf59da4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4bab145a-7e55-4bbd-9db3-0ba2eadf2019" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426d7e54-accd-4e49-acbf-f5af50db7dfe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="60abd9cc-50a5-4b2c-a62f-fa5935b48ac6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d36a0a86-c4b9-432a-a362-a193c8e61b82" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="fileIdentity" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e074bc68-8045-4342-baa2-1e057cf9cf1d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2f1aaf7d-2170-4965-8d98-a75a3a0c462f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeSceltaDispaccEvidenzeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeSceltaDispaccEvidenze" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="879d9bd8-0454-4dd6-943e-7f15ad1c8054" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e6c75b50-21d8-4228-8fc2-0ef4524fd432" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="d338d35f-37dc-4a19-930c-18c70129b768" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="256c145e-e769-47f3-9131-68e7c6c9ad43" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="63.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeDispaccEvidenzeIn" />
                    <om:Property Name="MessageName" Value="EzekeSceltaDispaccEvidenze" />
                    <om:Property Name="OperationName" Value="Request" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="ae6002d8-801e-45db-86ca-b6990484857c" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="77.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeSceltaDispaccEvidenze));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeSceltaDispaccEvidenze);&#xD;&#xA;&#xD;&#xA;fileIdentity = &quot;Not initialized&quot;;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="9d59f3c7-8b5e-4988-a74a-ce7261c9f8b6" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="88.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, originalFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoEvidenze&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="bc695ce6-ea60-4fd3-8bc4-50b12ba724e2" ParentLink="ServiceBody_Statement" LowerBound="88.1" HigherBound="162.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="75d5c77b-22d5-4452-a281-07a6b4de6c7c" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="95.1">
                        <om:Property Name="Expression" Value="fileIdentity = A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeSceltaDispacciamentoFileIdentity(originalFileName);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="File Identity" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Construct" OID="e5a941ef-6628-42f0-acd9-46b64c4f4422" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="105.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="0f2b10df-176e-4a41-9bc5-0416bed90e24" ParentLink="ComplexStatement_Statement" LowerBound="98.1" HigherBound="100.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeSceltaDispaccEvidenzeToDbInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="4022bf47-8b36-49a4-b783-3e69c99847a4" ParentLink="Transform_OutputMessagePartRef" LowerBound="99.36" HigherBound="99.79">
                                <om:Property Name="MessageRef" Value="EzekeDispaccEvidenzeInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="c69a094f-059d-4b79-b842-0d0fba244432" ParentLink="Transform_InputMessagePartRef" LowerBound="99.158" HigherBound="99.194">
                                <om:Property Name="MessageRef" Value="EzekeSceltaDispaccEvidenze" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="b35894f7-805a-468d-9316-6af4dfd581c5" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="104.1">
                            <om:Property Name="Expression" Value="EzekeDispaccEvidenzeInsertRequest.parameter.fileName = originalFileName;&#xD;&#xA;EzekeDispaccEvidenzeInsertRequest.parameter.idEzekeDispaccEvidenzeFile = activityInstanceId;&#xD;&#xA;EzekeDispaccEvidenzeInsertRequest.parameter.fileIdentity = fileIdentity;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="d1c2885f-e50e-4a18-8367-1934b6b6fd64" ParentLink="Construct_MessageRef" LowerBound="96.31" HigherBound="96.64">
                            <om:Property Name="Ref" Value="EzekeDispaccEvidenzeInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="df6dde8c-2931-488c-8f71-f7a58f148d5e" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="107.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="7874291a-b966-400b-9561-9fff1d68c01e" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                        <om:Property Name="PortName" Value="EzekeDispaccEvidenzeInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccEvidenzeInsertRequest" />
                        <om:Property Name="OperationName" Value="EzekeDispaccEvidenzeInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="53e0887c-4999-4268-8006-306cb38fc65f" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="111.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeDispaccEvidenzeInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccEvidenzeInsertResponse" />
                        <om:Property Name="OperationName" Value="EzekeDispaccEvidenzeInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="7ad1e361-7710-4c86-97fa-8a924599c6e5" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="117.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="b10eef8c-a9cf-4d6c-adff-661bca336286" ParentLink="Scope_Catch" LowerBound="120.1" HigherBound="134.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c81e6bb4-04c4-4ecd-9b46-5e6af35e1898" ParentLink="Catch_Statement" LowerBound="123.1" HigherBound="133.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="8317f130-3372-4ab6-b6d6-970e35812c97" ParentLink="Scope_Catch" LowerBound="134.1" HigherBound="147.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.SystemException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="SystemException Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="5077e204-8bf6-4e64-a382-960eb8256a49" ParentLink="Catch_Statement" LowerBound="137.1" HigherBound="146.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="4e79d49f-ad27-4d3f-96a0-108863824705" ParentLink="Scope_Catch" LowerBound="147.1" HigherBound="160.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="b6c3cae8-6e3a-4606-9dba-63aea207e9e2" ParentLink="Catch_Statement" LowerBound="150.1" HigherBound="159.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="e30c67ce-e93d-4c91-85f7-6dd15bb849f3" ParentLink="ServiceBody_Statement" LowerBound="162.1" HigherBound="195.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="dd419bed-574b-4142-a3a0-cfe49e57160f" ParentLink="ReallyComplexStatement_Branch" LowerBound="163.13" HigherBound="166.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="04c26bb3-3dbd-458b-8f4e-af90208863c6" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="77d1cde4-2ca5-4a33-884d-048d7acaec4c" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="194.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="9904bb70-0b32-41ac-875c-df19079c3904" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="190.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="e60d4da0-35f4-4642-8c94-e94940a956ba" ParentLink="ComplexStatement_Statement" LowerBound="176.1" HigherBound="189.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoEvidenze;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="630d42a9-cb97-4ff5-b7ba-c8d734fa791e" ParentLink="Construct_MessageRef" LowerBound="174.35" HigherBound="174.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="517835f1-3c40-4051-babb-067c25c5eb73" ParentLink="ComplexStatement_Statement" LowerBound="190.1" HigherBound="192.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d6c90b27-b8a5-4e2e-9355-0d54b1dbb11a" ParentLink="ServiceBody_Statement" LowerBound="195.1" HigherBound="204.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;File Identity: &quot;, fileIdentity),&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="cdf8c9ce-b1fd-4526-9d7c-3e6c57d3efc9" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccEvidenzeIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="63c4262a-ab71-438d-9c80-85b94efae153" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="be2341a2-063b-4ee2-b459-f9165e4226ba" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="43" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="dd1377b3-7b87-40dd-a999-6824b9b5743f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e4b8ea59-0b05-42bd-b042-7bf32a62d59e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="107" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="9060aab4-da00-46ab-a176-6843b33da1c8" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7230c92e-f2b1-41f0-9ba8-86cf415cb575" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeSceltaDispaccEvidenzeType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="bbcc8ea1-6748-4e91-b577-e16efad26353" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeSceltaDispaccEvidenze" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bf46fda7-5a8e-4c88-bde6-5548850ca2f6" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="48bc5409-5644-4bb2-98bc-992b097ca666" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccEvidenzeInsertTypedProcedure.EzekeDispaccEvidenzeInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="418db9b9-7314-42c4-b96d-d7855f50dd61" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="0fa05c94-8e43-44bb-b776-5e9609286eb2" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccEvidenzeInsertTypedProcedure.EzekeDispaccEvidenzeInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="c498f821-2142-4642-831c-02c493957203" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccEvidenzeInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="495c8119-ee0e-4520-8132-43396fb5268c" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Request" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="d252d376-b0e4-4a6b-b4f6-200e84e2d6d7" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.43">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeSceltaDispaccEvidenzeType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="cee82e96-3405-468a-add1-4a40996298c2" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="e330664a-dea3-437e-a82e-7b9dcb7416ee" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccEvidenzeInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9ad67a17-26e7-435e-b77d-5feb67054a62" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.50">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="7fce63d1-3377-40da-813f-4095c0889fd8" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.52" HigherBound="27.90">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccEvidenzeInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeSceltaDispaccEvidenzeType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeSceltaDispaccEvidenze parameter;
    };
    internal messagetype EzekeDispaccEvidenzeInsertRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccEvidenzeInsertTypedProcedure.EzekeDispaccEvidenzeInsert parameter;
    };
    internal messagetype EzekeDispaccEvidenzeInsertResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccEvidenzeInsertTypedProcedure.EzekeDispaccEvidenzeInsertResponse parameter;
    };
    internal porttype EzekeDispaccEvidenzeInType
    {
        oneway Request
        {
            EzekeSceltaDispaccEvidenzeType
        };
    };
    internal porttype EzekeDispaccEvidenzeInsertOutType
    {
        requestresponse EzekeDispaccEvidenzeInsert
        {
            EzekeDispaccEvidenzeInsertRequestType, EzekeDispaccEvidenzeInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeDispaccEvidenze
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeDispaccEvidenzeInType EzekeDispaccEvidenzeIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeDispaccEvidenzeInsertOutType EzekeDispaccEvidenzeInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeSceltaDispaccEvidenzeType EzekeSceltaDispaccEvidenze;
        message EzekeDispaccEvidenzeInsertRequestType EzekeDispaccEvidenzeInsertRequest;
        message EzekeDispaccEvidenzeInsertResponseType EzekeDispaccEvidenzeInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String fileIdentity;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("256c145e-e769-47f3-9131-68e7c6c9ad43")]
            activate receive (EzekeDispaccEvidenzeIn.Request, EzekeSceltaDispaccEvidenze);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            fileIdentity = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae6002d8-801e-45db-86ca-b6990484857c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeSceltaDispaccEvidenze));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeSceltaDispaccEvidenze);
            
            fileIdentity = "Not initialized";
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d59f3c7-8b5e-4988-a74a-ce7261c9f8b6")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", originalFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoEvidenze
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bc695ce6-ea60-4fd3-8bc4-50b12ba724e2")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("75d5c77b-22d5-4452-a281-07a6b4de6c7c")]
                    fileIdentity = A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeSceltaDispacciamentoFileIdentity(originalFileName);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e5a941ef-6628-42f0-acd9-46b64c4f4422")]
                    construct EzekeDispaccEvidenzeInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0f2b10df-176e-4a41-9bc5-0416bed90e24")]
                        transform (EzekeDispaccEvidenzeInsertRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeSceltaDispaccEvidenzeToDbInsert (EzekeSceltaDispaccEvidenze.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b35894f7-805a-468d-9316-6af4dfd581c5")]
                        EzekeDispaccEvidenzeInsertRequest.parameter.fileName = originalFileName;
                        EzekeDispaccEvidenzeInsertRequest.parameter.idEzekeDispaccEvidenzeFile = activityInstanceId;
                        EzekeDispaccEvidenzeInsertRequest.parameter.fileIdentity = fileIdentity;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("df6dde8c-2931-488c-8f71-f7a58f148d5e")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7874291a-b966-400b-9561-9fff1d68c01e")]
                    send (EzekeDispaccEvidenzeInsertOut.EzekeDispaccEvidenzeInsert, EzekeDispaccEvidenzeInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("53e0887c-4999-4268-8006-306cb38fc65f")]
                    receive (EzekeDispaccEvidenzeInsertOut.EzekeDispaccEvidenzeInsert, EzekeDispaccEvidenzeInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7ad1e361-7710-4c86-97fa-8a924599c6e5")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b10eef8c-a9cf-4d6c-adff-661bca336286")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c81e6bb4-04c4-4ecd-9b46-5e6af35e1898")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8317f130-3372-4ab6-b6d6-970e35812c97")]
                    catch (System.SystemException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5077e204-8bf6-4e64-a382-960eb8256a49")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e79d49f-ad27-4d3f-96a0-108863824705")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b6c3cae8-6e3a-4606-9dba-63aea207e9e2")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Evidenze - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e30c67ce-e93d-4c91-85f7-6dd15bb849f3")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("77d1cde4-2ca5-4a33-884d-048d7acaec4c")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9904bb70-0b32-41ac-875c-df19079c3904")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e60d4da0-35f4-4642-8c94-e94940a956ba")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoEvidenze;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("517835f1-3c40-4051-babb-067c25c5eb73")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d6c90b27-b8a5-4e2e-9355-0d54b1dbb11a")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Note", System.String.Concat("File Identity: ", fileIdentity),
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

