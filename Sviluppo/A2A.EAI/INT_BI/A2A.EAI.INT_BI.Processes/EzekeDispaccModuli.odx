﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="034987b9-16e6-46f6-a0fd-c28bfb7420ae" LowerBound="1.1" HigherBound="208.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="40913e29-edf9-4930-a0c2-aa1fe3b6b3a8" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="207.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccModuli" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="39b2a0ab-ccab-4c14-bbb6-5278d74239ba" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="fileIdentity" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6b546434-ba61-4a84-b451-6bf1bf5045f2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c8206546-471d-49a8-b06d-881391e87ed9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c6787591-c13f-4d10-819e-55b244702269" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e0e8c33a-dfd2-41e0-af7f-46ff5763c416" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9416796f-0145-4b7d-9bd9-e4735c2e2e8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="bf69101e-730b-4200-bbe6-e2599cf59da4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4bab145a-7e55-4bbd-9db3-0ba2eadf2019" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="426d7e54-accd-4e49-acbf-f5af50db7dfe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="60abd9cc-50a5-4b2c-a62f-fa5935b48ac6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e074bc68-8045-4342-baa2-1e057cf9cf1d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f9a452a9-7cd6-4030-b0a7-3ee8e2e2d52a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeSceltaDispaccModuliType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeSceltaDispaccModuli" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="67da5d3b-fb06-4111-abe4-411329c3c991" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccModuliInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="0d8e2965-a58b-45df-b338-4dc86c22a5a2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccModuliInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="7abac36f-c45b-416d-9403-5b30cd123f01" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="0161f18c-702c-4482-ba91-8e892b769720" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="63.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeDispaccModuliIn" />
                    <om:Property Name="MessageName" Value="EzekeSceltaDispaccModuli" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="34fc836e-b02a-426d-8db4-c68959af17e7" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="77.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeSceltaDispaccModuli));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeSceltaDispaccModuli);&#xD;&#xA;&#xD;&#xA;fileIdentity = &quot;Not initialized&quot;;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a8d7a3a9-e7ae-4621-913f-4d692e62f95d" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="88.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, originalFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoModuli&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="6d060f5d-d1f9-4e27-b207-a10a2a2adb0a" ParentLink="ServiceBody_Statement" LowerBound="88.1" HigherBound="163.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="35e57948-75fc-41e1-9de8-68ea023187b3" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="95.1">
                        <om:Property Name="Expression" Value="fileIdentity = A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeSceltaDispacciamentoFileIdentity(originalFileName);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="File Identity" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Construct" OID="e64a1712-00b7-4a1d-980d-1964872244b0" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="105.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="5b61ae91-54e2-46ee-b3c8-e40b8832b921" ParentLink="ComplexStatement_Statement" LowerBound="98.1" HigherBound="100.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeSceltaDispaccModuliToDbInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="40abf010-0728-401d-8d74-2a0078fea36f" ParentLink="Transform_InputMessagePartRef" LowerBound="99.154" HigherBound="99.188">
                                <om:Property Name="MessageRef" Value="EzekeSceltaDispaccModuli" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="9e2925ad-ddb9-49c2-ad4e-a2534107f1fd" ParentLink="Transform_OutputMessagePartRef" LowerBound="99.36" HigherBound="99.77">
                                <om:Property Name="MessageRef" Value="EzekeDispaccModuliInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="b5cef486-df6e-46be-940e-caf1ac6e9b9d" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="104.1">
                            <om:Property Name="Expression" Value="EzekeDispaccModuliInsertRequest.parameter.fileName = originalFileName;&#xD;&#xA;EzekeDispaccModuliInsertRequest.parameter.idEzekeDispaccModuliFile = activityInstanceId;&#xD;&#xA;EzekeDispaccModuliInsertRequest.parameter.fileIdentity = fileIdentity;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="63955f5a-4504-4b29-9c48-d9d81a5ea4b6" ParentLink="Construct_MessageRef" LowerBound="96.31" HigherBound="96.62">
                            <om:Property Name="Ref" Value="EzekeDispaccModuliInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="100f9990-7f3b-4e67-9ff6-a043864d7c36" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="107.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="619d0e91-135f-4278-a53a-1e2ad69d5d80" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                        <om:Property Name="PortName" Value="EzekeDispaccModuliInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccModuliInsertRequest" />
                        <om:Property Name="OperationName" Value="EzekeDispaccModuliInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="91852b2a-184d-4920-884d-97a94f86fc1d" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="111.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeDispaccModuliInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccModuliInsertResponse" />
                        <om:Property Name="OperationName" Value="EzekeDispaccModuliInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="145339dc-76b6-4671-ad5a-fa5050cadec7" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="117.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="6e33c7c0-a1ff-421f-8724-f26005e3ff5e" ParentLink="Scope_Catch" LowerBound="120.1" HigherBound="134.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e1928b47-d17e-4312-b988-9c044135785a" ParentLink="Catch_Statement" LowerBound="123.1" HigherBound="133.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="2dbea946-c1bc-4017-938c-804fd21253e8" ParentLink="Scope_Catch" LowerBound="134.1" HigherBound="147.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.SystemException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="SystemException Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="6bfa1c91-b31b-4dd8-be5b-096eb19eee92" ParentLink="Catch_Statement" LowerBound="137.1" HigherBound="146.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="6f2213e5-2886-418a-9bda-2611bc2e811a" ParentLink="Scope_Catch" LowerBound="147.1" HigherBound="161.1">
                        <om:Property Name="ExceptionName" Value="xlangExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="XLang Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="258e695f-18e5-439d-bfe1-a5221a657f84" ParentLink="Catch_Statement" LowerBound="150.1" HigherBound="160.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(xlangExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="aa0fc55e-7317-4ae1-899f-42607154df71" ParentLink="ServiceBody_Statement" LowerBound="163.1" HigherBound="196.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="de68531b-8fae-460a-8b9c-dc8a35e3a545" ParentLink="ReallyComplexStatement_Branch" LowerBound="164.13" HigherBound="167.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="227e2bc0-d3a2-49fb-9631-acb7671ce241" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="010bdead-6dbf-41df-bd17-1cccef3b6548" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="195.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="9ad04f84-becf-4c13-ae77-30b647433993" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="191.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="85146d5c-9fee-4fac-ae14-8f12a92c7ff5" ParentLink="ComplexStatement_Statement" LowerBound="177.1" HigherBound="190.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoModuli;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="30c9a335-046a-482f-932f-fd5b8dabf130" ParentLink="Construct_MessageRef" LowerBound="175.35" HigherBound="175.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="52e103e3-27b8-4adc-be24-b50ff90ac137" ParentLink="ComplexStatement_Statement" LowerBound="191.1" HigherBound="193.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a442040c-558d-414d-99f5-d50e2db42951" ParentLink="ServiceBody_Statement" LowerBound="196.1" HigherBound="205.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;File Identity: &quot;, fileIdentity),&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="06f1930a-ed78-46e0-9a95-2b73c9f4409e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccModuliIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="a6ca29a6-b0d8-4d75-b01d-97997c711712" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="829e3c83-d8ad-4c22-ab21-41da0a9e7625" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="48" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccModuliInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="55b4378f-18e7-4853-b645-a297b8944cca" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9d4e441c-0cca-41cc-9420-0e486f6c8121" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="107" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="fee615a9-9c6d-46a8-90fd-407c06a1bc4d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="87b13091-158d-4df4-9058-eca116ef7b7a" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeSceltaDispaccModuliType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="734e2f57-9f4b-492e-866c-d758ac414500" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeSceltaDispaccModuli" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="515d4818-01a5-453e-9a1b-f826dce3f06b" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccModuliInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e6ae43dd-cae7-4669-b75c-38f4593affda" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccModuliInsertTypedProcedure.EzekeDispaccModuliInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="234ee32b-c025-4633-8819-ad600fc12ed0" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccModuliInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e67cc5b2-c58d-44bf-b17d-1740289c60ed" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccModuliInsertTypedProcedure.EzekeDispaccModuliInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="f62c0d63-3a28-4686-bdb3-8779824c8cbe" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccModuliInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="5af34451-27fd-490a-af24-1b83e7dc0352" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="62c29595-b90e-4212-889c-6f01cb950716" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.41">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeSceltaDispaccModuliType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="8491a6eb-c76a-486e-9f89-1b4e6cc8f4d7" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccModuliInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="6594c64a-378b-44a2-a800-a65773c3f740" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccModuliInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2f7d8d7a-7d33-4cd6-be13-8e8f881dbcc6" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.48">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="ed5a7d91-63eb-43d1-9cda-87cbde557709" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.50" HigherBound="27.86">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccModuliInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeSceltaDispaccModuliType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeSceltaDispaccModuli parameter;
    };
    internal messagetype EzekeDispaccModuliInsertRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccModuliInsertTypedProcedure.EzekeDispaccModuliInsert parameter;
    };
    internal messagetype EzekeDispaccModuliInsertResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccModuliInsertTypedProcedure.EzekeDispaccModuliInsertResponse parameter;
    };
    internal porttype EzekeDispaccModuliInType
    {
        oneway Receive
        {
            EzekeSceltaDispaccModuliType
        };
    };
    internal porttype EzekeDispaccModuliInsertOutType
    {
        requestresponse EzekeDispaccModuliInsert
        {
            EzekeDispaccModuliInsertRequestType, EzekeDispaccModuliInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeDispaccModuli
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeDispaccModuliInType EzekeDispaccModuliIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeDispaccModuliInsertOutType EzekeDispaccModuliInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeSceltaDispaccModuliType EzekeSceltaDispaccModuli;
        message EzekeDispaccModuliInsertRequestType EzekeDispaccModuliInsertRequest;
        message EzekeDispaccModuliInsertResponseType EzekeDispaccModuliInsertResponse;
        System.String fileIdentity;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0161f18c-702c-4482-ba91-8e892b769720")]
            activate receive (EzekeDispaccModuliIn.Receive, EzekeSceltaDispaccModuli);
            fileIdentity = "";
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("34fc836e-b02a-426d-8db4-c68959af17e7")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EzekeSceltaDispaccModuli));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EzekeSceltaDispaccModuli);
            
            fileIdentity = "Not initialized";
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a8d7a3a9-e7ae-4621-913f-4d692e62f95d")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", originalFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoModuli
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d060f5d-d1f9-4e27-b207-a10a2a2adb0a")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("35e57948-75fc-41e1-9de8-68ea023187b3")]
                    fileIdentity = A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeSceltaDispacciamentoFileIdentity(originalFileName);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e64a1712-00b7-4a1d-980d-1964872244b0")]
                    construct EzekeDispaccModuliInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b61ae91-54e2-46ee-b3c8-e40b8832b921")]
                        transform (EzekeDispaccModuliInsertRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeSceltaDispaccModuliToDbInsert (EzekeSceltaDispaccModuli.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b5cef486-df6e-46be-940e-caf1ac6e9b9d")]
                        EzekeDispaccModuliInsertRequest.parameter.fileName = originalFileName;
                        EzekeDispaccModuliInsertRequest.parameter.idEzekeDispaccModuliFile = activityInstanceId;
                        EzekeDispaccModuliInsertRequest.parameter.fileIdentity = fileIdentity;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("100f9990-7f3b-4e67-9ff6-a043864d7c36")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("619d0e91-135f-4278-a53a-1e2ad69d5d80")]
                    send (EzekeDispaccModuliInsertOut.EzekeDispaccModuliInsert, EzekeDispaccModuliInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("91852b2a-184d-4920-884d-97a94f86fc1d")]
                    receive (EzekeDispaccModuliInsertOut.EzekeDispaccModuliInsert, EzekeDispaccModuliInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("145339dc-76b6-4671-ad5a-fa5050cadec7")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6e33c7c0-a1ff-421f-8724-f26005e3ff5e")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e1928b47-d17e-4312-b988-9c044135785a")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2dbea946-c1bc-4017-938c-804fd21253e8")]
                    catch (System.SystemException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6bfa1c91-b31b-4dd8-be5b-096eb19eee92")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6f2213e5-2886-418a-9bda-2611bc2e811a")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException xlangExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("258e695f-18e5-439d-bfe1-a5221a657f84")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Moduli - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(xlangExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("aa0fc55e-7317-4ae1-899f-42607154df71")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("010bdead-6dbf-41df-bd17-1cccef3b6548")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9ad04f84-becf-4c13-ae77-30b647433993")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("85146d5c-9fee-4fac-ae14-8f12a92c7ff5")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoModuli;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("52e103e3-27b8-4adc-be24-b50ff90ac137")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a442040c-558d-414d-99f5-d50e2db42951")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Note", System.String.Concat("File Identity: ", fileIdentity),
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

