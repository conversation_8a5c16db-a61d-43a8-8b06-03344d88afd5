﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="aa1b75e7-4808-4c7c-b2aa-c0b30eb9abde" LowerBound="1.1" HigherBound="252.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="e7917d28-0b49-49a6-81db-3bb7c2db3519" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="b02d0b81-c037-4ad0-82f9-ae1b2c1509b3" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="878fe486-9955-49b8-bdbb-728baf160a49" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.43">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="e400ceb3-b625-4e1a-82c2-362a1585c9a4" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="4d56ec2c-a925-4b05-8c97-799b78a99b86" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcess" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8b928b39-70ab-4b0b-bd58-46d56edef96d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.43">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="32d116a4-25d7-4782-9f5c-c44bc26c5d6e" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.45" HigherBound="35.76">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="56f3d31a-810e-4565-b0ab-832a2a325519" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="356c6238-603a-4c75-9713-0cbb38bbbcb6" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="108061fc-e0bf-47e4-a33a-51812d9aeb3d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.55">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="a1821b7d-1f1a-4f3c-9a60-b0f0672ed436" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.57" HigherBound="42.100">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9aca8ed5-162c-4027-81b7-7f700093c2ef" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6cf23a06-d144-4344-bcde-014c6cdb899d" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="25ca6580-6e67-4945-ba82-631e63827c9f" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="69c35a1d-ca77-419a-92f3-9af7d082f915" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedProcedure.EzekeDispaccProcess" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c3e38506-28ad-4b32-89c0-a41b8e16e2bc" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c3776c06-0e3d-4419-99c8-c6b99803f49e" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedProcedure.EzekeDispaccProcessResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="af0b373b-5337-40c9-a166-df2843f85487" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a3902f57-f9bf-4e72-8118-785273d0586c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessUpdateStatusTypedProcedure.EzekeDispaccProcessUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0afb3bf2-f473-4400-ba37-9ad7af3b3c26" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e5014b81-6fa6-4b9d-aa55-90495ca2292c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessUpdateStatusTypedProcedure.EzekeDispaccProcessUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="4f91bf70-c1d6-48bd-a079-d18b320fa15e" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="251.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeDispaccProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="6278715a-ba92-4786-a27b-15bef684d082" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="acbf5f27-3704-4411-8b8d-41f61bfa3b9c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cf761e5e-f1a4-48eb-997b-2dd083bdfd85" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8cadcd34-9648-4e8e-83b1-ecaf79838757" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ba68a939-2adc-4c93-9e8e-8c750d68490c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9331556f-a41e-42e1-a520-dfb7d43cb307" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="865657ad-062b-4bd1-b235-348b09ac8c2f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="92f04edc-fdae-42e9-b9d2-70c706daa4a7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="aa1c3d01-dfeb-4382-8ea6-fe8ad850c454" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="108409c0-9f04-494d-8234-751f70e927d9" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="0fca4c51-a64c-444d-9f19-5b5ef5893f33" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="424998db-65d3-42fa-93f9-b220c2ffbf78" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2527c1a2-dd0d-4793-a937-7a0c69df35e9" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="e186aaf3-5e02-4484-a962-d3f1558d32d1" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="e697bf82-ec51-4b3c-8caf-93df6460352e" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="77.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeDispaccProcessPollingIn" />
                    <om:Property Name="MessageName" Value="EzekeDispaccProcessPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="cb413d58-6355-45b6-b271-46ae35093931" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="87.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="02d81abd-8b3f-4e62-9fd6-b63dfffef26a" ParentLink="ServiceBody_Statement" LowerBound="87.1" HigherBound="104.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.String.Concat(EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccEvidenzeFileName, &quot;, &quot;, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccModuliFileName),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoProcess,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;File Identity: &quot;, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccModuliFileIdentity)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeDispaccModuliFile, &quot;Moduli&quot;);&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeDispaccEvidenzeFile, &quot;Evidenze&quot;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="fbae9b2e-e749-4ebc-b5bc-21954f7cb342" ParentLink="ServiceBody_Statement" LowerBound="104.1" HigherBound="157.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="c8f41794-4fdf-4462-a473-2b9e02099955" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="115.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="febf84a7-41a8-4622-8a49-0b956b21f313" ParentLink="ComplexStatement_Statement" LowerBound="112.1" HigherBound="114.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeDispaccProcessPollingToProcess" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="b547a7cd-226a-4845-a22b-6e297b944d35" ParentLink="Transform_OutputMessagePartRef" LowerBound="113.36" HigherBound="113.72">
                                <om:Property Name="MessageRef" Value="EzekeDispaccProcessRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="49d1adf3-096a-484b-aac9-9ce51496d2d9" ParentLink="Transform_InputMessagePartRef" LowerBound="113.150" HigherBound="113.186">
                                <om:Property Name="MessageRef" Value="EzekeDispaccProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="1e55b203-2741-433d-be54-ba95950bbbb9" ParentLink="Construct_MessageRef" LowerBound="110.31" HigherBound="110.57">
                            <om:Property Name="Ref" Value="EzekeDispaccProcessRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="5ea0e224-a70c-4f2d-8ec5-151a90bcc6cc" ParentLink="ComplexStatement_Statement" LowerBound="115.1" HigherBound="117.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="f58f438a-c2d6-4a42-a97a-60c6bbba47b1" ParentLink="ComplexStatement_Statement" LowerBound="117.1" HigherBound="119.1">
                        <om:Property Name="PortName" Value="EzekeDispaccProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccProcessRequest" />
                        <om:Property Name="OperationName" Value="EzekeDispaccProcess" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="0e385fa8-9d99-40d7-bb1f-2cff804ee4c8" ParentLink="ComplexStatement_Statement" LowerBound="119.1" HigherBound="121.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeDispaccProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccProcessResponse" />
                        <om:Property Name="OperationName" Value="EzekeDispaccProcess" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="eaf5f07d-e80e-4f60-9f54-cf7987648b99" ParentLink="ComplexStatement_Statement" LowerBound="121.1" HigherBound="127.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="cb73837e-99ab-4651-a92a-b2329f432257" ParentLink="Scope_Catch" LowerBound="130.1" HigherBound="143.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="efcb630d-30a4-4071-bbdb-738ba6754321" ParentLink="Catch_Statement" LowerBound="133.1" HigherBound="142.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="02ade05f-95fb-42be-93fe-180ebcaf9add" ParentLink="Scope_Catch" LowerBound="143.1" HigherBound="155.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="9faea13c-e0fe-48c5-865a-7dfb336bcb84" ParentLink="Catch_Statement" LowerBound="146.1" HigherBound="154.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="5cf2e75c-41f6-4a81-976e-67f7ccf6a2ff" ParentLink="ServiceBody_Statement" LowerBound="157.1" HigherBound="208.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Catch" OID="d44e3d65-dd9a-48d0-a69d-738361e0b394" ParentLink="Scope_Catch" LowerBound="178.1" HigherBound="192.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f9d1a853-f34c-4f85-b367-de53df3fc7eb" ParentLink="Catch_Statement" LowerBound="181.1" HigherBound="191.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="aa560b2c-d1d4-426e-a1d1-cba38be10184" ParentLink="Scope_Catch" LowerBound="192.1" HigherBound="206.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="649c3c94-4329-448f-a155-83c33e5b3f77" ParentLink="Catch_Statement" LowerBound="195.1" HigherBound="205.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Scelta Dispacciamento Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="eef8b99b-0f1f-4655-a139-698af0ce4bbe" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="171.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="479b5a94-4e45-429b-a0d4-d3072f0cb32b" ParentLink="Construct_MessageRef" LowerBound="163.31" HigherBound="163.69">
                            <om:Property Name="Ref" Value="EzekeDispaccProcessUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="e33f6daa-c11c-41ad-a506-65af9b0c5c32" ParentLink="ComplexStatement_Statement" LowerBound="165.1" HigherBound="167.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeDispaccProcessPollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="cd483eee-081c-48dd-99c2-a80eeddf9e70" ParentLink="Transform_OutputMessagePartRef" LowerBound="166.36" HigherBound="166.84">
                                <om:Property Name="MessageRef" Value="EzekeDispaccProcessUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="274bfc95-a178-4808-9eb5-a93cf74f87ab" ParentLink="Transform_InputMessagePartRef" LowerBound="166.167" HigherBound="166.203">
                                <om:Property Name="MessageRef" Value="EzekeDispaccProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="5c60eeeb-1159-4979-899d-c13f2d9bf61e" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="170.1">
                            <om:Property Name="Expression" Value="EzekeDispaccProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;EzekeDispaccProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="4b01cc62-8705-4da6-a7c8-4137d9e913e9" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="173.1">
                        <om:Property Name="PortName" Value="EzekeDispaccProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccProcessUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="EzekeDispaccProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="445bb716-2665-45b0-b223-6e210542d53d" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="175.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeDispaccProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeDispaccProcessUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="EzekeDispaccProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="6a47fb8b-374a-4608-a4b3-4d616b6f5889" ParentLink="ServiceBody_Statement" LowerBound="208.1" HigherBound="241.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="c04c473b-3417-49df-aadd-6d45f6abfa6b" ParentLink="ReallyComplexStatement_Branch" LowerBound="209.13" HigherBound="212.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="6ed7b064-3701-492e-a007-e2895d6e942c" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="929675d7-d82b-4a2f-9a86-9ca1fcdb7b6c" ParentLink="ComplexStatement_Statement" LowerBound="214.1" HigherBound="240.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="9e008998-0854-4a3d-8450-41d6b183e1da" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="236.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="8be15118-c35f-4b0b-a8a5-bd3ffaa30d0a" ParentLink="Construct_MessageRef" LowerBound="220.35" HigherBound="220.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="586acb2f-b714-4034-845a-eb3d92c318e3" ParentLink="ComplexStatement_Statement" LowerBound="222.1" HigherBound="235.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="cc44be4d-1862-4054-aa82-3ca127db304e" ParentLink="ComplexStatement_Statement" LowerBound="236.1" HigherBound="238.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="64366fcd-2f73-41c6-9885-f7cec45f7ee9" ParentLink="ServiceBody_Statement" LowerBound="241.1" HigherBound="249.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="13ecef29-42df-4ca7-9a76-6c1ca444ded6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="3c197031-2108-47f4-9475-e980886dea90" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7394f748-e50b-4ea7-a052-7bfb24461f15" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="36" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="dfe1b63e-e66b-4f5c-b4a2-d61a3191f3cb" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="fc3d5638-32f6-4a63-ad18-a348b240e724" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="56.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="98" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeDispaccProcessUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeDispaccProcessUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="e9267f53-f02e-4727-8eb9-178d6117118b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e9d5020e-8b7a-4ca0-8bef-7c6ca00804ac" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="56.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="149" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="5eb4b5ad-ecf1-4f24-ad82-f5b762907e4a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="56.1" HigherBound="57.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeDispaccProcessPollingType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedPolling.TypedPolling parameter;
    };
    internal messagetype EzekeDispaccProcessRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedProcedure.EzekeDispaccProcess parameter;
    };
    internal messagetype EzekeDispaccProcessResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessTypedProcedure.EzekeDispaccProcessResponse parameter;
    };
    internal messagetype EzekeDispaccProcessUpdateStatusRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessUpdateStatusTypedProcedure.EzekeDispaccProcessUpdateStatus parameter;
    };
    internal messagetype EzekeDispaccProcessUpdateStatusResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.Ezeke.EzekeDispaccProcessUpdateStatusTypedProcedure.EzekeDispaccProcessUpdateStatusResponse parameter;
    };
    internal porttype EzekeDispaccProcessPollingInType
    {
        oneway Receive
        {
            EzekeDispaccProcessPollingType
        };
    };
    internal porttype EzekeDispaccProcessOutType
    {
        requestresponse EzekeDispaccProcess
        {
            EzekeDispaccProcessRequestType, EzekeDispaccProcessResponseType
        };
    };
    internal porttype EzekeDispaccProcessUpdateStatusOutType
    {
        requestresponse EzekeDispaccProcessUpdateStatus
        {
            EzekeDispaccProcessUpdateStatusRequestType, EzekeDispaccProcessUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeDispaccProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeDispaccProcessPollingInType EzekeDispaccProcessPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeDispaccProcessOutType EzekeDispaccProcessOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeDispaccProcessUpdateStatusOutType EzekeDispaccProcessUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeDispaccProcessPollingType EzekeDispaccProcessPolling;
        message EzekeDispaccProcessRequestType EzekeDispaccProcessRequest;
        message EzekeDispaccProcessResponseType EzekeDispaccProcessResponse;
        message EzekeDispaccProcessUpdateStatusRequestType EzekeDispaccProcessUpdateStatusRequest;
        message EzekeDispaccProcessUpdateStatusResponseType EzekeDispaccProcessUpdateStatusResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e697bf82-ec51-4b3c-8caf-93df6460352e")]
            activate receive (EzekeDispaccProcessPollingIn.Receive, EzekeDispaccProcessPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cb413d58-6355-45b6-b271-46ae35093931")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("02d81abd-8b3f-4e62-9fd6-b63dfffef26a")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.String.Concat(EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccEvidenzeFileName, ", ", EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccModuliFileName),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoProcess,
            "Note", System.String.Concat("File Identity: ", EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.EzekeDispaccModuliFileIdentity)
            );
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeDispaccModuliFile, "Moduli");
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.AddRelationship(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, EzekeDispaccProcessPolling.parameter.TypedPollingResultSet0.TypedPollingResultSet0.idEzekeDispaccEvidenzeFile, "Evidenze");
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fbae9b2e-e749-4ebc-b5bc-21954f7cb342")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c8f41794-4fdf-4462-a473-2b9e02099955")]
                    construct EzekeDispaccProcessRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("febf84a7-41a8-4622-8a49-0b956b21f313")]
                        transform (EzekeDispaccProcessRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeDispaccProcessPollingToProcess (EzekeDispaccProcessPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ea0e224-a70c-4f2d-8ec5-151a90bcc6cc")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f58f438a-c2d6-4a42-a97a-60c6bbba47b1")]
                    send (EzekeDispaccProcessOut.EzekeDispaccProcess, EzekeDispaccProcessRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e385fa8-9d99-40d7-bb1f-2cff804ee4c8")]
                    receive (EzekeDispaccProcessOut.EzekeDispaccProcess, EzekeDispaccProcessResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eaf5f07d-e80e-4f60-9f54-cf7987648b99")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("cb73837e-99ab-4651-a92a-b2329f432257")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("efcb630d-30a4-4071-bbdb-738ba6754321")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("02ade05f-95fb-42be-93fe-180ebcaf9add")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9faea13c-e0fe-48c5-865a-7dfb336bcb84")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5cf2e75c-41f6-4a81-976e-67f7ccf6a2ff")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eef8b99b-0f1f-4655-a139-698af0ce4bbe")]
                    construct EzekeDispaccProcessUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e33f6daa-c11c-41ad-a506-65af9b0c5c32")]
                        transform (EzekeDispaccProcessUpdateStatusRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.Ezeke.EzekeDispaccProcessPollingToUpdateStatus (EzekeDispaccProcessPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5c60eeeb-1159-4979-899d-c13f2d9bf61e")]
                        EzekeDispaccProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                        EzekeDispaccProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4b01cc62-8705-4da6-a7c8-4137d9e913e9")]
                    send (EzekeDispaccProcessUpdateStatusOut.EzekeDispaccProcessUpdateStatus, EzekeDispaccProcessUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("445bb716-2665-45b0-b223-6e210542d53d")]
                    receive (EzekeDispaccProcessUpdateStatusOut.EzekeDispaccProcessUpdateStatus, EzekeDispaccProcessUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d44e3d65-dd9a-48d0-a69d-738361e0b394")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f9d1a853-f34c-4f85-b367-de53df3fc7eb")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("aa560b2c-d1d4-426e-a1d1-cba38be10184")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("649c3c94-4329-448f-a155-83c33e5b3f77")]
                        errorMessage.Append("INT_BI - Ezeke Scelta Dispacciamento Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6a47fb8b-374a-4608-a4b3-4d616b6f5889")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("929675d7-d82b-4a2f-9a86-9ca1fcdb7b6c")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9e008998-0854-4a3d-8450-41d6b183e1da")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("586acb2f-b714-4034-845a-eb3d92c318e3")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeDispacciamentoProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc44be4d-1862-4054-aa82-3ca127db304e")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("64366fcd-2f73-41c6-9885-f7cec45f7ee9")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

