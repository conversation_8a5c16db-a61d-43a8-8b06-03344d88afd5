﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="822ca7ed-b189-46d6-86f5-405f867a2899" LowerBound="1.1" HigherBound="247.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="61229bb5-9202-4fec-b8af-92627fe0fef6" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="d3c88524-6443-4fc5-b4d6-382aedc43fb4" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="6d5f4d49-cc7a-4fcb-b6f7-f06bdcbe2300" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.49">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="da46e6a0-e95e-4b79-909a-3c8293052127" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="12dea79e-a2d3-42fb-a60e-fd616dd22c3b" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcess" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="44ad0a00-c8e4-43e3-8494-3212d99caef6" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.49">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="74c25783-8b37-4641-bfab-dbfb097b024f" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.51" HigherBound="35.88">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="dd06bb9a-29ce-4650-a51c-4be3b6445763" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="4807a4cc-3151-4d5b-a4b9-d8c2ecf3372c" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="0d4c3671-7317-4aa8-b845-3a4d5abb996d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.61">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="15d34a1e-555c-4cec-968e-30bb2cd29493" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.63" HigherBound="42.112">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="dd8c4c6a-c238-475d-aa51-1db078d8ae05" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e4d108dd-22e3-401d-a39a-9fe275dbbe9a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedPolling.TypedPollingResultSet0" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="ff61f307-531a-48e4-93a1-080546d37100" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="9fa93af9-5b7e-4c44-b417-889315b761ce" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedProcedure.EzekeMatlabZipFileProcess" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="470fcb2d-c728-4945-b971-bcacaa00115a" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="ea1f057b-1f23-454d-8b2c-327e6dd22311" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedProcedure.EzekeMatlabZipFileProcessResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="5df78cbb-9a6b-4ce3-a81d-8c016a7bd82e" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6309b63c-9278-4649-9a16-a89ddd3c9b15" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.EzekeMatlabZipFileProcessUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9f4896ec-e0fb-442c-aa27-9886304dff3e" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="afd12ace-76af-45e4-946f-0dddf67eace9" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.EzekeMatlabZipFileProcessUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="d0417a3c-dcf1-46f0-9b60-425498efa4fd" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="246.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="37590cc2-ee7c-4dcc-9288-07e7024afa8d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ec136d14-4140-40de-92a6-5a8e648e2654" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="63e7b1c8-1e76-4659-99ae-4ebbf4bdf594" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b12da995-beaa-4aca-9562-3bb010b2dc27" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="68d2cd37-37dc-49f0-a8a8-cc39ed3c975b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e87fd264-0d98-4293-b99f-9a538a999b95" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d1aff816-5bb5-4eb0-a77c-7da90b1ba292" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7344f1ac-950d-4ccf-8003-cfd59916a746" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e6e301a0-1e14-4560-9a5a-3250ce812978" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6f070623-010a-426f-b3ff-b3a3b1199166" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f7a43357-cf92-4f5d-b54a-18bba95ae304" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="db7b0a78-ea80-46c8-a6da-9b509f6ca09e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1ef8285d-0161-4ab8-a694-ade274ce0fcb" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="332f6418-8f58-460e-b474-e468b2b21351" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="51583edc-24ba-44a8-aa90-c6b37cb634e6" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="77.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeMatlabZipFileProcessPollingIn" />
                    <om:Property Name="MessageName" Value="EzekeMatlabZipFileProcessPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="6ebd961b-5c88-460f-b870-fd7e55e82af8" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="87.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="ee817875-b498-4360-89ba-168413f041ef" ParentLink="ServiceBody_Statement" LowerBound="87.1" HigherBound="99.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, EzekeMatlabZipFileProcessPolling.parameter.filename,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlabZipFileProcess,&#xD;&#xA;&quot;id Elaborazione&quot;, EzekeMatlabZipFileProcessPolling.parameter.idelabEZEKE2B&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="c1b6dd2e-58af-458c-be2a-86cbbc77c0bc" ParentLink="ServiceBody_Statement" LowerBound="99.1" HigherBound="152.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="f2ed09a1-c288-4325-9dbc-8afcf1487db4" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="110.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="21aa21ca-55b7-46a1-be45-4c0f66e0e7be" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.EzekeMatlabZipFileProcessPollingToProcess" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="0df5da2d-1d2e-4f0c-894f-913350e05daf" ParentLink="Transform_OutputMessagePartRef" LowerBound="108.36" HigherBound="108.78">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileProcessRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="0bad799a-c19c-422d-8b54-5a27588cb4fa" ParentLink="Transform_InputMessagePartRef" LowerBound="108.167" HigherBound="108.209">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="507190a2-ba9e-4ff5-9771-b5bba53d8ebf" ParentLink="Construct_MessageRef" LowerBound="105.31" HigherBound="105.63">
                            <om:Property Name="Ref" Value="EzekeMatlabZipFileProcessRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="f53fc6b6-93f3-4668-9bf1-a65c1958cb9a" ParentLink="ComplexStatement_Statement" LowerBound="110.1" HigherBound="112.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="0a84875d-4750-4c9c-ba6a-0677c0a2c5b6" ParentLink="ComplexStatement_Statement" LowerBound="112.1" HigherBound="114.1">
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileProcessRequest" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileProcess" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="21da73ba-6162-46a3-9204-489e3c2ba63a" ParentLink="ComplexStatement_Statement" LowerBound="114.1" HigherBound="116.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileProcessOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileProcessResponse" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileProcess" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="58a9980e-8ae0-4afc-a92a-4518bdb29989" ParentLink="ComplexStatement_Statement" LowerBound="116.1" HigherBound="122.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="33c39617-9571-4435-accc-148b3586d0dd" ParentLink="Scope_Catch" LowerBound="125.1" HigherBound="138.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="2d8072a6-4865-4229-9e2f-1b8312c4f30c" ParentLink="Catch_Statement" LowerBound="128.1" HigherBound="137.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab Zip File Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="2be76c38-e2e1-4fe1-b45b-7f7a922d9914" ParentLink="Scope_Catch" LowerBound="138.1" HigherBound="150.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="a7b63ca5-cbdd-4970-8266-b93aa7f6cbfb" ParentLink="Catch_Statement" LowerBound="141.1" HigherBound="149.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab Zip File Process - Errore in fase di elaborazione dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="7cc171ca-db30-41ea-9c34-c09190851128" ParentLink="ServiceBody_Statement" LowerBound="152.1" HigherBound="203.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="806c020a-28a6-4e73-bd84-93b6c0b39347" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="166.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="516b2665-a1b0-4f04-be96-fef78e54b4e8" ParentLink="ComplexStatement_Statement" LowerBound="160.1" HigherBound="162.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.EzekeMatlabZipFileProcessPollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="50ab434c-13cc-4931-ae6c-dd22b129aa69" ParentLink="Transform_InputMessagePartRef" LowerBound="161.184" HigherBound="161.226">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileProcessPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="6fbb9ba9-d863-4f78-967f-ba6ec471025a" ParentLink="Transform_OutputMessagePartRef" LowerBound="161.36" HigherBound="161.90">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileProcessUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="1d4f13f1-e661-4aaf-b074-ce5feb180270" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="165.1">
                            <om:Property Name="Expression" Value="EzekeMatlabZipFileProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;EzekeMatlabZipFileProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="4370588a-a0a7-43ef-b85a-06a19d306e6c" ParentLink="Construct_MessageRef" LowerBound="158.31" HigherBound="158.75">
                            <om:Property Name="Ref" Value="EzekeMatlabZipFileProcessUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="b69a44ef-1ded-494b-98ed-98ddc0eb9566" ParentLink="ComplexStatement_Statement" LowerBound="166.1" HigherBound="168.1">
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileProcessUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="a419a946-75b7-4fe9-8b2c-1a8e4e488500" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="170.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileProcessUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileProcessUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileProcessUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="b7dd07ef-e973-4de4-beba-590796fc7407" ParentLink="Scope_Catch" LowerBound="173.1" HigherBound="187.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0a2719c7-c544-4d71-9a63-ca9e5bdc795a" ParentLink="Catch_Statement" LowerBound="176.1" HigherBound="186.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab Zip File Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="56049fc0-924f-4342-89fe-f1921f1adac3" ParentLink="Scope_Catch" LowerBound="187.1" HigherBound="201.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="4efad21e-68a6-47c4-b3f2-ce793522cfc6" ParentLink="Catch_Statement" LowerBound="190.1" HigherBound="200.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab Zip File Process - Errore durante aggiornamento stato tabelle di staging. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="4baae975-27b3-4d13-83ce-ccd9ebc7d4a4" ParentLink="ServiceBody_Statement" LowerBound="203.1" HigherBound="236.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="1e406336-04d1-487e-a01f-c12ffae0e6d9" ParentLink="ReallyComplexStatement_Branch" LowerBound="204.13" HigherBound="207.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="7e05f54e-2602-4d8e-a15a-1cd6eb8b951e" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="d42ce720-6347-44d2-9ea0-2dc760e12eb6" ParentLink="ComplexStatement_Statement" LowerBound="209.1" HigherBound="235.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="dc7e6ceb-b0c0-47a3-9d95-672c863236fa" ParentLink="ComplexStatement_Statement" LowerBound="214.1" HigherBound="231.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="0dc266c2-526f-4f2d-b0ee-8ae82325f783" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="230.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlabZipFileProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="05c72240-31c2-4f1d-9b61-f87d9b0e330d" ParentLink="Construct_MessageRef" LowerBound="215.35" HigherBound="215.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="74c2a19c-43eb-4d90-b5ff-2bc6a296c10a" ParentLink="ComplexStatement_Statement" LowerBound="231.1" HigherBound="233.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a3542dfc-2492-4920-bb40-d3fb9fb076dd" ParentLink="ServiceBody_Statement" LowerBound="236.1" HigherBound="244.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2efc46dd-5180-47ed-843b-0eeebf9ef128" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="caf049dd-28cd-4057-beb8-4f48b141d543" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="174e5435-f3ba-48ff-b843-747de4847da6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="35" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c905fed1-f31a-4226-9ff6-bd93ccc584be" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e7fc2342-cfb0-4938-9ed7-212e76b9bfee" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="56.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="92" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileProcessUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileProcessUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8eaf3e70-8b18-492a-a9b2-654b7ea3b26c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2227e5fb-4119-40fe-b574-5ef16c14a540" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="56.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="149" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="3c3367ff-2642-43df-9e5e-0e03cfc35035" ParentLink="PortDeclaration_CLRAttribute" LowerBound="56.1" HigherBound="57.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeMatlabZipFileProcessPollingType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedPolling.TypedPollingResultSet0 parameter;
    };
    internal messagetype EzekeMatlabZipFileProcessRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedProcedure.EzekeMatlabZipFileProcess parameter;
    };
    internal messagetype EzekeMatlabZipFileProcessResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessTypedProcedure.EzekeMatlabZipFileProcessResponse parameter;
    };
    internal messagetype EzekeMatlabZipFileProcessUpdateStatusRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.EzekeMatlabZipFileProcessUpdateStatus parameter;
    };
    internal messagetype EzekeMatlabZipFileProcessUpdateStatusResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileProcessUpdateStatusTypedProcedure.EzekeMatlabZipFileProcessUpdateStatusResponse parameter;
    };
    internal porttype EzekeMatlabZipFileProcessPollingInType
    {
        oneway Receive
        {
            EzekeMatlabZipFileProcessPollingType
        };
    };
    internal porttype EzekeMatlabZipFileProcessOutType
    {
        requestresponse EzekeMatlabZipFileProcess
        {
            EzekeMatlabZipFileProcessRequestType, EzekeMatlabZipFileProcessResponseType
        };
    };
    internal porttype EzekeMatlabZipFileProcessUpdateStatusOutType
    {
        requestresponse EzekeMatlabZipFileProcessUpdateStatus
        {
            EzekeMatlabZipFileProcessUpdateStatusRequestType, EzekeMatlabZipFileProcessUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeMatlabZipFileProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeMatlabZipFileProcessPollingInType EzekeMatlabZipFileProcessPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeMatlabZipFileProcessOutType EzekeMatlabZipFileProcessOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeMatlabZipFileProcessUpdateStatusOutType EzekeMatlabZipFileProcessUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeMatlabZipFileProcessPollingType EzekeMatlabZipFileProcessPolling;
        message EzekeMatlabZipFileProcessRequestType EzekeMatlabZipFileProcessRequest;
        message EzekeMatlabZipFileProcessResponseType EzekeMatlabZipFileProcessResponse;
        message EzekeMatlabZipFileProcessUpdateStatusRequestType EzekeMatlabZipFileProcessUpdateStatusRequest;
        message EzekeMatlabZipFileProcessUpdateStatusResponseType EzekeMatlabZipFileProcessUpdateStatusResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("51583edc-24ba-44a8-aa90-c6b37cb634e6")]
            activate receive (EzekeMatlabZipFileProcessPollingIn.Receive, EzekeMatlabZipFileProcessPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6ebd961b-5c88-460f-b870-fd7e55e82af8")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ee817875-b498-4360-89ba-168413f041ef")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", EzekeMatlabZipFileProcessPolling.parameter.filename,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlabZipFileProcess,
            "id Elaborazione", EzekeMatlabZipFileProcessPolling.parameter.idelabEZEKE2B
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c1b6dd2e-58af-458c-be2a-86cbbc77c0bc")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f2ed09a1-c288-4325-9dbc-8afcf1487db4")]
                    construct EzekeMatlabZipFileProcessRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("21aa21ca-55b7-46a1-be45-4c0f66e0e7be")]
                        transform (EzekeMatlabZipFileProcessRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.EzekeMatlabZipFileProcessPollingToProcess (EzekeMatlabZipFileProcessPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f53fc6b6-93f3-4668-9bf1-a65c1958cb9a")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0a84875d-4750-4c9c-ba6a-0677c0a2c5b6")]
                    send (EzekeMatlabZipFileProcessOut.EzekeMatlabZipFileProcess, EzekeMatlabZipFileProcessRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("21da73ba-6162-46a3-9204-489e3c2ba63a")]
                    receive (EzekeMatlabZipFileProcessOut.EzekeMatlabZipFileProcess, EzekeMatlabZipFileProcessResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("58a9980e-8ae0-4afc-a92a-4518bdb29989")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("33c39617-9571-4435-accc-148b3586d0dd")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2d8072a6-4865-4229-9e2f-1b8312c4f30c")]
                        errorMessage.Append("INT_BI - Ezeke Matlab Zip File Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2be76c38-e2e1-4fe1-b45b-7f7a922d9914")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a7b63ca5-cbdd-4970-8266-b93aa7f6cbfb")]
                        errorMessage.Append("INT_BI - Ezeke Matlab Zip File Process - Errore in fase di elaborazione dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7cc171ca-db30-41ea-9c34-c09190851128")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("806c020a-28a6-4e73-bd84-93b6c0b39347")]
                    construct EzekeMatlabZipFileProcessUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("516b2665-a1b0-4f04-be96-fef78e54b4e8")]
                        transform (EzekeMatlabZipFileProcessUpdateStatusRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.EzekeMatlabZipFileProcessPollingToUpdateStatus (EzekeMatlabZipFileProcessPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1d4f13f1-e661-4aaf-b074-ce5feb180270")]
                        EzekeMatlabZipFileProcessUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                        EzekeMatlabZipFileProcessUpdateStatusRequest.parameter.errorMessage = errorMessage.ToString();
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b69a44ef-1ded-494b-98ed-98ddc0eb9566")]
                    send (EzekeMatlabZipFileProcessUpdateStatusOut.EzekeMatlabZipFileProcessUpdateStatus, EzekeMatlabZipFileProcessUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a419a946-75b7-4fe9-8b2c-1a8e4e488500")]
                    receive (EzekeMatlabZipFileProcessUpdateStatusOut.EzekeMatlabZipFileProcessUpdateStatus, EzekeMatlabZipFileProcessUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b7dd07ef-e973-4de4-beba-590796fc7407")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0a2719c7-c544-4d71-9a63-ca9e5bdc795a")]
                        errorMessage.Append("INT_BI - Ezeke Matlab Zip File Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("56049fc0-924f-4342-89fe-f1921f1adac3")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4efad21e-68a6-47c4-b3f2-ce793522cfc6")]
                        errorMessage.Append("INT_BI - Ezeke Matlab Zip File Process - Errore durante aggiornamento stato tabelle di staging. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4baae975-27b3-4d13-83ce-ccd9ebc7d4a4")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("d42ce720-6347-44d2-9ea0-2dc760e12eb6")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dc7e6ceb-b0c0-47a3-9d95-672c863236fa")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0dc266c2-526f-4f2d-b0ee-8ae82325f783")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlabZipFileProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("74c2a19c-43eb-4d90-b5ff-2bc6a296c10a")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a3542dfc-2492-4920-bb40-d3fb9fb076dd")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

