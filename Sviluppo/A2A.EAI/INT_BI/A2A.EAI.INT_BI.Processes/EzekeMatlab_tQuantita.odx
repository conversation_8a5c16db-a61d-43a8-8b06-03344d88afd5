﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="1bd870a9-86fe-407d-bbee-ac89100308cb" LowerBound="1.1" HigherBound="178.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="4a72832a-dc6d-4f9a-b3c7-4484f3d617d1" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="177.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlab_tQuantita" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="7b5b0f39-b1fc-4a99-a330-7389820f1df3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e5d7df7b-3152-405e-a55d-d3667d9b90c8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8b06514b-556f-49a8-933e-7fcbf9cb254a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="733dc2c1-3424-4812-866e-68c334fa6467" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ccf6cddc-b9d3-4821-a9bf-415f2315cda5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="19ab70a4-9c4c-4101-ad1c-978ab45371d4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d72d9c30-9780-44c0-baba-8ef112ff17bf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2fade07b-f722-4c12-b902-936c4729173c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a1eb4f59-acf4-4922-a029-31a185caf387" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="287755fc-fd62-405f-ae03-e597ad7087e8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="idEzekeMatlabZip" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e6004a27-6367-4a48-9aea-546c85e6b2da" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="25.1" HigherBound="26.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="42007f7b-cf91-42de-8a23-87e6d0c3d002" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bf5011ec-9cc3-4f47-a3af-2b1b49ddc8c9" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="27.1" HigherBound="28.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d3d370c4-4401-4ed9-b5c9-21b7430595ca" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.eZK2B_tQuantitaType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="eZK2B_tQuantita" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="b2055dbc-c307-42cf-9f6f-23bafc1781f1" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="c1612a18-a3ec-404f-a9fa-01b3c5f9a62e" ParentLink="ServiceBody_Statement" LowerBound="41.1" HigherBound="48.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeMatlab_eZK2B_tQuantitaIn" />
                    <om:Property Name="MessageName" Value="eZK2B_tQuantita" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="414cb56f-c9aa-4acf-b0d6-865213bad374" ParentLink="ServiceBody_Statement" LowerBound="48.1" HigherBound="62.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(eZK2B_tQuantita));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(eZK2B_tQuantita);&#xD;&#xA;&#xD;&#xA;idEzekeMatlabZip = originalFileName.Substring(0, 36);&#xD;&#xA;originalFileName = originalFileName.Substring(37, originalFileName.Length - 37);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="451541ef-ad68-4b67-919a-4809c9e91496" ParentLink="ServiceBody_Statement" LowerBound="62.1" HigherBound="74.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, originalFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tQuantita,&#xD;&#xA;&quot;id Elaborazione&quot;, A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeIdElaborazione(eZK2B_tQuantita.parameter)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="4e274528-9960-4c49-ba48-0cd20fdd8e39" ParentLink="ServiceBody_Statement" LowerBound="74.1" HigherBound="134.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="c501b62f-b871-4303-99e4-d42b68735a8c" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="90.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="5f35eba6-a455-4088-b13d-4c65267aa239" ParentLink="ComplexStatement_Statement" LowerBound="82.1" HigherBound="84.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.eZK2B_tQuantitaToEzekeMatlabZipFileInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="6ef22eeb-37e0-4f8a-a5a2-53a88090641c" ParentLink="Transform_OutputMessagePartRef" LowerBound="83.36" HigherBound="83.77">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="6096b68f-b54f-4332-947a-17abf9ac5b78" ParentLink="Transform_InputMessagePartRef" LowerBound="83.166" HigherBound="83.191">
                                <om:Property Name="MessageRef" Value="eZK2B_tQuantita" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="2b82b5af-34cb-4cbc-bdcd-26ba6050367c" ParentLink="ComplexStatement_Statement" LowerBound="84.1" HigherBound="89.1">
                            <om:Property Name="Expression" Value="EzekeMatlabZipFileInsertRequest.parameter.filename = originalFileName;&#xD;&#xA;EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabFile = activityInstanceId;&#xD;&#xA;EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabZip = idEzekeMatlabZip;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="35cb50ae-d6f0-4d4c-8e1d-186308886888" ParentLink="Construct_MessageRef" LowerBound="80.31" HigherBound="80.62">
                            <om:Property Name="Ref" Value="EzekeMatlabZipFileInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="b978295a-2eb1-4645-9c1c-205fd9fe64bf" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="92.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="9c06837a-8cef-44d1-afe7-0d22cd20b0c9" ParentLink="ComplexStatement_Statement" LowerBound="92.1" HigherBound="94.1">
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileInsertRequest" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="0c586c98-0698-473e-bc09-90f207c0eabd" ParentLink="ComplexStatement_Statement" LowerBound="94.1" HigherBound="96.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileInsertResponse" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="d59a1c63-defb-4c41-a6f2-cca28f713215" ParentLink="ComplexStatement_Statement" LowerBound="96.1" HigherBound="102.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="813db65b-df27-4d51-b79c-07dbf754de60" ParentLink="Scope_Catch" LowerBound="105.1" HigherBound="119.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="83ef5d2b-c82a-4a6b-bef6-dd7d47889328" ParentLink="Catch_Statement" LowerBound="108.1" HigherBound="118.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab tQuantita - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="41154f5f-6688-4328-bd56-27b0ebf03041" ParentLink="Scope_Catch" LowerBound="119.1" HigherBound="132.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="2b0f127d-8816-47d2-b688-8f92bb5e59d7" ParentLink="Catch_Statement" LowerBound="122.1" HigherBound="131.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab tQuantita - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="7794bf26-7458-4bac-a64d-e608b9833b69" ParentLink="ServiceBody_Statement" LowerBound="134.1" HigherBound="167.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="1f9962dc-b4a6-4e79-8093-b20c9bd5f268" ParentLink="ReallyComplexStatement_Branch" LowerBound="135.13" HigherBound="138.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="70455d89-a225-4af9-880c-67dd9bbbc91f" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="f4fb1fc7-8cdb-4ec7-867e-e81bb51b20bf" ParentLink="ComplexStatement_Statement" LowerBound="140.1" HigherBound="166.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="b6bb3bb4-b2cc-4fa0-ad93-aacc4a1f9048" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="162.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="a50edaa0-f4cc-45ee-8b5d-9fdd7ef37cb5" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="161.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tQuantita;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="2141030f-431b-4016-abd8-6ebe5ace8e0e" ParentLink="Construct_MessageRef" LowerBound="146.35" HigherBound="146.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="26beae2d-838a-43ae-ae36-e3000f881fa5" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="164.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="1ddf7813-29a3-4a28-aa4d-53bd5b6e0a64" ParentLink="ServiceBody_Statement" LowerBound="167.1" HigherBound="175.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a374ff65-0857-48b4-a130-06aa13013b3a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlab_eZK2B_tQuantitaInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlab_eZK2B_tQuantitaIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="bf0e899e-68bf-4899-8c80-ce4522b11424" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7f2062df-149c-4479-a3dc-6073e9cc2581" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="23.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="31" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c8adb17d-8a1a-49f4-b95a-6202f9d7fa92" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4f6fedce-367e-4f3d-ab52-819333a47313" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="23.1" HigherBound="25.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="107" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="afd07b90-aee2-4f73-b917-21ca715dd06b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="23.1" HigherBound="24.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="996d1655-88f2-4242-b892-6aabcae9898c" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="eZK2B_tQuantitaType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6645e90a-013e-4515-a5bc-8022feff024f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.eZK2B_tQuantita" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="0b81d3d4-5b61-4316-92a3-f7f8fc5dc84b" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlab_eZK2B_tQuantitaInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="fa5383d5-1271-4fc7-a655-9878bd88fafb" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="fe857dd9-36cb-493f-8713-5db6bacb29de" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.eZK2B_tQuantitaType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype eZK2B_tQuantitaType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.eZK2B_tQuantita parameter;
    };
    internal porttype EzekeMatlab_eZK2B_tQuantitaInType
    {
        oneway Receive
        {
            eZK2B_tQuantitaType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeMatlab_tQuantita
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeMatlab_eZK2B_tQuantitaInType EzekeMatlab_eZK2B_tQuantitaIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeMatlabZipFileInsertOutType EzekeMatlabZipFileInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeMatlabZipFileInsertResponseType EzekeMatlabZipFileInsertResponse;
        message EzekeMatlabZipFileInsertRequestType EzekeMatlabZipFileInsertRequest;
        message eZK2B_tQuantitaType eZK2B_tQuantita;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String idEzekeMatlabZip;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c1612a18-a3ec-404f-a9fa-01b3c5f9a62e")]
            activate receive (EzekeMatlab_eZK2B_tQuantitaIn.Receive, eZK2B_tQuantita);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            idEzekeMatlabZip = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("414cb56f-c9aa-4acf-b0d6-865213bad374")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(eZK2B_tQuantita));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(eZK2B_tQuantita);
            
            idEzekeMatlabZip = originalFileName.Substring(0, 36);
            originalFileName = originalFileName.Substring(37, originalFileName.Length - 37);
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("451541ef-ad68-4b67-919a-4809c9e91496")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", originalFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tQuantita,
            "id Elaborazione", A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeIdElaborazione(eZK2B_tQuantita.parameter)
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e274528-9960-4c49-ba48-0cd20fdd8e39")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c501b62f-b871-4303-99e4-d42b68735a8c")]
                    construct EzekeMatlabZipFileInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f35eba6-a455-4088-b13d-4c65267aa239")]
                        transform (EzekeMatlabZipFileInsertRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.eZK2B_tQuantitaToEzekeMatlabZipFileInsert (eZK2B_tQuantita.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2b82b5af-34cb-4cbc-bdcd-26ba6050367c")]
                        EzekeMatlabZipFileInsertRequest.parameter.filename = originalFileName;
                        EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabFile = activityInstanceId;
                        EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabZip = idEzekeMatlabZip;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b978295a-2eb1-4645-9c1c-205fd9fe64bf")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9c06837a-8cef-44d1-afe7-0d22cd20b0c9")]
                    send (EzekeMatlabZipFileInsertOut.EzekeMatlabZipFileInsert, EzekeMatlabZipFileInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0c586c98-0698-473e-bc09-90f207c0eabd")]
                    receive (EzekeMatlabZipFileInsertOut.EzekeMatlabZipFileInsert, EzekeMatlabZipFileInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d59a1c63-defb-4c41-a6f2-cca28f713215")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("813db65b-df27-4d51-b79c-07dbf754de60")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("83ef5d2b-c82a-4a6b-bef6-dd7d47889328")]
                        errorMessage.Append("INT_BI - Ezeke Matlab tQuantita - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("41154f5f-6688-4328-bd56-27b0ebf03041")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2b0f127d-8816-47d2-b688-8f92bb5e59d7")]
                        errorMessage.Append("INT_BI - Ezeke Matlab tQuantita - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7794bf26-7458-4bac-a64d-e608b9833b69")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("f4fb1fc7-8cdb-4ec7-867e-e81bb51b20bf")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b6bb3bb4-b2cc-4fa0-ad93-aacc4a1f9048")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a50edaa0-f4cc-45ee-8b5d-9fdd7ef37cb5")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tQuantita;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("26beae2d-838a-43ae-ae36-e3000f881fa5")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1ddf7813-29a3-4a28-aa4d-53bd5b6e0a64")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

