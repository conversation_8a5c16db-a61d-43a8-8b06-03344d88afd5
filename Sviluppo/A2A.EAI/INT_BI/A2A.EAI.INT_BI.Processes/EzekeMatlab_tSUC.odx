﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="a6b03fde-4104-4e78-a557-c324bce181ae" LowerBound="1.1" HigherBound="178.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="3afa6607-f4bd-4a57-8a7a-d9dcff19bafd" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="177.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlab_tSUC" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="7b5b0f39-b1fc-4a99-a330-7389820f1df3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e5d7df7b-3152-405e-a55d-d3667d9b90c8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8b06514b-556f-49a8-933e-7fcbf9cb254a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="733dc2c1-3424-4812-866e-68c334fa6467" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ccf6cddc-b9d3-4821-a9bf-415f2315cda5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="19ab70a4-9c4c-4101-ad1c-978ab45371d4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d72d9c30-9780-44c0-baba-8ef112ff17bf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2fade07b-f722-4c12-b902-936c4729173c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a1eb4f59-acf4-4922-a029-31a185caf387" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="287755fc-fd62-405f-ae03-e597ad7087e8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="idEzekeMatlabZip" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e6004a27-6367-4a48-9aea-546c85e6b2da" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="25.1" HigherBound="26.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="42007f7b-cf91-42de-8a23-87e6d0c3d002" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bf5011ec-9cc3-4f47-a3af-2b1b49ddc8c9" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="27.1" HigherBound="28.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="797fafaa-c1ad-451a-9e77-22dd77238686" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.eZK2B_tSUCType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="eZK2B_tSUC" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="54d9990e-a248-448f-bbf2-f8776916d77c" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="5f9dcb0c-e2b4-4c52-88ce-8660c5b79eda" ParentLink="ServiceBody_Statement" LowerBound="41.1" HigherBound="48.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EzekeMatlab_eZK2B_tSUCIn" />
                    <om:Property Name="MessageName" Value="eZK2B_tSUC" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="454b801c-5af2-41ed-a3e8-83e1b5f805c8" ParentLink="ServiceBody_Statement" LowerBound="48.1" HigherBound="62.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(eZK2B_tSUC));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(eZK2B_tSUC);&#xD;&#xA;&#xD;&#xA;idEzekeMatlabZip = originalFileName.Substring(0, 36);&#xD;&#xA;originalFileName = originalFileName.Substring(37, originalFileName.Length - 37);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="aa3262b0-7be1-4637-8e52-3997bdcfb79b" ParentLink="ServiceBody_Statement" LowerBound="62.1" HigherBound="74.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, originalFileName,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tSUC,&#xD;&#xA;&quot;id Elaborazione&quot;, A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeIdElaborazione(eZK2B_tSUC.parameter)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="73585a71-d0bb-47ae-8492-d6dad387ad8b" ParentLink="ServiceBody_Statement" LowerBound="74.1" HigherBound="134.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="de1603fa-5ecf-4256-9182-afee9640b2af" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="90.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="042e7204-510f-4ad9-be0b-8d90bafd4254" ParentLink="ComplexStatement_Statement" LowerBound="82.1" HigherBound="84.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.eZK2B_tSUCToEzekeMatlabZipFileInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="6a64dc3d-aa51-4ba7-b367-95874573982d" ParentLink="Transform_OutputMessagePartRef" LowerBound="83.36" HigherBound="83.77">
                                <om:Property Name="MessageRef" Value="EzekeMatlabZipFileInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="15894031-9dc9-41fc-b1fa-648b09d27037" ParentLink="Transform_InputMessagePartRef" LowerBound="83.161" HigherBound="83.181">
                                <om:Property Name="MessageRef" Value="eZK2B_tSUC" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="dd879fd8-4c97-4652-b8e9-eb5c6e0a44dc" ParentLink="ComplexStatement_Statement" LowerBound="84.1" HigherBound="89.1">
                            <om:Property Name="Expression" Value="EzekeMatlabZipFileInsertRequest.parameter.filename = originalFileName;&#xD;&#xA;EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabFile = activityInstanceId;&#xD;&#xA;EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabZip = idEzekeMatlabZip;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="4f1e0028-49e3-486d-a584-74e4d771d640" ParentLink="Construct_MessageRef" LowerBound="80.31" HigherBound="80.62">
                            <om:Property Name="Ref" Value="EzekeMatlabZipFileInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="af99d5dd-5d86-4a6b-881e-54b932372d1a" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="92.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="065cc297-ec26-4c32-9c11-e473897865dd" ParentLink="ComplexStatement_Statement" LowerBound="92.1" HigherBound="94.1">
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileInsertRequest" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="51d37cbf-739f-49ba-b4f1-6133e35bab30" ParentLink="ComplexStatement_Statement" LowerBound="94.1" HigherBound="96.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EzekeMatlabZipFileInsertOut" />
                        <om:Property Name="MessageName" Value="EzekeMatlabZipFileInsertResponse" />
                        <om:Property Name="OperationName" Value="EzekeMatlabZipFileInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="184b3d15-ee31-4ba5-bbab-96a1186ab01d" ParentLink="ComplexStatement_Statement" LowerBound="96.1" HigherBound="102.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="4760a459-2aa8-4556-b0e7-ebc0fbe91a79" ParentLink="Scope_Catch" LowerBound="105.1" HigherBound="119.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="1137f459-d426-478b-8a39-c99a6a7ea8c1" ParentLink="Catch_Statement" LowerBound="108.1" HigherBound="118.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab tSUC - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="9e1d6f07-621d-4269-990f-53e4d40a3b51" ParentLink="Scope_Catch" LowerBound="119.1" HigherBound="132.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0d284800-7cf5-433c-9baa-816a7f75ea71" ParentLink="Catch_Statement" LowerBound="122.1" HigherBound="131.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI - Ezeke Matlab tSUC - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="b8bc1946-7d09-42ef-bd57-21e80b6f514c" ParentLink="ServiceBody_Statement" LowerBound="134.1" HigherBound="167.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="7a27f046-45c5-4a95-bd49-fbfbd6b864b5" ParentLink="ReallyComplexStatement_Branch" LowerBound="135.13" HigherBound="138.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="22fcb415-3dfb-4a86-92a9-7004f23be595" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="a762aa06-80b4-4d29-9bb3-cfe578f81171" ParentLink="ComplexStatement_Statement" LowerBound="140.1" HigherBound="166.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="146fa56d-193c-4521-a9b9-57f8d4d681ec" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="162.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="68d2cb0d-89a8-48f2-8305-eb5d5f7fbd9b" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="161.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tSUC;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="c3a9b487-61e7-4b4f-8c72-9791429581ee" ParentLink="Construct_MessageRef" LowerBound="146.35" HigherBound="146.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="27b11998-8c47-46c4-8db5-feda06de72da" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="164.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="699405c2-b309-4103-a810-03b6cff460be" ParentLink="ServiceBody_Statement" LowerBound="167.1" HigherBound="175.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="93d824b6-4d6c-4b40-9c4f-598cc29ccb22" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlab_eZK2B_tSUCInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlab_eZK2B_tSUCIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="a1726417-708a-4a13-ab00-f48422a8bc02" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a7cd1947-a769-46d7-a1d1-f4af1a1c1413" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="23.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="31" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="2ba5c1ad-6d14-4e48-b766-96c964ca1b6a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="572513f6-3bae-431e-843b-a263397b370c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="23.1" HigherBound="25.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="107" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="b1bcb80b-744f-4866-9417-6595fba5c68d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="23.1" HigherBound="24.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="4bbeb6a6-e9df-4cbd-8744-e11b0a23b51e" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="eZK2B_tSUCType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="dafaf8e3-b485-4b1a-8aa0-c669391527c7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.eZK2B_tSUC" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="7aa54fb3-047c-4e21-b1bd-15e50b3eaceb" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlab_eZK2B_tSUCInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="2e0e1274-25e1-4e02-9afa-c791b23cd8fa" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="55429a40-bc3f-4ceb-84e0-e654b3c5e101" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.27">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.eZK2B_tSUCType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype eZK2B_tSUCType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.eZK2B_tSUC parameter;
    };
    internal porttype EzekeMatlab_eZK2B_tSUCInType
    {
        oneway Receive
        {
            eZK2B_tSUCType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EzekeMatlab_tSUC
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EzekeMatlab_eZK2B_tSUCInType EzekeMatlab_eZK2B_tSUCIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EzekeMatlabZipFileInsertOutType EzekeMatlabZipFileInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message EzekeMatlabZipFileInsertResponseType EzekeMatlabZipFileInsertResponse;
        message EzekeMatlabZipFileInsertRequestType EzekeMatlabZipFileInsertRequest;
        message eZK2B_tSUCType eZK2B_tSUC;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String idEzekeMatlabZip;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f9dcb0c-e2b4-4c52-88ce-8660c5b79eda")]
            activate receive (EzekeMatlab_eZK2B_tSUCIn.Receive, eZK2B_tSUC);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            idEzekeMatlabZip = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("454b801c-5af2-41ed-a3e8-83e1b5f805c8")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(eZK2B_tSUC));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(eZK2B_tSUC);
            
            idEzekeMatlabZip = originalFileName.Substring(0, 36);
            originalFileName = originalFileName.Substring(37, originalFileName.Length - 37);
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("aa3262b0-7be1-4637-8e52-3997bdcfb79b")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", originalFileName,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tSUC,
            "id Elaborazione", A2A.EAI.INT_BI.Services.ProcessServices.GetEzekeIdElaborazione(eZK2B_tSUC.parameter)
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("73585a71-d0bb-47ae-8492-d6dad387ad8b")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("de1603fa-5ecf-4256-9182-afee9640b2af")]
                    construct EzekeMatlabZipFileInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("042e7204-510f-4ad9-be0b-8d90bafd4254")]
                        transform (EzekeMatlabZipFileInsertRequest.parameter) = A2A.EAI.INT_BI.Messaging.Maps.EzekeMatlab.eZK2B_tSUCToEzekeMatlabZipFileInsert (eZK2B_tSUC.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dd879fd8-4c97-4652-b8e9-eb5c6e0a44dc")]
                        EzekeMatlabZipFileInsertRequest.parameter.filename = originalFileName;
                        EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabFile = activityInstanceId;
                        EzekeMatlabZipFileInsertRequest.parameter.idEzekeMatlabZip = idEzekeMatlabZip;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("af99d5dd-5d86-4a6b-881e-54b932372d1a")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("065cc297-ec26-4c32-9c11-e473897865dd")]
                    send (EzekeMatlabZipFileInsertOut.EzekeMatlabZipFileInsert, EzekeMatlabZipFileInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51d37cbf-739f-49ba-b4f1-6133e35bab30")]
                    receive (EzekeMatlabZipFileInsertOut.EzekeMatlabZipFileInsert, EzekeMatlabZipFileInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("184b3d15-ee31-4ba5-bbab-96a1186ab01d")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4760a459-2aa8-4556-b0e7-ebc0fbe91a79")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1137f459-d426-478b-8a39-c99a6a7ea8c1")]
                        errorMessage.Append("INT_BI - Ezeke Matlab tSUC - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9e1d6f07-621d-4269-990f-53e4d40a3b51")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0d284800-7cf5-433c-9baa-816a7f75ea71")]
                        errorMessage.Append("INT_BI - Ezeke Matlab tSUC - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b8bc1946-7d09-42ef-bd57-21e80b6f514c")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("a762aa06-80b4-4d29-9bb3-cfe578f81171")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("146fa56d-193c-4521-a9b9-57f8d4d681ec")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("68d2cb0d-89a8-48f2-8305-eb5d5f7fbd9b")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_BI.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_BI.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_BI.Services.ProcessServices.FlowDescriptionEzekeMatlab_tSUC;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("27b11998-8c47-46c4-8db5-feda06de72da")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("699405c2-b309-4103-a810-03b6cff460be")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI.Services.ProcessServices.ActivityNameEzeke, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

