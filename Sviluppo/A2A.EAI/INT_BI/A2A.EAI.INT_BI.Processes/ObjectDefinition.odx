﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="7aec80d4-2c94-4b60-acea-2f4d83e12a28" LowerBound="1.1" HigherBound="34.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="b570c5b5-20ee-4e2a-9481-5b64c04d9a9e" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="17d03ef6-d703-46a4-bc4d-6e01a8c63ef8" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="cebb49a4-387c-40a2-a4bf-52765f0477fd" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.56">
                    <om:Property Name="Ref" Value="Microsys.EAI.Framework.Schemas.Notification" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="d95d1b25-843d-41d4-9d5d-bd98b2e5e507" ParentLink="Module_PortType" LowerBound="19.1" HigherBound="26.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileInsertOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="b578b0c5-c34c-4aca-a6a7-9f2d16441520" ParentLink="PortType_OperationDeclaration" LowerBound="21.1" HigherBound="25.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EzekeMatlabZipFileInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="4dbc0959-949f-40d5-a63f-1b8f6338ce9c" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="23.13" HigherBound="23.48">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b62960e8-e9f7-4dfb-a035-6ad974961f2d" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="23.50" HigherBound="23.86">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI.Processes.EzekeMatlabZipFileInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="067e069b-f3c5-439c-b7a3-efe03ea3479f" ParentLink="Module_ServiceDeclaration" LowerBound="26.1" HigherBound="33.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="89776ba6-9553-4430-9edc-c89c41be2cd4" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="ccc71f41-b2e2-422d-87c9-3ebd1778b277" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3336458c-67b3-485c-b311-811380589faf" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileInsertTypedProcedure.EzekeMatlabZipFileInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0c480bba-84c5-446d-a62a-648eb5e64401" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EzekeMatlabZipFileInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="ff8ac84f-6387-4a19-8f4e-25609ae1fbff" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileInsertTypedProcedure.EzekeMatlabZipFileInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI.Processes
{
    internal messagetype EzekeMatlabZipFileInsertRequestType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileInsertTypedProcedure.EzekeMatlabZipFileInsert parameter;
    };
    internal messagetype EzekeMatlabZipFileInsertResponseType
    {
        body A2A.EAI.INT_BI.Messaging.Schemas.EzekeMatlab.EzekeMatlabZipFileInsertTypedProcedure.EzekeMatlabZipFileInsertResponse parameter;
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            Microsys.EAI.Framework.Schemas.Notification
        };
    };
    internal porttype EzekeMatlabZipFileInsertOutType
    {
        requestresponse EzekeMatlabZipFileInsert
        {
            EzekeMatlabZipFileInsertRequestType, EzekeMatlabZipFileInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service ObjectDefinition
    {
        body ()
        {
        }
    }
}

