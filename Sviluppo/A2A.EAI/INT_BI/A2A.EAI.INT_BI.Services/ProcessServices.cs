﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.IO;
using System.Xml;

namespace A2A.EAI.INT_BI.Services
{
    [Serializable]
    public class ProcessServices
    {
        #region BAM
        
        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_BI";

        /// <summary>NBDO Settlement Activity Name (for BAM purpose)</summary>
        public const string ActivityName = "BI";
        public const string ActivityNameEzeke = "Ezeke";

        /// <summary>Flow Description</summary>
        public const string FlowDescriptionForecast = "Forecast";
        public const string FlowDescriptionBudgetSbil = "Budget Sbilanciamenti";

        public const string FlowDescriptionEzekeMatlab_tINFO = "Ezeke Matlab tINFO";
        public const string FlowDescriptionEzekeMatlab_tkeyMargini = "Ezeke Matlab tkeyMargini";
        public const string FlowDescriptionEzekeMatlab_tkeyQuantita = "Ezeke Matlab tkeyQuantita";
        public const string FlowDescriptionEzekeMatlab_tMargini = "Ezeke Matlab tMargini";
        public const string FlowDescriptionEzekeMatlab_tParco = "Ezeke Matlab tParco";
        public const string FlowDescriptionEzekeMatlab_tQuantita = "Ezeke Matlab tQuantita";
        public const string FlowDescriptionEzekeMatlab_tSUC = "Ezeke Matlab tSUC";

        public const string FlowDescriptionEzekeMatlabZipFileProcess = "Ezeke Matlab Zip File Process";

        public const string FlowDescriptionEzekeDispacciamentoEvidenze = "Ezeke Scelta Dispacciamento Evidenze";
        public const string FlowDescriptionEzekeDispacciamentoModuli = "Ezeke Scelta Dispacciamento Moduli";
        public const string FlowDescriptionEzekeDispacciamentoProcess = "Ezeke Scelta Dispacciamento Process";

        public const string FlowDescriptionEzekeAnelloStaging = "Ezeke Anello Staging";
        public const string FlowDescriptionEzekeAnelloProcess = "Ezeke Anello Process";
        public const string FlowDescriptionEzekeConfrontoStaging = "Ezeke Confronto Staging";
        public const string FlowDescriptionEzekeConfrontoProcess = "Ezeke Confronto Process";

        #endregion

        #region Forecast

        /// <summary>
        /// Definisce il tipo di impianto in base al nomeFile
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns>"I" - "T"</returns>
        public static string GetTipologiaImpianto(string fileName, string tipoDato)
        {
            string returnValue = String.Empty;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                if (tipoDato == "tipologiaImpianto")
                {
                    if (fileName.ToLower().Substring(0, 5) == ("termo"))
                    {
                        returnValue = "T";
                    }
                    else if (fileName.ToLower().Substring(0, 4) == ("idro"))
                    {
                        returnValue = "I";
                    }
                    else if (fileName.ToLower().Substring(0, 21) == ("idro_forecastserbatoi"))
                    {
                        returnValue = "I";
                    }
                }
                else if (tipoDato == "idTipoFileForecast")
                {
                    if (fileName.ToLower().Substring(0, 21) == ("idro_forecastserbatoi"))
                    {
                        returnValue = "2";
                    }
                    else if (fileName.ToLower().Substring(0, 5) == ("termo"))
                    {
                        returnValue = "1";
                    }
                    else if (fileName.ToLower().Substring(0, 4) == ("idro"))
                    {
                        returnValue = "1";
                    }
                }
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        /// <summary>
        /// Ricava l'id dell'elaborazione Ezeke Scelta Dispacciamento Evidenze/Moduli
        /// dal nome file.
        /// Il metodo si aspetta la seguente naming convention:
        /// SceltaDispacciamento_[FileType]_[DateFrom]_[DateTo]_[TimeStamp].TXT
        /// </summary>
        /// <param name="fileName">Original file name (Es: SceltaDispacciamento_evidenze_20190924_20191231_20200423093430.txt)</param>
        /// <returns>Restituisce [DateFrom]_[DateTo]_[TimeStamp]</returns>
        public static string GetEzekeSceltaDispacciamentoFileIdentity(string originalFileName)
        {
            string returnValue = String.Empty;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                string fileName = Path.GetFileNameWithoutExtension(originalFileName);

                string[] fileNameParts = fileName.Split('_');

                if (fileNameParts.Length != 5)
                {
                    throw new SystemException($"File name '{originalFileName}' does not respect naming convention 'SceltaDispacciamento_[FileType]_[DateFrom]_[DateTo]_[TimeStamp].TXT'");
                }

                returnValue = string.Concat(fileNameParts[2], "_", fileNameParts[3], "_", fileNameParts[4]);

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        public static string GetEzekeIdElaborazione(XmlDocument message)
        {
            string returnValue = String.Empty;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                returnValue = message.GetElementsByTagName("idelabEZEKE2B")[0].InnerText;
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                
                // Ignore
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        #endregion
    }
}
