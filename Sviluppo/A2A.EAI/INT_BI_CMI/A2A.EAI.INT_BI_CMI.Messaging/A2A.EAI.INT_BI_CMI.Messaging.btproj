﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{27E36E75-4CAE-4588-9DB5-0BF12BF1D693}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_BI_CMI.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_BI_CMI.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\RpaEsitoAfrr\N2MessaggiErroriAFRR.xsd">
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr</Namespace>
      <TypeName>N2MessaggiErroriAFRR</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoAfrr\N2MessaggiErroriAFRR.Type.xsd">
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr</Namespace>
      <TypeName>N2MessaggiErroriAFRR_Type</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoAfrr\RpaEsitoAfrrInsertTypedProcedure.Type.xsd">
      <TypeName>RpaEsitoAfrrInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoAfrr\RpaEsitoAfrrInsertTypedProcedure.xsd">
      <TypeName>RpaEsitoAfrrInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoAfrr\RpaEsitoAfrr.xsd">
      <SubType>Task</SubType>
      <TypeName>RpaEsitoMrr</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr</Namespace>
    </Schema>
    <Schema Include="Schemas\SyncDateOreQuartiEstesa\DateOreQuartiEstesaInsertTypedProcedure.Table.xsd">
      <TypeName>DateOreQuartiEstesaInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SyncDateOreQuartiEstesa\DateOreQuartiEstesaInsertTypedProcedure.xsd">
      <TypeName>DateOreQuartiEstesaInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SyncDateOreQuartiEstesa\BiCmiSyncDateOreQuartiEstesaTypedPolling.xsd">
      <TypeName>BiCmiSyncDateOreQuartiEstesaTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\MercatiMrrDati\MercatiMrrDatiInsertTypedProcedure.Table.xsd">
      <TypeName>MercatiMrrDatiInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\MercatiMrrDati\MercatiMrrDatiInsertTypedProcedure.xsd">
      <TypeName>MercatiMrrDatiInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\MercatiMrrDati\MercatiMrrDatiUpdateStatusTypedProcedure.xsd">
      <TypeName>MercatiMrrDatiUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\MercatiMrrDati\MercatiMrrDatiTypedPolling.xsd">
      <TypeName>MercatiMrrDatiTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SyncImpianti\BiCmiImpiantiInsertTypedProcedure.Table.xsd">
      <TypeName>BiCmiImpiantiInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncImpianti</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SyncImpianti\BiCmiImpiantiInsertTypedProcedure.xsd">
      <TypeName>BiCmiImpiantiInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncImpianti</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SyncImpianti\BiCmiSyncImpiantiTypedPolling.xsd">
      <TypeName>BiCmiSyncImpiantiTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncImpianti</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoMrr\RpaEsitoMrrInsertTypedProcedure.xsd">
      <TypeName>RpaEsitoMrrInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\RpaEsitoMrr\RpaEsitoMrr.xsd">
      <TypeName>RpaEsitoMrr</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipelines\In\RpaEsitoMrr.btp">
      <TypeName>RpaEsitoMrr</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\RpaEsitoMrr\RpaEsitoMrrInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\SyncImpianti\BiCmiSyncImpiantiTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\SyncImpianti\BiCmiImpiantiInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\MercatiMrrDati\MercatiMrrDatiTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\MercatiMrrDati\MercatiMrrDatiUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\MercatiMrrDati\MercatiMrrDatiInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\SyncDateOreQuartiEstesa\BiCmiSyncDateOreQuartiEstesaTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\SyncDateOreQuartiEstesa\DateOreQuartiEstesaInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\RpaEsitoAfrr\RpaEsitoAfrrInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\RpaEsitoAfrr\N2MessaggiErroriAFRR.BindingInfo.xml" />
    <Map Include="Maps\RpaEsitoAfrr\RpaEsitoAfrrToN2MessaggiErroriAFRR.btm">
      <TypeName>RpaEsitoAfrrToN2MessaggiErroriAFRR</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\RpaEsitoAfrr\RpaEsitoAfrrToRpaEsitoAfrrInsert.btm">
      <TypeName>RpaEsitoAfrrToRpaEsitoAfrrInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\In\RpaEsitoAfrr.btp">
      <TypeName>RpaEsitoAfrr</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\SyncDateOreQuartiEstesa\BiCmiSyncDateOreQuartiEstesaPollingToDateOreQuartiEstesaInsert.btm">
      <TypeName>BiCmiSyncDateOreQuartiEstesaPollingToDateOreQuartiEstesaInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.SyncDateOreQuartiEstesa</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\MercatiMrrDati\MercatiMrrDatiPollingMercatiMrrDatiInsert.btm">
      <TypeName>MercatiMrrDatiPollingMercatiMrrDatiInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\MercatiMrrDati\MercatiMrrDatiPollingToMercatiMrrDatiUpdateStatus.btm">
      <TypeName>MercatiMrrDatiPollingToMercatiMrrDatiUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\SyncImpianti\BiCmiSyncImpiantiPollingToInsert.btm">
      <TypeName>BiCmiSyncImpiantiPollingToInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.SyncImpianti</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\RpaEsitoMrr\RpaEsitoMrrToRpaEsitoMrrInsert.btm">
      <TypeName>RpaEsitoMrrToRpaEsitoMrrInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoMrr</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>