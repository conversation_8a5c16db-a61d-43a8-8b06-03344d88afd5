<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="MercatiMrrDatiType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="QattivaRR_ID" nillable="true" type="xs:long" />
      <xs:element minOccurs="0" maxOccurs="1" name="UnitaProduttiva_ID" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuarto" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="Oradelgiorno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Quarto" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="QattivaRR" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="9" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="QOFFRR_Sell" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="9" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="QOFFRR_Buy" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="9" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="QACCRR" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="9" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="MercatiMrrDatiType" nillable="true" type="ns3:MercatiMrrDatiType" />
  <xs:complexType name="ArrayOfMercatiMrrDatiType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MercatiMrrDatiType" type="ns3:MercatiMrrDatiType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfMercatiMrrDatiType" nillable="true" type="ns3:ArrayOfMercatiMrrDatiType" />
</xs:schema>