<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Orchestrations.BDM_Import.Schema.ImportF6QtaValidaPicassoWS" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Orchestrations.BDM_Import.Schema.ImportF6QtaValidaPicassoWS" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="importF6MessaggiErroriAFRR" type="tns:ImportF6MessaggiErroriAFRR" />
  <xs:complexType name="ImportF6MessaggiErroriAFRR">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="TimeStamp" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="EsitoVerifica" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="DataMessaggio" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Oggetto" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Testo" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="PeriodoDiInteresse" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>