<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.microsoft.com/Sql/2008/05/TypedPolling/idSyncDateOreQuartiEstesa" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/TypedPolling/idSyncDateOreQuartiEstesa" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TypedPolling.idSyncDateOreQuartiEstesa</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="TypedPollingResultSet0_b97a11">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="DateOreQuartiEstesa_ID" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraUTCPiuUnora" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="Anno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Mese" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Oradelgiorno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Quartodelgiorno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="_x0031_5MIN" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Quarto" nillable="true" type="xs:short" />
      <xs:element minOccurs="0" maxOccurs="1" name="TipoMercatoMSD" nillable="true" type="xs:short" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoUTC25" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoNormalizzatoLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoNormalizzatoUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="TipoMercatoXB" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="TypedPollingResultSet0" nillable="true" type="tns:TypedPollingResultSet0_b97a11" />
  <xs:complexType name="ArrayOfTypedPollingResultSet0_b97a11">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="TypedPollingResultSet0" type="tns:TypedPollingResultSet0_b97a11" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfTypedPollingResultSet0" nillable="true" type="tns:ArrayOfTypedPollingResultSet0_b97a11" />
  <xs:element name="TypedPolling">
    <xs:annotation>
      <xs:documentation>
        <doc:action xmlns:doc="http://schemas.microsoft.com/servicemodel/adapters/metadata/documentation">TypedPolling</doc:action>
        <doc:description xmlns:doc="http://schemas.microsoft.com/servicemodel/adapters/metadata/documentation">EXEC BiCmiSyncDateOreQuartiEstesaPolling</doc:description>
      </xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="TypedPollingResultSet0" nillable="true" type="tns:ArrayOfTypedPollingResultSet0_b97a11" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>