<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="DateOreQuartiEstesaType_9ee7b1">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="DateOreQuartiEstesa_ID" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraUTCPiuUnora" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="Anno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Mese" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Oradelgiorno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="Quartodelgiorno" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="_x0031_5MIN" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="10" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Quarto" nillable="true" type="xs:short" />
      <xs:element minOccurs="0" maxOccurs="1" name="TipoMercatoMSD" nillable="true" type="xs:short" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoUTC25" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoNormalizzatoLocal" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="DataOraQuartoNormalizzatoUTC" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="TipoMercatoXB" nillable="true" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="DateOreQuartiEstesaType" nillable="true" type="ns3:DateOreQuartiEstesaType_9ee7b1" />
  <xs:complexType name="ArrayOfDateOreQuartiEstesaType_9ee7b1">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="DateOreQuartiEstesaType" type="ns3:DateOreQuartiEstesaType_9ee7b1" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfDateOreQuartiEstesaType" nillable="true" type="ns3:ArrayOfDateOreQuartiEstesaType_9ee7b1" />
</xs:schema>