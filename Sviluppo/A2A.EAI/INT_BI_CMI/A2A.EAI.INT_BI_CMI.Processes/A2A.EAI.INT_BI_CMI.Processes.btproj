﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{D3DB5F8F-405C-43AB-9074-D58055A8ADAC}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_BI_CMI.Processes</RootNamespace>
    <AssemblyName>A2A.EAI.INT_BI_CMI.Processes</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsys.EAI.Framework.Azure.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ec334306cc72c98, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Azure.Services\v4.0_1.0.0.0__0ec334306cc72c98\Microsys.EAI.Framework.Azure.Services.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Schemas, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Schemas\v4.0_1.0.0.0__e651f0f96466e9b3\Microsys.EAI.Framework.Schemas.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Services\v4.0_1.0.0.0__e651f0f96466e9b3\Microsys.EAI.Framework.Services.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Services\A2A.EAI.Common.Services.csproj">
      <Project>{16facaa8-43de-45b6-b1a8-bbbc0b8f7bbf}</Project>
      <Name>A2A.EAI.Common.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_BI_CMI.Messaging\A2A.EAI.INT_BI_CMI.Messaging.btproj">
      <Project>{27e36e75-4cae-4588-9db5-0bf12bf1d693}</Project>
      <Name>A2A.EAI.INT_BI_CMI.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_BI_CMI.Services\A2A.EAI.INT_BI_CMI.Services.csproj">
      <Project>{03b761cb-2d7a-46fa-b3ca-640f5a5e6336}</Project>
      <Name>A2A.EAI.INT_BI_CMI.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="RpaEsitoMrrInsert.odx">
      <TypeName>BizTalk_Orchestration1</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="ObjectDefinition.odx">
      <TypeName>ObjectDefinition</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="SyncImpianti.odx">
      <TypeName>SyncImpianti</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="MercatiMrrDati.odx">
      <TypeName>MercatiRrDati</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="SyncDateOreQuartiEstesa.odx">
      <TypeName>SyncDateOreQuartiEstesa</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="RpaEsitoAfrrInsert.odx">
      <TypeName>RpaEsitoAfrrInsert</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="RpaEsitoAfrrToN2.odx">
      <TypeName>RpaEsitoAfrrToN2</TypeName>
      <Namespace>A2A.EAI.INT_BI_CMI.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>