﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="74dce5a8-2ad8-4388-b51a-cb687b9492dc" LowerBound="1.1" HigherBound="233.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="9ae10b38-508f-4650-a3d2-717119e692db" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="56c4ed0f-5999-4411-af1a-ea34e4e3bdc9" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2c45a387-a9bb-4f71-b7eb-b2be4cd1d0be" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.38">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="5e2b2427-862d-44ae-b72b-6db048d9989e" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="16e894e2-4b94-4d03-96a8-5c8ea82fdefe" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="6e111c2e-c1b0-4526-99b9-7371b353329d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.44">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="ec9be4e5-87c2-4652-a882-e5532c021e19" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.46" HigherBound="35.78">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="10157d3e-9640-4dc8-bd46-08dd5996440b" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ae515895-4ffe-4d83-8607-780861842038" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiMercatiMrrDatiUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b2214679-ce94-43b9-a3ad-b74cb58856f0" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.50">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="0b1c252d-8ea8-4e06-8fd2-2484dc949c89" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.52" HigherBound="42.90">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="330fc468-c707-4b49-89f6-bcb708244a6b" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4f0787a1-5310-4eee-b061-4af12da149d1" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="066a7fd1-36dc-45ee-9029-601a387d3ae1" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a637225f-fa2a-4a5c-a3ce-d0852ea0c54c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiInsertTypedProcedure.MercatiMrrDatiInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="285bb4a3-7441-4111-9524-b2ebdb777e26" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a86c32f5-c7d1-48e7-a77c-fcb2a2e977ca" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiInsertTypedProcedure.MercatiMrrDatiInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="493db33a-9ad4-4f0c-9c71-65c81c87891d" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c0e7b32a-d4af-49ef-a4e2-8aaee6a82612" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiUpdateStatusTypedProcedure.BiCmiMercatiMrrDatiUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b2d47f93-9cc4-441d-b2da-b033b8ac2b51" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="117eae18-4451-4b7f-9689-8d612ad1769f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiUpdateStatusTypedProcedure.BiCmiMercatiMrrDatiUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="d34fd70e-30ee-4165-9241-a7a4dca6b23b" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="232.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="MercatiMrrDati" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="511c07a8-c1af-4b5c-b7bb-ad620645034e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="28d74a45-93e9-4239-be3c-3445a45cbd96" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2392443b-ecad-4d47-9d61-a113e8db10a9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ce3446bd-aabf-4a07-9b19-260c42d52607" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9f11cc31-a778-4aa1-8745-b95ec0f3fb8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2c693b6b-6618-41cb-85d0-88eedaaf261b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d0e26f08-f262-4604-a1b7-b239b90b3b21" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="be41eef5-ad58-400c-8183-2d5b9a0e0373" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7c557c12-56d1-42e7-be69-6d0252743645" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="723408d9-fa62-43b7-a948-f620ff5c427c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c5efdd2d-b979-486e-869f-5e17f0df0d3a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4737aaf5-36ef-45e8-b8ce-32e41bef2446" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f645e48d-6060-4487-a507-19331fb9be49" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="e138e9ec-b1a8-420d-83d3-3175476ca9f5" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="6aaee507-47c8-4dd4-99b4-2d596c913079" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="77.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="MercatiMrrDatiPollingIn" />
                    <om:Property Name="MessageName" Value="MercatiMrrDatiPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a35ff16c-0fcc-4fba-9a0d-2b628515876c" ParentLink="ServiceBody_Statement" LowerBound="77.1" HigherBound="84.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="31351bf6-736a-4a6f-a242-f726edaa5931" ParentLink="ServiceBody_Statement" LowerBound="84.1" HigherBound="93.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="1524f88c-b82d-46ba-ac89-2227c8d9ee0c" ParentLink="ServiceBody_Statement" LowerBound="93.1" HigherBound="143.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="51664ca5-f024-4051-99e6-5789dfef6010" ParentLink="ComplexStatement_Statement" LowerBound="98.1" HigherBound="100.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="254dcf52-aad8-4f32-a732-e5b682f2674c" ParentLink="ComplexStatement_Statement" LowerBound="100.1" HigherBound="106.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="b7f5054f-2448-4986-ac50-11bd830ed16a" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="105.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati.MercatiMrrDatiPollingMercatiMrrDatiInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="975c56ae-e7aa-420f-8463-82e20177d677" ParentLink="Transform_InputMessagePartRef" LowerBound="104.169" HigherBound="104.200">
                                <om:Property Name="MessageRef" Value="MercatiMrrDatiPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="ddc50704-49f5-43d3-b0c8-685436502812" ParentLink="Transform_OutputMessagePartRef" LowerBound="104.36" HigherBound="104.73">
                                <om:Property Name="MessageRef" Value="MercatiMrrDatiInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="32e21e84-fa7f-4170-bfc0-9bcf783dd83e" ParentLink="Construct_MessageRef" LowerBound="101.31" HigherBound="101.58">
                            <om:Property Name="Ref" Value="MercatiMrrDatiInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="ea88912b-b269-4fff-9d5e-6a1e9dea7b8d" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="108.1">
                        <om:Property Name="PortName" Value="MercatiMrrDatiInsertOut" />
                        <om:Property Name="MessageName" Value="MercatiMrrDatiInsertRequest" />
                        <om:Property Name="OperationName" Value="MercatiMrrDatiInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="58fffe22-8a99-4431-a6da-de62bd3a0fc2" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="110.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="MercatiMrrDatiInsertOut" />
                        <om:Property Name="MessageName" Value="MercatiMrrDatiInsertResponse" />
                        <om:Property Name="OperationName" Value="MercatiMrrDatiInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="73957867-b789-4b39-a2ed-c3e702baa485" ParentLink="ComplexStatement_Statement" LowerBound="110.1" HigherBound="116.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="08025e52-1a65-4b58-bc5d-8cb83ef6686d" ParentLink="Scope_Catch" LowerBound="119.1" HigherBound="130.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="00de3006-5ac0-43b4-95bc-e3cbe68f8a22" ParentLink="Catch_Statement" LowerBound="122.1" HigherBound="129.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI MercatiMrrDati - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="39c0eb99-17c0-408e-8180-ba0e489e16d0" ParentLink="Scope_Catch" LowerBound="130.1" HigherBound="141.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="992ea27f-a59e-430e-8e15-b84af20008f5" ParentLink="Catch_Statement" LowerBound="133.1" HigherBound="140.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI MercatiMrrDati - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="22930e5e-12ed-4c93-9985-35782788afb4" ParentLink="ServiceBody_Statement" LowerBound="143.1" HigherBound="188.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="False" />
                    <om:Element Type="Catch" OID="40c9c03d-b18d-40d1-b390-ddaed7bf8203" ParentLink="Scope_Catch" LowerBound="163.1" HigherBound="175.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d5a77452-bd53-458b-92d6-3283fe2a30ab" ParentLink="Catch_Statement" LowerBound="166.1" HigherBound="174.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI MercatiMrrDati - Errore in fase di update status. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="2893646f-54f8-4d4c-a155-969bcf3b8f91" ParentLink="Scope_Catch" LowerBound="175.1" HigherBound="186.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="10d3d786-246e-410c-9d12-5c2ddf327c6c" ParentLink="Catch_Statement" LowerBound="178.1" HigherBound="185.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI MercatiMrrDati - Errore in fase di update status. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Construct" OID="4f2fe34f-df67-4e19-b834-5cc0a9088ddc" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="156.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="b7ca5368-d263-43cf-9099-8ebdaddd007c" ParentLink="ComplexStatement_Statement" LowerBound="151.1" HigherBound="153.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati.MercatiMrrDatiPollingToMercatiMrrDatiUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="36e55d6d-cdf4-41c5-9aca-91b84f6a464f" ParentLink="Transform_InputMessagePartRef" LowerBound="152.183" HigherBound="152.214">
                                <om:Property Name="MessageRef" Value="MercatiMrrDatiPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="d29dbdb1-dfa9-48ec-b29c-ccaf73257017" ParentLink="Transform_OutputMessagePartRef" LowerBound="152.36" HigherBound="152.79">
                                <om:Property Name="MessageRef" Value="MercatiMrrDatiUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="fbe28868-7851-449c-ae15-2b403db8494e" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="155.1">
                            <om:Property Name="Expression" Value="MercatiMrrDatiUpdateStatusRequest.parameter.status = (System.Int32)resultCode;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign Result" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="f23ffc60-6191-4bea-9cf2-c7eb5fa91728" ParentLink="Construct_MessageRef" LowerBound="149.31" HigherBound="149.64">
                            <om:Property Name="Ref" Value="MercatiMrrDatiUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="4a40aa0f-d206-4aed-bff7-1e377dbe672f" ParentLink="ComplexStatement_Statement" LowerBound="156.1" HigherBound="158.1">
                        <om:Property Name="PortName" Value="MercatiMrrDatiUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="MercatiMrrDatiUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="BiCmiMercatiMrrDatiUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="e6323a07-8f61-4b77-966e-c0f5a467c70e" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="160.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="MercatiMrrDatiUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="MercatiMrrDatiUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="BiCmiMercatiMrrDatiUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="118bc46a-0e7a-4914-93a1-76e25e08281b" ParentLink="ServiceBody_Statement" LowerBound="188.1" HigherBound="222.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="2fdf244d-f4b2-4f5b-9903-cd5cd71d49ed" ParentLink="ReallyComplexStatement_Branch" LowerBound="189.13" HigherBound="192.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="3e40f45e-66c2-462b-9c67-c87304981c7a" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="c25930fe-1de3-475c-ba7e-21c09a721a44" ParentLink="ComplexStatement_Statement" LowerBound="194.1" HigherBound="221.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="b5a23d3e-3f61-4d41-a46f-beca1e1f73d2" ParentLink="ComplexStatement_Statement" LowerBound="199.1" HigherBound="217.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="82f5d4d8-8f19-4b7f-a1bc-f91b14f167e2" ParentLink="Construct_MessageRef" LowerBound="200.35" HigherBound="200.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="36617c9c-9dff-4e73-8dfc-12e0df1a3b70" ParentLink="ComplexStatement_Statement" LowerBound="202.1" HigherBound="216.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionMercatiMrrDati;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;//Notification.messageNotes = System.String.Format(&quot;ActivityId: '{0}'&quot;, activityInstanceId);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="5cb25462-8137-4467-84f3-3c923886e88d" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="219.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="8f46c8ac-ccc5-4430-9d89-7c871f574186" ParentLink="ServiceBody_Statement" LowerBound="222.1" HigherBound="230.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e87e4836-43d6-41a5-91db-2f3dd0071f22" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="66b8ad9b-7464-45fe-921d-e9fec074ac3e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3c23c88d-7986-4b4d-8db9-7304fdd079f5" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="34" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="94aecc5e-f503-4c74-b228-f9594d8d3ba9" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4eb9679c-1280-4725-b1e1-5bed259164e1" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="56.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="104" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.MercatiMrrDatiUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="MercatiMrrDatiUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c6b2ccf6-9865-4d97-a068-1ed233dc8181" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8ec40cce-f167-4839-b803-d1682fb23425" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="56.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="158" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="1c06eaf6-5219-4342-8dd3-068896fa5e1c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="56.1" HigherBound="57.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype MercatiMrrDatiPollingType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiTypedPolling.TypedPolling parameter;
    };
    internal messagetype MercatiMrrDatiInsertRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiInsertTypedProcedure.MercatiMrrDatiInsert parameter;
    };
    internal messagetype MercatiMrrDatiInsertResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiInsertTypedProcedure.MercatiMrrDatiInsertResponse parameter;
    };
    internal messagetype MercatiMrrDatiUpdateStatusRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiUpdateStatusTypedProcedure.BiCmiMercatiMrrDatiUpdateStatus parameter;
    };
    internal messagetype MercatiMrrDatiUpdateStatusResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.MercatiMrrDati.MercatiMrrDatiUpdateStatusTypedProcedure.BiCmiMercatiMrrDatiUpdateStatusResponse parameter;
    };
    internal porttype MercatiMrrDatiPollingInType
    {
        oneway Receive
        {
            MercatiMrrDatiPollingType
        };
    };
    internal porttype MercatiMrrDatiInsertOutType
    {
        requestresponse MercatiMrrDatiInsert
        {
            MercatiMrrDatiInsertRequestType, MercatiMrrDatiInsertResponseType
        };
    };
    internal porttype MercatiMrrDatiUpdateStatusOutType
    {
        requestresponse BiCmiMercatiMrrDatiUpdateStatus
        {
            MercatiMrrDatiUpdateStatusRequestType, MercatiMrrDatiUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service MercatiMrrDati
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements MercatiMrrDatiPollingInType MercatiMrrDatiPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses MercatiMrrDatiInsertOutType MercatiMrrDatiInsertOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses MercatiMrrDatiUpdateStatusOutType MercatiMrrDatiUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message MercatiMrrDatiPollingType MercatiMrrDatiPolling;
        message MercatiMrrDatiInsertRequestType MercatiMrrDatiInsertRequest;
        message MercatiMrrDatiInsertResponseType MercatiMrrDatiInsertResponse;
        message MercatiMrrDatiUpdateStatusRequestType MercatiMrrDatiUpdateStatusRequest;
        message MercatiMrrDatiUpdateStatusResponseType MercatiMrrDatiUpdateStatusResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6aaee507-47c8-4dd4-99b4-2d596c913079")]
            activate receive (MercatiMrrDatiPollingIn.Receive, MercatiMrrDatiPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a35ff16c-0fcc-4fba-9a0d-2b628515876c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("31351bf6-736a-4a6f-a242-f726edaa5931")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1524f88c-b82d-46ba-ac89-2227c8d9ee0c")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51664ca5-f024-4051-99e6-5789dfef6010")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("254dcf52-aad8-4f32-a732-e5b682f2674c")]
                    construct MercatiMrrDatiInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b7f5054f-2448-4986-ac50-11bd830ed16a")]
                        transform (MercatiMrrDatiInsertRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati.MercatiMrrDatiPollingMercatiMrrDatiInsert (MercatiMrrDatiPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea88912b-b269-4fff-9d5e-6a1e9dea7b8d")]
                    send (MercatiMrrDatiInsertOut.MercatiMrrDatiInsert, MercatiMrrDatiInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("58fffe22-8a99-4431-a6da-de62bd3a0fc2")]
                    receive (MercatiMrrDatiInsertOut.MercatiMrrDatiInsert, MercatiMrrDatiInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("73957867-b789-4b39-a2ed-c3e702baa485")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("08025e52-1a65-4b58-bc5d-8cb83ef6686d")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("00de3006-5ac0-43b4-95bc-e3cbe68f8a22")]
                        errorMessage.Append("INT_BI_CMI MercatiMrrDati - Errore in fase di inserimento dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("39c0eb99-17c0-408e-8180-ba0e489e16d0")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("992ea27f-a59e-430e-8e15-b84af20008f5")]
                        errorMessage.Append("INT_BI_CMI MercatiMrrDati - Errore in fase di inserimento dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("22930e5e-12ed-4c93-9985-35782788afb4")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4f2fe34f-df67-4e19-b834-5cc0a9088ddc")]
                    construct MercatiMrrDatiUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b7ca5368-d263-43cf-9099-8ebdaddd007c")]
                        transform (MercatiMrrDatiUpdateStatusRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.MercatiMrrDati.MercatiMrrDatiPollingToMercatiMrrDatiUpdateStatus (MercatiMrrDatiPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fbe28868-7851-449c-ae15-2b403db8494e")]
                        MercatiMrrDatiUpdateStatusRequest.parameter.status = (System.Int32)resultCode;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4a40aa0f-d206-4aed-bff7-1e377dbe672f")]
                    send (MercatiMrrDatiUpdateStatusOut.BiCmiMercatiMrrDatiUpdateStatus, MercatiMrrDatiUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6323a07-8f61-4b77-966e-c0f5a467c70e")]
                    receive (MercatiMrrDatiUpdateStatusOut.BiCmiMercatiMrrDatiUpdateStatus, MercatiMrrDatiUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("40c9c03d-b18d-40d1-b390-ddaed7bf8203")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d5a77452-bd53-458b-92d6-3283fe2a30ab")]
                        errorMessage.Append("INT_BI_CMI MercatiMrrDati - Errore in fase di update status. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2893646f-54f8-4d4c-a155-969bcf3b8f91")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("10d3d786-246e-410c-9d12-5c2ddf327c6c")]
                        errorMessage.Append("INT_BI_CMI MercatiMrrDati - Errore in fase di update status. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("118bc46a-0e7a-4914-93a1-76e25e08281b")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c25930fe-1de3-475c-ba7e-21c09a721a44")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b5a23d3e-3f61-4d41-a46f-beca1e1f73d2")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("36617c9c-9dff-4e73-8dfc-12e0df1a3b70")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionMercatiMrrDati;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            //Notification.messageNotes = System.String.Format("ActivityId: '{0}'", activityInstanceId);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5cb25462-8137-4467-84f3-3c923886e88d")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8f46c8ac-ccc5-4430-9d89-7c871f574186")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameMercatiMrrDati, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

