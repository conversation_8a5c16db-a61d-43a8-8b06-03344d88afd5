﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="d9975b84-2948-436e-b84d-a994f44c40ea" LowerBound="1.1" HigherBound="38.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="1d8c97c7-55a2-4843-812d-ac99d9c0f39f" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="0dde7585-f996-4565-8028-639c14b559be" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="5fbfc8a6-e5d9-4dc1-9f4a-aa4892222e58" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="63199376-5568-443c-abd5-9a3f178bad21" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="40ad7f15-bcb7-44e5-83b8-200cdc76178f" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="ffb481fb-fd29-4518-a2e4-e58b0ce59462" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="c0fda2c8-a6c7-4ba5-88a0-d7f0a00d0ccd" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="37.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="87618359-da80-4107-b2ff-da49677daf0b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0a1d63ef-9802-47dc-bb8c-4e23fc848f4c" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FaultType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6246b46b-4b71-4953-b268-5edf88c33fc4" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="BTS.soap_envelope_1__1.Fault" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultString" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="01c02358-3adb-4b27-9ac2-99b414a4a48a" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="ac36283a-1a17-414e-9c19-e3eeb3ff465f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoMrr" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="fd8ce8d9-a823-4869-abcf-1e0f5e0d697e" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="eb857073-094a-4cec-a9e9-449f0d636a28" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype FaultType
    {
        body BTS.soap_envelope_1__1.Fault faultString;
    };
    internal messagetype RpaEsitoAfrrType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoMrr parameter;
    };
    internal messagetype NotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameter;
    };
    internal porttype RpaEsitoAfrrInType
    {
        oneway Receive
        {
            RpaEsitoAfrrType
        };
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            NotificationType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service ObjectDefinition
    {
        body ()
        {
        }
    }
}

