﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="5e512c89-4876-4f0e-82a9-8a97c8254de4" LowerBound="1.1" HigherBound="175.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="c8147bc9-9152-404c-846a-0efd9ca06de2" ParentLink="Module_ServiceDeclaration" LowerBound="19.1" HigherBound="174.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrInsert" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="eddf4899-fc05-4801-8933-125e9fcdb6bd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3603f9ea-118d-4ba5-86cd-efbe6d8035eb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cb8dd526-7e76-4575-a490-9ca6a30e69ef" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="afb1f0ce-b7d6-4511-b127-a43c3f29b2a5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ecdf7ec4-e28d-497f-bb02-667d60f274ec" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f831fc6f-1376-42f3-b8da-ebe6f6a08617" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed59ce9a-d080-4e26-a815-1fb4c8c6b6cb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d6816393-c9d6-42b3-8855-ed35d65e8a5d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d7fccd6c-39a3-4a94-a1ee-3370ae308baf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5d828506-edcb-4e61-963b-cd830c5e1987" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c34a07c2-de42-49d3-9fff-cb65fbb4fe3e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrr" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f9b37b8f-fcb6-4d23-b1d2-9c637a8c7085" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="0d0da87b-ff14-47d9-972d-367761dfb4b2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="1f4fc2d1-9f85-45c2-a5ac-30257f468413" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="85972f38-803b-40fc-b4ba-80642ed27f25" ParentLink="ServiceBody_Statement" LowerBound="44.1" HigherBound="50.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="RpaEsitoAfrrIn" />
                    <om:Property Name="MessageName" Value="RpaEsitoAfrr" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="fbdee209-5089-4d9f-b4e8-9e1f0b34958d" ParentLink="ServiceBody_Statement" LowerBound="50.1" HigherBound="60.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoAfrr));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoAfrr);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="bb62c195-4bb0-4240-8d2b-601c2facccae" ParentLink="ServiceBody_Statement" LowerBound="60.1" HigherBound="72.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Esito Verifica&quot;, RpaEsitoAfrr.parameter.Detail.esitoVerifica,&#xD;&#xA;&quot;Data Messaggio&quot;, RpaEsitoAfrr.parameter.Detail.dataMessaggio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="adfd2dea-8e0a-4e04-b31e-bbf82a1785b8" ParentLink="ServiceBody_Statement" LowerBound="72.1" HigherBound="130.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Catch" OID="0e684887-2bf7-47bc-9b14-58a448739062" ParentLink="Scope_Catch" LowerBound="104.1" HigherBound="116.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="1ea51070-396d-463a-b2f2-683e1fe9b0da" ParentLink="Catch_Statement" LowerBound="107.1" HigherBound="115.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI RpaEsitoAfrrInsert - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="c1753147-0b8f-4445-bb43-3e290b9d9c12" ParentLink="Scope_Catch" LowerBound="116.1" HigherBound="128.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="19a73894-39c5-42ed-8cd7-ffe99531b081" ParentLink="Catch_Statement" LowerBound="119.1" HigherBound="127.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI RpaEsitoAfrrInsert - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="356f0b3e-5dad-4443-96c0-8495774509f3" ParentLink="ComplexStatement_Statement" LowerBound="77.1" HigherBound="79.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="f5b3e1ca-2280-4c49-b2a6-efa61643ac32" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="88.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct Insert BI Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="d8cd8d7a-c98d-414f-a16e-598ede560026" ParentLink="ComplexStatement_Statement" LowerBound="82.1" HigherBound="84.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr.RpaEsitoAfrrToRpaEsitoAfrrInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="28e74359-8328-4b26-ab3a-0144f7e10303" ParentLink="Transform_InputMessagePartRef" LowerBound="83.156" HigherBound="83.178">
                                <om:Property Name="MessageRef" Value="RpaEsitoAfrr" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="1c909b39-c44e-4753-86ba-f5c38f3a67e6" ParentLink="Transform_OutputMessagePartRef" LowerBound="83.36" HigherBound="83.71">
                                <om:Property Name="MessageRef" Value="RpaEsitoAfrrInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="2fa8fe04-ec34-40e9-a496-f20334d81336" ParentLink="ComplexStatement_Statement" LowerBound="84.1" HigherBound="87.1">
                            <om:Property Name="Expression" Value="RpaEsitoAfrrInsertRequest.parameter.fileName = @originalFileName;&#xD;&#xA;RpaEsitoAfrrInsertRequest.parameter.activityId = @activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign FileName" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="0beabdb7-c466-4ff1-9965-fa5367781b4e" ParentLink="Construct_MessageRef" LowerBound="80.31" HigherBound="80.56">
                            <om:Property Name="Ref" Value="RpaEsitoAfrrInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="3b52b100-fe5c-473b-bf00-030fc87b40e0" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                        <om:Property Name="PortName" Value="RpaEsitoAfrrInsertOut" />
                        <om:Property Name="MessageName" Value="RpaEsitoAfrrInsertRequest" />
                        <om:Property Name="OperationName" Value="RpaEsitoAfrrInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="6181ab83-431f-449e-8a67-bbce26a6125f" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="92.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="RpaEsitoAfrrInsertOut" />
                        <om:Property Name="MessageName" Value="RpaEsitoAfrrInsertResponse" />
                        <om:Property Name="OperationName" Value="RpaEsitoAfrrInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="4e1ee08b-67c8-4648-92eb-aead58477e2b" ParentLink="ComplexStatement_Statement" LowerBound="92.1" HigherBound="101.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Periodo Dalle&quot;, RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.periodoInteresseDalle,&#xD;&#xA;&quot;Periodo Alle&quot;, RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.periodoInteresseAlle,&#xD;&#xA;&quot;note&quot;, RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.note&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="f23629e0-a037-43df-ae62-b3333dd5c65d" ParentLink="ServiceBody_Statement" LowerBound="130.1" HigherBound="164.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="6d8d5fc8-0ed2-4da7-8ae6-16d8a90be538" ParentLink="ReallyComplexStatement_Branch" LowerBound="131.13" HigherBound="134.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="221dd17c-aa8a-4ba0-9291-d41034f91a8f" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="2aa779f1-0bb2-4e00-b163-d10ff7430986" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="163.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="96162353-1324-44f9-b1e5-a18bdc5cffb8" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="159.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="78c21d25-3668-4603-8f8e-8816a136a109" ParentLink="Construct_MessageRef" LowerBound="142.35" HigherBound="142.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="78367e5e-fc4f-4096-b03b-4235fc35caa7" ParentLink="ComplexStatement_Statement" LowerBound="144.1" HigherBound="158.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaAfrrInsert;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;//Notification.messageNotes = System.String.Format(&quot;ActivityId: '{0}'&quot;, activityInstanceId);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e3b94dfc-39af-4866-92d1-91cf9722d429" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="161.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5f256031-2cc8-44d1-b840-b1f6421a50d7" ParentLink="ServiceBody_Statement" LowerBound="164.1" HigherBound="172.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="52e4c311-bf42-4ba1-a29c-8e2ea855ce62" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="24.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="41dffa86-49d2-41b1-9bec-9926881200e5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5b835e26-5e00-41b3-a36d-adf3144a16c9" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="24.1" HigherBound="27.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="42" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="6fba8732-3a37-4d85-b424-cfba515856eb" ParentLink="PortDeclaration_CLRAttribute" LowerBound="24.1" HigherBound="25.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3dab3a47-1c35-4727-89d8-0eccac245451" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="27.1" HigherBound="29.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="103" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="c0238af8-f690-42c2-8850-8092f21c7dda" ParentLink="PortDeclaration_CLRAttribute" LowerBound="27.1" HigherBound="28.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7caef402-c421-497b-aef9-a839fc748b1d" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="339c3400-1c6b-47ea-b9c4-6a101e3707cf" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoAfrrInsertTypedProcedure.RpaEsitoAfrrInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="f7ce2973-c7f2-4e0f-afe6-12d4aed388bb" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="6aeec086-5575-40ab-a03e-2302f339c555" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoAfrrInsertTypedProcedure.RpaEsitoAfrrInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="73c046a0-50f9-4730-8133-db9c3a83c3ee" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9e6b3b6c-e24c-4d3b-8b7b-a2f4125590e3" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="c2b8a35f-6923-473f-a104-18d87aea36f0" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.42">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="24ec815e-547c-45cb-bd3d-8dddd0255fca" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="16.44" HigherBound="16.74">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype RpaEsitoAfrrInsertRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoAfrrInsertTypedProcedure.RpaEsitoAfrrInsert parameter;
    };
    internal messagetype RpaEsitoAfrrInsertResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.RpaEsitoAfrrInsertTypedProcedure.RpaEsitoAfrrInsertResponse parameter;
    };
    internal porttype RpaEsitoAfrrInsertOutType
    {
        requestresponse RpaEsitoAfrrInsert
        {
            RpaEsitoAfrrInsertRequestType, RpaEsitoAfrrInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service RpaEsitoAfrrInsert
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements RpaEsitoAfrrInType RpaEsitoAfrrIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses RpaEsitoAfrrInsertOutType RpaEsitoAfrrInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message RpaEsitoAfrrType RpaEsitoAfrr;
        message RpaEsitoAfrrInsertRequestType RpaEsitoAfrrInsertRequest;
        message RpaEsitoAfrrInsertResponseType RpaEsitoAfrrInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("85972f38-803b-40fc-b4ba-80642ed27f25")]
            activate receive (RpaEsitoAfrrIn.Receive, RpaEsitoAfrr);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fbdee209-5089-4d9f-b4e8-9e1f0b34958d")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoAfrr));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoAfrr);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bb62c195-4bb0-4240-8d2b-601c2facccae")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Esito Verifica", RpaEsitoAfrr.parameter.Detail.esitoVerifica,
            "Data Messaggio", RpaEsitoAfrr.parameter.Detail.dataMessaggio,
            "instanceId", activityInstanceId
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("adfd2dea-8e0a-4e04-b31e-bbf82a1785b8")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("356f0b3e-5dad-4443-96c0-8495774509f3")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5b3e1ca-2280-4c49-b2a6-efa61643ac32")]
                    construct RpaEsitoAfrrInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8cd8d7a-c98d-414f-a16e-598ede560026")]
                        transform (RpaEsitoAfrrInsertRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr.RpaEsitoAfrrToRpaEsitoAfrrInsert (RpaEsitoAfrr.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2fa8fe04-ec34-40e9-a496-f20334d81336")]
                        RpaEsitoAfrrInsertRequest.parameter.fileName = @originalFileName;
                        RpaEsitoAfrrInsertRequest.parameter.activityId = @activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b52b100-fe5c-473b-bf00-030fc87b40e0")]
                    send (RpaEsitoAfrrInsertOut.RpaEsitoAfrrInsert, RpaEsitoAfrrInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6181ab83-431f-449e-8a67-bbce26a6125f")]
                    receive (RpaEsitoAfrrInsertOut.RpaEsitoAfrrInsert, RpaEsitoAfrrInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e1ee08b-67c8-4648-92eb-aead58477e2b")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now),
                    "Periodo Dalle", RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.periodoInteresseDalle,
                    "Periodo Alle", RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.periodoInteresseAlle,
                    "note", RpaEsitoAfrrInsertResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.note
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e684887-2bf7-47bc-9b14-58a448739062")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1ea51070-396d-463a-b2f2-683e1fe9b0da")]
                        errorMessage.Append("INT_BI_CMI RpaEsitoAfrrInsert - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c1753147-0b8f-4445-bb43-3e290b9d9c12")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("19a73894-39c5-42ed-8cd7-ffe99531b081")]
                        errorMessage.Append("INT_BI_CMI RpaEsitoAfrrInsert - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f23629e0-a037-43df-ae62-b3333dd5c65d")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("2aa779f1-0bb2-4e00-b163-d10ff7430986")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("96162353-1324-44f9-b1e5-a18bdc5cffb8")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("78367e5e-fc4f-4096-b03b-4235fc35caa7")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaAfrrInsert;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            //Notification.messageNotes = System.String.Format("ActivityId: '{0}'", activityInstanceId);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3b94dfc-39af-4866-92d1-91cf9722d429")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f256031-2cc8-44d1-b840-b1f6421a50d7")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaAfrrInsert, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

