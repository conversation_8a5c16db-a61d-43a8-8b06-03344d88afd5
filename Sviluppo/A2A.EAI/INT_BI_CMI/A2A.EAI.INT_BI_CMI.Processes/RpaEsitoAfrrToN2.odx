﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="6756af04-6130-434a-84f6-05fcb5786d69" LowerBound="1.1" HigherBound="189.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="2661b98e-cdcd-41f0-81b0-61f9e2e15d28" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:portType name=&quot;F6MessaggiErroriAFRRSoap&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="N2MessaggiErroriAFRROutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="5391184a-1b38-49a5-9f4d-4c18376dc7bb" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:operation name=&quot;ImportazioneF6MessaggiErroriAFRR&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="ImportazioneF6MessaggiErroriAFRR" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="MessageRef" OID="d2abf470-e19a-466e-97f8-521fcf488942" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="16.80" HigherBound="16.97">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.FaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="757829ba-75f6-4804-8fe4-d59a85dfbe8f" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="16.46" HigherBound="16.78">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.N2MessaggiErroriAFRRResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:output message=&quot;http://www.edipower.it/nbdows:ImportazioneF6MessaggiErroriAFRRSoapOut&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b5567bc7-dbab-4bc4-a97d-ab51d986b857" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.44">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.N2MessaggiErroriAFRRRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:input message=&quot;http://www.edipower.it/nbdows:ImportazioneF6MessaggiErroriAFRRSoapIn&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="56ff563a-1daf-4c6c-9317-f38540eb2b5c" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2MessaggiErroriAFRRRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d01082c7-29ca-46a0-bdca-bf1bb9165a01" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.N2MessaggiErroriAFRR.ImportazioneF6MessaggiErroriAFRR" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="fc155cfb-b934-48fd-81c9-4facd4ee777c" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="N2MessaggiErroriAFRRResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e889bfea-0aec-4c6c-bada-e03e45b9ae78" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.N2MessaggiErroriAFRR.ImportazioneF6MessaggiErroriAFRRResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="e191331e-dcd2-4d15-82e7-8ddf29b4f279" ParentLink="Module_ServiceDeclaration" LowerBound="19.1" HigherBound="188.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoAfrrToN2" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="b41ffd7e-d4c3-47da-9c5e-ce0f36a380ac" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="048d1f94-191f-414c-8b81-73f178b72786" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9e9a9efe-52ba-4508-a8c3-139a424637d4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="49b0c7b9-e839-47f0-a770-3ac2193c1478" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0a66ad82-8442-459b-aa99-cd44337111e8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ca1fa397-434d-4e0d-9051-c064fb24c5d2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a210984a-04c2-4926-9fb8-d407f5c3a55d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="20495b0c-2b6c-4827-bc33-3e13bc9b460f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0b81590b-2b21-4519-afa4-12622fd16603" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="dfbbb419-0e5f-4330-aebd-7ae697e9b161" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="58789953-b1a4-46f3-b2bb-eb4ef9e5e8b1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="31f830b7-48ff-4756-a52e-1038b57ea0ba" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrr" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bcbde740-5378-4992-93c0-bc4569a2934f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.N2MessaggiErroriAFRRRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2MessaggiErroriAFRRRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2d8c7ca3-68da-4899-b8d3-cd3f766fa9ba" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.N2MessaggiErroriAFRRResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2MessaggiErroriAFRRResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="dd8dd130-669a-4869-a8dd-5e923540f15b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="c60d9d04-7ce1-47f4-8124-83a137382133" ParentLink="ServiceBody_Statement" LowerBound="45.1" HigherBound="52.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="RpaEsitoAfrrIn" />
                    <om:Property Name="MessageName" Value="RpaEsitoAfrr" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a6338cc4-ebc1-4047-b393-f3f47eb98c6a" ParentLink="ServiceBody_Statement" LowerBound="52.1" HigherBound="63.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoAfrr));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoAfrr);&#xD;&#xA;flowName = &quot;RpaEsitoAfrrToN2&quot;;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d0283472-a03f-428e-89e5-18bc268075fc" ParentLink="ServiceBody_Statement" LowerBound="63.1" HigherBound="75.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Flusso&quot;, flowName,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="21cbb8a5-a8f2-46a1-92ba-4a2398018b49" ParentLink="ServiceBody_Statement" LowerBound="75.1" HigherBound="146.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="5ebb63cc-0635-4837-9a73-cf8810b8500d" ParentLink="ComplexStatement_Statement" LowerBound="80.1" HigherBound="88.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct WS" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="a9048885-53df-46dd-abbf-9463ad63d646" ParentLink="ComplexStatement_Statement" LowerBound="83.1" HigherBound="85.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr.RpaEsitoAfrrToN2MessaggiErroriAFRR" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="de8c60d5-8ba9-4e50-8c83-8441908f8f88" ParentLink="Transform_OutputMessagePartRef" LowerBound="84.36" HigherBound="84.73">
                                <om:Property Name="MessageRef" Value="N2MessaggiErroriAFRRRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="818cfd5c-cbc5-481b-9724-9e371464df4f" ParentLink="Transform_InputMessagePartRef" LowerBound="84.160" HigherBound="84.182">
                                <om:Property Name="MessageRef" Value="RpaEsitoAfrr" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="1e934b87-62ae-4c07-856a-e23290e057b6" ParentLink="ComplexStatement_Statement" LowerBound="85.1" HigherBound="87.1">
                            <om:Property Name="Expression" Value="N2MessaggiErroriAFRRRequest.parameter.importF6MessaggiErroriAFRR.NomeFile = originalFileName;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign FileName" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="38c9c3d1-2f7f-45f6-96aa-4e6452cda1e1" ParentLink="Construct_MessageRef" LowerBound="81.31" HigherBound="81.58">
                            <om:Property Name="Ref" Value="N2MessaggiErroriAFRRRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="eaa48b8d-e75e-4dac-8ebe-0f45737dece2" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="b4024577-b45d-46e9-9df4-bb7d39d61a35" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="92.1">
                        <om:Property Name="PortName" Value="N2MessaggiErroriAFRROut" />
                        <om:Property Name="MessageName" Value="N2MessaggiErroriAFRRRequest" />
                        <om:Property Name="OperationName" Value="ImportazioneF6MessaggiErroriAFRR" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="5dd435a8-1050-4e25-b3c5-a12c3ba0eb93" ParentLink="ComplexStatement_Statement" LowerBound="92.1" HigherBound="94.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="N2MessaggiErroriAFRROut" />
                        <om:Property Name="MessageName" Value="N2MessaggiErroriAFRRResponse" />
                        <om:Property Name="OperationName" Value="ImportazioneF6MessaggiErroriAFRR" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="d90877bb-490a-464c-bfa3-ff2ed04d374d" ParentLink="ComplexStatement_Statement" LowerBound="94.1" HigherBound="102.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Esito WS&quot;, N2MessaggiErroriAFRRResponse.parameter.ImportazioneF6MessaggiErroriAFRRResult,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="6710b00e-44c8-4a34-9e37-36affa07dd07" ParentLink="Scope_Catch" LowerBound="105.1" HigherBound="117.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="3f442b2e-de93-4a0c-9aea-5296eced1504" ParentLink="Catch_Statement" LowerBound="108.1" HigherBound="116.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="32378cc4-ac24-40a1-a665-6fac6d184d0c" ParentLink="Scope_Catch" LowerBound="117.1" HigherBound="132.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="N2MessaggiErroriAFRROut.ImportazioneF6MessaggiErroriAFRR.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="5334b0df-b3cc-4a13-a3eb-2d913f2b80f0" ParentLink="Catch_Statement" LowerBound="120.1" HigherBound="131.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="e6def280-a887-4b98-9c50-58728b5291be" ParentLink="Scope_Catch" LowerBound="132.1" HigherBound="144.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="a2f6163a-6344-4efa-9995-a73c88dd909b" ParentLink="Catch_Statement" LowerBound="135.1" HigherBound="143.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="2337da04-c3d0-47ee-910d-fc460f8650ff" ParentLink="ServiceBody_Statement" LowerBound="146.1" HigherBound="180.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="b9d52ad9-8575-4256-bacf-97f82f62a934" ParentLink="ReallyComplexStatement_Branch" LowerBound="147.13" HigherBound="150.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="fb2c2a8e-be35-4902-bf0b-3e5ea17fcc2c" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="9c01b8a4-8b4b-4281-b922-540d7dddadaf" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="179.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="4d51b060-2bd6-493f-be19-1165212643fc" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="175.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="1b79bba4-3aeb-493f-88de-e8a79cf59e95" ParentLink="ComplexStatement_Statement" LowerBound="160.1" HigherBound="174.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameter.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="7e2166ed-e379-4cff-801e-b0f40e77da21" ParentLink="Construct_MessageRef" LowerBound="158.35" HigherBound="158.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="8a24a905-70dd-4230-8110-8da1cef8dd30" ParentLink="ComplexStatement_Statement" LowerBound="175.1" HigherBound="177.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="23e7d71d-71f0-494b-8b64-61852623d5a2" ParentLink="ServiceBody_Statement" LowerBound="180.1" HigherBound="186.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="bd00010c-d4ae-4dc3-8bdd-c0198a04bf9d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="24.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoAfrrInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoAfrrIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="86669e1c-53c9-4eac-8ea4-b9e212aba373" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ddaac6f6-c5d7-4f7d-bcc4-cb3206c1b6ca" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="24.1" HigherBound="27.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="42" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.N2MessaggiErroriAFRROutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="N2MessaggiErroriAFRROut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="40dc01b4-3df1-4ef3-8e28-88bc9c36fd33" ParentLink="PortDeclaration_CLRAttribute" LowerBound="24.1" HigherBound="25.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c40c5aea-945e-4a12-87c5-cae60816b2af" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="27.1" HigherBound="29.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="112" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="70262cc8-4359-4dc3-9d66-21831757e3ee" ParentLink="PortDeclaration_CLRAttribute" LowerBound="27.1" HigherBound="28.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype N2MessaggiErroriAFRRRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.N2MessaggiErroriAFRR.ImportazioneF6MessaggiErroriAFRR parameter;
    };
    internal messagetype N2MessaggiErroriAFRRResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoAfrr.N2MessaggiErroriAFRR.ImportazioneF6MessaggiErroriAFRRResponse parameter;
    };
    internal porttype N2MessaggiErroriAFRROutType
    {
        requestresponse ImportazioneF6MessaggiErroriAFRR
        {
            N2MessaggiErroriAFRRRequestType, N2MessaggiErroriAFRRResponseType, Fault = FaultType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service RpaEsitoAfrrToN2
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements RpaEsitoAfrrInType RpaEsitoAfrrIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses N2MessaggiErroriAFRROutType N2MessaggiErroriAFRROut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message RpaEsitoAfrrType RpaEsitoAfrr;
        message N2MessaggiErroriAFRRRequestType N2MessaggiErroriAFRRRequest;
        message N2MessaggiErroriAFRRResponseType N2MessaggiErroriAFRRResponse;
        System.DateTimeOffset dataInizioWs;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String flowName;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c60d9d04-7ce1-47f4-8124-83a137382133")]
            activate receive (RpaEsitoAfrrIn.Receive, RpaEsitoAfrr);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            flowName = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6338cc4-ebc1-4047-b393-f3f47eb98c6a")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoAfrr));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoAfrr);
            flowName = "RpaEsitoAfrrToN2";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d0283472-a03f-428e-89e5-18bc268075fc")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Flusso", flowName,
            "Inviato a N2", "NO",
            "instanceId", activityInstanceId
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("21cbb8a5-a8f2-46a1-92ba-4a2398018b49")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ebb63cc-0635-4837-9a73-cf8810b8500d")]
                    construct N2MessaggiErroriAFRRRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a9048885-53df-46dd-abbf-9463ad63d646")]
                        transform (N2MessaggiErroriAFRRRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoAfrr.RpaEsitoAfrrToN2MessaggiErroriAFRR (RpaEsitoAfrr.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e934b87-62ae-4c07-856a-e23290e057b6")]
                        N2MessaggiErroriAFRRRequest.parameter.importF6MessaggiErroriAFRR.NomeFile = originalFileName;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eaa48b8d-e75e-4dac-8ebe-0f45737dece2")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b4024577-b45d-46e9-9df4-bb7d39d61a35")]
                    send (N2MessaggiErroriAFRROut.ImportazioneF6MessaggiErroriAFRR, N2MessaggiErroriAFRRRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5dd435a8-1050-4e25-b3c5-a12c3ba0eb93")]
                    receive (N2MessaggiErroriAFRROut.ImportazioneF6MessaggiErroriAFRR, N2MessaggiErroriAFRRResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d90877bb-490a-464c-bfa3-ff2ed04d374d")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,
                    "Inviato a N2", "SI",
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Esito WS", N2MessaggiErroriAFRRResponse.parameter.ImportazioneF6MessaggiErroriAFRRResult,
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6710b00e-44c8-4a34-9e37-36affa07dd07")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3f442b2e-de93-4a0c-9aea-5296eced1504")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("32378cc4-ac24-40a1-a665-6fac6d184d0c")]
                    catch (N2MessaggiErroriAFRROut.ImportazioneF6MessaggiErroriAFRR.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5334b0df-b3cc-4a13-a3eb-2d913f2b80f0")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6def280-a887-4b98-9c50-58728b5291be")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a2f6163a-6344-4efa-9995-a73c88dd909b")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2));
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2337da04-c3d0-47ee-910d-fc460f8650ff")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("9c01b8a4-8b4b-4281-b922-540d7dddadaf")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4d51b060-2bd6-493f-be19-1165212643fc")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1b79bba4-3aeb-493f-88de-e8a79cf59e95")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaEsitoAfrrToN2;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameter.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameter.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8a24a905-70dd-4230-8110-8da1cef8dd30")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("23e7d71d-71f0-494b-8b64-61852623d5a2")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaEsitoAfrrToN2, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));
        }
    }
}

