﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="3d411fc6-22fc-4bf8-8494-02859b88a535" LowerBound="1.1" HigherBound="183.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="5d851fab-60ae-4947-962b-d6cb3f708760" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="182.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrInsert" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="eddf4899-fc05-4801-8933-125e9fcdb6bd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3603f9ea-118d-4ba5-86cd-efbe6d8035eb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cb8dd526-7e76-4575-a490-9ca6a30e69ef" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="afb1f0ce-b7d6-4511-b127-a43c3f29b2a5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ecdf7ec4-e28d-497f-bb02-667d60f274ec" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f831fc6f-1376-42f3-b8da-ebe6f6a08617" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed59ce9a-d080-4e26-a815-1fb4c8c6b6cb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d6816393-c9d6-42b3-8855-ed35d65e8a5d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d7fccd6c-39a3-4a94-a1ee-3370ae308baf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5d828506-edcb-4e61-963b-cd830c5e1987" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c83b1f4b-fe0f-4d9c-a42e-2fad71e74ba6" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrr" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d4632ae9-dfd7-4738-942e-465e07a7d445" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrrInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="093927bd-426f-4e31-9680-c9834c4efc7d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrrInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="88133717-1dfb-422c-9e71-8ec16facc613" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="183a0583-b74f-4187-a0fb-bda270255ca0" ParentLink="ServiceBody_Statement" LowerBound="55.1" HigherBound="61.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="RpaEsitoMrrIn" />
                    <om:Property Name="MessageName" Value="RpaEsitoMrr" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="13087786-3ec6-4449-b868-5565676fc874" ParentLink="ServiceBody_Statement" LowerBound="61.1" HigherBound="71.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoMrr));&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoMrr);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="993f022d-a3d6-4b89-93d3-3c16cc4188ed" ParentLink="ServiceBody_Statement" LowerBound="71.1" HigherBound="83.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Esito Verifica&quot;, RpaEsitoMrr.parameter.Detail.esitoVerifica,&#xD;&#xA;&quot;Data Messaggio&quot;, RpaEsitoMrr.parameter.Detail.dataMessaggio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="cbadef4d-0941-45cc-aa5c-2843a0159554" ParentLink="ServiceBody_Statement" LowerBound="83.1" HigherBound="138.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="46734ca1-a35e-47da-b319-bd66fccc8399" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="12fbb22d-a235-4ced-bfe0-2786a833ed60" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="99.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct Insert BI Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="0b8b8269-9083-4a1c-9fd4-1a5bf6a432a9" ParentLink="ComplexStatement_Statement" LowerBound="93.1" HigherBound="95.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoMrr.RpaEsitoMrrToRpaEsitoMrrInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="2131778b-f887-4b58-9dec-8adca091559d" ParentLink="Transform_InputMessagePartRef" LowerBound="94.152" HigherBound="94.173">
                                <om:Property Name="MessageRef" Value="RpaEsitoMrr" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="e2fba8e0-cc14-4122-ba98-6a21e6c459df" ParentLink="Transform_OutputMessagePartRef" LowerBound="94.36" HigherBound="94.70">
                                <om:Property Name="MessageRef" Value="RpaEsitoMrrInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="3a31f9ab-50fb-42eb-8d9e-e001d851d685" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="98.1">
                            <om:Property Name="Expression" Value="RpaEsitoMrrInsertRequest.parameter.fileName = @originalFileName;&#xD;&#xA;RpaEsitoMrrInsertRequest.parameter.activityId = @activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign FileName" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="64e302e8-5d08-47c3-bf36-0a7a283bf8b0" ParentLink="Construct_MessageRef" LowerBound="91.31" HigherBound="91.55">
                            <om:Property Name="Ref" Value="RpaEsitoMrrInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="ac6d504f-686e-4500-a098-1495266d110e" ParentLink="ComplexStatement_Statement" LowerBound="99.1" HigherBound="101.1">
                        <om:Property Name="PortName" Value="RpaEsitoMrrInsertOut" />
                        <om:Property Name="MessageName" Value="RpaEsitoMrrInsertRequest" />
                        <om:Property Name="OperationName" Value="RpaEsitoMrrInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="52001d1e-33e8-4132-bb8c-a5c29bb2e9de" ParentLink="ComplexStatement_Statement" LowerBound="101.1" HigherBound="103.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="RpaEsitoMrrInsertOut" />
                        <om:Property Name="MessageName" Value="RpaEsitoMrrInsertResponse" />
                        <om:Property Name="OperationName" Value="RpaEsitoMrrInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="37f27155-9352-4707-83c4-b26c70787aed" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="109.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="25f88052-d0f0-4420-92da-7d32952d8e75" ParentLink="Scope_Catch" LowerBound="112.1" HigherBound="124.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c7bfa7e2-c1f0-44f0-a9c7-4c56557e3041" ParentLink="Catch_Statement" LowerBound="115.1" HigherBound="123.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI RpaEsitoMrrInsert - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}';&lt;br&gt;&lt;br&gt;&quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="d3b5c553-db8d-441f-a489-cdc400417a8e" ParentLink="Scope_Catch" LowerBound="124.1" HigherBound="136.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="2741890c-d610-412a-b1a8-3efeab120717" ParentLink="Catch_Statement" LowerBound="127.1" HigherBound="135.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI RpaEsitoMrrInsert - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;&lt;br&gt;&lt;br&gt;File di input: '{0}'&lt;br&gt;File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="33f37f62-09d9-4146-a5ed-cdddb5bc7ba5" ParentLink="ServiceBody_Statement" LowerBound="138.1" HigherBound="172.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="666b9304-ac07-4d67-8faf-f5c6099a1ef1" ParentLink="ReallyComplexStatement_Branch" LowerBound="139.13" HigherBound="142.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="15c88f7a-e432-41cb-9633-be95c0df51c0" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="eb47e5c9-7917-4c35-93e3-107eb00c23ad" ParentLink="ComplexStatement_Statement" LowerBound="144.1" HigherBound="171.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="0e76beb9-5dca-4b63-8ac8-823901a2b937" ParentLink="ComplexStatement_Statement" LowerBound="149.1" HigherBound="167.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="9e1ab955-d4cf-4534-9b86-e0cdbec4437e" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="166.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaMrrInsert;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;//Notification.messageNotes = System.String.Format(&quot;ActivityId: '{0}'&quot;, activityInstanceId);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="30f9505b-a767-4ee5-8dd3-83f23dbb885e" ParentLink="Construct_MessageRef" LowerBound="150.35" HigherBound="150.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e4a4b81f-a463-4e10-bc0a-d1c3b6b9e660" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0eec5cf4-a67f-49b6-b5bc-489ae0acfd81" ParentLink="ServiceBody_Statement" LowerBound="172.1" HigherBound="180.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="eece058d-76a0-418b-854b-82403d23e115" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrrIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="ecdd7581-34fc-4997-bc56-be75003e3bb3" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8e777fde-f31b-4c34-98cf-a22a6da65781" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="37.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="103" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="bf2fc94d-3d12-4243-94db-b5bbf38f7d25" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7bf969b8-8907-4aa8-8aa6-8e47f79f6a2a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="37.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="44" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrrInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="80ae6790-54e8-452c-853a-5e345fe2ef06" ParentLink="PortDeclaration_CLRAttribute" LowerBound="37.1" HigherBound="38.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="e3479391-ed6e-44cd-9fd4-71b7a23c3236" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="fdeb6ac7-9843-4837-abec-3808b50d112d" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrr" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="329ba6b1-32bc-44d1-bc72-446fd5b6420a" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3c4d1ae3-a8ed-42e9-89da-a4dd33b80138" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrrInsertTypedProcedure.RpaEsitoMrrInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="fef6572e-5390-40c5-bc19-bd99d15396fc" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="834209b7-47a9-4610-adf6-e4ada11441f0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrrInsertTypedProcedure.RpaEsitoMrrInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="90fd0e70-ecdb-4cde-b536-32b1a3e6fd0e" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ee3638e9-8e6a-45cb-bdcc-31123478ad89" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="d09706d0-c1a7-43e5-a0fe-1cef71993f4b" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="61106598-2385-4f69-b494-50dce1e2594e" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="RpaEsitoMrrInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9a3a067e-0d47-4a84-9ecc-4f33e6119db7" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="RpaEsitoMrrInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="d093a300-d806-4de0-b567-ea34e16debd9" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.41">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="a53932d4-f892-40e9-ba72-d78392be4cc8" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.43" HigherBound="27.72">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.RpaEsitoMrrInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype RpaEsitoMrrType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrr parameter;
    };
    internal messagetype RpaEsitoMrrInsertRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrrInsertTypedProcedure.RpaEsitoMrrInsert parameter;
    };
    internal messagetype RpaEsitoMrrInsertResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.RpaEsitoMrr.RpaEsitoMrrInsertTypedProcedure.RpaEsitoMrrInsertResponse parameter;
    };
    internal porttype RpaEsitoMrrInType
    {
        oneway Receive
        {
            RpaEsitoMrrType
        };
    };
    internal porttype RpaEsitoMrrInsertOutType
    {
        requestresponse RpaEsitoMrrInsert
        {
            RpaEsitoMrrInsertRequestType, RpaEsitoMrrInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service RpaEsitoMrrInsert
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements RpaEsitoMrrInType RpaEsitoMrrIn;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses RpaEsitoMrrInsertOutType RpaEsitoMrrInsertOut;
        message NotificationType Notification;
        message RpaEsitoMrrType RpaEsitoMrr;
        message RpaEsitoMrrInsertRequestType RpaEsitoMrrInsertRequest;
        message RpaEsitoMrrInsertResponseType RpaEsitoMrrInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("183a0583-b74f-4187-a0fb-bda270255ca0")]
            activate receive (RpaEsitoMrrIn.Receive, RpaEsitoMrr);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("13087786-3ec6-4449-b868-5565676fc874")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = System.IO.Path.GetFileName(Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(RpaEsitoMrr));
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(RpaEsitoMrr);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("993f022d-a3d6-4b89-93d3-3c16cc4188ed")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Esito Verifica", RpaEsitoMrr.parameter.Detail.esitoVerifica,
            "Data Messaggio", RpaEsitoMrr.parameter.Detail.dataMessaggio,
            "instanceId", activityInstanceId
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cbadef4d-0941-45cc-aa5c-2843a0159554")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("46734ca1-a35e-47da-b319-bd66fccc8399")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("12fbb22d-a235-4ced-bfe0-2786a833ed60")]
                    construct RpaEsitoMrrInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0b8b8269-9083-4a1c-9fd4-1a5bf6a432a9")]
                        transform (RpaEsitoMrrInsertRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.RpaEsitoMrr.RpaEsitoMrrToRpaEsitoMrrInsert (RpaEsitoMrr.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a31f9ab-50fb-42eb-8d9e-e001d851d685")]
                        RpaEsitoMrrInsertRequest.parameter.fileName = @originalFileName;
                        RpaEsitoMrrInsertRequest.parameter.activityId = @activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ac6d504f-686e-4500-a098-1495266d110e")]
                    send (RpaEsitoMrrInsertOut.RpaEsitoMrrInsert, RpaEsitoMrrInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("52001d1e-33e8-4132-bb8c-a5c29bb2e9de")]
                    receive (RpaEsitoMrrInsertOut.RpaEsitoMrrInsert, RpaEsitoMrrInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("37f27155-9352-4707-83c4-b26c70787aed")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("25f88052-d0f0-4420-92da-7d32952d8e75")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c7bfa7e2-c1f0-44f0-a9c7-4c56557e3041")]
                        errorMessage.Append("INT_BI_CMI RpaEsitoMrrInsert - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}';<br><br>", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d3b5c553-db8d-441f-a489-cdc400417a8e")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2741890c-d610-412a-b1a8-3efeab120717")]
                        errorMessage.Append("INT_BI_CMI RpaEsitoMrrInsert - Errore in fase di inserimento dati. ");
                        errorMessage.Append(System.String.Format("<br><br>File di input: '{0}'<br>File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("33f37f62-09d9-4146-a5ed-cdddb5bc7ba5")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb47e5c9-7917-4c35-93e3-107eb00c23ad")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e76beb9-5dca-4b63-8ac8-823901a2b937")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9e1ab955-d4cf-4534-9b86-e0cdbec4437e")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionRpaMrrInsert;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            //Notification.messageNotes = System.String.Format("ActivityId: '{0}'", activityInstanceId);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e4a4b81f-a463-4e10-bc0a-d1c3b6b9e660")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0eec5cf4-a67f-49b6-b5bc-489ae0acfd81")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameRpaMrrInsert, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

