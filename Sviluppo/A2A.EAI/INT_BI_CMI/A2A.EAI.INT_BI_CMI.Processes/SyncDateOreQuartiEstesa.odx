﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="bce5d120-c899-4ace-b930-895d531a46a1" LowerBound="1.1" HigherBound="168.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BI_CMI.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="a7a3f390-b3ff-418d-95c7-ffe7052bb2b6" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="67287a4a-c585-4c0f-a1d3-9e41bc8060ac" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="0e85584c-03d1-4176-a0fe-0de9881817ed" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.52">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="1cdcb32e-5f60-4161-9bd3-e6508245616b" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="78a428a6-aa63-403f-89eb-1b180ae9dde7" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="DateOreQuartiEstesaInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="3d3fa8a4-c823-4404-b38b-7a20375ba4d0" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.58">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="6adcef96-8a67-4744-9051-3bf0f481aff9" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.60" HigherBound="27.106">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d66a9cb7-3a91-47ba-a9d8-5827d876fad0" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="15ee56ff-f9f1-42ec-a8f8-8b9387a12c03" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.BiCmiSyncDateOreQuartiEstesaTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b3fa877b-13c0-41dd-9158-f4bf742cf86d" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d95cfd83-cb2b-44dd-86d8-8e8122ed7dd8" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.DateOreQuartiEstesaInsertTypedProcedure.DateOreQuartiEstesaInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="8f803671-448a-4c26-beff-5b8066a5a004" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5da215b7-eecd-4b96-b715-800d227d237a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.DateOreQuartiEstesaInsertTypedProcedure.DateOreQuartiEstesaInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="c8d6d03e-99d6-4c2c-9095-ab2b869ad067" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="167.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="SyncDateOreQuartiEstesa" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="ebadf843-2917-41f5-8b06-2b958c4ed206" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ac921990-16ef-4f55-a0d4-7684270d6788" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ea234c98-5275-42cc-bb74-00096017025d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed20640b-bac8-4b0f-bf89-f25d88537833" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1e30a8d6-5deb-4812-978c-006735d98066" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c4250e37-71d6-4ab0-99b8-bad6b7d4304e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="fb6de928-c787-46ef-a51e-fb7611d5f6c7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f3c6a7df-9430-4109-8798-1ae866fb838d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="56aea870-43e8-4bc9-af5e-daa36e51e394" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e5f3f1ba-c73b-41c6-bdb5-ed404beef0f2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c74ffa25-d9bb-4f1d-b75d-0f355ac1d524" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="2b9a2a37-11e6-4001-9a5b-3daa48c28934" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="5a1ad2d3-ed3a-4b1e-b418-823a125a75fd" ParentLink="ServiceBody_Statement" LowerBound="53.1" HigherBound="57.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="BiCmiSyncDateOreQuartiEstesaPollingIn" />
                    <om:Property Name="MessageName" Value="BiCmiSyncDateOreQuartiEstesaPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="665eea81-8154-4ef7-adc5-28650a5aab1e" ParentLink="ServiceBody_Statement" LowerBound="57.1" HigherBound="64.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="8961c826-cb91-4ab7-aadd-d4219293440e" ParentLink="ServiceBody_Statement" LowerBound="64.1" HigherBound="73.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionSyncDateOreQuartiEstesa&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="24b89009-1ccc-40db-babc-02ce1c163024" ParentLink="ServiceBody_Statement" LowerBound="73.1" HigherBound="123.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="5ab46fbc-e924-4300-98ea-f775121e1496" ParentLink="ComplexStatement_Statement" LowerBound="78.1" HigherBound="80.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="b001725b-e121-418c-8c50-c1f0c98b6d39" ParentLink="ComplexStatement_Statement" LowerBound="80.1" HigherBound="86.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct Insert BI Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="231ea04b-ffcc-4be8-83ec-17a8886f5cc0" ParentLink="Construct_MessageRef" LowerBound="81.31" HigherBound="81.72">
                            <om:Property Name="Ref" Value="BiCmiSyncDateOreQuartiEstesaInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="6c8a3ee4-6485-48f4-bb1d-204bb0cdddb5" ParentLink="ComplexStatement_Statement" LowerBound="83.1" HigherBound="85.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_BI_CMI.Messaging.Maps.SyncDateOreQuartiEstesa.BiCmiSyncDateOreQuartiEstesaPollingToDateOreQuartiEstesaInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="a3e3ff36-0d68-4f52-8299-29fa70a65a15" ParentLink="Transform_InputMessagePartRef" LowerBound="84.213" HigherBound="84.258">
                                <om:Property Name="MessageRef" Value="BiCmiSyncDateOreQuartiEstesaPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="c167c4ba-6729-4ab3-9616-49cdc019f10c" ParentLink="Transform_OutputMessagePartRef" LowerBound="84.36" HigherBound="84.87">
                                <om:Property Name="MessageRef" Value="BiCmiSyncDateOreQuartiEstesaInsertRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="de53c3af-c499-4e49-b79b-cfa2d0c23a70" ParentLink="ComplexStatement_Statement" LowerBound="86.1" HigherBound="88.1">
                        <om:Property Name="PortName" Value="BiCmiSyncDateOreQuartiEstesaInsertOut" />
                        <om:Property Name="MessageName" Value="BiCmiSyncDateOreQuartiEstesaInsertRequest" />
                        <om:Property Name="OperationName" Value="DateOreQuartiEstesaInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="0bab1f87-4e6f-4891-8c89-574afafaa255" ParentLink="ComplexStatement_Statement" LowerBound="88.1" HigherBound="90.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="BiCmiSyncDateOreQuartiEstesaInsertOut" />
                        <om:Property Name="MessageName" Value="BiCmiSyncDateOreQuartiEstesaInsertResponse" />
                        <om:Property Name="OperationName" Value="DateOreQuartiEstesaInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="04edfb77-7a40-4175-8635-4c400cc7dba0" ParentLink="ComplexStatement_Statement" LowerBound="90.1" HigherBound="96.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="4e5a1899-003e-485e-8cda-191daa37c15e" ParentLink="Scope_Catch" LowerBound="99.1" HigherBound="110.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="265e6d3a-4642-45a7-9954-0c141cd30d65" ParentLink="Catch_Statement" LowerBound="102.1" HigherBound="109.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI SyncDateOreQuartiEstesa - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="7bf999e9-0ed5-4b30-9549-537c61fac6e8" ParentLink="Scope_Catch" LowerBound="110.1" HigherBound="121.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e2ad02a4-1ab2-4da3-9212-45447c8325fa" ParentLink="Catch_Statement" LowerBound="113.1" HigherBound="120.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;INT_BI_CMI SyncDateOreQuartiEstesa - Errore in fase di inserimento dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="1bb2d3e2-be4d-489a-97f1-ad30604c4aa1" ParentLink="ServiceBody_Statement" LowerBound="123.1" HigherBound="157.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="ed5b3020-e0a7-4183-a6ad-0d57745ae2ee" ParentLink="ReallyComplexStatement_Branch" LowerBound="124.13" HigherBound="127.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="05a04ee3-6977-4702-b04a-cd436bc5e602" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="54bc303e-b3a1-4452-b0c5-8e1f529e6c4a" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="156.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="7e0aea37-4aac-4c28-8804-bee963a15ae0" ParentLink="ComplexStatement_Statement" LowerBound="134.1" HigherBound="152.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="faa88d5a-12cc-4a60-a8ba-80e2799d8f9a" ParentLink="ComplexStatement_Statement" LowerBound="137.1" HigherBound="151.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionSyncDateOreQuartiEstesa;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;//Notification.messageNotes = System.String.Format(&quot;ActivityId: '{0}'&quot;, activityInstanceId);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="fa91805e-d0e9-4e68-9fd5-be3a674acaa1" ParentLink="Construct_MessageRef" LowerBound="135.35" HigherBound="135.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="5372d4fa-c88a-4f49-bac6-8eafcb38b66a" ParentLink="ComplexStatement_Statement" LowerBound="152.1" HigherBound="154.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="60c0b8de-1699-470b-b53f-36314aa135c0" ParentLink="ServiceBody_Statement" LowerBound="157.1" HigherBound="165.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9784389d-eb92-4b62-b919-8d99583d238c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="35.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="c171d65f-6b1d-4736-97e0-acd02f67f10c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9b6a2d66-e366-4b5f-9c54-d6f2697e534e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="35.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="33" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.BiCmiSyncDateOreQuartiEstesaInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="BiCmiSyncDateOreQuartiEstesaInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="2c3c5962-942e-4b97-ab9f-11bc56a2d5a8" ParentLink="PortDeclaration_CLRAttribute" LowerBound="35.1" HigherBound="36.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="188a895b-6fbf-4f5b-9556-8a00eeb15291" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="40.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="105" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BI_CMI.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="b9a2b167-4c05-453a-a5e5-ddbea7d6f2b0" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BI_CMI.Processes
{
    internal messagetype BiCmiSyncDateOreQuartiEstesaPollingType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.BiCmiSyncDateOreQuartiEstesaTypedPolling.TypedPolling parameter;
    };
    internal messagetype BiCmiSyncDateOreQuartiEstesaInsertRequestType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.DateOreQuartiEstesaInsertTypedProcedure.DateOreQuartiEstesaInsert parameter;
    };
    internal messagetype BiCmiSyncDateOreQuartiEstesaInsertResponseType
    {
        body A2A.EAI.INT_BI_CMI.Messaging.Schemas.SyncDateOreQuartiEstesa.DateOreQuartiEstesaInsertTypedProcedure.DateOreQuartiEstesaInsertResponse parameter;
    };
    internal porttype BiCmiSyncDateOreQuartiEstesaPollingInType
    {
        oneway Receive
        {
            BiCmiSyncDateOreQuartiEstesaPollingType
        };
    };
    internal porttype BiCmiSyncDateOreQuartiEstesaInsertOutType
    {
        requestresponse DateOreQuartiEstesaInsert
        {
            BiCmiSyncDateOreQuartiEstesaInsertRequestType, BiCmiSyncDateOreQuartiEstesaInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service SyncDateOreQuartiEstesa
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements BiCmiSyncDateOreQuartiEstesaPollingInType BiCmiSyncDateOreQuartiEstesaPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses BiCmiSyncDateOreQuartiEstesaInsertOutType BiCmiSyncDateOreQuartiEstesaInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message BiCmiSyncDateOreQuartiEstesaPollingType BiCmiSyncDateOreQuartiEstesaPolling;
        message BiCmiSyncDateOreQuartiEstesaInsertRequestType BiCmiSyncDateOreQuartiEstesaInsertRequest;
        message BiCmiSyncDateOreQuartiEstesaInsertResponseType BiCmiSyncDateOreQuartiEstesaInsertResponse;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5a1ad2d3-ed3a-4b1e-b418-823a125a75fd")]
            activate receive (BiCmiSyncDateOreQuartiEstesaPollingIn.Receive, BiCmiSyncDateOreQuartiEstesaPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("665eea81-8154-4ef7-adc5-28650a5aab1e")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8961c826-cb91-4ab7-aadd-d4219293440e")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId,
            "Flusso", A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionSyncDateOreQuartiEstesa
            );
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("24b89009-1ccc-40db-babc-02ce1c163024")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ab46fbc-e924-4300-98ea-f775121e1496")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b001725b-e121-418c-8c50-c1f0c98b6d39")]
                    construct BiCmiSyncDateOreQuartiEstesaInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6c8a3ee4-6485-48f4-bb1d-204bb0cdddb5")]
                        transform (BiCmiSyncDateOreQuartiEstesaInsertRequest.parameter) = A2A.EAI.INT_BI_CMI.Messaging.Maps.SyncDateOreQuartiEstesa.BiCmiSyncDateOreQuartiEstesaPollingToDateOreQuartiEstesaInsert (BiCmiSyncDateOreQuartiEstesaPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("de53c3af-c499-4e49-b79b-cfa2d0c23a70")]
                    send (BiCmiSyncDateOreQuartiEstesaInsertOut.DateOreQuartiEstesaInsert, BiCmiSyncDateOreQuartiEstesaInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bab1f87-4e6f-4891-8c89-574afafaa255")]
                    receive (BiCmiSyncDateOreQuartiEstesaInsertOut.DateOreQuartiEstesaInsert, BiCmiSyncDateOreQuartiEstesaInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("04edfb77-7a40-4175-8635-4c400cc7dba0")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e5a1899-003e-485e-8cda-191daa37c15e")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("265e6d3a-4642-45a7-9954-0c141cd30d65")]
                        errorMessage.Append("INT_BI_CMI SyncDateOreQuartiEstesa - Errore in fase di inserimento dati. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7bf999e9-0ed5-4b30-9549-537c61fac6e8")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e2ad02a4-1ab2-4da3-9212-45447c8325fa")]
                        errorMessage.Append("INT_BI_CMI SyncDateOreQuartiEstesa - Errore in fase di inserimento dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1bb2d3e2-be4d-489a-97f1-ad30604c4aa1")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("54bc303e-b3a1-4452-b0c5-8e1f529e6c4a")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7e0aea37-4aac-4c28-8804-bee963a15ae0")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("faa88d5a-12cc-4a60-a8ba-80e2799d8f9a")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BI_CMI.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = A2A.EAI.INT_BI_CMI.Services.ProcessServices.FlowDescriptionSyncDateOreQuartiEstesa;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            //Notification.messageNotes = System.String.Format("ActivityId: '{0}'", activityInstanceId);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5372d4fa-c88a-4f49-bac6-8eafcb38b66a")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("60c0b8de-1699-470b-b53f-36314aa135c0")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BI_CMI.Services.ProcessServices.ActivityNameBi, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

