﻿using System;

namespace A2A.EAI.INT_BI_CMI.Services
{
    [Serializable]
    public class ProcessServices
    {
        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_BI_CMI";

        public const string ActivityNameRpaMrrInsert = "RPA Esito MRR";
        public const string FlowDescriptionRpaMrrInsert = "RPA Esito MRR";

        public const string ActivityNameRpaAfrrInsert = "RPA Esito AFRR";
        public const string FlowDescriptionRpaAfrrInsert = "RPA Esito AFRR";

        public const string ActivityNameBi = "BI";
        public const string FlowDescriptionSyncImpianti = "Sincronizzazione Gerarchia Impianti";
        public const string FlowDescriptionSyncDateOreQuartiEstesa = "Sincronizzazione DateOreQuartiEstesa";

        public const string ActivityNameMercatiMrrDati = "Mercati MRR Dati";
        public const string FlowDescriptionMercatiMrrDati = "Mercati MRR Dati";

        public const string ActivityNameRpaEsitoAfrrToN2 = "Import";
        public const string FlowDescriptionRpaEsitoAfrrToN2 = "Rpa Esito AFRR";

    }

}
