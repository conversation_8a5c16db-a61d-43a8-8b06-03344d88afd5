﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="91f6b1c1-b71a-41f3-a7ac-e0fec56c9b1f" LowerBound="1.1" HigherBound="374.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_BROKER.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="7bc22cc3-5841-490f-b5f1-98889642ac4c" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouterInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3a61ce1d-dd04-40a0-af23-9ef82b3b86b5" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="f7c7d178-b0b9-412f-9a7f-63fb293ce74d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="3d6afb58-7bb6-4832-beb5-cde6153a29e5" ParentLink="Module_PortType" LowerBound="19.1" HigherBound="26.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouterGetConfigOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="47f739d1-c61a-4d79-9cb6-b5e4a21b9a29" ParentLink="PortType_OperationDeclaration" LowerBound="21.1" HigherBound="25.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterGetConfig" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8a087b70-caf8-4fef-b77c-b6e0f0e2c1b5" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="23.13" HigherBound="23.43">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BROKER.Processes.GediRouterGetConfigRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="84fdd16e-9506-49b8-8a6d-5b3f1b25f30d" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="23.45" HigherBound="23.76">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_BROKER.Processes.GediRouterGetConfigResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="377db0ee-607b-43dd-8882-d60ca895a587" ParentLink="Module_PortType" LowerBound="26.1" HigherBound="33.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouterOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="5894e5f1-4f78-4c1d-ba1a-6b061d0961a7" ParentLink="PortType_OperationDeclaration" LowerBound="28.1" HigherBound="32.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="0612770f-548f-4066-9390-6b1312ea909e" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="30.13" HigherBound="30.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="64afe4d8-2e7c-49be-b996-3ee41b20be3e" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouterGetConfigRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="19c25be5-6b6d-4b81-99af-630c3ab5675f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BROKER.Messaging.GediRouterGetConfigTypedProcedure.GediRouterGetConfig" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="437e1865-7319-4b19-b56e-213d1b4f7155" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouterGetConfigResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="869f35ee-4f42-48a3-98ef-f16ca1d524fd" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_BROKER.Messaging.GediRouterGetConfigTypedProcedure.GediRouterGetConfigResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="06849667-0d36-4514-911d-a4c4dfe80dbb" ParentLink="Module_ServiceDeclaration" LowerBound="33.1" HigherBound="373.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediRouter" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="64606c91-ef6f-4c03-9789-e4fa544afb7d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="folderOutput2" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6f2cb073-bbae-471b-835d-130cdac993c3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5c03952c-630c-4f48-b118-3edbf906047c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6be1648d-f6ae-4037-b1ee-beffea0e6860" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c97a3a6b-2440-4241-8071-af4c157aeb01" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5177fc9e-b4ea-4164-abf0-151fc1d9468d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="76d4af13-9ccf-4170-b2d6-c7abe7747e87" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6594155d-30fd-4b9b-b720-9d2d03eb50ed" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="666540a8-0e32-41af-830f-2eb15cb555d2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b2f7399a-d616-422f-97f0-0d7ecc1c1041" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="349e9a24-27e2-406d-97d5-a79bd06ab06b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="configRequestXml" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="58beb60f-8645-4c34-ac81-ef8cd5d938d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="getConfigRetries" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="405c6a46-8e4b-4fe3-abee-ebf28cb1fa6b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="folderOutput1" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="378b7a73-568f-46d2-8baf-a7c9d5686742" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1206ec13-9242-4496-8c11-cb4c71f928bc" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterInput" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6f1636ac-3755-4180-ae40-87286be05eba" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterOutput" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="11387c54-bb8e-48b7-8088-3990f66adcdd" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.GediRouterGetConfigRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterGetConfigRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e40b941c-4839-424d-a48e-0e0ac294f48e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.GediRouterGetConfigResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterGetConfigResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="e6a03920-2d92-4049-9de1-7939de313d19" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="a37020dc-92cb-464f-b350-1f5898d218c1" ParentLink="ServiceBody_Statement" LowerBound="66.1" HigherBound="76.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="GediRouterIn" />
                    <om:Property Name="MessageName" Value="GediRouterInput" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0fe1a872-050d-4c3c-9187-df61258e4f31" ParentLink="ServiceBody_Statement" LowerBound="76.1" HigherBound="92.1">
                    <om:Property Name="Expression" Value="activityId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;flowName = GediRouterInput(A2A.EAI.INT_BROKER.Messaging.GediRouterConfigName);&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(GediRouterInput);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(GediRouterInput);&#xD;&#xA;folderOutput1 = &quot;NA&quot;;&#xD;&#xA;folderOutput2 = &quot;NA&quot;;&#xD;&#xA;&#xD;&#xA;getConfigRetries = 0;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="f6d2637c-af84-40ee-a0ff-4b3ef760c4eb" ParentLink="ServiceBody_Statement" LowerBound="92.1" HigherBound="100.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, flowName,&#xD;&#xA;&quot;File Input&quot;, originalFileName&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="While" OID="35bc2272-4c1b-4801-85f0-6bd3af0bbd27" ParentLink="ServiceBody_Statement" LowerBound="100.1" HigherBound="190.1">
                    <om:Property Name="Expression" Value="getConfigRetries &lt;= 5" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Get Config" />
                    <om:Property Name="Signal" Value="False" />
                    <om:Element Type="Scope" OID="17ebec00-2fe9-46b4-b9da-77dbcae4f38f" ParentLink="ComplexStatement_Statement" LowerBound="103.1" HigherBound="159.1">
                        <om:Property Name="InitializedTransactionType" Value="True" />
                        <om:Property Name="IsSynchronized" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Main Transaction" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Catch" OID="55a05725-1be6-4a37-9b3f-dcb5f16fb8e5" ParentLink="Scope_Catch" LowerBound="128.1" HigherBound="143.1">
                            <om:Property Name="ExceptionName" Value="soapExc" />
                            <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                            <om:Property Name="IsFaultMessage" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Soap Exception" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="14a40dde-764e-48fa-814f-0a9da7cb448b" ParentLink="Catch_Statement" LowerBound="131.1" HigherBound="142.1">
                                <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. &quot;, flowName);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Catch" OID="fb0b59ec-8dae-4a47-9ca5-e897c9eceebe" ParentLink="Scope_Catch" LowerBound="143.1" HigherBound="157.1">
                            <om:Property Name="ExceptionName" Value="systemExc" />
                            <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                            <om:Property Name="IsFaultMessage" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="System Exception" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="c9c7eb61-3a97-4773-bd3f-4ec229acfb41" ParentLink="Catch_Statement" LowerBound="146.1" HigherBound="156.1">
                                <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. &quot;, flowName);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Construct" OID="f990eda7-df59-4d98-821c-7967cb974369" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="118.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup EAI" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessageAssignment" OID="7099d775-59e2-4662-bf20-0ad8ec12b94a" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="117.1">
                                <om:Property Name="Expression" Value="configRequestXml = new System.Xml.XmlDocument();&#xD;&#xA;configRequestXml.LoadXml(&quot;&lt;ns0:GediRouterGetConfig xmlns:ns0=\&quot;http://schemas.microsoft.com/Sql/2008/05/TypedProcedures/dbo\&quot;&gt;&lt;ns0:gediRouterConfigName&gt;gediRouterConfigNamegediRouterConfigNamegediRouter&lt;/ns0:gediRouterConfigName&gt;&lt;/ns0:GediRouterGetConfig&gt;&quot;);&#xD;&#xA;&#xD;&#xA;GediRouterGetConfigRequest.parameters = configRequestXml;&#xD;&#xA;GediRouterGetConfigRequest.parameters.gediRouterConfigName = flowName;" />
                                <om:Property Name="ReportToAnalyst" Value="False" />
                                <om:Property Name="Name" Value="Set parameters" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="MessageRef" OID="945126ac-2f59-4ad9-afd8-ba04ddbbcc27" ParentLink="Construct_MessageRef" LowerBound="109.35" HigherBound="109.61">
                                <om:Property Name="Ref" Value="GediRouterGetConfigRequest" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Send" OID="1d11fbfb-ba7f-48dd-aa8a-474511f391e0" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="120.1">
                            <om:Property Name="PortName" Value="GediRouterGetConfigOut" />
                            <om:Property Name="MessageName" Value="GediRouterGetConfigRequest" />
                            <om:Property Name="OperationName" Value="GediRouterGetConfig" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Receive" OID="cbf9a4de-d960-49a0-9965-d89d5116b15d" ParentLink="ComplexStatement_Statement" LowerBound="120.1" HigherBound="122.1">
                            <om:Property Name="Activate" Value="False" />
                            <om:Property Name="PortName" Value="GediRouterGetConfigOut" />
                            <om:Property Name="MessageName" Value="GediRouterGetConfigResponse" />
                            <om:Property Name="OperationName" Value="GediRouterGetConfig" />
                            <om:Property Name="OperationMessageName" Value="Response" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Receive" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="18524572-9d4a-45fd-a6c0-62045a214aa2" ParentLink="ComplexStatement_Statement" LowerBound="122.1" HigherBound="125.1">
                            <om:Property Name="Expression" Value="folderOutput1 = GediRouterGetConfigResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.folderOutput1;&#xD;&#xA;folderOutput2 = GediRouterGetConfigResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.folderOutput2;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Store Config" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="217c5f31-e31a-48ae-981b-69687305dccf" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="189.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Transaction Succeded" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="a5987dd5-f446-4301-b98b-a7d4cd89ce74" ParentLink="ReallyComplexStatement_Branch" LowerBound="160.17" HigherBound="165.1">
                            <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="b45b6386-87fb-4f72-a996-3c95365578c7" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="164.1">
                                <om:Property Name="Expression" Value="getConfigRetries = 100;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Exit" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="ea7d5924-57ce-42cc-96a3-35d0c0458653" ParentLink="ReallyComplexStatement_Branch" LowerBound="165.22" HigherBound="172.1">
                            <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; getConfigRetries &lt; 5" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Retry" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="11d6906f-bc89-4c4a-8d71-dd2d4701e252" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                                <om:Property Name="Expression" Value="getConfigRetries = getConfigRetries + 1;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Retry" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Delay" OID="96089986-4e47-4914-a772-3b52312f6aa3" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                                <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, 0, 1);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Delay" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="f728b794-b767-4b15-9013-0d186e668428" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="e5fd9bfc-be30-470e-8dbb-cb63b6e8dd5f" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="188.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0}: Get config error. &quot;, flowName));&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.BizTalk;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;getConfigRetries = 100;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Error Management" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="3fab271f-8acf-41f8-8d96-ceecc7341e49" ParentLink="ServiceBody_Statement" LowerBound="190.1" HigherBound="328.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Config Success" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="6820b808-6dd8-4a33-a93c-73012fdd7cc6" ParentLink="ReallyComplexStatement_Branch" LowerBound="191.13" HigherBound="316.1">
                        <om:Property Name="Expression" Value="folderOutput1 != &quot;NA&quot;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Yes" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="6d359cb5-beb1-430c-9329-af0433e05d7f" ParentLink="ComplexStatement_Statement" LowerBound="193.1" HigherBound="244.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Output 1" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="14815f2d-b452-47f3-8b8e-94067f9c19e0" ParentLink="ComplexStatement_Statement" LowerBound="198.1" HigherBound="212.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Outputs" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="f71feda9-ef6f-4dfc-bde4-b8f08c486dd7" ParentLink="ComplexStatement_Statement" LowerBound="201.1" HigherBound="211.1">
                                    <om:Property Name="Expression" Value="GediRouterOutput = GediRouterInput;&#xD;&#xA;GediRouterOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(folderOutput1, System.IO.Path.GetFileName(originalFileName));&#xD;&#xA;GediRouterOut(Microsoft.XLANGs.BaseTypes.TransportType) = &quot;FILE&quot;;&#xD;&#xA;&#xD;&#xA;GediRouterOutput(FILE.CopyMode) = 2; // Overwrite&#xD;&#xA;GediRouterOutput(BTS.RetryCount) = 0;&#xD;&#xA;GediRouterOutput(BTS.RetryInterval) = 0;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Set FileName" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="2b660355-4b9a-40a2-b615-ccea7b510207" ParentLink="Construct_MessageRef" LowerBound="199.35" HigherBound="199.51">
                                    <om:Property Name="Ref" Value="GediRouterOutput" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="da28dd16-121b-4f99-a82b-2d0319a36842" ParentLink="ComplexStatement_Statement" LowerBound="212.1" HigherBound="214.1">
                                <om:Property Name="PortName" Value="GediRouterOut" />
                                <om:Property Name="MessageName" Value="GediRouterOutput" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Catch" OID="c6da1ea0-62bd-42b5-b2b5-8c548fc6c9d5" ParentLink="Scope_Catch" LowerBound="217.1" HigherBound="229.1">
                                <om:Property Name="ExceptionName" Value="deliveryExc" />
                                <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                                <om:Property Name="IsFaultMessage" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Delivery Exception" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="1313827f-a37f-4f18-bafe-388d4a2fcdd5" ParentLink="Catch_Statement" LowerBound="220.1" HigherBound="228.1">
                                    <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 1 &quot;, flowName);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;errorMessage.Append(deliveryExc.ErrorDescription);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="WriteLog" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Catch" OID="7c9000b1-4ee6-44b9-933c-0777dd4bbb30" ParentLink="Scope_Catch" LowerBound="229.1" HigherBound="242.1">
                                <om:Property Name="ExceptionName" Value="systemExc" />
                                <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                <om:Property Name="IsFaultMessage" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="System Exception" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="7df71173-b0b9-474c-b4a3-6ad792316363" ParentLink="Catch_Statement" LowerBound="232.1" HigherBound="241.1">
                                    <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 1 &quot;, flowName);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="WriteLog" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Decision" OID="c434207a-569b-432b-9df6-462f17f16e74" ParentLink="ComplexStatement_Statement" LowerBound="244.1" HigherBound="252.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Process Result" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="DecisionBranch" OID="16143083-a6ea-41ac-847b-43302cc3c72e" ParentLink="ReallyComplexStatement_Branch" LowerBound="245.17" HigherBound="252.1">
                                <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="OK" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="VariableAssignment" OID="1a7bd85d-5fae-476a-a94d-70aa0378f54f" ParentLink="ComplexStatement_Statement" LowerBound="247.1" HigherBound="251.1">
                                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,&#xD;&#xA;&quot;File Output 1&quot;, System.IO.Path.Combine(folderOutput1, System.IO.Path.GetFileName(originalFileName))&#xD;&#xA;);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="BAM Update" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="5dc4fb0d-d1fd-4218-8a18-dd18c9214ffa" ParentLink="ReallyComplexStatement_Branch">
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Else" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Decision" OID="ebd75b80-c24d-415d-94b8-1fc61b7cc5ef" ParentLink="ComplexStatement_Statement" LowerBound="252.1" HigherBound="315.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Process Result" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="DecisionBranch" OID="34210767-36f3-40b0-8298-7276ace2c032" ParentLink="ReallyComplexStatement_Branch" LowerBound="253.17" HigherBound="315.1">
                                <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="OK" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Scope" OID="08709f6d-c523-4dae-bf86-74ff5e61daa1" ParentLink="ComplexStatement_Statement" LowerBound="255.1" HigherBound="306.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Output 2" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Construct" OID="d94ec0d2-01a1-4162-812e-d9101e6bfbb0" ParentLink="ComplexStatement_Statement" LowerBound="260.1" HigherBound="274.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Outputs" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessageRef" OID="f3fa8515-aee7-4513-8493-133a847456e0" ParentLink="Construct_MessageRef" LowerBound="261.39" HigherBound="261.55">
                                            <om:Property Name="Ref" Value="GediRouterOutput" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessageAssignment" OID="75b4e51d-3c49-46e6-bf51-5ef5acb3005f" ParentLink="ComplexStatement_Statement" LowerBound="263.1" HigherBound="273.1">
                                            <om:Property Name="Expression" Value="GediRouterOutput = GediRouterInput;&#xD;&#xA;GediRouterOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(folderOutput2, System.IO.Path.GetFileName(originalFileName));&#xD;&#xA;GediRouterOut(Microsoft.XLANGs.BaseTypes.TransportType) = &quot;FILE&quot;;&#xD;&#xA;GediRouterOutput(FILE.CopyMode) = 2; // Overwrite&#xD;&#xA;GediRouterOutput(BTS.RetryCount) = 0;&#xD;&#xA;GediRouterOutput(BTS.RetryInterval) = 0;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="False" />
                                            <om:Property Name="Name" Value="Set FileName" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="51653ad7-942d-4b50-9b6c-cd0eeb47c92e" ParentLink="ComplexStatement_Statement" LowerBound="274.1" HigherBound="276.1">
                                        <om:Property Name="PortName" Value="GediRouterOut" />
                                        <om:Property Name="MessageName" Value="GediRouterOutput" />
                                        <om:Property Name="OperationName" Value="Send" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="5ed43e13-9384-4ca7-b915-e34dcb6e66e4" ParentLink="Scope_Catch" LowerBound="279.1" HigherBound="291.1">
                                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Delivery Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="8ba1298a-9162-4ea9-b417-bfe3e7ee5f17" ParentLink="Catch_Statement" LowerBound="282.1" HigherBound="290.1">
                                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 2 &quot;, flowName);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;errorMessage.Append(deliveryExc.ErrorDescription);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="2f16d636-1a5c-4fed-916e-d1ad05de9e46" ParentLink="Scope_Catch" LowerBound="291.1" HigherBound="304.1">
                                        <om:Property Name="ExceptionName" Value="systemExc" />
                                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="System Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="63356d3f-30f3-4773-bb38-0dd7e9418424" ParentLink="Catch_Statement" LowerBound="294.1" HigherBound="303.1">
                                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 2 &quot;, flowName);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="e0b1456a-21ad-4876-a36e-02d56d2eec73" ParentLink="ComplexStatement_Statement" LowerBound="306.1" HigherBound="314.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Process Result" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="6575b39d-ca16-43a6-811c-86d31875f69b" ParentLink="ReallyComplexStatement_Branch" LowerBound="307.21" HigherBound="314.1">
                                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="OK" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="3207fe4b-0184-414e-a180-7641c87f077b" ParentLink="ComplexStatement_Statement" LowerBound="309.1" HigherBound="313.1">
                                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,&#xD;&#xA;&quot;File Output 2&quot;, System.IO.Path.Combine(folderOutput2, System.IO.Path.GetFileName(originalFileName))&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="07e2e5ca-9629-4cde-8b98-377c2b5695ad" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="DecisionBranch" OID="fdef263d-bbca-4485-8dee-d77454da60b6" ParentLink="ReallyComplexStatement_Branch">
                                <om:Property Name="IsGhostBranch" Value="True" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Else" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="288b84f8-db02-4ea0-9f95-dd1d6cea1282" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="VariableAssignment" OID="085c78d2-18cd-4b95-8822-f89944da161d" ParentLink="ComplexStatement_Statement" LowerBound="318.1" HigherBound="327.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. &quot;, flowName);&#xD;&#xA;errorMessage.Append(&quot;Configurazione non trovata.&quot;);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="1e76ffc2-7b3c-4f8e-a99e-8e84a67b53cc" ParentLink="ServiceBody_Statement" LowerBound="328.1" HigherBound="364.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="bf40cacb-e349-4fe5-9288-9f6ed0f86bf3" ParentLink="ReallyComplexStatement_Branch" LowerBound="329.13" HigherBound="332.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="35c6182a-2165-4d3b-b23b-6b5f4eccdf27" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="b3df50ba-51d6-4d11-862c-0e539c620b1d" ParentLink="ComplexStatement_Statement" LowerBound="334.1" HigherBound="361.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="3185fa86-e1ef-4ea5-b4f1-e7dbf3dda277" ParentLink="ComplexStatement_Statement" LowerBound="339.1" HigherBound="357.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="a6ce59bb-dc5b-41ce-a018-ce9c33bef096" ParentLink="Construct_MessageRef" LowerBound="340.35" HigherBound="340.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="f8430ea6-98d3-417f-9f0a-faf93aeaedd6" ParentLink="ComplexStatement_Statement" LowerBound="342.1" HigherBound="356.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_BROKER.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_BROKER.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, &quot; - &quot;, flowName);&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageNotes = System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath);&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="793c1739-b9e8-4d19-8f5c-94c61626a714" ParentLink="ComplexStatement_Statement" LowerBound="357.1" HigherBound="359.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="VariableAssignment" OID="283cda2a-a28a-4e55-84fb-902e831ac02d" ParentLink="ComplexStatement_Statement" LowerBound="361.1" HigherBound="363.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Assign errorMessage" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="7dce6b0d-c256-4e61-816e-219c693ef4e5" ParentLink="ServiceBody_Statement" LowerBound="364.1" HigherBound="371.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3492f82d-238b-4f82-b6f4-b6b47271d396" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="36.1" HigherBound="38.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.GediRouterInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="eeda2f12-c0e8-43c7-a5a1-5f6e6b8423b1" ParentLink="PortDeclaration_CLRAttribute" LowerBound="36.1" HigherBound="37.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="221dd510-be66-4293-b6a0-c2cea0f53e2e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="38.1" HigherBound="41.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="31" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.GediRouterGetConfigOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterGetConfigOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="b936e80b-0f9a-458b-a480-2efb527e3fc3" ParentLink="PortDeclaration_CLRAttribute" LowerBound="38.1" HigherBound="39.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="73a931e9-5b42-46d5-a58c-7416c6e9cda5" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="41.1" HigherBound="44.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="161" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.GediRouterOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediRouterOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="PhysicalBindingAttribute" OID="b0794a4f-b6f3-449b-a9b6-37255effe555" ParentLink="PortDeclaration_CLRAttribute" LowerBound="41.1" HigherBound="42.1">
                    <om:Property Name="InPipeline" Value="Microsoft.BizTalk.DefaultPipelines.XMLReceive" />
                    <om:Property Name="OutPipeline" Value="Microsoft.BizTalk.DefaultPipelines.PassThruTransmit" />
                    <om:Property Name="TransportType" Value="HTTP" />
                    <om:Property Name="URI" Value="http://tempURI" />
                    <om:Property Name="IsDynamic" Value="True" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="12e3f976-17f9-4867-96fc-fcbf729533ac" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="44.1" HigherBound="46.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="267" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_BROKER.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="bc8d646e-5c3a-434f-af9c-f3faacfbcfa6" ParentLink="PortDeclaration_CLRAttribute" LowerBound="44.1" HigherBound="45.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_BROKER.Processes
{
    internal messagetype GediRouterGetConfigRequestType
    {
        body A2A.EAI.INT_BROKER.Messaging.GediRouterGetConfigTypedProcedure.GediRouterGetConfig parameters;
    };
    internal messagetype GediRouterGetConfigResponseType
    {
        body A2A.EAI.INT_BROKER.Messaging.GediRouterGetConfigTypedProcedure.GediRouterGetConfigResponse parameters;
    };
    internal porttype GediRouterInType
    {
        oneway Receive
        {
            System.Xml.XmlDocument
        };
    };
    internal porttype GediRouterGetConfigOutType
    {
        requestresponse GediRouterGetConfig
        {
            GediRouterGetConfigRequestType, GediRouterGetConfigResponseType
        };
    };
    internal porttype GediRouterOutType
    {
        oneway Send
        {
            System.Xml.XmlDocument
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service GediRouter
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements GediRouterInType GediRouterIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses GediRouterGetConfigOutType GediRouterGetConfigOut;
        [Microsoft.XLANGs.BaseTypes.PhysicalBinding(typeof(Microsoft.BizTalk.DefaultPipelines.PassThruTransmit))]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses dynamic GediRouterOutType GediRouterOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message System.Xml.XmlDocument GediRouterInput;
        message System.Xml.XmlDocument GediRouterOutput;
        message GediRouterGetConfigRequestType GediRouterGetConfigRequest;
        message GediRouterGetConfigResponseType GediRouterGetConfigResponse;
        System.String folderOutput2;
        System.String archiveFilePath;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        System.String flowName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityId;
        System.Xml.XmlDocument configRequestXml;
        System.Int32 getConfigRetries;
        System.String folderOutput1;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a37020dc-92cb-464f-b350-1f5898d218c1")]
            activate receive (GediRouterIn.Receive, GediRouterInput);
            folderOutput2 = "";
            archiveFilePath = "";
            originalFileName = "";
            flowName = "";
            errorMessage = new System.Text.StringBuilder();
            activityId = "";
            configRequestXml = new System.Xml.XmlDocument();
            folderOutput1 = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0fe1a872-050d-4c3c-9187-df61258e4f31")]
            activityId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            flowName = GediRouterInput(A2A.EAI.INT_BROKER.Messaging.GediRouterConfigName);
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(GediRouterInput);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(GediRouterInput);
            folderOutput1 = "NA";
            folderOutput2 = "NA";
            
            getConfigRetries = 0;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f6d2637c-af84-40ee-a0ff-4b3ef760c4eb")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", flowName,
            "File Input", originalFileName
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("35bc2272-4c1b-4801-85f0-6bd3af0bbd27")]
            while (getConfigRetries <= 5)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("17ebec00-2fe9-46b4-b9da-77dbcae4f38f")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f990eda7-df59-4d98-821c-7967cb974369")]
                        construct GediRouterGetConfigRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7099d775-59e2-4662-bf20-0ad8ec12b94a")]
                            configRequestXml = new System.Xml.XmlDocument();
                            configRequestXml.LoadXml("<ns0:GediRouterGetConfig xmlns:ns0=\"http://schemas.microsoft.com/Sql/2008/05/TypedProcedures/dbo\"><ns0:gediRouterConfigName>gediRouterConfigNamegediRouterConfigNamegediRouter</ns0:gediRouterConfigName></ns0:GediRouterGetConfig>");
                            
                            GediRouterGetConfigRequest.parameters = configRequestXml;
                            GediRouterGetConfigRequest.parameters.gediRouterConfigName = flowName;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1d11fbfb-ba7f-48dd-aa8a-474511f391e0")]
                        send (GediRouterGetConfigOut.GediRouterGetConfig, GediRouterGetConfigRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cbf9a4de-d960-49a0-9965-d89d5116b15d")]
                        receive (GediRouterGetConfigOut.GediRouterGetConfig, GediRouterGetConfigResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("18524572-9d4a-45fd-a6c0-62045a214aa2")]
                        folderOutput1 = GediRouterGetConfigResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.folderOutput1;
                        folderOutput2 = GediRouterGetConfigResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.folderOutput2;
                    }
                    exceptions
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("55a05725-1be6-4a37-9b3f-dcb5f16fb8e5")]
                        catch (System.Web.Services.Protocols.SoapException soapExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("14a40dde-764e-48fa-814f-0a9da7cb448b")]
                            errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. ", flowName);
                            errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                            
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                            
                            
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fb0b59ec-8dae-4a47-9ca5-e897c9eceebe")]
                        catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c9c7eb61-3a97-4773-bd3f-4ec229acfb41")]
                            errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. ", flowName);
                            errorMessage.Append(systemExc.Message);
                            
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                            
                            
                        }
                    }
                }
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("217c5f31-e31a-48ae-981b-69687305dccf")]
                if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b45b6386-87fb-4f72-a996-3c95365578c7")]
                    getConfigRetries = 100;
                }
                else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && getConfigRetries < 5)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("11d6906f-bc89-4c4a-8d71-dd2d4701e252")]
                    getConfigRetries = getConfigRetries + 1;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("96089986-4e47-4914-a772-3b52312f6aa3")]
                    delay new System.TimeSpan(0, 0, 0, 1);
                }
                else 
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e5fd9bfc-be30-470e-8dbb-cb63b6e8dd5f")]
                    errorMessage.Append(System.String.Format("{0}: Get config error. ", flowName));
                    
                    
                    resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                    connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.BizTalk;
                    faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
                    
                    getConfigRetries = 100;
                    
                    
                    
                    
                    
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3fab271f-8acf-41f8-8d96-ceecc7341e49")]
            if (folderOutput1 != "NA")
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d359cb5-beb1-430c-9329-af0433e05d7f")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("14815f2d-b452-47f3-8b8e-94067f9c19e0")]
                        construct GediRouterOutput
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f71feda9-ef6f-4dfc-bde4-b8f08c486dd7")]
                            GediRouterOutput = GediRouterInput;
                            GediRouterOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(folderOutput1, System.IO.Path.GetFileName(originalFileName));
                            GediRouterOut(Microsoft.XLANGs.BaseTypes.TransportType) = "FILE";
                            
                            GediRouterOutput(FILE.CopyMode) = 2; // Overwrite
                            GediRouterOutput(BTS.RetryCount) = 0;
                            GediRouterOutput(BTS.RetryInterval) = 0;
                            
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("da28dd16-121b-4f99-a82b-2d0319a36842")]
                        send (GediRouterOut.Send, GediRouterOutput);
                    }
                    exceptions
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c6da1ea0-62bd-42b5-b2b5-8c548fc6c9d5")]
                        catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1313827f-a37f-4f18-bafe-388d4a2fcdd5")]
                            errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 1 ", flowName);
                            errorMessage.Append(deliveryExc.Message);
                            errorMessage.Append(deliveryExc.ErrorDescription);
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7c9000b1-4ee6-44b9-933c-0777dd4bbb30")]
                        catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7df71173-b0b9-474c-b4a3-6ad792316363")]
                            errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 1 ", flowName);
                            errorMessage.Append(systemExc.Message);
                            
                            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;
                            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                            
                            
                        }
                    }
                }
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c434207a-569b-432b-9df6-462f17f16e74")]
                if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1a7bd85d-5fae-476a-a94d-70aa0378f54f")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,
                    "File Output 1", System.IO.Path.Combine(folderOutput1, System.IO.Path.GetFileName(originalFileName))
                    );
                }
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebd75b80-c24d-415d-94b8-1fc61b7cc5ef")]
                if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("08709f6d-c523-4dae-bf86-74ff5e61daa1")]
                    scope
                    {
                        body
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d94ec0d2-01a1-4162-812e-d9101e6bfbb0")]
                            construct GediRouterOutput
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("75b4e51d-3c49-46e6-bf51-5ef5acb3005f")]
                                GediRouterOutput = GediRouterInput;
                                GediRouterOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(folderOutput2, System.IO.Path.GetFileName(originalFileName));
                                GediRouterOut(Microsoft.XLANGs.BaseTypes.TransportType) = "FILE";
                                GediRouterOutput(FILE.CopyMode) = 2; // Overwrite
                                GediRouterOutput(BTS.RetryCount) = 0;
                                GediRouterOutput(BTS.RetryInterval) = 0;
                                
                                
                                
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("51653ad7-942d-4b50-9b6c-cd0eeb47c92e")]
                            send (GediRouterOut.Send, GediRouterOutput);
                        }
                        exceptions
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ed43e13-9384-4ca7-b915-e34dcb6e66e4")]
                            catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("8ba1298a-9162-4ea9-b417-bfe3e7ee5f17")]
                                errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 2 ", flowName);
                                errorMessage.Append(deliveryExc.Message);
                                errorMessage.Append(deliveryExc.ErrorDescription);
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2f16d636-1a5c-4fed-916e-d1ad05de9e46")]
                            catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("63356d3f-30f3-4773-bb38-0dd7e9418424")]
                                errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di elaborazione dati. Output 2 ", flowName);
                                errorMessage.Append(systemExc.Message);
                                
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Oracle;
                                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                
                                
                            }
                        }
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0b1456a-21ad-4876-a36e-02d56d2eec73")]
                    if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3207fe4b-0184-414e-a180-7641c87f077b")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,
                        "File Output 2", System.IO.Path.Combine(folderOutput2, System.IO.Path.GetFileName(originalFileName))
                        );
                    }
                }
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("085c78d2-18cd-4b95-8822-f89944da161d")]
                errorMessage.AppendFormat("INT_BROKER - '{0}' - Errore in fase di ricerca configurazione. ", flowName);
                errorMessage.Append("Configurazione non trovata.");
                
                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                
                
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e76ffc2-7b3c-4f8e-a99e-8e84a67b53cc")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("b3df50ba-51d6-4d11-862c-0e539c620b1d")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3185fa86-e1ef-4ea5-b4f1-e7dbf3dda277")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f8430ea6-98d3-417f-9f0a-faf93aeaedd6")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_BROKER.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_BROKER.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, " - ", flowName);
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageNotes = System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath);
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("793c1739-b9e8-4d19-8f5c-94c61626a714")]
                        send (NotificationOut.Send, Notification);
                    }
                }
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("283cda2a-a28a-4e55-84fb-902e831ac02d")]
                errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7dce6b0d-c256-4e61-816e-219c693ef4e5")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_BROKER.Services.ProcessServices.ActivityNameGediRouter, activityId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

