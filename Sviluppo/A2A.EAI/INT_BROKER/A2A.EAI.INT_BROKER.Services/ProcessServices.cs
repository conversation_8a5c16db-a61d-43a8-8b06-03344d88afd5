﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsys.EAI.Framework.Services;

namespace A2A.EAI.INT_BROKER.Services
{
    [Serializable]
    public class ProcessServices
    {

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_BROKER";

        /// <summary>Scandale Broker</summary>
        public const string FlowDescriptionScandale = "Scandale Broker";

        public const string ActivityNameGediRouter = "Gedi Router";
        public const string FlowDescriptionGediRouter = "Gedi Router";

    }
}
