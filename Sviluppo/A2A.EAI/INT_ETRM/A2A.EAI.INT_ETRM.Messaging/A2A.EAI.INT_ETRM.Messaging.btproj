﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{4DD6E159-FB7E-4C16-8914-701A523E26AE}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_ETRM.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_ETRM.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Schema Include="Schemas\EtrmSettAppContractInsertTypedProcedure.Type.xsd">
      <TypeName>EtrmSettAppContractInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppContractInsertTypedProcedure.xsd">
      <TypeName>EtrmSettAppContractInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Content Include="Bindings\EtrmSettAppContractInsert.bindinginfo.xml" />
    <Content Include="Bindings\EtrmSettAppContractPriceInsert.bindinginfo.xml" />
    <Content Include="Bindings\EtrmSettAppPowerThresholdInsert.bindinginfo.xml" />
    <Content Include="Bindings\EtrmSettAppFileUpdateStatus.bindinginfo.xml" />
    <Map Include="Maps\EtrmSettAppFileXmlPollingToSettAppExcelToErtmSettAppFileUpdateStatus.btm">
      <TypeName>EtrmSettAppFileXmlPollingToSettAppExcelToErtmSettAppFileUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\EtrmSettAppFileXmlPollingToSettAppExcel.btm">
      <TypeName>EtrmSettAppFileXmlPollingToSettAppExcel</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\Out\EtrmSettAppExcelFile.btp">
      <TypeName>EtrmSettAppExcelFile</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Pipelines.Out</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipelines\In\EtrmSettAppFile.btp">
      <TypeName>EtrmSettAppFile</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\EtrmSettAppFileToEtrmSettAppPowerThresholdInsert.btm">
      <TypeName>EtrmSettAppFileToEtrmSettAppPowerThresholdInsert</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\EtrmSettAppFileToEtrmSettAppContractPriceInsert.btm">
      <TypeName>EtrmSettAppFileToEtrmSettAppContractPriceInsert</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\EtrmSettAppFileToEtrmSettAppContractInsert.btm">
      <TypeName>EtrmSettAppFileToEtrmSettAppContractInsert</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Maps</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\EtrmSettAppFileUpdateStatusTypedProcedure.xsd">
      <TypeName>EtrmSettAppFileUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppFileXmlPolling.xsd">
      <TypeName>EtrmSettAppFileXmlPolling</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppFileXmlPolling.Types.xsd">
      <TypeName>EtrmSettAppFileXmlPolling_Types</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\SettAppExcel.xsd">
      <TypeName>SettAppExcel</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppFile.xsd">
      <TypeName>EtrmSettAppFile</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppPowerThresholdInsertTypedProcedure.Type.xsd">
      <TypeName>EtrmSettAppPowerThresholdInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppPowerThresholdInsertTypedProcedure.xsd">
      <TypeName>EtrmSettAppPowerThresholdInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppContractPriceInsertTypedProcedure.Type.xsd">
      <TypeName>EtrmSettAppContractPriceInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\EtrmSettAppContractPriceInsertTypedProcedure.xsd">
      <TypeName>EtrmSettAppContractPriceInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_ETRM.Messaging.Schemas</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>