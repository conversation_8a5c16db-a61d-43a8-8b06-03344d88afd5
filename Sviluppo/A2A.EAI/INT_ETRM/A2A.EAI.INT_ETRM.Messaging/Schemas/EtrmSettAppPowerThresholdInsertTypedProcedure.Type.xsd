<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="EtrmSettAppPowerThresholdType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Action" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="200" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="SupplyContractId" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="200" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="ThresholdId" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="200" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="ThresholdFrom" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="200" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="ThresholdTo" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="200" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
		<xs:element minOccurs="0" maxOccurs="1" name="ThresholdMwhFrom" nillable="true">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:maxLength value="200" />
				</xs:restriction>
			</xs:simpleType>
		</xs:element>
		<xs:element minOccurs="0" maxOccurs="1" name="ThresholdMwhTo" nillable="true">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:maxLength value="200" />
				</xs:restriction>
			</xs:simpleType>
		</xs:element>
	</xs:sequence>
  </xs:complexType>
  <xs:element name="EtrmSettAppPowerThresholdType" nillable="true" type="ns3:EtrmSettAppPowerThresholdType" />
  <xs:complexType name="ArrayOfEtrmSettAppPowerThresholdType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EtrmSettAppPowerThresholdType" type="ns3:EtrmSettAppPowerThresholdType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfEtrmSettAppPowerThresholdType" nillable="true" type="ns3:ArrayOfEtrmSettAppPowerThresholdType" />
</xs:schema>