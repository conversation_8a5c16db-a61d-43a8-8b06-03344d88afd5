﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="5b3f5ebb-3cc4-4fd3-95f9-cfc42d4a12d1" LowerBound="1.1" HigherBound="259.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ETRM.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="df8db3f9-0240-4a4b-9fc2-4f5969709fa0" ParentLink="Module_PortType" LowerBound="20.1" HigherBound="27.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileXmlPollingInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="cee2e99b-9681-4797-a71e-b3b75dd2fda2" ParentLink="PortType_OperationDeclaration" LowerBound="22.1" HigherBound="26.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9c5d4a70-ff46-4071-8c78-8d4a1bba4a7d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="24.13" HigherBound="24.42">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileXmlPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="0d6714ea-4e4d-4030-80d7-fcc14ecee323" ParentLink="Module_PortType" LowerBound="27.1" HigherBound="34.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppExcelOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9ff61087-6ce7-45d0-a110-6270d8fc6c92" ParentLink="PortType_OperationDeclaration" LowerBound="29.1" HigherBound="33.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b98dc43d-aac0-400d-991d-e65d7ae54c71" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="31.13" HigherBound="31.37">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppExcelFileType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="d522eefc-c97d-4125-8b05-9d3275e2462d" ParentLink="Module_PortType" LowerBound="34.1" HigherBound="41.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="b5b41dfa-97d4-4810-8ed5-32df57037ee1" ParentLink="PortType_OperationDeclaration" LowerBound="36.1" HigherBound="40.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="189414e9-fc7e-4bc5-a7f4-c44fd1d068ea" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="38.13" HigherBound="38.51">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="56638732-1a7a-4b9d-97ff-380f8383f2bb" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="38.53" HigherBound="38.92">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="96ee421b-308d-44bd-9095-ad0b3cc281ff" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileXmlPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="659c12c2-c88b-430d-bbb0-7fe2bb99f2e7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileXmlPolling_Types" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="6ec787c0-4920-401f-bb9d-2c70bcb58363" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="bff36024-5772-4653-9ecc-980c70fe59f3" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileUpdateStatusTypedProcedure.EtrmSettAppFileUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a9076e43-b091-4b59-9ab6-d9471b4e6fc6" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4335661e-7455-4d13-9a6d-57f80a70d2f3" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileUpdateStatusTypedProcedure.EtrmSettAppFileUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="1ca3b60f-3a74-4f76-8b48-a6200acf281e" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppExcelFileType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4b425dbd-bae4-4c46-a6b5-ee0408e1d73a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.SettAppExcel.excelfile" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="a955813b-5727-4610-9109-49b3186e4220" ParentLink="Module_ServiceDeclaration" LowerBound="41.1" HigherBound="258.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="3bd2e60c-9e7b-4b99-a844-d9c01a5289d3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b0b83d09-a68b-4022-adea-25d77b2bbf78" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9939b511-3c9c-4390-a274-155fea86f104" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="96a66466-0fdb-4c7e-9e4f-5bb8fc64e664" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="10781353-9f75-4a4d-a88a-fb9ac9d2809a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2bc6d4aa-170d-4fe1-9a61-56012e03edb6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ea63f275-db9f-4d6e-999e-c686fbc97a3f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3da08a53-902d-47ad-b4bc-1ac662353225" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a023ea4e-1c0a-4625-afd8-e7a6893ad38b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e07ca0c5-3cd5-413b-9d1e-9fc91fdbcf39" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileXmlPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileXmlPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f10bbca5-b429-4e90-9346-8463504b90c8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d674acf3-63fd-42ee-931e-a1b8ab2113a6" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="65d726f1-b2ca-48b9-a298-bbd6cf680540" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppExcelFileType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppExcelFile" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="2feee292-bde5-4ab0-a78d-a8dc1e4b20ee" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="46064614-8743-4045-ab1c-0a1771d9d042" ParentLink="ServiceBody_Statement" LowerBound="69.1" HigherBound="74.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EtrmSettAppFileXmlPollingIn" />
                    <om:Property Name="MessageName" Value="EtrmSettAppFileXmlPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="b1d01e63-6bc0-453d-848a-7afd8c5453c4" ParentLink="ServiceBody_Statement" LowerBound="74.1" HigherBound="85.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowDescriptionEtrmSettAppProcess;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d5b5c09e-868a-4199-8366-9b341628c8ce" ParentLink="ServiceBody_Statement" LowerBound="85.1" HigherBound="94.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;A2A.EAI.INT_ETRM.Services.ProcessServices.LogRelatedActivities(activityInstanceId, EtrmSettAppFileXmlPolling.parameters.activityIdStaging);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="e0fc757a-b765-43eb-b564-4e1752ff7156" ParentLink="ServiceBody_Statement" LowerBound="94.1" HigherBound="154.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="a1f36a50-ae3d-49f0-9700-57a3c95af804" ParentLink="ComplexStatement_Statement" LowerBound="99.1" HigherBound="108.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Excel" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="0ed98828-e12b-4bb1-8f5c-00efb7ce1888" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileXmlPollingToSettAppExcel" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Excel" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="3c3a86c1-fc2b-4a5e-a07f-238414a52c82" ParentLink="Transform_InputMessagePartRef" LowerBound="103.144" HigherBound="103.180">
                                <om:Property Name="MessageRef" Value="EtrmSettAppFileXmlPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="fcfadf15-6667-4f86-9e12-6bfc53ffb992" ParentLink="Transform_OutputMessagePartRef" LowerBound="103.36" HigherBound="103.67">
                                <om:Property Name="MessageRef" Value="EtrmSettAppExcelFile" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="0ba4f9d0-e955-4617-a85e-0540de6ee15a" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="107.1">
                            <om:Property Name="Expression" Value="EtrmSettAppExcelFile(FILE.ReceivedFileName) = System.String.Format(&quot;{0}.xlsx&quot;, EtrmSettAppFileXmlPolling.parameters.fileNameOut);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="MessageAssignment_1" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="95fbecd3-be17-4288-9e60-f1b357853f3a" ParentLink="Construct_MessageRef" LowerBound="100.31" HigherBound="100.51">
                            <om:Property Name="Ref" Value="EtrmSettAppExcelFile" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="94946b76-1f30-4fb8-87d1-a74d3cfd2043" ParentLink="ComplexStatement_Statement" LowerBound="108.1" HigherBound="110.1">
                        <om:Property Name="PortName" Value="EtrmSettAppExcelOut" />
                        <om:Property Name="MessageName" Value="EtrmSettAppExcelFile" />
                        <om:Property Name="OperationName" Value="Send" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="a388d59c-2826-44cb-8d7f-2099077f27c3" ParentLink="Scope_Catch" LowerBound="113.1" HigherBound="127.1">
                        <om:Property Name="ExceptionName" Value="validationExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Message Type Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e0827bc2-97d1-4fad-baee-878b1b1b5eac" ParentLink="Catch_Statement" LowerBound="116.1" HigherBound="126.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(validationExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="bc8471be-571d-42bb-9f68-9ce68d566101" ParentLink="Scope_Catch" LowerBound="127.1" HigherBound="140.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="ee30dddf-968a-43aa-965b-6aeb24c49ac6" ParentLink="Catch_Statement" LowerBound="130.1" HigherBound="139.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="6c93478b-6558-441c-bad0-02c6dee8f530" ParentLink="Scope_Catch" LowerBound="140.1" HigherBound="152.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="99e73263-22b1-45b1-882d-cb9be1b38050" ParentLink="Catch_Statement" LowerBound="143.1" HigherBound="151.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="fb859ae5-fb61-4998-8d41-dc49f3c03ca3" ParentLink="ServiceBody_Statement" LowerBound="154.1" HigherBound="210.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="ae655146-a6bd-4062-8888-06a147756864" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="169.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="69b8fe47-5474-4bb5-9ac7-47ec8b7de967" ParentLink="Construct_MessageRef" LowerBound="160.31" HigherBound="160.65">
                            <om:Property Name="Ref" Value="EtrmSettAppFileUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="3721dd49-3e1b-4b6f-8c60-acebd2d4582a" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="164.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileXmlPollingToSettAppExcelToErtmSettAppFileUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="a73443ed-3fac-4846-8107-b918ffc00af0" ParentLink="Transform_InputMessagePartRef" LowerBound="163.187" HigherBound="163.223">
                                <om:Property Name="MessageRef" Value="EtrmSettAppFileXmlPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="2313f150-4ca4-4820-ae4c-b740fc7ea7c0" ParentLink="Transform_OutputMessagePartRef" LowerBound="163.36" HigherBound="163.81">
                                <om:Property Name="MessageRef" Value="EtrmSettAppFileUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="bd297d13-31a0-4729-ab01-ea8783675828" ParentLink="ComplexStatement_Statement" LowerBound="164.1" HigherBound="168.1">
                            <om:Property Name="Expression" Value="EtrmSettAppFileUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;EtrmSettAppFileUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;EtrmSettAppFileUpdateStatusRequest.parameters.activityId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="a96fd1ff-ebfd-4032-a91e-24d8fe5958b0" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                        <om:Property Name="PortName" Value="EtrmSettAppFileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EtrmSettAppFileUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="EtrmSettAppFileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="31cb55e8-96f5-421b-b84f-b43016bfce67" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="173.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EtrmSettAppFileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="EtrmSettAppFileUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="EtrmSettAppFileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="6c048e5f-8acf-451b-a7b6-79eeaed8a66d" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="177.1">
                        <om:Property Name="Expression" Value="sendNotification = EtrmSettAppFileUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="f68f46f1-41b2-421b-bf62-33f9a6283176" ParentLink="Scope_Catch" LowerBound="180.1" HigherBound="194.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="e631f6d6-f594-468f-a622-6015e111d323" ParentLink="Catch_Statement" LowerBound="183.1" HigherBound="193.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="e74c2ea7-15da-4d04-ab4a-e3f57e7356ef" ParentLink="Scope_Catch" LowerBound="194.1" HigherBound="208.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="a6e88ef4-e0ce-4da7-9cfc-441c2359c243" ParentLink="Catch_Statement" LowerBound="197.1" HigherBound="207.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="56e62ed5-978a-4954-ae31-a0308c6d09d0" ParentLink="ServiceBody_Statement" LowerBound="210.1" HigherBound="247.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="df4eb1a7-5011-4ad5-ba3e-af71213a6a7d" ParentLink="ReallyComplexStatement_Branch" LowerBound="211.13" HigherBound="214.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="ac62eba3-90cc-4533-ba55-609378b13159" ParentLink="ReallyComplexStatement_Branch" LowerBound="214.18" HigherBound="217.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="d8395740-0c7d-4212-80ef-a08007099aed" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="5869a638-0295-488b-9ea9-280043dd428e" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="246.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="df69cd47-5ecd-4df6-a7fd-15ffffa37817" ParentLink="ComplexStatement_Statement" LowerBound="224.1" HigherBound="242.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="69725560-9a4a-4b23-b63c-6624e2b61559" ParentLink="ComplexStatement_Statement" LowerBound="227.1" HigherBound="241.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameters.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="d555ce56-f5fa-42d0-97f9-7106c5c301f9" ParentLink="Construct_MessageRef" LowerBound="225.35" HigherBound="225.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e6d05c92-5775-4035-8f8e-67d0ca9e5c6c" ParentLink="ComplexStatement_Statement" LowerBound="242.1" HigherBound="244.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="cd163f11-91ce-45de-937d-0a859520cf4d" ParentLink="ServiceBody_Statement" LowerBound="247.1" HigherBound="256.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameProcess, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="593f8be3-c2f4-42d2-bab2-75f27c917d2d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="44.1" HigherBound="46.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileXmlPollingInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileXmlPollingIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="a1818fba-a6d9-4053-870a-2886d40f92ce" ParentLink="PortDeclaration_CLRAttribute" LowerBound="44.1" HigherBound="45.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="06c682e9-80c4-45e4-8d85-48a98652a6df" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="46.1" HigherBound="49.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="31" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppExcelOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppExcelOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8f0b545e-bdc2-4d73-b710-774a6e1009d9" ParentLink="PortDeclaration_CLRAttribute" LowerBound="46.1" HigherBound="47.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="d3fe2740-1214-49a9-baeb-9c734784a2c1" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="49.1" HigherBound="52.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="93" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="80be4edf-5114-46fe-9246-d27daa0299e9" ParentLink="PortDeclaration_CLRAttribute" LowerBound="49.1" HigherBound="50.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="43b103a7-24c4-424a-884c-048dc60e4717" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="52.1" HigherBound="54.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="141" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="e5940089-973b-444d-82d0-ee013e34ff74" ParentLink="PortDeclaration_CLRAttribute" LowerBound="52.1" HigherBound="53.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ETRM.Processes
{
    internal messagetype EtrmSettAppFileXmlPollingType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileXmlPolling_Types parameters;
    };
    internal messagetype EtrmSettAppFileUpdateStatusRequestType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileUpdateStatusTypedProcedure.EtrmSettAppFileUpdateStatus parameters;
    };
    internal messagetype EtrmSettAppFileUpdateStatusResponseType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFileUpdateStatusTypedProcedure.EtrmSettAppFileUpdateStatusResponse parameters;
    };
    internal messagetype EtrmSettAppExcelFileType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.SettAppExcel.excelfile parameters;
    };
    internal porttype EtrmSettAppFileXmlPollingInType
    {
        oneway Receive
        {
            EtrmSettAppFileXmlPollingType
        };
    };
    internal porttype EtrmSettAppExcelOutType
    {
        oneway Send
        {
            EtrmSettAppExcelFileType
        };
    };
    internal porttype EtrmSettAppFileUpdateStatusOutType
    {
        requestresponse EtrmSettAppFileUpdateStatus
        {
            EtrmSettAppFileUpdateStatusRequestType, EtrmSettAppFileUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EtrmSettAppProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EtrmSettAppFileXmlPollingInType EtrmSettAppFileXmlPollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EtrmSettAppExcelOutType EtrmSettAppExcelOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EtrmSettAppFileUpdateStatusOutType EtrmSettAppFileUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message EtrmSettAppFileXmlPollingType EtrmSettAppFileXmlPolling;
        message EtrmSettAppFileUpdateStatusRequestType EtrmSettAppFileUpdateStatusRequest;
        message EtrmSettAppFileUpdateStatusResponseType EtrmSettAppFileUpdateStatusResponse;
        message EtrmSettAppExcelFileType EtrmSettAppExcelFile;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("46064614-8743-4045-ab1c-0a1771d9d042")]
            activate receive (EtrmSettAppFileXmlPollingIn.Receive, EtrmSettAppFileXmlPolling);
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b1d01e63-6bc0-453d-848a-7afd8c5453c4")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowDescriptionEtrmSettAppProcess;
            sendNotification = 0;
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d5b5c09e-868a-4199-8366-9b341628c8ce")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId()
            );
            
            A2A.EAI.INT_ETRM.Services.ProcessServices.LogRelatedActivities(activityInstanceId, EtrmSettAppFileXmlPolling.parameters.activityIdStaging);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0fc757a-b765-43eb-b564-4e1752ff7156")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a1f36a50-ae3d-49f0-9700-57a3c95af804")]
                    construct EtrmSettAppExcelFile
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ed98828-e12b-4bb1-8f5c-00efb7ce1888")]
                        transform (EtrmSettAppExcelFile.parameters) = A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileXmlPollingToSettAppExcel (EtrmSettAppFileXmlPolling.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ba4f9d0-e955-4617-a85e-0540de6ee15a")]
                        EtrmSettAppExcelFile(FILE.ReceivedFileName) = System.String.Format("{0}.xlsx", EtrmSettAppFileXmlPolling.parameters.fileNameOut);
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("94946b76-1f30-4fb8-87d1-a74d3cfd2043")]
                    send (EtrmSettAppExcelOut.Send, EtrmSettAppExcelFile);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a388d59c-2826-44cb-8d7f-2099077f27c3")]
                    catch (Microsoft.XLANGs.BaseTypes.UnexpectedMessageTypeException validationExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0827bc2-97d1-4fad-baee-878b1b1b5eac")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(validationExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.SchemaValidation;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("bc8471be-571d-42bb-9f68-9ce68d566101")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ee30dddf-968a-43aa-965b-6aeb24c49ac6")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6c93478b-6558-441c-bad0-02c6dee8f530")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("99e73263-22b1-45b1-882d-cb9be1b38050")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fb859ae5-fb61-4998-8d41-dc49f3c03ca3")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae655146-a6bd-4062-8888-06a147756864")]
                    construct EtrmSettAppFileUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3721dd49-3e1b-4b6f-8c60-acebd2d4582a")]
                        transform (EtrmSettAppFileUpdateStatusRequest.parameters) = A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileXmlPollingToSettAppExcelToErtmSettAppFileUpdateStatus (EtrmSettAppFileXmlPolling.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bd297d13-31a0-4729-ab01-ea8783675828")]
                        EtrmSettAppFileUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        EtrmSettAppFileUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        EtrmSettAppFileUpdateStatusRequest.parameters.activityId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a96fd1ff-ebfd-4032-a91e-24d8fe5958b0")]
                    send (EtrmSettAppFileUpdateStatusOut.EtrmSettAppFileUpdateStatus, EtrmSettAppFileUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("31cb55e8-96f5-421b-b84f-b43016bfce67")]
                    receive (EtrmSettAppFileUpdateStatusOut.EtrmSettAppFileUpdateStatus, EtrmSettAppFileUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6c048e5f-8acf-451b-a7b6-79eeaed8a66d")]
                    sendNotification = EtrmSettAppFileUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f68f46f1-41b2-421b-bf62-33f9a6283176")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e631f6d6-f594-468f-a622-6015e111d323")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e74c2ea7-15da-4d04-ab4a-e3f57e7356ef")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6e88ef4-e0ce-4da7-9cfc-441c2359c243")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("56e62ed5-978a-4954-ae31-a0308c6d09d0")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("5869a638-0295-488b-9ea9-280043dd428e")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("df69cd47-5ecd-4df6-a7fd-15ffffa37817")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("69725560-9a4a-4b23-b63c-6624e2b61559")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameters.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6d05c92-5775-4035-8f8e-67d0ca9e5c6c")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cd163f11-91ce-45de-937d-0a859520cf4d")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameProcess, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

