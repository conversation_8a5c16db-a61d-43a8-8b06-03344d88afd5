﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="eec4f705-a0b6-4a65-865b-da34645a6ee3" LowerBound="1.1" HigherBound="498.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ETRM.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="1f4474a4-c434-4f8d-b63b-f43d22238633" ParentLink="Module_PortType" LowerBound="32.1" HigherBound="39.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="91271ea1-e131-4758-917a-84dff4a3539e" ParentLink="PortType_OperationDeclaration" LowerBound="34.1" HigherBound="38.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="6a403727-832e-4788-8667-1c3d096d058c" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="36.13" HigherBound="36.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="55322637-0ab8-4059-841a-7a5d9e6f7b73" ParentLink="Module_PortType" LowerBound="39.1" HigherBound="46.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="be1a9537-3a28-460c-9ec3-1158240c6e8b" ParentLink="PortType_OperationDeclaration" LowerBound="41.1" HigherBound="45.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="aad9fb30-2467-46a1-a95c-93321dfa7590" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="43.13" HigherBound="43.49">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="96ebac49-061b-4d13-a11b-72326933ed8f" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="43.51" HigherBound="43.88">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="6e731c03-4c1d-43fb-b5e1-ceaf07a8e164" ParentLink="Module_PortType" LowerBound="46.1" HigherBound="53.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="b4b0c0bc-0cd6-43b5-804a-04f2cef55def" ParentLink="PortType_OperationDeclaration" LowerBound="48.1" HigherBound="52.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractPriceInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b3234a5b-a7ab-458c-88cd-a40ab35a21dc" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="50.13" HigherBound="50.54">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractPriceInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="1abc0532-e930-41e4-bcf4-cac6657044ed" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="50.56" HigherBound="50.98">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractPriceInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="23ae9a87-b986-45d6-9bb9-45a554125717" ParentLink="Module_PortType" LowerBound="53.1" HigherBound="60.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="1ce3b803-ee31-4263-9323-e4126a735e80" ParentLink="PortType_OperationDeclaration" LowerBound="55.1" HigherBound="59.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2cc5b314-34b6-4032-b176-77952ce86e48" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="57.13" HigherBound="57.55">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppPowerThresholdInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="ccd03f12-e52e-4105-b6b5-5263156f8282" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="57.57" HigherBound="57.100">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppPowerThresholdInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="eb358319-e371-4366-91f7-1a0adbe31cd1" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppFileType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5b9fcbcc-ab3a-479e-bcec-71901028d699" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFile" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="df63c77f-9d5d-433d-9309-124083bd2694" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c035c4c0-4275-4130-94f3-10b18507ac17" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractInsertTypedProcedure.EtrmSettAppContractInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="139c0c6b-227b-4863-ac44-b2cc05c96c3b" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f85428ff-5328-4376-b4d9-42f12fc88fb6" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractInsertTypedProcedure.EtrmSettAppContractInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="278105fe-883f-4350-956b-191ad2d51184" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="11bc6365-81f5-49bc-990d-791b01dadba6" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractPriceInsertTypedProcedure.EtrmSettAppContractPriceInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="eeed8b4f-37ad-44bc-8911-526cba5347b8" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="97152ee4-70f5-406c-bfbe-2548813f9c62" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractPriceInsertTypedProcedure.EtrmSettAppContractPriceInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9046547f-d9e6-4364-80cd-0da6495d8e77" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7d84c40c-02d3-4fee-88c8-998ce823be4b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppPowerThresholdInsertTypedProcedure.EtrmSettAppPowerThresholdInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="cd642c51-10cb-4cea-b77f-1947ac35f558" ParentLink="Module_MessageType" LowerBound="28.1" HigherBound="32.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e0e86236-d701-4793-b321-f47c71d7d63b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppPowerThresholdInsertTypedProcedure.EtrmSettAppPowerThresholdInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="b700f8dd-9f4f-4098-9fde-936539168a6a" ParentLink="Module_ServiceDeclaration" LowerBound="60.1" HigherBound="497.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EtrmSettAppStaging" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="696d1918-9a12-422d-814a-35810b3bc373" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="84.1" HigherBound="85.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="reportType" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="641caf95-76bd-48e1-ac39-90a5b2ca0f72" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="85.1" HigherBound="86.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="insertSucceded" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c2336c3c-7aa5-4781-9feb-1503c3fd26cc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="86.1" HigherBound="87.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="loopNumber" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="74fb7c7c-6466-4d43-af04-9a45ea37f453" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="87.1" HigherBound="88.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTime" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="fileCreationTime" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3c557c0f-6736-4245-b135-0696ff0fb87b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="88.1" HigherBound="89.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f946528b-6e5a-460c-9630-4e3cc65bb3aa" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="89.1" HigherBound="90.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="53d97308-ead3-4cdc-a5cd-f3f37135ebef" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="90.1" HigherBound="91.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowDescription" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="471dc791-4825-47ac-a2fd-70e7555b4c53" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="91.1" HigherBound="92.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="881ce3db-e056-4c3d-94d3-0939e47e2caf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="92.1" HigherBound="93.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7fe61a69-a0bf-4cae-bb68-34fd300a6552" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="93.1" HigherBound="94.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1cf0b2ab-99ba-4938-b3de-1174d8713ad5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="94.1" HigherBound="95.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="27ca675e-2f9a-426e-a617-65a86b0ef3e7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="95.1" HigherBound="96.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="21f298ab-ca3f-48aa-8b5d-c21264428911" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="96.1" HigherBound="97.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a42404ca-d1bf-4046-a433-70d653418997" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="97.1" HigherBound="98.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d191d0d8-3151-4e16-9a6a-b1446d806ffd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="98.1" HigherBound="99.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="filetype" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3b2b5f08-80e8-41b5-a849-a13686701ead" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="99.1" HigherBound="100.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="fileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a578d5d9-ed91-46f4-b624-0ff1eabf8fc1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="73a8c9a5-6b3a-423c-b579-a9500b57cb63" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFile" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="07f6e3f6-966f-4645-9e21-7f43c7f1f9f7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="78.1" HigherBound="79.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e1c69f87-ce3c-479e-a739-01075369e5a0" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="79.1" HigherBound="80.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6eb07502-f541-48b4-a186-e7cf443d5eed" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="80.1" HigherBound="81.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractPriceInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c98c56cd-25b0-4095-afeb-5af6cd03aab4" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="81.1" HigherBound="82.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractPriceInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d9f36a4f-0033-40cb-b230-69376d859678" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="82.1" HigherBound="83.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppPowerThresholdInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e820c1c4-302b-447c-ba60-f844b8c1cfe7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="83.1" HigherBound="84.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppPowerThresholdInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="0bb0693d-7c98-47b3-9e27-a42fec86ec82" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="8fd445ae-c6c2-4a62-bf73-1870f74269b7" ParentLink="ServiceBody_Statement" LowerBound="102.1" HigherBound="113.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="EtrmSettAppFileIn" />
                    <om:Property Name="MessageName" Value="EtrmSettAppFile" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="b62ded10-15cd-4b36-a2d0-279295c84e43" ParentLink="ServiceBody_Statement" LowerBound="113.1" HigherBound="133.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;flowDescription = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowDescriptionEtrmSettAppStaging;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EtrmSettAppFile);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EtrmSettAppFile);&#xD;&#xA;fileCreationTime = EtrmSettAppFile(FILE.FileCreationTime);&#xD;&#xA;fileName = System.IO.Path.GetFileName(originalFileName);&#xD;&#xA;filetype = &quot;N/A&quot;;&#xD;&#xA;reportType = &quot;STANDARD&quot;;&#xD;&#xA;loopNumber = 0;&#xD;&#xA;insertSucceded = false;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="f908290e-b1b7-47df-8e52-9e0ac29ba353" ParentLink="ServiceBody_Statement" LowerBound="133.1" HigherBound="143.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;File Name&quot;, originalFileName&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="faa0f561-4867-4809-a079-7fba7ad0155b" ParentLink="ServiceBody_Statement" LowerBound="143.1" HigherBound="454.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Decision" OID="cc45c857-4356-4afc-87d5-5825acc6de34" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="154.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="reportType" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="d6f1caa3-29e8-4140-bcb0-dc3acd455ad6" ParentLink="ReallyComplexStatement_Branch" LowerBound="149.21" HigherBound="154.1">
                            <om:Property Name="Expression" Value="fileName.Contains(&quot;UPSA&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="UPSA" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="0454f930-f873-4877-9a5a-b47155264ca2" ParentLink="ComplexStatement_Statement" LowerBound="151.1" HigherBound="153.1">
                                <om:Property Name="Expression" Value="reportType = &quot;UPSA&quot;;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set reportType" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="691ff310-decf-4f49-a4d4-6e3b501f6853" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="d5543524-19d0-479b-a7e0-69e0b20b8f77" ParentLink="ComplexStatement_Statement" LowerBound="154.1" HigherBound="422.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="File Type" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="bcd80835-7df1-4655-9b0e-154f57887668" ParentLink="ReallyComplexStatement_Branch" LowerBound="155.21" HigherBound="241.1">
                            <om:Property Name="Expression" Value="fileName.StartsWith(&quot;SettAppContract_&quot;) || fileName.StartsWith(&quot;SettAppUPSAContract_&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="SettAppContract" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="49b79a3e-22a2-4ee3-b1de-4f49326af86b" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="159.1">
                                <om:Property Name="Expression" Value="filetype = &quot;SettAppContract&quot;;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Filetype" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Construct" OID="6837fc36-648b-43a3-b23f-4a9c963cdcfb" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="170.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create DB Insert" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="bf29052c-7472-499e-bb69-3789b992a3a7" ParentLink="Construct_MessageRef" LowerBound="160.35" HigherBound="160.67">
                                    <om:Property Name="Ref" Value="EtrmSettAppContractInsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="85719f3b-372f-4358-b116-05aa78024122" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="164.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppContractInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup DB Insert" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="2fd8d086-3e16-41d2-aaf4-555e6bcd2651" ParentLink="Transform_OutputMessagePartRef" LowerBound="163.40" HigherBound="163.83">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppContractInsertRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="7b0daa16-4092-4dd4-a784-cdb143bf091b" ParentLink="Transform_InputMessagePartRef" LowerBound="163.163" HigherBound="163.189">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppFile" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="7634075c-9214-4124-91a5-0afdf0a94d1b" ParentLink="ComplexStatement_Statement" LowerBound="164.1" HigherBound="169.1">
                                    <om:Property Name="Expression" Value="EtrmSettAppContractInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;EtrmSettAppContractInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;EtrmSettAppContractInsertRequest.parameters.reportType = reportType;&#xD;&#xA;EtrmSettAppContractInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString(&quot;yyyy-MM-dd HH:mm:ss.fff&quot;);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Assign" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="64ca7322-7c31-43a2-bbae-75238d8e9f1d" ParentLink="ComplexStatement_Statement" LowerBound="170.1" HigherBound="240.1">
                                <om:Property Name="Expression" Value="loopNumber &lt; 10 &amp;&amp; insertSucceded == false" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Insert Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="Scope" OID="69003656-cae9-4150-b9ee-a857b582a567" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="228.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Staging on DB" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="8bf9fa99-e7d8-4959-bbb8-3ffb840274c6" ParentLink="ComplexStatement_Statement" LowerBound="178.1" HigherBound="181.1">
                                        <om:Property Name="Expression" Value="loopNumber = loopNumber + 1;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set LoopNumber" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="175a9fb6-40c0-4565-8086-4c7cac1a6c2e" ParentLink="ComplexStatement_Statement" LowerBound="181.1" HigherBound="183.1">
                                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Take Time" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Send" OID="b30d2124-e201-4ab9-bc99-8b019f514e8f" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="185.1">
                                        <om:Property Name="PortName" Value="EtrmSettAppContractInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppContractInsertRequest" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppContractInsert" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="e1f55bb8-25af-4877-adbc-26d999b9d114" ParentLink="ComplexStatement_Statement" LowerBound="185.1" HigherBound="187.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="EtrmSettAppContractInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppContractInsertResponse" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppContractInsert" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="914ab250-e3a9-49ac-bf87-a00a1a68f54b" ParentLink="ComplexStatement_Statement" LowerBound="187.1" HigherBound="189.1">
                                        <om:Property Name="Expression" Value="insertSucceded = true;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set insertSucceded" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="0debc763-761f-4c37-93aa-41e4a9b622db" ParentLink="ComplexStatement_Statement" LowerBound="189.1" HigherBound="196.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Tipo File&quot;, filetype,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="a32b2bcd-ea28-45f6-ac27-5e9de7605996" ParentLink="Scope_Catch" LowerBound="199.1" HigherBound="213.1">
                                        <om:Property Name="ExceptionName" Value="soapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="dbe0b2a5-4d12-4e47-b514-961247be1cdf" ParentLink="Catch_Statement" LowerBound="202.1" HigherBound="212.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="622f0886-9c0d-4fe6-8b5e-686266d6137a" ParentLink="Scope_Catch" LowerBound="213.1" HigherBound="226.1">
                                        <om:Property Name="ExceptionName" Value="systemExc" />
                                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="System Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="355a51a9-d1f2-45ea-bb61-bc9e01e3a8a9" ParentLink="Catch_Statement" LowerBound="216.1" HigherBound="225.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="27304628-2c19-41f5-ac3b-c8a5760a5197" ParentLink="ComplexStatement_Statement" LowerBound="228.1" HigherBound="239.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Insert Succeded" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="8b1a6e50-f351-4097-8e7e-4a2287ad7ffc" ParentLink="ReallyComplexStatement_Branch" LowerBound="229.29" HigherBound="234.1">
                                        <om:Property Name="Expression" Value="insertSucceded" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Yes" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="824e9272-0547-444c-9e84-f2e687c06a15" ParentLink="ComplexStatement_Statement" LowerBound="231.1" HigherBound="233.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="resultCode Succeded" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="66c2771b-fd50-4257-b6db-b032a34e9184" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="Delay" OID="54c5a028-ae89-4b53-89ed-1141cda064f3" ParentLink="ComplexStatement_Statement" LowerBound="236.1" HigherBound="238.1">
                                            <om:Property Name="Timeout" Value="System.TimeSpan.FromSeconds(30);" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Wait 30 sec" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="68c702a9-ff68-4254-bceb-a4e15bc662b5" ParentLink="ReallyComplexStatement_Branch" LowerBound="241.26" HigherBound="327.1">
                            <om:Property Name="Expression" Value="fileName.StartsWith(&quot;SettAppContractPrice_&quot;) || fileName.StartsWith(&quot;SettAppUPSAPrice_&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="SettAppContractPrice" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="35e59ee0-baf9-4a4e-b3cc-cac306c6bf57" ParentLink="ComplexStatement_Statement" LowerBound="243.1" HigherBound="245.1">
                                <om:Property Name="Expression" Value="filetype = &quot;SettAppContract&quot;;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Filetype" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Construct" OID="d1584a5a-ded6-4704-a193-1cf0fe025de9" ParentLink="ComplexStatement_Statement" LowerBound="245.1" HigherBound="256.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create DB Insert" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="14981af4-d672-49e5-a481-9d964f882953" ParentLink="ComplexStatement_Statement" LowerBound="248.1" HigherBound="250.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppContractPriceInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup DB Insert" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="49caa34f-f249-4625-aed9-0cce743169e7" ParentLink="Transform_InputMessagePartRef" LowerBound="249.173" HigherBound="249.199">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppFile" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="75e4f5ae-74ab-4fc4-849a-92b4405c4cb1" ParentLink="Transform_OutputMessagePartRef" LowerBound="249.40" HigherBound="249.88">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppContractPriceInsertRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="7d39ac41-905c-4b5f-a2bd-59db4b84ca08" ParentLink="ComplexStatement_Statement" LowerBound="250.1" HigherBound="255.1">
                                    <om:Property Name="Expression" Value="EtrmSettAppContractPriceInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;EtrmSettAppContractPriceInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;EtrmSettAppContractPriceInsertRequest.parameters.reportType = reportType;&#xD;&#xA;EtrmSettAppContractPriceInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString(&quot;yyyy-MM-dd HH:mm:ss.fff&quot;);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Assign" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="ea881d49-ec10-4932-9939-97da06b33193" ParentLink="Construct_MessageRef" LowerBound="246.35" HigherBound="246.72">
                                    <om:Property Name="Ref" Value="EtrmSettAppContractPriceInsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="48f6a5fa-a709-4d19-90b8-87fe760a3fb1" ParentLink="ComplexStatement_Statement" LowerBound="256.1" HigherBound="326.1">
                                <om:Property Name="Expression" Value="loopNumber &lt; 10 &amp;&amp; insertSucceded == false" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Insert Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="Scope" OID="e3581e47-7544-4232-9dbe-602119d24efa" ParentLink="ComplexStatement_Statement" LowerBound="259.1" HigherBound="314.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Staging on DB" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="3958a6b1-139b-42e8-a112-d21e0262f3db" ParentLink="ComplexStatement_Statement" LowerBound="264.1" HigherBound="267.1">
                                        <om:Property Name="Expression" Value="loopNumber = loopNumber + 1;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set LoopNumber" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="ff6559da-81f5-481a-95e7-4750034bfb0a" ParentLink="ComplexStatement_Statement" LowerBound="267.1" HigherBound="269.1">
                                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Take Time" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Send" OID="79c7ee02-f316-4478-b622-d79e3111f579" ParentLink="ComplexStatement_Statement" LowerBound="269.1" HigherBound="271.1">
                                        <om:Property Name="PortName" Value="EtrmSettAppContractPriceInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppContractPriceInsertRequest" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppContractPriceInsert" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="768c7280-5af8-41fe-87ba-8034d0d0f1d4" ParentLink="ComplexStatement_Statement" LowerBound="271.1" HigherBound="273.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="EtrmSettAppContractPriceInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppContractPriceInsertResponse" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppContractPriceInsert" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="e7a622a2-1b0d-4e05-b98f-db86b26c4377" ParentLink="ComplexStatement_Statement" LowerBound="273.1" HigherBound="275.1">
                                        <om:Property Name="Expression" Value="insertSucceded = true;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set insertSucceded" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="3b1c3562-684f-4690-8f46-e30064123c50" ParentLink="ComplexStatement_Statement" LowerBound="275.1" HigherBound="282.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Tipo File&quot;, filetype,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="aca0c630-79db-452f-ace1-024278ee5320" ParentLink="Scope_Catch" LowerBound="285.1" HigherBound="299.1">
                                        <om:Property Name="ExceptionName" Value="soapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="1fe76614-912f-4461-a934-c375b2083e3b" ParentLink="Catch_Statement" LowerBound="288.1" HigherBound="298.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="8653145f-6c51-4c02-a94e-d87ff481b403" ParentLink="Scope_Catch" LowerBound="299.1" HigherBound="312.1">
                                        <om:Property Name="ExceptionName" Value="systemExc" />
                                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="System Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="3c1b9942-7f21-4b3c-8402-461d8a22a672" ParentLink="Catch_Statement" LowerBound="302.1" HigherBound="311.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="4c6ebd8d-22a8-4ef0-8ddb-8169b73f6b26" ParentLink="ComplexStatement_Statement" LowerBound="314.1" HigherBound="325.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Insert Succeded" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="20e3ee58-bdab-45d7-b5c5-cb054e3695b5" ParentLink="ReallyComplexStatement_Branch" LowerBound="315.29" HigherBound="320.1">
                                        <om:Property Name="Expression" Value="insertSucceded" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Yes" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="6173fa65-b7ad-419e-a867-5c7e0459f77d" ParentLink="ComplexStatement_Statement" LowerBound="317.1" HigherBound="319.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="resultCode Succeded" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="73bff20d-1e77-4158-a118-23d4ba1ee42e" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="Delay" OID="72afa28d-1e10-43a3-9c9f-bca6d47d6fbf" ParentLink="ComplexStatement_Statement" LowerBound="322.1" HigherBound="324.1">
                                            <om:Property Name="Timeout" Value="System.TimeSpan.FromSeconds(30);" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Wait 30 sec" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="ca14934f-bcbc-40d5-88ee-97490204194a" ParentLink="ReallyComplexStatement_Branch" LowerBound="327.26" HigherBound="413.1">
                            <om:Property Name="Expression" Value="fileName.StartsWith(&quot;SettAppPowerThresholds_&quot;) || fileName.StartsWith(&quot;SettAppUPSAThresholds_&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="SettAppPowerThresholds" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="004f10ac-ca4c-4733-8726-1d2d31b3f7d8" ParentLink="ComplexStatement_Statement" LowerBound="329.1" HigherBound="331.1">
                                <om:Property Name="Expression" Value="filetype = &quot;SettAppContract&quot;;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Filetype" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Construct" OID="58da350b-c925-4d98-add5-6b11870c4a96" ParentLink="ComplexStatement_Statement" LowerBound="331.1" HigherBound="342.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create DB Insert" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="ad04e431-8771-44f4-965d-d1f626a1a30e" ParentLink="ComplexStatement_Statement" LowerBound="334.1" HigherBound="336.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppPowerThresholdInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup DB Insert" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="fe1abc70-3e2e-4956-83ac-23bfb2dc0ad8" ParentLink="Transform_InputMessagePartRef" LowerBound="335.175" HigherBound="335.201">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppFile" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_5" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="c3e977da-2252-45e4-abca-b2e123e688c4" ParentLink="Transform_OutputMessagePartRef" LowerBound="335.40" HigherBound="335.89">
                                        <om:Property Name="MessageRef" Value="EtrmSettAppPowerThresholdInsertRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_6" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="d0b8ec2e-e485-4f01-843f-d46ab1c83ad8" ParentLink="ComplexStatement_Statement" LowerBound="336.1" HigherBound="341.1">
                                    <om:Property Name="Expression" Value="EtrmSettAppPowerThresholdInsertRequest.parameters.fileName = originalFileName;&#xD;&#xA;EtrmSettAppPowerThresholdInsertRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;EtrmSettAppPowerThresholdInsertRequest.parameters.reportType = reportType;&#xD;&#xA;EtrmSettAppPowerThresholdInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString(&quot;yyyy-MM-dd HH:mm:ss.fff&quot;);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Assign" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="a3663246-9a81-4432-9f63-c3c86e4329c0" ParentLink="Construct_MessageRef" LowerBound="332.35" HigherBound="332.73">
                                    <om:Property Name="Ref" Value="EtrmSettAppPowerThresholdInsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="While" OID="af033aca-7f8b-45b4-ae17-ebcedd0f4e00" ParentLink="ComplexStatement_Statement" LowerBound="342.1" HigherBound="412.1">
                                <om:Property Name="Expression" Value="loopNumber &lt; 10 &amp;&amp; insertSucceded == false" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Insert Loop" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="Scope" OID="20b170c8-4f1a-4217-962f-78fac6761006" ParentLink="ComplexStatement_Statement" LowerBound="345.1" HigherBound="400.1">
                                    <om:Property Name="InitializedTransactionType" Value="True" />
                                    <om:Property Name="IsSynchronized" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Staging on DB" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="55ef4ff9-0210-4eef-ad5a-73dbaf2a0e1e" ParentLink="ComplexStatement_Statement" LowerBound="350.1" HigherBound="353.1">
                                        <om:Property Name="Expression" Value="loopNumber = loopNumber + 1;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set LoopNumber" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="77f26d39-c3d5-46fe-945a-6998e94db092" ParentLink="ComplexStatement_Statement" LowerBound="353.1" HigherBound="355.1">
                                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Take Time" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Send" OID="8ff91e0b-7b65-4410-bd00-8937bbb480c9" ParentLink="ComplexStatement_Statement" LowerBound="355.1" HigherBound="357.1">
                                        <om:Property Name="PortName" Value="EtrmSettAppPowerThresholdInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppPowerThresholdInsertRequest" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppPowerThresholdInsert" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="d0d952e8-a267-4782-b740-7c769f8feb7f" ParentLink="ComplexStatement_Statement" LowerBound="357.1" HigherBound="359.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="EtrmSettAppPowerThresholdInsertOut" />
                                        <om:Property Name="MessageName" Value="EtrmSettAppPowerThresholdInsertResponse" />
                                        <om:Property Name="OperationName" Value="EtrmSettAppPowerThresholdInsert" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="3f69667e-5770-42f4-96d3-19e1a4d4923e" ParentLink="ComplexStatement_Statement" LowerBound="359.1" HigherBound="361.1">
                                        <om:Property Name="Expression" Value="insertSucceded = true;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set insertSucceded" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="056f6e27-0b3e-426f-8848-bbde249a1155" ParentLink="ComplexStatement_Statement" LowerBound="361.1" HigherBound="368.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Tipo File&quot;, filetype,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Catch" OID="3a522126-6292-42d8-b7a1-101cd70bb0e2" ParentLink="Scope_Catch" LowerBound="371.1" HigherBound="385.1">
                                        <om:Property Name="ExceptionName" Value="soapExc" />
                                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Soap Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="f1bf3e0d-df8f-45db-bdf9-43328f0f33e1" ParentLink="Catch_Statement" LowerBound="374.1" HigherBound="384.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Catch" OID="2ad1191b-fa51-450d-b1f1-e1393c62ebfb" ParentLink="Scope_Catch" LowerBound="385.1" HigherBound="398.1">
                                        <om:Property Name="ExceptionName" Value="systemExc" />
                                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                        <om:Property Name="IsFaultMessage" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="System Exception" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="VariableAssignment" OID="c5201f5e-f58d-4998-9e59-4a74234ce197" ParentLink="Catch_Statement" LowerBound="388.1" HigherBound="397.1">
                                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="WriteLog" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Decision" OID="2698b411-74cb-4b41-bc73-8bec229b6300" ParentLink="ComplexStatement_Statement" LowerBound="400.1" HigherBound="411.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Insert Succeded" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="DecisionBranch" OID="eac8ccbc-d9bb-4acb-ac4e-69a20cc04554" ParentLink="ReallyComplexStatement_Branch" LowerBound="401.29" HigherBound="406.1">
                                        <om:Property Name="Expression" Value="insertSucceded" />
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Yes" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="VariableAssignment" OID="3dcad3b1-bc86-4d09-aed1-175e462ee6af" ParentLink="ComplexStatement_Statement" LowerBound="403.1" HigherBound="405.1">
                                            <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="resultCode Succeded" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="DecisionBranch" OID="26b065c7-6f5f-4c41-ad64-291e43cc240f" ParentLink="ReallyComplexStatement_Branch">
                                        <om:Property Name="IsGhostBranch" Value="True" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Else" />
                                        <om:Property Name="Signal" Value="False" />
                                        <om:Element Type="Delay" OID="8d340f0d-0f43-482a-9ad8-4a012df8ede1" ParentLink="ComplexStatement_Statement" LowerBound="408.1" HigherBound="410.1">
                                            <om:Property Name="Timeout" Value="System.TimeSpan.FromSeconds(30);" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Wait 30 sec" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="0df8297f-2f0e-4096-b494-30ad3ef3a0f3" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="dad62933-b9e7-4de8-acd2-bfb4abe7a63b" ParentLink="ComplexStatement_Statement" LowerBound="415.1" HigherBound="421.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;Tipo di file non riconosciuto&quot;));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Error" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="6788e306-1056-496e-b6a2-60f2133b6f47" ParentLink="Scope_Catch" LowerBound="425.1" HigherBound="439.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0a24730c-f3ff-4027-a929-0cf20b7675f0" ParentLink="Catch_Statement" LowerBound="428.1" HigherBound="438.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="9526abe2-6144-448f-b6ab-5d6ebd16fb7c" ParentLink="Scope_Catch" LowerBound="439.1" HigherBound="452.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="1beaace1-4ec5-4ed5-b24c-51471b72be05" ParentLink="Catch_Statement" LowerBound="442.1" HigherBound="451.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore in fase di elaborazione dati. &quot;, A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="e0fca7c3-d966-41b3-a0d1-369ffa489585" ParentLink="ServiceBody_Statement" LowerBound="454.1" HigherBound="487.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="815d89ee-6574-4f06-9087-3657eeb9192c" ParentLink="ReallyComplexStatement_Branch" LowerBound="455.13" HigherBound="458.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="34772fa6-6a94-4ca8-8616-29570a6cc756" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="8069584a-8147-4234-a9a3-52d19b21b92a" ParentLink="ComplexStatement_Statement" LowerBound="460.1" HigherBound="486.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="e26d31a7-5aab-4ce9-909c-98a33bfedb2c" ParentLink="ComplexStatement_Statement" LowerBound="465.1" HigherBound="482.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="4599d220-3c8b-4a5d-9d2e-3a6595e56996" ParentLink="ComplexStatement_Statement" LowerBound="468.1" HigherBound="481.1">
                                    <om:Property Name="Expression" Value="Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameters.applicationName = A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameters.flowGroup = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameters.flowDescription = flowDescription;&#xD;&#xA;Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="1024b54f-e298-4247-a5c6-e4a3b18071e0" ParentLink="Construct_MessageRef" LowerBound="466.35" HigherBound="466.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="2a64983d-f397-4ac1-b46d-a6b09844cc51" ParentLink="ComplexStatement_Statement" LowerBound="482.1" HigherBound="484.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a4fe81f7-b885-45f8-bf46-96151c868a52" ParentLink="ServiceBody_Statement" LowerBound="487.1" HigherBound="495.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c140249f-e1e6-476e-809a-88ad90dd12e5" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="63.1" HigherBound="65.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppFileInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppFileIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="e339b238-0854-47af-a623-663d66159307" ParentLink="PortDeclaration_CLRAttribute" LowerBound="63.1" HigherBound="64.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="502f3662-7632-487e-a78d-b540882b3d1d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="65.1" HigherBound="68.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="45" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="6ece0713-9e70-45dd-8a1a-3456b4f59b0c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="65.1" HigherBound="66.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="c5a94f56-e709-4501-82f3-c62f3b1b9e5c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="68.1" HigherBound="71.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppContractPriceInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppContractPriceInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="2d446fea-7f70-489c-b796-1a89f7a6e6e4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="68.1" HigherBound="69.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="427764f5-5022-4505-aab1-41af077b12bc" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="71.1" HigherBound="74.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="42" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.EtrmSettAppPowerThresholdInsertOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EtrmSettAppPowerThresholdInsertOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="2d10e68f-48e8-4865-b86a-4ba97c8d8d92" ParentLink="PortDeclaration_CLRAttribute" LowerBound="71.1" HigherBound="72.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="37dd41fe-6196-403b-ad84-868cacd54756" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="74.1" HigherBound="76.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="178" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_ETRM.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="bcb40510-3f9a-401a-bcfc-44a8e2a1e8cd" ParentLink="PortDeclaration_CLRAttribute" LowerBound="74.1" HigherBound="75.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ETRM.Processes
{
    internal messagetype EtrmSettAppFileType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppFile parameters;
    };
    internal messagetype EtrmSettAppContractInsertRequestType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractInsertTypedProcedure.EtrmSettAppContractInsert parameters;
    };
    internal messagetype EtrmSettAppContractInsertResponseType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractInsertTypedProcedure.EtrmSettAppContractInsertResponse parameters;
    };
    internal messagetype EtrmSettAppContractPriceInsertRequestType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractPriceInsertTypedProcedure.EtrmSettAppContractPriceInsert parameters;
    };
    internal messagetype EtrmSettAppContractPriceInsertResponseType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppContractPriceInsertTypedProcedure.EtrmSettAppContractPriceInsertResponse parameters;
    };
    internal messagetype EtrmSettAppPowerThresholdInsertRequestType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppPowerThresholdInsertTypedProcedure.EtrmSettAppPowerThresholdInsert parameters;
    };
    internal messagetype EtrmSettAppPowerThresholdInsertResponseType
    {
        body A2A.EAI.INT_ETRM.Messaging.Schemas.EtrmSettAppPowerThresholdInsertTypedProcedure.EtrmSettAppPowerThresholdInsertResponse parameters;
    };
    internal porttype EtrmSettAppFileInType
    {
        oneway Receive
        {
            EtrmSettAppFileType
        };
    };
    internal porttype EtrmSettAppContractInsertOutType
    {
        requestresponse EtrmSettAppContractInsert
        {
            EtrmSettAppContractInsertRequestType, EtrmSettAppContractInsertResponseType
        };
    };
    internal porttype EtrmSettAppContractPriceInsertOutType
    {
        requestresponse EtrmSettAppContractPriceInsert
        {
            EtrmSettAppContractPriceInsertRequestType, EtrmSettAppContractPriceInsertResponseType
        };
    };
    internal porttype EtrmSettAppPowerThresholdInsertOutType
    {
        requestresponse EtrmSettAppPowerThresholdInsert
        {
            EtrmSettAppPowerThresholdInsertRequestType, EtrmSettAppPowerThresholdInsertResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service EtrmSettAppStaging
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements EtrmSettAppFileInType EtrmSettAppFileIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EtrmSettAppContractInsertOutType EtrmSettAppContractInsertOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EtrmSettAppContractPriceInsertOutType EtrmSettAppContractPriceInsertOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EtrmSettAppPowerThresholdInsertOutType EtrmSettAppPowerThresholdInsertOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message NotificationType Notification;
        message EtrmSettAppFileType EtrmSettAppFile;
        message EtrmSettAppContractInsertRequestType EtrmSettAppContractInsertRequest;
        message EtrmSettAppContractInsertResponseType EtrmSettAppContractInsertResponse;
        message EtrmSettAppContractPriceInsertRequestType EtrmSettAppContractPriceInsertRequest;
        message EtrmSettAppContractPriceInsertResponseType EtrmSettAppContractPriceInsertResponse;
        message EtrmSettAppPowerThresholdInsertRequestType EtrmSettAppPowerThresholdInsertRequest;
        message EtrmSettAppPowerThresholdInsertResponseType EtrmSettAppPowerThresholdInsertResponse;
        System.String reportType;
        System.Boolean insertSucceded;
        System.Int32 loopNumber;
        System.DateTime fileCreationTime;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        System.String flowDescription;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String filetype;
        System.String fileName;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8fd445ae-c6c2-4a62-bf73-1870f74269b7")]
            activate receive (EtrmSettAppFileIn.Receive, EtrmSettAppFile);
            reportType = "";
            insertSucceded = true;
            originalFileName = "";
            flowDescription = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            filetype = "";
            fileName = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b62ded10-15cd-4b36-a2d0-279295c84e43")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            flowDescription = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowDescriptionEtrmSettAppStaging;
            
            
            dataInizio = System.DateTimeOffset.Now;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(EtrmSettAppFile);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(EtrmSettAppFile);
            fileCreationTime = EtrmSettAppFile(FILE.FileCreationTime);
            fileName = System.IO.Path.GetFileName(originalFileName);
            filetype = "N/A";
            reportType = "STANDARD";
            loopNumber = 0;
            insertSucceded = false;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f908290e-b1b7-47df-8e52-9e0ac29ba353")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "File Name", originalFileName
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("faa0f561-4867-4809-a079-7fba7ad0155b")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("cc45c857-4356-4afc-87d5-5825acc6de34")]
                    if (fileName.Contains("UPSA"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0454f930-f873-4877-9a5a-b47155264ca2")]
                        reportType = "UPSA";
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d5543524-19d0-479b-a7e0-69e0b20b8f77")]
                    if (fileName.StartsWith("SettAppContract_") || fileName.StartsWith("SettAppUPSAContract_"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("49b79a3e-22a2-4ee3-b1de-4f49326af86b")]
                        filetype = "SettAppContract";
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6837fc36-648b-43a3-b23f-4a9c963cdcfb")]
                        construct EtrmSettAppContractInsertRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("85719f3b-372f-4358-b116-05aa78024122")]
                            transform (EtrmSettAppContractInsertRequest.parameters) = A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppContractInsert (EtrmSettAppFile.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7634075c-9214-4124-91a5-0afdf0a94d1b")]
                            EtrmSettAppContractInsertRequest.parameters.fileName = originalFileName;
                            EtrmSettAppContractInsertRequest.parameters.activityId = activityInstanceId;
                            EtrmSettAppContractInsertRequest.parameters.reportType = reportType;
                            EtrmSettAppContractInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("64ca7322-7c31-43a2-bbae-75238d8e9f1d")]
                        while (loopNumber < 10 && insertSucceded == false)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("69003656-cae9-4150-b9ee-a857b582a567")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8bf9fa99-e7d8-4959-bbb8-3ffb840274c6")]
                                    loopNumber = loopNumber + 1;
                                    errorMessage = new System.Text.StringBuilder();
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("175a9fb6-40c0-4565-8086-4c7cac1a6c2e")]
                                    dataInizioDb = System.DateTimeOffset.Now;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b30d2124-e201-4ab9-bc99-8b019f514e8f")]
                                    send (EtrmSettAppContractInsertOut.EtrmSettAppContractInsert, EtrmSettAppContractInsertRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e1f55bb8-25af-4877-adbc-26d999b9d114")]
                                    receive (EtrmSettAppContractInsertOut.EtrmSettAppContractInsert, EtrmSettAppContractInsertResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("914ab250-e3a9-49ac-bf87-a00a1a68f54b")]
                                    insertSucceded = true;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0debc763-761f-4c37-93aa-41e4a9b622db")]
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                                    "Data Inizio DB", dataInizioDb,
                                    "Data Fine DB", System.DateTimeOffset.Now,
                                    "Tipo File", filetype,
                                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a32b2bcd-ea28-45f6-ac27-5e9de7605996")]
                                    catch (System.Web.Services.Protocols.SoapException soapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dbe0b2a5-4d12-4e47-b514-961247be1cdf")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                        
                                        
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("622f0886-9c0d-4fe6-8b5e-686266d6137a")]
                                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("355a51a9-d1f2-45ea-bb61-bc9e01e3a8a9")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(systemExc.Message);
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                        
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("27304628-2c19-41f5-ac3b-c8a5760a5197")]
                            if (insertSucceded)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("824e9272-0547-444c-9e84-f2e687c06a15")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("54c5a028-ae89-4b53-89ed-1141cda064f3")]
                                delay System.TimeSpan.FromSeconds(30);
                            }
                        }
                    }
                    else if (fileName.StartsWith("SettAppContractPrice_") || fileName.StartsWith("SettAppUPSAPrice_"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("35e59ee0-baf9-4a4e-b3cc-cac306c6bf57")]
                        filetype = "SettAppContract";
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1584a5a-ded6-4704-a193-1cf0fe025de9")]
                        construct EtrmSettAppContractPriceInsertRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("14981af4-d672-49e5-a481-9d964f882953")]
                            transform (EtrmSettAppContractPriceInsertRequest.parameters) = A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppContractPriceInsert (EtrmSettAppFile.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7d39ac41-905c-4b5f-a2bd-59db4b84ca08")]
                            EtrmSettAppContractPriceInsertRequest.parameters.fileName = originalFileName;
                            EtrmSettAppContractPriceInsertRequest.parameters.activityId = activityInstanceId;
                            EtrmSettAppContractPriceInsertRequest.parameters.reportType = reportType;
                            EtrmSettAppContractPriceInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("48f6a5fa-a709-4d19-90b8-87fe760a3fb1")]
                        while (loopNumber < 10 && insertSucceded == false)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3581e47-7544-4232-9dbe-602119d24efa")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3958a6b1-139b-42e8-a112-d21e0262f3db")]
                                    loopNumber = loopNumber + 1;
                                    errorMessage = new System.Text.StringBuilder();
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ff6559da-81f5-481a-95e7-4750034bfb0a")]
                                    dataInizioDb = System.DateTimeOffset.Now;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("79c7ee02-f316-4478-b622-d79e3111f579")]
                                    send (EtrmSettAppContractPriceInsertOut.EtrmSettAppContractPriceInsert, EtrmSettAppContractPriceInsertRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("768c7280-5af8-41fe-87ba-8034d0d0f1d4")]
                                    receive (EtrmSettAppContractPriceInsertOut.EtrmSettAppContractPriceInsert, EtrmSettAppContractPriceInsertResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e7a622a2-1b0d-4e05-b98f-db86b26c4377")]
                                    insertSucceded = true;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b1c3562-684f-4690-8f46-e30064123c50")]
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                                    "Data Inizio DB", dataInizioDb,
                                    "Data Fine DB", System.DateTimeOffset.Now,
                                    "Tipo File", filetype,
                                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("aca0c630-79db-452f-ace1-024278ee5320")]
                                    catch (System.Web.Services.Protocols.SoapException soapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1fe76614-912f-4461-a934-c375b2083e3b")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                        
                                        
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8653145f-6c51-4c02-a94e-d87ff481b403")]
                                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c1b9942-7f21-4b3c-8402-461d8a22a672")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(systemExc.Message);
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                        
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4c6ebd8d-22a8-4ef0-8ddb-8169b73f6b26")]
                            if (insertSucceded)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("6173fa65-b7ad-419e-a867-5c7e0459f77d")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("72afa28d-1e10-43a3-9c9f-bca6d47d6fbf")]
                                delay System.TimeSpan.FromSeconds(30);
                            }
                        }
                    }
                    else if (fileName.StartsWith("SettAppPowerThresholds_") || fileName.StartsWith("SettAppUPSAThresholds_"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("004f10ac-ca4c-4733-8726-1d2d31b3f7d8")]
                        filetype = "SettAppContract";
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("58da350b-c925-4d98-add5-6b11870c4a96")]
                        construct EtrmSettAppPowerThresholdInsertRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ad04e431-8771-44f4-965d-d1f626a1a30e")]
                            transform (EtrmSettAppPowerThresholdInsertRequest.parameters) = A2A.EAI.INT_ETRM.Messaging.Maps.EtrmSettAppFileToEtrmSettAppPowerThresholdInsert (EtrmSettAppFile.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d0b8ec2e-e485-4f01-843f-d46ab1c83ad8")]
                            EtrmSettAppPowerThresholdInsertRequest.parameters.fileName = originalFileName;
                            EtrmSettAppPowerThresholdInsertRequest.parameters.activityId = activityInstanceId;
                            EtrmSettAppPowerThresholdInsertRequest.parameters.reportType = reportType;
                            EtrmSettAppPowerThresholdInsertRequest.parameters.fileCreationTime = fileCreationTime.ToString("yyyy-MM-dd HH:mm:ss.fff");
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("af033aca-7f8b-45b4-ae17-ebcedd0f4e00")]
                        while (loopNumber < 10 && insertSucceded == false)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("20b170c8-4f1a-4217-962f-78fac6761006")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("55ef4ff9-0210-4eef-ad5a-73dbaf2a0e1e")]
                                    loopNumber = loopNumber + 1;
                                    errorMessage = new System.Text.StringBuilder();
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("77f26d39-c3d5-46fe-945a-6998e94db092")]
                                    dataInizioDb = System.DateTimeOffset.Now;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8ff91e0b-7b65-4410-bd00-8937bbb480c9")]
                                    send (EtrmSettAppPowerThresholdInsertOut.EtrmSettAppPowerThresholdInsert, EtrmSettAppPowerThresholdInsertRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d0d952e8-a267-4782-b740-7c769f8feb7f")]
                                    receive (EtrmSettAppPowerThresholdInsertOut.EtrmSettAppPowerThresholdInsert, EtrmSettAppPowerThresholdInsertResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3f69667e-5770-42f4-96d3-19e1a4d4923e")]
                                    insertSucceded = true;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("056f6e27-0b3e-426f-8848-bbde249a1155")]
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
                                    "Data Inizio DB", dataInizioDb,
                                    "Data Fine DB", System.DateTimeOffset.Now,
                                    "Tipo File", filetype,
                                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3a522126-6292-42d8-b7a1-101cd70bb0e2")]
                                    catch (System.Web.Services.Protocols.SoapException soapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f1bf3e0d-df8f-45db-bdf9-43328f0f33e1")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                        
                                        
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2ad1191b-fa51-450d-b1f1-e1393c62ebfb")]
                                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c5201f5e-f58d-4998-9e59-4a74234ce197")]
                                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(systemExc.Message);
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                        
                                    }
                                }
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2698b411-74cb-4b41-bc73-8bec229b6300")]
                            if (insertSucceded)
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("3dcad3b1-bc86-4d09-aed1-175e462ee6af")]
                                resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
                            }
                            else 
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("8d340f0d-0f43-482a-9ad8-4a012df8ede1")]
                                delay System.TimeSpan.FromSeconds(30);
                            }
                        }
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dad62933-b9e7-4de8-acd2-bfb4abe7a63b")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("Tipo di file non riconosciuto"));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6788e306-1056-496e-b6a2-60f2133b6f47")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0a24730c-f3ff-4027-a929-0cf20b7675f0")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9526abe2-6144-448f-b6ab-5d6ebd16fb7c")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1beaace1-4ec5-4ed5-b24c-51471b72be05")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore in fase di elaborazione dati. ", A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName, flowDescription));
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e0fca7c3-d966-41b3-a0d1-369ffa489585")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("8069584a-8147-4234-a9a3-52d19b21b92a")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e26d31a7-5aab-4ce9-909c-98a33bfedb2c")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4599d220-3c8b-4a5d-9d2e-3a6595e56996")]
                            Notification.parameters = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameters.applicationName = A2A.EAI.INT_ETRM.Services.ProcessServices.ApplicationName;
                            Notification.parameters.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameters.flowGroup = A2A.EAI.INT_ETRM.Services.ProcessServices.FlowGroup;
                            Notification.parameters.flowDescription = flowDescription;
                            Notification.parameters.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameters.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameters.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            Notification.parameters.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameters.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2a64983d-f397-4ac1-b46d-a6b09844cc51")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a4fe81f7-b885-45f8-bf46-96151c868a52")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_ETRM.Services.ProcessServices.ActivityNameStaging, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

