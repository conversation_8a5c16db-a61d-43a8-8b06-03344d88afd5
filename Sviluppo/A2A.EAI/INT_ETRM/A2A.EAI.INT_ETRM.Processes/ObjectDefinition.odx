﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="79f1d20c-c5a9-437b-9ba8-541222f7318a" LowerBound="1.1" HigherBound="23.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_ETRM.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="7caa0333-f059-4df0-93d2-6199afd0f4ee" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="b34363d7-a52f-48b7-ac35-c3d52e114e0a" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b6c31d75-697e-4da4-872a-1b8f967f73d2" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_ETRM.Processes.NotificationType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="37706d75-0337-4082-9781-ed0293ad684d" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="74009ebc-6f9e-40f9-8bfb-9ee6a06857c9" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="79112dc6-c2c2-4ac5-bfee-88ed31d0bca2" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="22.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="fd23b99c-7f6a-4448-adab-b4f5a8904ead" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_ETRM.Processes
{
    internal messagetype NotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameters;
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            NotificationType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service ObjectDefinition
    {
        body ()
        {
        }
    }
}

