﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsys.EAI.Framework.Azure.Services;

namespace A2A.EAI.INT_ETRM.Services
{
    [Serializable]
    public class ProcessServices
    {

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_ETRM";

        public const string FlowDescriptionEtrmSettAppStaging = "Etrm Staging";
        public const string FlowDescriptionEtrmSettAppProcess = "Etrm Process";

        public const string ActivityNameStaging = "Etrm Staging";
        public const string ActivityNameProcess = "Etrm Process";

        public static void LogRelatedActivities(string activityid, string allActivityIdStaging) {
            foreach (var activityIdStaging in allActivityIdStaging.Split(',')) { 
                BamHelper.AddRelationship(ActivityNameProcess, activityid,
                    ActivityNameStaging, activityIdStaging, "");
            }

        }

    }
}
