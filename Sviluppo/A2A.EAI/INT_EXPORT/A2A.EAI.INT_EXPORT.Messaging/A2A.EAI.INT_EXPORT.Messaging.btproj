﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21107</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{A2616525-1ED2-4B33-83BA-A82AC892483A}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_EXPORT.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_EXPORT.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <OldToolsVersion>4.0</OldToolsVersion>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'INT_AMBIENTE|AnyCPU'">
    <OutputPath>bin\INT_AMBIENTE\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <CodeAnalysisLogFile>bin\Release\A2A.EAI.INT_EXPORT.Messaging.dll.CodeAnalysisLog.xml</CodeAnalysisLogFile>
    <CodeAnalysisUseTypeNameInSuppression>true</CodeAnalysisUseTypeNameInSuppression>
    <CodeAnalysisModuleSuppressionsFile>GlobalSuppressions.cs</CodeAnalysisModuleSuppressionsFile>
    <ErrorReport>prompt</ErrorReport>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <CodeAnalysisRuleSetDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\\Rule Sets</CodeAnalysisRuleSetDirectories>
    <CodeAnalysisIgnoreBuiltInRuleSets>true</CodeAnalysisIgnoreBuiltInRuleSets>
    <CodeAnalysisRuleDirectories>;C:\Program Files (x86)\Microsoft Visual Studio 10.0\Team Tools\Static Analysis Tools\FxCop\\Rules</CodeAnalysisRuleDirectories>
    <CodeAnalysisIgnoreBuiltInRules>true</CodeAnalysisIgnoreBuiltInRules>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ContextAccessor, Version=*******, Culture=neutral, PublicKeyToken=8c24991755142725, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\Microsoft BizTalk Server 2013\Pipeline Components\Microsys.EAI.Framework.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schErgPowerOutagesV2.xsd">
      <TypeName>schErgPowerOutagesV2</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schErgGediDisponibilitaToGediPollingTypedPolling.xsd">
      <TypeName>schErgGediDisponibilitaToGediPollingTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schErgGediDisponibilitaToGediUpdateStatusTypedProcedure.xsd">
      <TypeName>schErgGediDisponibilitaToGediUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schDisponibilitaErgGediInsertTypedProcedure.Type.xsd">
      <TypeName>schDisponibilitaErgGediInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schDisponibilitaErgGediInsertTypedProcedure.xsd">
      <TypeName>schDisponibilitaErgGediInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Gedi\schOutputGediErg.xsd">
      <SubType>Task</SubType>
      <TypeName>schOutputGediErg</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi</Namespace>
    </Schema>
    <Schema Include="PropertySchema.xsd">
      <TypeName>PropertySchema</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schErgIndisponibilitaToGediUpdateStatusTypedProcedure.xsd">
      <TypeName>schErgIndisponibilitaToGediUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ErgIndisponibilitaToGedi\schErgIndisponibilitaToGediTypedPolling.xsd">
      <TypeName>schErgIndisponibilitaToGediTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2GediXmlPolling.xsd">
      <TypeName>schDisponibilitaN2GediXmlPolling</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2GediXmlPolling.Type.xsd">
      <TypeName>schDisponibilitaN2GediXmlPolling_Type</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2GediInsertStgTypedProcedure.Table.xsd">
      <TypeName>schDisponibilitaN2GediInsertStgTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2GediInsertStgTypedProcedure.xsd">
      <TypeName>schDisponibilitaN2GediInsertStgTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaGedi.xsd">
      <TypeName>schDisponibilitaGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2.xsd">
      <TypeName>schDisponibilitaN2</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\DisponibilitaN2Gedi\schDisponibilitaN2.Types.xsd">
      <TypeName>schDisponibilitaN2_Types</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ExportRdpSorgenia\schExportRdpSorgeniaUpdateStatusTypedProcedure.xsd">
      <TypeName>schExportRdpSorgeniaUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.ExportRdpSorgenia</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Gedi\schOutputGedi.xsd">
      <TypeName>schOutputGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Gedi\schInputGedi.xsd">
      <TypeName>schInputGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Gedi\schInputGedi.Types.xsd">
      <TypeName>schInputGedi_Types</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.5">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.5 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipelines\Arpa\rppExportArpa.btp">
      <TypeName>rppExportArpa</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.Arpa</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipelines\Gedi\sppGedi.btp">
      <TypeName>sppGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Pipeline Include="Pipelines\DisponibilitaN2Gedi\sppDisponibilitaGedi.btp">
      <TypeName>sppDisponibilitaGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\DisponibilitaN2Gedi\mapDisponibilitaXmlPollingToGedi.btm">
      <TypeName>mapDisponibilitaXmlPollingToGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\DisponibilitaN2Gedi\rppDisponibilitaN2.btp">
      <TypeName>rppDisponibilitaN2Gedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\DisponibilitaN2Gedi\mapDisponibilitaN2ToDisponibilitaN2GediInsertStg.btm">
      <TypeName>mapDisponibilitaN2ToDisponibilitaGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.DisponibilitaN2Gedi</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\Gedi\rppGedi.btp">
      <TypeName>rppGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Gedi\mapInputGediToOutputGedi.btm">
      <TypeName>mapInputGediToOutputGedi</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.Gedi</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\ErgIndisponibilitaToGedi\schErgIndisponibilitaToGediTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\ErgIndisponibilitaToGedi\schErgIndisponibilitaToGediUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\ErgIndisponibilitaToGedi\sptDisponibilitaErgGediInsert.bindinginfo.xml" />
    <Content Include="Bindings\ErgIndisponibilitaToGedi\sptErgGediDisponibilitaToGediUpdateStatus.bindinginfo.xml" />
    <Content Include="Bindings\ErgIndisponibilitaToGedi\rptErgGediDisponibilitaToGediPolling.bindinginfo.xml" />
    <Pipeline Include="Pipelines\FermoImpianti\rppFermoImpianti.btp">
      <TypeName>rppFermoImpianti</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.FermoImpianti</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\ErgIndisponibilitaToGedi\mapErgResponseV2ToDisponibilitaErgGediInsert.btm">
      <SubType>Task</SubType>
      <TypeName>mapErgResponseV2ToDisponibilitaErgGediInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi</Namespace>
    </Map>
    <Map Include="Maps\ErgIndisponibilitaToGedi\mapErgIndisponibilitaToGediPollingToErgRequestV2.btm">
      <SubType>Task</SubType>
      <TypeName>mapErgIndisponibilitaToGediPollingToErgRequestV2</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi</Namespace>
    </Map>
    <Pipeline Include="Pipelines\ErgIndisponibilitaToGedi\sppErgPowerOutagesRequest.btp">
      <TypeName>sppErgPowerOutagesRequest</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\ErgIndisponibilitaToGedi\mapErgIndisponibilitaToGediToUpdateStatus.btm">
      <TypeName>mapErgIndisponibilitaToGediToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\ErgIndisponibilitaToGedi\mapErgIndisponibilitaToGediToGediErg.btm">
      <TypeName>mapErgIndisponibilitaToGediToGediErg</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\Gedi\sppGediErg.btp">
      <SubType>Task</SubType>
      <TypeName>sppGediErg</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.Gedi</Namespace>
    </Pipeline>
    <Pipeline Include="Pipelines\ErgIndisponibilitaToGedi\rppErgPowerOutagesResponse.btp">
      <TypeName>rppErgPowerOutagesResponse</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Pipelines.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\ErgIndisponibilitaToGedi\mapErgIndisponibilitaToGediPollingToUpdateStatus.btm">
      <TypeName>mapErgIndisponibilitaToGediPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Shared\A2A.EAI.Common.Shared.btproj">
      <Project>{fe830fb0-b65e-459d-b592-53001be3d7b6}</Project>
      <Name>A2A.EAI.Common.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>