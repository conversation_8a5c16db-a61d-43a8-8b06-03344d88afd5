﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="7ccc56a5-05e0-45bc-b144-8c847cadb652" LowerBound="1.1" HigherBound="157.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="b3768ea3-2b06-4801-8216-1ac7c18d9704" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="rptDisponibilitaN2GediPollingStgType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ce1beeaf-a0f8-4a0a-aba4-f9fd6f118e03" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="10ac1ee0-ac69-42d5-ae3c-fe06cabef1b9" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.49">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaN2GediXmlPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="0abdd1a6-1837-45f9-8006-47c6c2bea906" ParentLink="Module_PortType" LowerBound="19.1" HigherBound="26.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptDisponibilitaGediType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="12af8b8a-2011-47fc-975d-f3646aabfec9" ParentLink="PortType_OperationDeclaration" LowerBound="21.1" HigherBound="25.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="53815d63-6fcf-40d9-9f52-58284834da03" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="23.13" HigherBound="23.37">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaGediType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b0a487b2-ba14-473d-8b82-294f47068bc9" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgDisponibilitaN2GediXmlPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="8a6b804e-56ab-4db4-be5a-5c9299ab29b8" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi.schDisponibilitaN2GediXmlPolling_Type" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="9da8797c-46e5-4475-b005-9bdc19e0f315" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgDisponibilitaGediType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e2f241c7-b007-4edc-994b-ebdbe754e3fd" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi.schDisponibilitaGedi" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="2300802a-9af1-48e0-ad7e-b325170082d1" ParentLink="Module_ServiceDeclaration" LowerBound="26.1" HigherBound="156.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcDisponibilitaN2GediProcess" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="23b76e2f-ff64-47d3-9e58-e48e99fffc68" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="957bd797-4858-4db1-8265-f12da0ae42d6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="03037f4e-f995-4f40-acfb-f068289fd3b7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5e4b7f46-3d43-46f5-9446-43bc94ede237" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cfac07ae-36e2-487d-91ef-67ed65149180" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9202c155-a889-4a00-b1f1-b700720cb5eb" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5bd3a740-1e17-4699-889b-3f10be974a52" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="729ff496-28ff-4761-ae32-a5e9af415c49" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaN2GediXmlPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDisponibilitaN2GediXmlPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f7ac441f-0290-4fd0-bd9e-aa3e9d73eb7b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaGediType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDisponibilitaGedi" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="47f8d5fa-30fd-470b-8b51-e58bf2d1b47d" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="006e1634-292f-43fd-a0f6-e20bad230e6d" ParentLink="ServiceBody_Statement" LowerBound="47.1" HigherBound="51.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptDisponibilitaN2GediPollingStg" />
                    <om:Property Name="MessageName" Value="msgDisponibilitaN2GediXmlPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="fd095861-feb6-4259-a4c3-3bd612fb72e5" ParentLink="ServiceBody_Statement" LowerBound="51.1" HigherBound="58.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0ad8f5a0-1834-48b6-8505-a45fb81aae13" ParentLink="ServiceBody_Statement" LowerBound="58.1" HigherBound="69.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(msgDisponibilitaN2GediXmlPolling.parameter.fileName),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, &quot;Disponibilità N2 To Gedi Process&quot;&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="5f0a6a9a-a49f-42f6-8f94-3bf96fb36090" ParentLink="ServiceBody_Statement" LowerBound="69.1" HigherBound="113.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="fe092ab5-265b-47ea-a15c-36900a3f6ffe" ParentLink="ComplexStatement_Statement" LowerBound="74.1" HigherBound="82.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Output File" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="1d431e17-20fd-4c7d-9ba8-a0e23215b0a6" ParentLink="ComplexStatement_Statement" LowerBound="77.1" HigherBound="79.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.DisponibilitaN2Gedi.mapDisponibilitaXmlPollingToGedi" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Output File" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="cbb39a21-8690-49a4-b20e-bf222e7ced31" ParentLink="Transform_InputMessagePartRef" LowerBound="78.159" HigherBound="78.201">
                                <om:Property Name="MessageRef" Value="msgDisponibilitaN2GediXmlPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="37dcf46f-bafb-4277-8948-fa959ce2dd5c" ParentLink="Transform_OutputMessagePartRef" LowerBound="78.36" HigherBound="78.66">
                                <om:Property Name="MessageRef" Value="msgDisponibilitaGedi" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="f736597f-e081-479c-be67-d86226760869" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="81.1">
                            <om:Property Name="Expression" Value="msgDisponibilitaGedi(FILE.ReceivedFileName) = System.IO.Path.GetFileNameWithoutExtension(msgDisponibilitaN2GediXmlPolling.parameter.fileName) + &quot;.csv&quot;;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="File Name" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="fe08207d-f2b2-4eff-bbb0-69731d7185a9" ParentLink="Construct_MessageRef" LowerBound="75.31" HigherBound="75.51">
                            <om:Property Name="Ref" Value="msgDisponibilitaGedi" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="5393eaa8-2e7d-4932-95d4-e7489c6b25b1" ParentLink="ComplexStatement_Statement" LowerBound="82.1" HigherBound="84.1">
                        <om:Property Name="PortName" Value="sptDisponibilitaGedi" />
                        <om:Property Name="MessageName" Value="msgDisponibilitaGedi" />
                        <om:Property Name="OperationName" Value="Send" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="2ee6670b-50a5-4b7a-912f-d37eb9373bc6" ParentLink="Scope_Catch" LowerBound="87.1" HigherBound="100.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="deliveryException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="2eb75665-2a8a-4f4f-bf88-2c74716e21ff" ParentLink="Catch_Statement" LowerBound="90.1" HigherBound="99.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la scrittura delle disponibilità N2 verso GEDI.&quot;);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="60b82c1f-72eb-4239-98f6-dddea328d976" ParentLink="Scope_Catch" LowerBound="100.1" HigherBound="111.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="bd30ca4f-f707-43f9-85e3-31ed858ace75" ParentLink="Catch_Statement" LowerBound="103.1" HigherBound="110.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la scrittura delle disponibilità N2 verso GEDI.&quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="6d40572c-79e4-41a7-a9e2-8e0cf3127e85" ParentLink="ServiceBody_Statement" LowerBound="113.1" HigherBound="146.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="fe2b56fc-4433-4059-8983-354782888d88" ParentLink="ReallyComplexStatement_Branch" LowerBound="114.13" HigherBound="117.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="a8c8da0a-d8f4-4717-8818-58f12ff4200e" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="75b3b5f6-8d90-430e-909a-9a4bae73363c" ParentLink="ComplexStatement_Statement" LowerBound="119.1" HigherBound="145.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="12d347fb-b923-4c99-a8cd-01820f64518e" ParentLink="ComplexStatement_Statement" LowerBound="124.1" HigherBound="141.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="629cd75f-f32f-4292-bdf3-6cfe11b0818a" ParentLink="ComplexStatement_Statement" LowerBound="127.1" HigherBound="140.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionDisponibilitaN2Gedi;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="9231e4e3-59f7-4365-9614-b2e278ba3161" ParentLink="Construct_MessageRef" LowerBound="125.35" HigherBound="125.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="7033dc3f-fda2-4cc6-b69d-f2edadca027a" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="143.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="206c8db3-29f8-4c9b-9c78-61ab1fe762dd" ParentLink="ServiceBody_Statement" LowerBound="146.1" HigherBound="154.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8fb43474-12ed-4fc3-b771-93b77d06fb83" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="29.1" HigherBound="31.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.rptDisponibilitaN2GediPollingStgType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptDisponibilitaN2GediPollingStg" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="4ed59d8a-c57e-4372-a2d1-77ac097cd490" ParentLink="PortDeclaration_CLRAttribute" LowerBound="29.1" HigherBound="30.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8b209575-8c04-4fe6-9939-665479b9bef6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="31.1" HigherBound="34.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="22" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.sptDisponibilitaGediType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptDisponibilitaGedi" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="67a16abb-a7ae-4361-a8d4-3ddcfd66a79c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="31.1" HigherBound="32.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="d0ec3dd6-8914-4b9a-b1d6-219e3cb802d3" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="34.1" HigherBound="36.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="78" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="b7f09ff4-26e6-46f6-bb0c-bd8594fde473" ParentLink="PortDeclaration_CLRAttribute" LowerBound="34.1" HigherBound="35.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal messagetype msgDisponibilitaN2GediXmlPollingType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi.schDisponibilitaN2GediXmlPolling_Type parameter;
    };
    internal messagetype msgDisponibilitaGediType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.DisponibilitaN2Gedi.schDisponibilitaGedi parameter;
    };
    internal porttype rptDisponibilitaN2GediPollingStgType
    {
        oneway Receive
        {
            msgDisponibilitaN2GediXmlPollingType
        };
    };
    internal porttype sptDisponibilitaGediType
    {
        oneway Send
        {
            msgDisponibilitaGediType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcDisponibilitaN2GediProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements rptDisponibilitaN2GediPollingStgType rptDisponibilitaN2GediPollingStg;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptDisponibilitaGediType sptDisponibilitaGedi;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgDisponibilitaN2GediXmlPollingType msgDisponibilitaN2GediXmlPolling;
        message msgDisponibilitaGediType msgDisponibilitaGedi;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("006e1634-292f-43fd-a0f6-e20bad230e6d")]
            activate receive (rptDisponibilitaN2GediPollingStg.Receive, msgDisponibilitaN2GediXmlPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fd095861-feb6-4259-a4c3-3bd612fb72e5")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ad8f5a0-1834-48b6-8505-a45fb81aae13")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(msgDisponibilitaN2GediXmlPolling.parameter.fileName),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", "Disponibilità N2 To Gedi Process"
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f0a6a9a-a49f-42f6-8f94-3bf96fb36090")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fe092ab5-265b-47ea-a15c-36900a3f6ffe")]
                    construct msgDisponibilitaGedi
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1d431e17-20fd-4c7d-9ba8-a0e23215b0a6")]
                        transform (msgDisponibilitaGedi.parameter) = A2A.EAI.INT_EXPORT.Messaging.Maps.DisponibilitaN2Gedi.mapDisponibilitaXmlPollingToGedi (msgDisponibilitaN2GediXmlPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f736597f-e081-479c-be67-d86226760869")]
                        msgDisponibilitaGedi(FILE.ReceivedFileName) = System.IO.Path.GetFileNameWithoutExtension(msgDisponibilitaN2GediXmlPolling.parameter.fileName) + ".csv";
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5393eaa8-2e7d-4932-95d4-e7489c6b25b1")]
                    send (sptDisponibilitaGedi.Send, msgDisponibilitaGedi);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2ee6670b-50a5-4b7a-912f-d37eb9373bc6")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2eb75665-2a8a-4f4f-bf88-2c74716e21ff")]
                        errorMessage.Append("Errore durante la scrittura delle disponibilità N2 verso GEDI.");
                        errorMessage.Append(deliveryExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("60b82c1f-72eb-4239-98f6-dddea328d976")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bd30ca4f-f707-43f9-85e3-31ed858ace75")]
                        errorMessage.Append("Errore durante la scrittura delle disponibilità N2 verso GEDI.");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d40572c-79e4-41a7-a9e2-8e0cf3127e85")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("75b3b5f6-8d90-430e-909a-9a4bae73363c")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("12d347fb-b923-4c99-a8cd-01820f64518e")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("629cd75f-f32f-4292-bdf3-6cfe11b0818a")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionDisponibilitaN2Gedi;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7033dc3f-fda2-4cc6-b69d-f2edadca027a")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("206c8db3-29f8-4c9b-9c78-61ab1fe762dd")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

