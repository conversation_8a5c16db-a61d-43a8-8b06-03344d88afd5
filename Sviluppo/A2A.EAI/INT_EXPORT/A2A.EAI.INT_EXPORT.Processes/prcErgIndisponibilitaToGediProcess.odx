﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="776d7774-c376-463d-a6e5-90ea642c6d46" LowerBound="1.1" HigherBound="288.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="4ca99bac-bbb0-4b78-aa0f-a395de263757" ParentLink="Module_ServiceDeclaration" LowerBound="41.1" HigherBound="287.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcErgIndisponibilitaToGediProcess" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="9daa370e-1247-47ed-b40a-a6e92fae52dc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4a0cfef5-9e4e-41c0-bce1-feda7470d2ab" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6bedbd51-2c95-4141-9515-2e099647db41" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5198b858-a921-4529-b80e-3cb4a56fb8bf" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="40963f09-6073-4d86-8492-8c1c2839e99a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2b9c9db2-3fc5-4d7f-b63b-85f2878159d3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6f166d5d-9b8b-46ac-90de-c2fb33728914" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="attachFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7b002a5f-2142-4366-ae04-aba0f18ec175" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="60d975ee-0322-4e9d-a72b-9fa5856329c3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3a7d527b-94f9-4022-842b-4a48ac2d052f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="attachFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3db0ada4-f323-459b-b25a-c03d7e9e6a71" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgGediErgType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgOutputGediErg" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="7f44fc0c-aff0-4659-b7f2-b062628a6145" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="42403751-6c91-40f1-92c9-6c7da460a767" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="fab1615c-0291-4e06-aa19-8014286642ae" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="3a56f05d-67bf-4bdc-a171-1fe964289ba6" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="853e5453-c2ea-4ee0-a8c8-8565fdb67b29" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="f4096c73-1925-4db5-beff-3f664fa30980" ParentLink="ServiceBody_Statement" LowerBound="71.1" HigherBound="78.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptErgGediDisponibilitaToGediPolling" />
                    <om:Property Name="MessageName" Value="msgErgGediDisponibilitaToGediPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Receive" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="387809ae-5e34-45c6-abf3-6bb01be2a4ac" ParentLink="ServiceBody_Statement" LowerBound="78.1" HigherBound="91.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;flowName = msgErgGediDisponibilitaToGediPolling.parameters.TypedPollingResultSet1.TypedPollingResultSet1.flowName;&#xD;&#xA;attachFileName = System.String.Concat(&quot;meteologica_availability_&quot;, System.DateTimeOffset.Now.ToString(&quot;yyyyMMddHHmmss&quot;), &quot;.csv&quot;);&#xD;&#xA;attachFilePath = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(&quot;A2A.EAI&quot;, &quot;ergIndisponibilitaMailAttachPath&quot;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5e045918-8814-43db-9a14-30b825fab76d" ParentLink="ServiceBody_Statement" LowerBound="91.1" HigherBound="101.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, flowName,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;Invio mail&quot;),&#xD;&#xA;&quot;Nome File&quot;, attachFileName&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="a6bf9798-5693-4e4b-8320-16bd93a57866" ParentLink="ServiceBody_Statement" LowerBound="101.1" HigherBound="183.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Create and Send Attach" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="bab2c358-f7f4-4b4a-b630-af642d0937e0" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="112.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create CSV" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="011af151-66de-4038-b039-0821ebf0f7da" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="111.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediToGediErg" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create CSV" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="86d88c5e-6d20-4ac8-a45a-3a11b25a374c" ParentLink="Transform_InputMessagePartRef" LowerBound="110.164" HigherBound="110.211">
                                <om:Property Name="MessageRef" Value="msgErgGediDisponibilitaToGediPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="0fc5b9ef-e1fc-4cd8-9b6f-67a21dd22bdd" ParentLink="Transform_OutputMessagePartRef" LowerBound="110.36" HigherBound="110.63">
                                <om:Property Name="MessageRef" Value="msgOutputGediErg" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="c0fdfbcc-d1ac-4eb4-b221-6e13a1d53b1e" ParentLink="Construct_MessageRef" LowerBound="107.31" HigherBound="107.47">
                            <om:Property Name="Ref" Value="msgOutputGediErg" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="9d65d2bb-cdf2-430e-a76c-217a9ddc4c27" ParentLink="ComplexStatement_Statement" LowerBound="112.1" HigherBound="116.1">
                        <om:Property Name="Expression" Value="sptGediOutputErgMail(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(attachFilePath, attachFileName);&#xD;&#xA;sptGediOutputErgMail(Microsoft.XLANGs.BaseTypes.TransportType) = &quot;FILE&quot;;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Port Address" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="dcb9bb1d-65c9-4af6-a29c-245b4f6c5a2e" ParentLink="ComplexStatement_Statement" LowerBound="116.1" HigherBound="118.1">
                        <om:Property Name="PortName" Value="sptGediOutputErgMail" />
                        <om:Property Name="MessageName" Value="msgOutputGediErg" />
                        <om:Property Name="OperationName" Value="Send" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="e3232f11-53b6-4f08-a5c2-f27e7e5e7258" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="137.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Mail Attach" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="f7667b2d-8669-4c5b-9fed-afd3c0e386c8" ParentLink="Construct_MessageRef" LowerBound="119.31" HigherBound="119.46">
                            <om:Property Name="Ref" Value="msgNotification" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="fc254570-ab8b-4954-ba98-79a82548cc79" ParentLink="ComplexStatement_Statement" LowerBound="121.1" HigherBound="136.1">
                            <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = &quot;meteologica_availability&quot;;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();&#xD;&#xA;msgNotification.messageText = System.String.Concat(&quot;E' stata rilevata una Nuova indisponibilità per l' impianto &quot;, msgErgGediDisponibilitaToGediPolling.parameters.TypedPollingResultSet1.TypedPollingResultSet1.up);&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;msgNotification.attachment = System.IO.Path.Combine(attachFilePath, attachFileName);&#xD;&#xA;msgNotification.emailAddress = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(&quot;A2A.EAI&quot;, &quot;ergIndisponibilitaMailTo&quot;);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Create Attach" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="6b75c959-ab2a-4a2b-9e1c-58145657ab09" ParentLink="ComplexStatement_Statement" LowerBound="137.1" HigherBound="139.1">
                        <om:Property Name="PortName" Value="sptNotification" />
                        <om:Property Name="MessageName" Value="msgNotification" />
                        <om:Property Name="OperationName" Value="Send" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="6d442126-bf05-47fb-b378-6dee53f1c5cd" ParentLink="Scope_Catch" LowerBound="142.1" HigherBound="155.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="aacad534-abd8-466c-b226-f4602031385c" ParentLink="Catch_Statement" LowerBound="145.1" HigherBound="154.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'invio indisponibilità ERG a GEDI via mail.&quot;);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="c45311a2-54a0-4e3c-b6c5-db9464b9bad1" ParentLink="Scope_Catch" LowerBound="155.1" HigherBound="168.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f55d9de8-8b44-46d7-924f-0afe109ebe4f" ParentLink="Catch_Statement" LowerBound="158.1" HigherBound="167.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'invio indisponibilità ERG a GEDI via mail.&quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f7803271-004f-4120-8e60-28f31b17fc88" ParentLink="Scope_Catch" LowerBound="168.1" HigherBound="181.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="a6202b3e-359b-489f-8841-6ff228ba5abd" ParentLink="Catch_Statement" LowerBound="171.1" HigherBound="180.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'invio indisponibilità ERG a GEDI via mail.&quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="2e95c368-69ce-41c0-bcc3-0fea8aeecea3" ParentLink="ServiceBody_Statement" LowerBound="183.1" HigherBound="240.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="7498b5e0-3bb7-4c7d-a169-226cdfeafe0d" ParentLink="ComplexStatement_Statement" LowerBound="188.1" HigherBound="199.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="0cc79acf-fdf7-4002-909a-237440de4825" ParentLink="Construct_MessageRef" LowerBound="189.31" HigherBound="189.79">
                            <om:Property Name="Ref" Value="msgErgGediDisponibilitaToGediUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="90cef3c2-de10-4813-932f-37ab31f3d778" ParentLink="ComplexStatement_Statement" LowerBound="191.1" HigherBound="193.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="2390e2fc-11eb-4ee7-a0fb-3092cc823069" ParentLink="Transform_InputMessagePartRef" LowerBound="192.201" HigherBound="192.248">
                                <om:Property Name="MessageRef" Value="msgErgGediDisponibilitaToGediPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="1f38e554-03b0-49ae-9e32-0d43e79b38bc" ParentLink="Transform_OutputMessagePartRef" LowerBound="192.36" HigherBound="192.95">
                                <om:Property Name="MessageRef" Value="msgErgGediDisponibilitaToGediUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="a42d624e-290e-4927-9bf9-856543a9f75a" ParentLink="ComplexStatement_Statement" LowerBound="193.1" HigherBound="198.1">
                            <om:Property Name="Expression" Value="msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.activityId = activityInstanceId;&#xD;&#xA;msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="1bdff9e2-c6ee-4391-9356-2425a8df6530" ParentLink="ComplexStatement_Statement" LowerBound="199.1" HigherBound="201.1">
                        <om:Property Name="PortName" Value="sptErgGediDisponibilitaToGediUpdateStatus" />
                        <om:Property Name="MessageName" Value="msgErgGediDisponibilitaToGediUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ErgGediDisponibilitaToGediUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="c635b3ac-5411-4fef-8a67-a17d561b1df9" ParentLink="ComplexStatement_Statement" LowerBound="201.1" HigherBound="203.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptErgGediDisponibilitaToGediUpdateStatus" />
                        <om:Property Name="MessageName" Value="msgErgGediDisponibilitaToGediUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ErgGediDisponibilitaToGediUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="6eae8d2a-5e14-4147-8931-f412161c717a" ParentLink="ComplexStatement_Statement" LowerBound="203.1" HigherBound="207.1">
                        <om:Property Name="Expression" Value="sendNotification = msgErgGediDisponibilitaToGediUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="f79b5607-3797-439d-88e8-15be9e91c9b8" ParentLink="Scope_Catch" LowerBound="210.1" HigherBound="224.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d265eff2-c680-451d-b0de-294715245ca5" ParentLink="Catch_Statement" LowerBound="213.1" HigherBound="223.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, flowName));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="fde85c4c-80e5-45b9-8182-9ffcf9bec0bb" ParentLink="Scope_Catch" LowerBound="224.1" HigherBound="238.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="53edc7b9-e724-44e8-904a-ec1d7ac1cd20" ParentLink="Catch_Statement" LowerBound="227.1" HigherBound="237.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, flowName));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="b817069d-f36a-42ec-b82c-455214ad746b" ParentLink="ServiceBody_Statement" LowerBound="240.1" HigherBound="277.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="b78ff66b-502c-4f0e-83cc-50f071db3241" ParentLink="ReallyComplexStatement_Branch" LowerBound="241.13" HigherBound="244.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="4d0e780a-e3d0-48ed-b08d-7ee8cf3894a6" ParentLink="ReallyComplexStatement_Branch" LowerBound="244.18" HigherBound="247.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="0aaf4e42-4494-42a5-9393-9f720307d7e4" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="d8638e37-840c-4288-9648-d0719c344604" ParentLink="ComplexStatement_Statement" LowerBound="249.1" HigherBound="276.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="dcb576d4-d63c-4fa5-b087-ada070765bbc" ParentLink="ComplexStatement_Statement" LowerBound="254.1" HigherBound="272.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="2d6c83d7-3230-4f08-9594-88e21498a0c9" ParentLink="Construct_MessageRef" LowerBound="255.35" HigherBound="255.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="1a326a61-39a4-48ca-9200-a5362db6264d" ParentLink="ComplexStatement_Statement" LowerBound="257.1" HigherBound="271.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = flowName;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;msgNotification.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;msgNotification.messageNotes = &quot;BizTalk Instance ID: &quot; + activityInstanceId;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="714f7710-e6c7-4741-98ef-455577b06413" ParentLink="ComplexStatement_Statement" LowerBound="272.1" HigherBound="274.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e9ccaa0d-fbd3-4cdd-9135-054e844fbb42" ParentLink="ServiceBody_Statement" LowerBound="277.1" HigherBound="285.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="333b1153-17dc-4f80-a579-f646385a14d8" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="44.1" HigherBound="46.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.rptErgGediDisponibilitaToGediPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptErgGediDisponibilitaToGediPolling" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="a8e5005d-0d30-4b8b-8e15-77bee43f1f32" ParentLink="PortDeclaration_CLRAttribute" LowerBound="44.1" HigherBound="45.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9c52e18b-7bbb-4e4b-b939-fa9666b0e66d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="46.1" HigherBound="49.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="98" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.sptErgGediDisponibilitaToGediUpdateStatusType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptErgGediDisponibilitaToGediUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="9408bf7b-adb4-432e-9f5d-4a5ee52b61ca" ParentLink="PortDeclaration_CLRAttribute" LowerBound="46.1" HigherBound="47.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7de21f27-da62-4560-a589-1e06815d1ea6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="49.1" HigherBound="51.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="155" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="58cf7e79-7fdf-45c3-83a0-78cc244bb056" ParentLink="PortDeclaration_CLRAttribute" LowerBound="49.1" HigherBound="50.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b9e7e1ca-08e0-4742-b55b-f5ca8e66b268" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="51.1" HigherBound="54.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="33" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.sptGediOutputErgMailType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptGediOutputErgMail" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="PhysicalBindingAttribute" OID="3b5e225f-2c9a-4cc2-9a11-f3350ff4aeab" ParentLink="PortDeclaration_CLRAttribute" LowerBound="51.1" HigherBound="52.1">
                    <om:Property Name="InPipeline" Value="Microsoft.BizTalk.DefaultPipelines.XMLReceive" />
                    <om:Property Name="OutPipeline" Value="A2A.EAI.INT_EXPORT.Messaging.Pipelines.Gedi.sppGediErg" />
                    <om:Property Name="TransportType" Value="HTTP" />
                    <om:Property Name="URI" Value="http://tempURI" />
                    <om:Property Name="IsDynamic" Value="True" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="251eec61-0551-4b06-9092-763e18183b63" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e59eefb9-2a9f-443d-aeaa-6e135664ac89" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediPollingTypedPolling.TypedPolling" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="523a37c4-7e52-473a-bd32-73c9b7b33ebd" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4e4be7d4-74d4-4e9c-963c-2cb7dc631cad" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediUpdateStatusTypedProcedure.ErgGediDisponibilitaToGediUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="cb69f5cc-a6d8-4e87-84a5-e2090c2f5391" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgGediDisponibilitaToGediUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="77c2bfd8-40ca-47a9-8249-9709e4330395" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediUpdateStatusTypedProcedure.ErgGediDisponibilitaToGediUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="f897d6d1-fcf4-4e97-95a8-c074a3ed4f7d" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgGediErgType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="61e1bb45-c6e0-491c-a0cb-6485746279dd" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schOutputGediErg" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="33bd420b-f67b-415f-8879-10e23c1b5a16" ParentLink="Module_PortType" LowerBound="20.1" HigherBound="27.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="rptErgGediDisponibilitaToGediPollingType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9a9851bb-79af-4e97-ba7a-37a5518f932c" ParentLink="PortType_OperationDeclaration" LowerBound="22.1" HigherBound="26.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="817d1b5c-692b-4190-a55f-bf6336006592" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="24.13" HigherBound="24.53">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="fb8f7760-c419-4ece-a56a-7b785f35722c" ParentLink="Module_PortType" LowerBound="27.1" HigherBound="34.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptErgGediDisponibilitaToGediUpdateStatusType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="11c4305d-0b87-44d2-9582-5d97a2d8e830" ParentLink="PortType_OperationDeclaration" LowerBound="29.1" HigherBound="33.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ErgGediDisponibilitaToGediUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9f795b01-3190-4518-a73d-123ba006d3c7" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="31.13" HigherBound="31.65">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="decdebe5-726f-443f-8097-e4b6d3dc5a5f" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="31.67" HigherBound="31.120">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgGediDisponibilitaToGediUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="b5db8eab-2e23-4894-a9e6-82f0e2853d41" ParentLink="Module_PortType" LowerBound="34.1" HigherBound="41.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptGediOutputErgMailType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="4844e44a-082e-46e6-8d69-88f659f70bc9" ParentLink="PortType_OperationDeclaration" LowerBound="36.1" HigherBound="40.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="f1bc37e6-a210-4d06-9103-9eadb08b821d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="38.13" HigherBound="38.27">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgGediErgType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal messagetype msgErgGediDisponibilitaToGediPollingType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediPollingTypedPolling.TypedPolling parameters;
    };
    internal messagetype msgErgGediDisponibilitaToGediUpdateStatusRequestType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediUpdateStatusTypedProcedure.ErgGediDisponibilitaToGediUpdateStatus parameters;
    };
    internal messagetype msgErgGediDisponibilitaToGediUpdateStatusResponseType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgGediDisponibilitaToGediUpdateStatusTypedProcedure.ErgGediDisponibilitaToGediUpdateStatusResponse parameters;
    };
    internal messagetype msgGediErgType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schOutputGediErg parameters;
    };
    internal porttype rptErgGediDisponibilitaToGediPollingType
    {
        oneway Receive
        {
            msgErgGediDisponibilitaToGediPollingType
        };
    };
    internal porttype sptErgGediDisponibilitaToGediUpdateStatusType
    {
        requestresponse ErgGediDisponibilitaToGediUpdateStatus
        {
            msgErgGediDisponibilitaToGediUpdateStatusRequestType, msgErgGediDisponibilitaToGediUpdateStatusResponseType
        };
    };
    internal porttype sptGediOutputErgMailType
    {
        oneway Send
        {
            msgGediErgType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcErgIndisponibilitaToGediProcess
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements rptErgGediDisponibilitaToGediPollingType rptErgGediDisponibilitaToGediPolling;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptErgGediDisponibilitaToGediUpdateStatusType sptErgGediDisponibilitaToGediUpdateStatus;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.PhysicalBinding(typeof(A2A.EAI.INT_EXPORT.Messaging.Pipelines.Gedi.sppGediErg))]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses dynamic sptGediOutputErgMailType sptGediOutputErgMail;
        message msgGediErgType msgOutputGediErg;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgErgGediDisponibilitaToGediPollingType msgErgGediDisponibilitaToGediPolling;
        message msgErgGediDisponibilitaToGediUpdateStatusRequestType msgErgGediDisponibilitaToGediUpdateStatusRequest;
        message msgErgGediDisponibilitaToGediUpdateStatusResponseType msgErgGediDisponibilitaToGediUpdateStatusResponse;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String attachFileName;
        System.String activityInstanceId;
        System.String flowName;
        System.String attachFilePath;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f4096c73-1925-4db5-beff-3f664fa30980")]
            activate receive (rptErgGediDisponibilitaToGediPolling.Receive, msgErgGediDisponibilitaToGediPolling);
            errorMessage = new System.Text.StringBuilder();
            attachFileName = "";
            activityInstanceId = "";
            flowName = "";
            attachFilePath = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("387809ae-5e34-45c6-abf3-6bb01be2a4ac")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            sendNotification = 0;
            
            flowName = msgErgGediDisponibilitaToGediPolling.parameters.TypedPollingResultSet1.TypedPollingResultSet1.flowName;
            attachFileName = System.String.Concat("meteologica_availability_", System.DateTimeOffset.Now.ToString("yyyyMMddHHmmss"), ".csv");
            attachFilePath = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue("A2A.EAI", "ergIndisponibilitaMailAttachPath");
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5e045918-8814-43db-9a14-30b825fab76d")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId,
            "Flusso", flowName,
            "Note", System.String.Concat("Invio mail"),
            "Nome File", attachFileName
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6bf9798-5693-4e4b-8320-16bd93a57866")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("bab2c358-f7f4-4b4a-b630-af642d0937e0")]
                    construct msgOutputGediErg
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("011af151-66de-4038-b039-0821ebf0f7da")]
                        transform (msgOutputGediErg.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediToGediErg (msgErgGediDisponibilitaToGediPolling.parameters);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d65d2bb-cdf2-430e-a76c-217a9ddc4c27")]
                    sptGediOutputErgMail(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(attachFilePath, attachFileName);
                    sptGediOutputErgMail(Microsoft.XLANGs.BaseTypes.TransportType) = "FILE";
                    
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("dcb9bb1d-65c9-4af6-a29c-245b4f6c5a2e")]
                    send (sptGediOutputErgMail.Send, msgOutputGediErg);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3232f11-53b6-4f08-a5c2-f27e7e5e7258")]
                    construct msgNotification
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fc254570-ab8b-4954-ba98-79a82548cc79")]
                        msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                        
                        msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                        msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                        msgNotification.flowGroup = "meteologica_availability";
                        msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                        msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                        msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();
                        msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();
                        msgNotification.messageText = System.String.Concat("E' stata rilevata una Nuova indisponibilità per l' impianto ", msgErgGediDisponibilitaToGediPolling.parameters.TypedPollingResultSet1.TypedPollingResultSet1.up);
                        msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                        msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        msgNotification.attachment = System.IO.Path.Combine(attachFilePath, attachFileName);
                        msgNotification.emailAddress = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue("A2A.EAI", "ergIndisponibilitaMailTo");
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6b75c959-ab2a-4a2b-9e1c-58145657ab09")]
                    send (sptNotification.Send, msgNotification);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d442126-bf05-47fb-b378-6dee53f1c5cd")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("aacad534-abd8-466c-b226-f4602031385c")]
                        errorMessage.Append("Errore durante l'invio indisponibilità ERG a GEDI via mail.");
                        errorMessage.Append(deliveryExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c45311a2-54a0-4e3c-b6c5-db9464b9bad1")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f55d9de8-8b44-46d7-924f-0afe109ebe4f")]
                        errorMessage.Append("Errore durante l'invio indisponibilità ERG a GEDI via mail.");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f7803271-004f-4120-8e60-28f31b17fc88")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a6202b3e-359b-489f-8841-6ff228ba5abd")]
                        errorMessage.Append("Errore durante l'invio indisponibilità ERG a GEDI via mail.");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2e95c368-69ce-41c0-bcc3-0fea8aeecea3")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7498b5e0-3bb7-4c7d-a169-226cdfeafe0d")]
                    construct msgErgGediDisponibilitaToGediUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("90cef3c2-de10-4813-932f-37ab31f3d778")]
                        transform (msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediToUpdateStatus (msgErgGediDisponibilitaToGediPolling.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a42d624e-290e-4927-9bf9-856543a9f75a")]
                        msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                        msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.activityId = activityInstanceId;
                        msgErgGediDisponibilitaToGediUpdateStatusRequest.parameters.errorMessage = errorMessage.ToString();
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1bdff9e2-c6ee-4391-9356-2425a8df6530")]
                    send (sptErgGediDisponibilitaToGediUpdateStatus.ErgGediDisponibilitaToGediUpdateStatus, msgErgGediDisponibilitaToGediUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c635b3ac-5411-4fef-8a67-a17d561b1df9")]
                    receive (sptErgGediDisponibilitaToGediUpdateStatus.ErgGediDisponibilitaToGediUpdateStatus, msgErgGediDisponibilitaToGediUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6eae8d2a-5e14-4147-8931-f412161c717a")]
                    sendNotification = msgErgGediDisponibilitaToGediUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f79b5607-3797-439d-88e8-15be9e91c9b8")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d265eff2-c680-451d-b0de-294715245ca5")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, flowName));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fde85c4c-80e5-45b9-8182-9ffcf9bec0bb")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("53edc7b9-e724-44e8-904a-ec1d7ac1cd20")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, flowName));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b817069d-f36a-42ec-b82c-455214ad746b")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8638e37-840c-4288-9648-d0719c344604")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dcb576d4-d63c-4fa5-b087-ada070765bbc")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1a326a61-39a4-48ca-9200-a5362db6264d")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = flowName;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            msgNotification.messageText = errorMessage.ToString(0, System.Math.Min(errorMessage.Length, 8000));
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            msgNotification.messageNotes = "BizTalk Instance ID: " + activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("714f7710-e6c7-4741-98ef-455577b06413")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e9ccaa0d-fbd3-4cdd-9135-054e844fbb42")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

