﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="69f5787d-2147-4486-ba30-ec0df5f0f033" LowerBound="1.1" HigherBound="303.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="ed17184e-9b6a-4a66-be53-3462d23c50a2" ParentLink="Module_PortType" LowerBound="32.1" HigherBound="39.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typErgIndisponibilitaToGediPolling" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="4f72cb69-8165-4867-9f3a-1e94383d8610" ParentLink="PortType_OperationDeclaration" LowerBound="34.1" HigherBound="38.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9a4c4c5a-5735-4c17-a473-d5595e99f1df" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="36.13" HigherBound="36.51">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="13b63afa-6e8e-4322-8983-aba8c004f0e0" ParentLink="Module_PortType" LowerBound="39.1" HigherBound="46.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typErgIndisponibilitaToGediUpdateStatus" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3b2e1504-e59e-4ddd-b904-8eb72b288226" ParentLink="PortType_OperationDeclaration" LowerBound="41.1" HigherBound="45.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ErgIndisponibilitaToGediUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="a21da0b3-551d-4a4a-b16e-5431cb209c99" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="43.13" HigherBound="43.63">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="4d85a29a-759b-4459-9a04-31ea481f59d3" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="43.65" HigherBound="43.116">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="808e8daf-d4b8-4bb5-976b-84cd350e6224" ParentLink="Module_PortType" LowerBound="46.1" HigherBound="53.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptNotificationWithAttachType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="980e9343-bd63-4759-9dce-027141e0ccc8" ParentLink="PortType_OperationDeclaration" LowerBound="48.1" HigherBound="52.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="a654bfbd-023e-4e90-bc6e-1139c3cbdfbf" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="50.13" HigherBound="50.66">
                    <om:Property Name="Ref" Value="Microsys.EAI.Framework.Schemas.NotificationWithAttach" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="617e4ae6-fd34-471a-a276-803c90e085b2" ParentLink="Module_PortType" LowerBound="53.1" HigherBound="60.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptDisponibilitaErgGediInsertType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="309e3d5e-1f29-4f6f-be96-3585a257c25c" ParentLink="PortType_OperationDeclaration" LowerBound="55.1" HigherBound="59.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="DisponibilitaErgGediInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="1755d9e9-ba47-4c35-9dd0-5e6557178832" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="57.13" HigherBound="57.53">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaErgGediInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="485eeda0-992b-439d-bd78-6b28dd613292" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="57.55" HigherBound="57.96">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaErgGediInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="c7fc9eee-67d8-4cce-bd39-bd675ed2f1d9" ParentLink="Module_PortType" LowerBound="60.1" HigherBound="67.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typErgPowerOutagesV2" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="252a131c-145f-4a55-9b92-09309bea05ec" ParentLink="PortType_OperationDeclaration" LowerBound="62.1" HigherBound="66.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="POST" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="0c7a5efe-4dd2-43ff-b4ce-c764296b00f6" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="64.13" HigherBound="64.44">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgPowerOutagesV2RequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="e79864d9-4841-46eb-aa18-e6dce4b3d3d3" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="64.46" HigherBound="64.78">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgErgPowerOutagesV2ResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d4f8cd37-6b12-4779-908a-e1df3d907900" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgIndisponibilitaToGediPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="cd67abbc-6725-4a28-a660-a9f78e7808ac" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediTypedPolling.TypedPollingResultSet0" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7247485d-9da7-41cb-9bbb-9143aa90b369" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgIndisponibilitaToGediUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="b1033c70-a005-4c58-92c6-b501b2298302" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediUpdateStatusTypedProcedure.ErgIndisponibilitaToGediUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="2cec558c-53cc-4ca5-a33c-99a0fac8666e" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgIndisponibilitaToGediUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="c78b174d-8ecc-4a2d-b515-61f95ea52448" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediUpdateStatusTypedProcedure.ErgIndisponibilitaToGediUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a4e2c660-ae04-4605-b622-7b6b74fedd34" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgDisponibilitaErgGediInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="da5c0ab4-cf06-49dc-9f42-c630bf6c7465" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schDisponibilitaErgGediInsertTypedProcedure.DisponibilitaErgGediInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a9eeb37a-9fd0-45d4-9a28-9914f3ee0bb5" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgDisponibilitaErgGediInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="facf31a5-3075-4652-829d-3ed017659eff" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schDisponibilitaErgGediInsertTypedProcedure.DisponibilitaErgGediInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c610336d-e5a4-4c77-bad7-71929f342c79" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgPowerOutagesV2RequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="bab8f4ba-fa5a-46e7-8ba0-42cc94e8f060" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgPowerOutagesV2.Request" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c4792fe0-aed9-4488-a5c9-2b1fcb563820" ParentLink="Module_MessageType" LowerBound="28.1" HigherBound="32.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgErgPowerOutagesV2ResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3e5a0c09-a967-44f2-b606-0892fa67f632" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgPowerOutagesV2.Response" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="e8e0207c-87b6-4a7f-a1a6-66426c84998f" ParentLink="Module_ServiceDeclaration" LowerBound="67.1" HigherBound="302.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcErgIndisponibilitaToGediStagingV2" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="868d390e-3f13-439b-9934-dd9a8a2fa53f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="91.1" HigherBound="92.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="17b48364-a9a7-418e-bb12-fecbd33ca67f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="92.1" HigherBound="93.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="72bcdbd6-215a-4e73-ab7a-63315841e732" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="93.1" HigherBound="94.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="fab83b54-94bf-4dd0-b785-5533e26b9e79" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="94.1" HigherBound="95.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="06437f97-87f2-4edd-bc15-3e8fff1d1b54" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="95.1" HigherBound="96.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ccdf3902-0322-4302-959f-6d0702261ae4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="96.1" HigherBound="97.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d0e57fee-66ca-4284-ad8f-525c75bfe31c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="97.1" HigherBound="98.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8fc5f28c-e264-4a70-bfd8-c1c2db093f8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="98.1" HigherBound="99.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7926bc17-5dc4-4967-9ecc-3f3931ae2b4b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="99.1" HigherBound="100.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e838e0ce-4540-4fe3-ae8c-abc1dedfc01c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="100.1" HigherBound="101.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="attachFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="637d7d0e-88b6-4d96-bb3f-d04a2fdcbce0" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="83.1" HigherBound="84.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="87702ef4-7881-4c08-911b-419f30034f5b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="84.1" HigherBound="85.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgIndisponibilitaToGediPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6cfad893-4b70-4bd5-b4b7-75ba27cac185" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="85.1" HigherBound="86.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgIndisponibilitaToGediUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4f95f6e6-deec-461f-b479-0ec4ef3fcf3b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="86.1" HigherBound="87.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgIndisponibilitaToGediUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgIndisponibilitaToGediUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f4b25bfd-3bee-4385-95ee-d9ec53ed208a" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="87.1" HigherBound="88.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgPowerOutagesV2RequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgPowerOutagesRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4d48f728-94de-4a8b-b67a-a567586d75a2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="88.1" HigherBound="89.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgErgPowerOutagesV2ResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgErgPowerOutagesResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b96aee7b-8a93-49b4-88a6-ecbc2a87d6d4" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="89.1" HigherBound="90.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaErgGediInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDisponibilitaErgGediInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1b910430-7f43-4906-a0b8-f1576c78f71c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="90.1" HigherBound="91.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgDisponibilitaErgGediInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgDisponibilitaErgGediInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="522dcd20-195b-4c47-92d7-1a56f6815536" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="42969ad9-2dd1-427a-af3a-7b0f2fb1222d" ParentLink="ServiceBody_Statement" LowerBound="103.1" HigherBound="108.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptErgIndisponibilitaToGediPolling" />
                    <om:Property Name="MessageName" Value="msgErgIndisponibilitaToGediPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="b5e07d69-d5f4-4196-8254-2250f97660c8" ParentLink="ServiceBody_Statement" LowerBound="108.1" HigherBound="119.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;sendNotification = 0;&#xD;&#xA;&#xD;&#xA;attachFileName = System.String.Concat(&quot;meteologica_availability_&quot;, msgErgIndisponibilitaToGediPolling.parameters.dateFrom, &quot;.csv&quot;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="617f0464-cfc7-4266-933b-a2b6e4b6458e" ParentLink="ServiceBody_Statement" LowerBound="119.1" HigherBound="128.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, msgErgIndisponibilitaToGediPolling.parameters.flowDescription,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;Importazione data: '&quot;, msgErgIndisponibilitaToGediPolling.parameters.dateFrom, &quot;'&quot;)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="102041ee-ed0c-4c5e-98f9-666304b0819d" ParentLink="ServiceBody_Statement" LowerBound="128.1" HigherBound="202.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="20bba1ac-d477-4559-affa-4c3b3ecb68d3" ParentLink="ComplexStatement_Statement" LowerBound="133.1" HigherBound="139.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create ERG Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="79af8a52-2f29-4709-8d4f-c785cb2c85a1" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="138.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediPollingToErgRequestV2" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup ERG Message" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="a477fd52-5f91-47ed-82b6-9a924acf0670" ParentLink="Transform_OutputMessagePartRef" LowerBound="137.36" HigherBound="137.72">
                                <om:Property Name="MessageRef" Value="msgErgPowerOutagesRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="0dee5830-81b9-4cdf-a65b-98d0c8b6f9ba" ParentLink="Transform_InputMessagePartRef" LowerBound="137.185" HigherBound="137.230">
                                <om:Property Name="MessageRef" Value="msgErgIndisponibilitaToGediPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="5e52e649-b652-41aa-b682-dac0aba0264c" ParentLink="Construct_MessageRef" LowerBound="134.31" HigherBound="134.56">
                            <om:Property Name="Ref" Value="msgErgPowerOutagesRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="94a090d0-62dc-495f-a366-531811eb91c3" ParentLink="ComplexStatement_Statement" LowerBound="139.1" HigherBound="141.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="e7006cfe-3aaf-4aad-9031-29404fab64eb" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="143.1">
                        <om:Property Name="PortName" Value="sptErgPowerOutagesV2" />
                        <om:Property Name="MessageName" Value="msgErgPowerOutagesRequest" />
                        <om:Property Name="OperationName" Value="POST" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="7b4f3476-1bc7-47ce-aa4b-324e3c8b6aea" ParentLink="ComplexStatement_Statement" LowerBound="143.1" HigherBound="145.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptErgPowerOutagesV2" />
                        <om:Property Name="MessageName" Value="msgErgPowerOutagesResponse" />
                        <om:Property Name="OperationName" Value="POST" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="c5be7b98-5539-4ff4-8733-6e7086b6e806" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="151.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="40108b89-598d-4cf8-a98a-b278fcfd7cdb" ParentLink="ComplexStatement_Statement" LowerBound="151.1" HigherBound="157.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create DB Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="8d25ee39-98c0-4f28-acdb-c51a059f9425" ParentLink="Construct_MessageRef" LowerBound="152.31" HigherBound="152.67">
                            <om:Property Name="Ref" Value="msgDisponibilitaErgGediInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="b1b00a45-3b06-4058-8ee6-25ec221f82c6" ParentLink="ComplexStatement_Statement" LowerBound="154.1" HigherBound="156.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgResponseV2ToDisponibilitaErgGediInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup DB Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="43fcc427-7796-423f-8693-6d51bc3091e2" ParentLink="Transform_InputMessagePartRef" LowerBound="155.192" HigherBound="155.229">
                                <om:Property Name="MessageRef" Value="msgErgPowerOutagesResponse" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="2cdc1076-1eab-4cb3-8bdf-329ac8acd92f" ParentLink="Transform_OutputMessagePartRef" LowerBound="155.36" HigherBound="155.83">
                                <om:Property Name="MessageRef" Value="msgDisponibilitaErgGediInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="a874b886-8b8b-4b22-8035-890989ae2eb8" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="159.1">
                        <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Send" OID="49f70afb-e827-46f1-8640-da296f5993b1" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="161.1">
                        <om:Property Name="PortName" Value="sptDisponibilitaErgGediInsert" />
                        <om:Property Name="MessageName" Value="msgDisponibilitaErgGediInsertRequest" />
                        <om:Property Name="OperationName" Value="DisponibilitaErgGediInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="b48baeff-d5a1-4675-ac1b-b0de4f358e1d" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="163.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptDisponibilitaErgGediInsert" />
                        <om:Property Name="MessageName" Value="msgDisponibilitaErgGediInsertResponse" />
                        <om:Property Name="OperationName" Value="DisponibilitaErgGediInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="32a400bb-217b-4ad1-933d-018a40fce424" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="169.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="dcef4721-cf18-4613-b6aa-a6b46d8d3ccd" ParentLink="Scope_Catch" LowerBound="172.1" HigherBound="186.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="4aabd481-ee91-44c8-8fdd-bf12e40f0d25" ParentLink="Catch_Statement" LowerBound="175.1" HigherBound="185.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante il recupero delle indisponibilita ERG.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;Data: '{0}'. &quot;, msgErgIndisponibilitaToGediPolling.parameters.dateFrom));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="b4f81684-db15-4841-85cd-554c89b84db7" ParentLink="Scope_Catch" LowerBound="186.1" HigherBound="200.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c50ff914-5f43-4b35-b76d-f3629e54f26a" ParentLink="Catch_Statement" LowerBound="189.1" HigherBound="199.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante il recupero delle indisponibilita ERG.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;Data: '{0}'. &quot;, msgErgIndisponibilitaToGediPolling.parameters.dateFrom));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="b48f18b4-10d8-40ce-b49d-e2cb2c622535" ParentLink="ServiceBody_Statement" LowerBound="202.1" HigherBound="256.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="f1f0d582-1c33-4cde-9fcb-239cc12d39c9" ParentLink="ComplexStatement_Statement" LowerBound="207.1" HigherBound="215.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageRef" OID="74e015fe-a3fe-4daa-94db-90b9a028c137" ParentLink="Construct_MessageRef" LowerBound="208.31" HigherBound="208.77">
                            <om:Property Name="Ref" Value="msgErgIndisponibilitaToGediUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="Transform" OID="6c433ba6-f75c-45c2-8942-1b931a714218" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="212.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediPollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Update Status" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="43a7dce9-7398-4eab-9eaa-af304eb14e9b" ParentLink="Transform_OutputMessagePartRef" LowerBound="211.36" HigherBound="211.93">
                                <om:Property Name="MessageRef" Value="msgErgIndisponibilitaToGediUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_6" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="55d31d97-7dab-4783-80fc-8851cd098e2d" ParentLink="Transform_InputMessagePartRef" LowerBound="211.206" HigherBound="211.251">
                                <om:Property Name="MessageRef" Value="msgErgIndisponibilitaToGediPolling" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_5" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="7637e275-135d-4f8d-9999-71dd3cbe63f7" ParentLink="ComplexStatement_Statement" LowerBound="212.1" HigherBound="214.1">
                            <om:Property Name="Expression" Value="msgErgIndisponibilitaToGediUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Parameters" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="25f7723c-9376-4fbf-a061-298f46787e1e" ParentLink="ComplexStatement_Statement" LowerBound="215.1" HigherBound="217.1">
                        <om:Property Name="PortName" Value="sptErgIndisponibilitaToGediUpdateStatus" />
                        <om:Property Name="MessageName" Value="msgErgIndisponibilitaToGediUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ErgIndisponibilitaToGediUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="e394fbcc-8351-4ab9-9f96-a723f817158f" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="219.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptErgIndisponibilitaToGediUpdateStatus" />
                        <om:Property Name="MessageName" Value="msgErgIndisponibilitaToGediUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ErgIndisponibilitaToGediUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="4fd61cf1-67fb-46dd-8ff5-c96c86c864b9" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="223.1">
                        <om:Property Name="Expression" Value="sendNotification = msgErgIndisponibilitaToGediUpdateStatusResponse.parameters.ReturnValue;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="sendNotification" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="c70a94d7-8944-419f-a72f-a57ff7915181" ParentLink="Scope_Catch" LowerBound="226.1" HigherBound="240.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="75162cec-0b45-4d6f-8cdb-5335f349ff4e" ParentLink="Catch_Statement" LowerBound="229.1" HigherBound="239.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, msgErgIndisponibilitaToGediPolling.parameters.flowDescription));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="1db7074a-b47e-4415-acff-5fb9c1a44ec0" ParentLink="Scope_Catch" LowerBound="240.1" HigherBound="254.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="b0ecce3a-a514-4653-ae63-ddf9a70ccdea" ParentLink="Catch_Statement" LowerBound="243.1" HigherBound="253.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0} - {1} - Errore durante aggiornamento stato. &quot;, A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, msgErgIndisponibilitaToGediPolling.parameters.flowDescription));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="bb277585-5250-40ea-b8ef-2ade207914ae" ParentLink="ServiceBody_Statement" LowerBound="256.1" HigherBound="292.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="328f651b-d2cf-4d94-bb78-9c5a60c0ccfe" ParentLink="ReallyComplexStatement_Branch" LowerBound="257.13" HigherBound="260.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="dffd9fb8-acc2-42f3-8596-396c9e2b3ff4" ParentLink="ReallyComplexStatement_Branch" LowerBound="260.18" HigherBound="263.1">
                        <om:Property Name="Expression" Value="resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; sendNotification == 1" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Retry" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="2d3ebd70-91b8-4bb1-beee-88967fa9ff69" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="0d033aea-f382-4b75-b242-b84666e2465e" ParentLink="ComplexStatement_Statement" LowerBound="265.1" HigherBound="291.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="47e8fb8f-75e8-4e37-81dd-f599baf568c1" ParentLink="ComplexStatement_Statement" LowerBound="270.1" HigherBound="287.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="5796f7dd-8433-4874-87da-9bc1b7f65c86" ParentLink="ComplexStatement_Statement" LowerBound="273.1" HigherBound="286.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = msgErgIndisponibilitaToGediPolling.parameters.flowDescription;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="49162ef9-0074-42d4-afda-4dd627cb58ef" ParentLink="Construct_MessageRef" LowerBound="271.35" HigherBound="271.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="f33b7a46-84bf-4ccf-933a-7c99d74716be" ParentLink="ComplexStatement_Statement" LowerBound="287.1" HigherBound="289.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="481fc25d-6de7-49de-a481-e2799c2a0715" ParentLink="ServiceBody_Statement" LowerBound="292.1" HigherBound="300.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="163e6ef3-9056-405e-be32-96281b9c5141" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="70.1" HigherBound="72.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typErgIndisponibilitaToGediPolling" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptErgIndisponibilitaToGediPolling" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="70948bdd-f168-44b5-af11-6b4e16703612" ParentLink="PortDeclaration_CLRAttribute" LowerBound="70.1" HigherBound="71.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2c3fd042-c87e-40a5-9f7e-bba85a049a24" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="72.1" HigherBound="75.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="70" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.sptDisponibilitaErgGediInsertType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptDisponibilitaErgGediInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="7b6142ef-c618-4372-8aa8-34be16c26e67" ParentLink="PortDeclaration_CLRAttribute" LowerBound="72.1" HigherBound="73.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="0ff4f220-be33-4f64-adeb-f188d6b4051e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="75.1" HigherBound="78.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="124" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typErgIndisponibilitaToGediUpdateStatus" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptErgIndisponibilitaToGediUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c841276e-7f25-46de-8e91-ba9350cfe065" ParentLink="PortDeclaration_CLRAttribute" LowerBound="75.1" HigherBound="76.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="1d235c56-e776-408a-81c7-4ada611c8255" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="78.1" HigherBound="80.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="172" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="5ee2d8e3-b75b-4182-9654-d5699d5c4a21" ParentLink="PortDeclaration_CLRAttribute" LowerBound="78.1" HigherBound="79.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="f3a64a27-10e2-414b-aa6a-f053ca9d7a6f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="80.1" HigherBound="83.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="42" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typErgPowerOutagesV2" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptErgPowerOutagesV2" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="6350c58e-4e23-41bb-bbd5-132e4b4ba93a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="80.1" HigherBound="81.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal messagetype msgErgIndisponibilitaToGediPollingType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediTypedPolling.TypedPollingResultSet0 parameters;
    };
    internal messagetype msgErgIndisponibilitaToGediUpdateStatusRequestType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediUpdateStatusTypedProcedure.ErgIndisponibilitaToGediUpdateStatus parameters;
    };
    internal messagetype msgErgIndisponibilitaToGediUpdateStatusResponseType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgIndisponibilitaToGediUpdateStatusTypedProcedure.ErgIndisponibilitaToGediUpdateStatusResponse parameters;
    };
    internal messagetype msgDisponibilitaErgGediInsertRequestType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schDisponibilitaErgGediInsertTypedProcedure.DisponibilitaErgGediInsert parameters;
    };
    internal messagetype msgDisponibilitaErgGediInsertResponseType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schDisponibilitaErgGediInsertTypedProcedure.DisponibilitaErgGediInsertResponse parameters;
    };
    internal messagetype msgErgPowerOutagesV2RequestType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgPowerOutagesV2.Request parameters;
    };
    internal messagetype msgErgPowerOutagesV2ResponseType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.ErgIndisponibilitaToGedi.schErgPowerOutagesV2.Response parameters;
    };
    internal porttype typErgIndisponibilitaToGediPolling
    {
        oneway Receive
        {
            msgErgIndisponibilitaToGediPollingType
        };
    };
    internal porttype typErgIndisponibilitaToGediUpdateStatus
    {
        requestresponse ErgIndisponibilitaToGediUpdateStatus
        {
            msgErgIndisponibilitaToGediUpdateStatusRequestType, msgErgIndisponibilitaToGediUpdateStatusResponseType
        };
    };
    internal porttype sptNotificationWithAttachType
    {
        oneway Send
        {
            Microsys.EAI.Framework.Schemas.NotificationWithAttach
        };
    };
    internal porttype sptDisponibilitaErgGediInsertType
    {
        requestresponse DisponibilitaErgGediInsert
        {
            msgDisponibilitaErgGediInsertRequestType, msgDisponibilitaErgGediInsertResponseType
        };
    };
    internal porttype typErgPowerOutagesV2
    {
        requestresponse POST
        {
            msgErgPowerOutagesV2RequestType, msgErgPowerOutagesV2ResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcErgIndisponibilitaToGediStagingV2
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typErgIndisponibilitaToGediPolling rptErgIndisponibilitaToGediPolling;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptDisponibilitaErgGediInsertType sptDisponibilitaErgGediInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typErgIndisponibilitaToGediUpdateStatus sptErgIndisponibilitaToGediUpdateStatus;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typErgPowerOutagesV2 sptErgPowerOutagesV2;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgErgIndisponibilitaToGediPollingType msgErgIndisponibilitaToGediPolling;
        message msgErgIndisponibilitaToGediUpdateStatusRequestType msgErgIndisponibilitaToGediUpdateStatusRequest;
        message msgErgIndisponibilitaToGediUpdateStatusResponseType msgErgIndisponibilitaToGediUpdateStatusResponse;
        message msgErgPowerOutagesV2RequestType msgErgPowerOutagesRequest;
        message msgErgPowerOutagesV2ResponseType msgErgPowerOutagesResponse;
        message msgDisponibilitaErgGediInsertRequestType msgDisponibilitaErgGediInsertRequest;
        message msgDisponibilitaErgGediInsertResponseType msgDisponibilitaErgGediInsertResponse;
        System.DateTimeOffset dataInizioDb;
        System.Int32 sendNotification;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        System.DateTimeOffset dataInizioWs;
        System.String attachFileName;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("42969ad9-2dd1-427a-af3a-7b0f2fb1222d")]
            activate receive (rptErgIndisponibilitaToGediPolling.Receive, msgErgIndisponibilitaToGediPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            attachFileName = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b5e07d69-d5f4-4196-8254-2250f97660c8")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            sendNotification = 0;
            
            attachFileName = System.String.Concat("meteologica_availability_", msgErgIndisponibilitaToGediPolling.parameters.dateFrom, ".csv");
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("617f0464-cfc7-4266-933b-a2b6e4b6458e")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId,
            "Flusso", msgErgIndisponibilitaToGediPolling.parameters.flowDescription,
            "Note", System.String.Concat("Importazione data: '", msgErgIndisponibilitaToGediPolling.parameters.dateFrom, "'")
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("102041ee-ed0c-4c5e-98f9-666304b0819d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("20bba1ac-d477-4559-affa-4c3b3ecb68d3")]
                    construct msgErgPowerOutagesRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("79af8a52-2f29-4709-8d4f-c785cb2c85a1")]
                        transform (msgErgPowerOutagesRequest.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediPollingToErgRequestV2 (msgErgIndisponibilitaToGediPolling.parameters);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("94a090d0-62dc-495f-a366-531811eb91c3")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e7006cfe-3aaf-4aad-9031-29404fab64eb")]
                    send (sptErgPowerOutagesV2.POST, msgErgPowerOutagesRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7b4f3476-1bc7-47ce-aa4b-324e3c8b6aea")]
                    receive (sptErgPowerOutagesV2.POST, msgErgPowerOutagesResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c5be7b98-5539-4ff4-8733-6e7086b6e806")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now)
                    );
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("40108b89-598d-4cf8-a98a-b278fcfd7cdb")]
                    construct msgDisponibilitaErgGediInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b1b00a45-3b06-4058-8ee6-25ec221f82c6")]
                        transform (msgDisponibilitaErgGediInsertRequest.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgResponseV2ToDisponibilitaErgGediInsert (msgErgPowerOutagesResponse.parameters);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a874b886-8b8b-4b22-8035-890989ae2eb8")]
                    dataInizioDb = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("49f70afb-e827-46f1-8640-da296f5993b1")]
                    send (sptDisponibilitaErgGediInsert.DisponibilitaErgGediInsert, msgDisponibilitaErgGediInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b48baeff-d5a1-4675-ac1b-b0de4f358e1d")]
                    receive (sptDisponibilitaErgGediInsert.DisponibilitaErgGediInsert, msgDisponibilitaErgGediInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("32a400bb-217b-4ad1-933d-018a40fce424")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
                    "Data Inizio DB", dataInizioDb,
                    "Data Fine DB", System.DateTimeOffset.Now,
                    "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("dcef4721-cf18-4613-b6aa-a6b46d8d3ccd")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4aabd481-ee91-44c8-8fdd-bf12e40f0d25")]
                        errorMessage.Append("Errore durante il recupero delle indisponibilita ERG.");
                        errorMessage.Append(System.String.Format("Data: '{0}'. ", msgErgIndisponibilitaToGediPolling.parameters.dateFrom));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b4f81684-db15-4841-85cd-554c89b84db7")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c50ff914-5f43-4b35-b76d-f3629e54f26a")]
                        errorMessage.Append("Errore durante il recupero delle indisponibilita ERG.");
                        errorMessage.Append(System.String.Format("Data: '{0}'. ", msgErgIndisponibilitaToGediPolling.parameters.dateFrom));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b48f18b4-10d8-40ce-b49d-e2cb2c622535")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f1f0d582-1c33-4cde-9fcb-239cc12d39c9")]
                    construct msgErgIndisponibilitaToGediUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6c433ba6-f75c-45c2-8942-1b931a714218")]
                        transform (msgErgIndisponibilitaToGediUpdateStatusRequest.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.ErgIndisponibilitaToGedi.mapErgIndisponibilitaToGediPollingToUpdateStatus (msgErgIndisponibilitaToGediPolling.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7637e275-135d-4f8d-9999-71dd3cbe63f7")]
                        msgErgIndisponibilitaToGediUpdateStatusRequest.parameters.transactionStatus = System.Convert.ToInt32(resultCode);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("25f7723c-9376-4fbf-a061-298f46787e1e")]
                    send (sptErgIndisponibilitaToGediUpdateStatus.ErgIndisponibilitaToGediUpdateStatus, msgErgIndisponibilitaToGediUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e394fbcc-8351-4ab9-9f96-a723f817158f")]
                    receive (sptErgIndisponibilitaToGediUpdateStatus.ErgIndisponibilitaToGediUpdateStatus, msgErgIndisponibilitaToGediUpdateStatusResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4fd61cf1-67fb-46dd-8ff5-c96c86c864b9")]
                    sendNotification = msgErgIndisponibilitaToGediUpdateStatusResponse.parameters.ReturnValue;
                    
                    
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c70a94d7-8944-419f-a72f-a57ff7915181")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("75162cec-0b45-4d6f-8cdb-5335f349ff4e")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, msgErgIndisponibilitaToGediPolling.parameters.flowDescription));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1db7074a-b47e-4415-acff-5fb9c1a44ec0")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b0ecce3a-a514-4653-ae63-ddf9a70ccdea")]
                        errorMessage.Append(System.String.Format("{0} - {1} - Errore durante aggiornamento stato. ", A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName, msgErgIndisponibilitaToGediPolling.parameters.flowDescription));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bb277585-5250-40ea-b8ef-2ade207914ae")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (resultCode != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && sendNotification == 1)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0d033aea-f382-4b75-b242-b84666e2465e")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("47e8fb8f-75e8-4e37-81dd-f599baf568c1")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5796f7dd-8433-4874-87da-9bc1b7f65c86")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = msgErgIndisponibilitaToGediPolling.parameters.flowDescription;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f33b7a46-84bf-4ccf-933a-7c99d74716be")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("481fc25d-6de7-49de-a481-e2799c2a0715")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

