﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="e5def6a5-6818-47d5-b8b2-95d6d92839e2" LowerBound="1.1" HigherBound="139.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="d1518b8f-0fdf-43c4-b46b-38d59f9f4fa1" ParentLink="Module_PortType" LowerBound="4.1" HigherBound="11.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptFermoImpiantiType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="460227c3-d2cf-43f5-ab22-7abacd999b5a" ParentLink="PortType_OperationDeclaration" LowerBound="6.1" HigherBound="10.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="SEND" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="41420746-cd35-4e46-ad10-7c4ecbe5af27" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="8.13" HigherBound="8.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="ce76bb2f-49ca-4e81-a756-883cbb29f997" ParentLink="Module_PortType" LowerBound="11.1" HigherBound="18.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="rptFermoImpiantiType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="96cd9a63-a544-4777-8b74-d12272356afc" ParentLink="PortType_OperationDeclaration" LowerBound="13.1" HigherBound="17.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="b8634873-6337-4304-ac0c-45b83229e9c5" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="15.13" HigherBound="15.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="446ffa02-e61a-44b9-a1d7-b81680e0e87f" ParentLink="Module_ServiceDeclaration" LowerBound="18.1" HigherBound="138.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcFermoImpianti" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="f9660965-1d40-43b5-8f71-aadf5f2238ea" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b7deb194-bdd8-4c44-b77c-dc3fc7513a2c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="539dab4e-859c-417e-ae51-01db952be87a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b0c3c408-d473-4b87-81ea-f38eabb917df" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4b0f046d-f0d6-4119-9ab1-89fe10f6a709" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1beb49c9-264f-45ae-999d-840a79ffce75" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e814acde-5b6d-4baf-bf0e-61036b202fd8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="27.1" HigherBound="28.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f93f5b0d-31e2-4da1-a5c6-5db42a06f94b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgFermoImpianti" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="b2e18ef4-b205-4666-bbb7-9bf34cb8d147" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="d925ab03-3c70-4ba8-ab05-f24b57dc63e7" ParentLink="ServiceBody_Statement" LowerBound="37.1" HigherBound="41.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptFermoImpianti" />
                    <om:Property Name="MessageName" Value="msgFermoImpianti" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="4242455a-e578-41b9-9a23-9fe145f3b279" ParentLink="ServiceBody_Statement" LowerBound="41.1" HigherBound="48.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="d37e21c6-a9d9-4bec-b049-ecf1ae22dcfb" ParentLink="ServiceBody_Statement" LowerBound="48.1" HigherBound="59.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, msgFermoImpianti(FILE.ReceivedFileName),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionFermoImpianti&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="86911c61-1845-4ae0-a260-a3d93a69b82f" ParentLink="ServiceBody_Statement" LowerBound="59.1" HigherBound="95.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Send" OID="81b18b43-5482-4932-84ac-a8d893b8099d" ParentLink="ComplexStatement_Statement" LowerBound="64.1" HigherBound="66.1">
                        <om:Property Name="PortName" Value="sptFermoImpianti" />
                        <om:Property Name="MessageName" Value="msgFermoImpianti" />
                        <om:Property Name="OperationName" Value="SEND" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="5c70d49e-0b9f-4bee-99a2-0d154b6507f9" ParentLink="Scope_Catch" LowerBound="69.1" HigherBound="82.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="deliveryException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="ea8bc53d-d147-48f4-859f-382dd8fb32db" ParentLink="Catch_Statement" LowerBound="72.1" HigherBound="81.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la scrittura di fermo impianti da N2 verso ###.&quot;);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="3dd2d098-c6a8-424b-98a1-c841e887471b" ParentLink="Scope_Catch" LowerBound="82.1" HigherBound="93.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="5899fc24-487e-4c26-8d7e-668b93afb75a" ParentLink="Catch_Statement" LowerBound="85.1" HigherBound="92.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la scrittura di fermo impianti da N2 verso ###.&quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="7a74f66c-cc2e-47c8-8ac4-c1ff901a64f5" ParentLink="ServiceBody_Statement" LowerBound="95.1" HigherBound="128.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="0f92f68d-6f5f-45ef-901e-0e4f0f8db175" ParentLink="ReallyComplexStatement_Branch" LowerBound="96.13" HigherBound="99.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="654282c4-729e-440e-99a4-dc8328edd5cb" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="c215bf9f-2bf9-4211-bd93-297838d2fa22" ParentLink="ComplexStatement_Statement" LowerBound="101.1" HigherBound="127.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="4869d815-5ba6-4e38-b103-3eedaa0c780e" ParentLink="ComplexStatement_Statement" LowerBound="106.1" HigherBound="123.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="df43fadc-00fe-4b13-b130-97af65f9fe89" ParentLink="Construct_MessageRef" LowerBound="107.35" HigherBound="107.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="3b82ea4f-ea19-4779-97d7-4b6a1c586e5f" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="122.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionFermoImpianti;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="9f2b19f0-f84c-4681-bd03-cecd976691b3" ParentLink="ComplexStatement_Statement" LowerBound="123.1" HigherBound="125.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="475c4e8d-4a5c-408e-8b53-7699676cddc9" ParentLink="ServiceBody_Statement" LowerBound="128.1" HigherBound="136.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="f684c648-a131-44bf-9fc8-7a5bcdae136a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="21.1" HigherBound="23.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="19" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.sptFermoImpiantiType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptFermoImpianti" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="972247a6-1780-4985-ac19-a3f5db6cb66e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="21.1" HigherBound="22.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ea23152d-de48-4768-92c0-bf5745796d60" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="23.1" HigherBound="25.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.rptFermoImpiantiType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptFermoImpianti" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="a360ccda-9e9c-48f9-98ba-c9def1d7333e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="23.1" HigherBound="24.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b3a5c6db-de9c-44bd-b7b4-90aff1e8b86e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="25.1" HigherBound="27.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="73" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="14c70263-9c14-4a8c-ac89-cf628141381c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="25.1" HigherBound="26.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal porttype sptFermoImpiantiType
    {
        oneway SEND
        {
            System.Xml.XmlDocument
        };
    };
    internal porttype rptFermoImpiantiType
    {
        oneway Receive
        {
            System.Xml.XmlDocument
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcFermoImpianti
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses sptFermoImpiantiType sptFermoImpianti;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements rptFermoImpiantiType rptFermoImpianti;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message System.Xml.XmlDocument msgFermoImpianti;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d925ab03-3c70-4ba8-ab05-f24b57dc63e7")]
            activate receive (rptFermoImpianti.Receive, msgFermoImpianti);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4242455a-e578-41b9-9a23-9fe145f3b279")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d37e21c6-a9d9-4bec-b049-ecf1ae22dcfb")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", msgFermoImpianti(FILE.ReceivedFileName),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionFermoImpianti
            );
            
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("86911c61-1845-4ae0-a260-a3d93a69b82f")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("81b18b43-5482-4932-84ac-a8d893b8099d")]
                    send (sptFermoImpianti.SEND, msgFermoImpianti);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5c70d49e-0b9f-4bee-99a2-0d154b6507f9")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea8bc53d-d147-48f4-859f-382dd8fb32db")]
                        errorMessage.Append("Errore durante la scrittura di fermo impianti da N2 verso ###.");
                        errorMessage.Append(deliveryExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("3dd2d098-c6a8-424b-98a1-c841e887471b")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5899fc24-487e-4c26-8d7e-668b93afb75a")]
                        errorMessage.Append("Errore durante la scrittura di fermo impianti da N2 verso ###.");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7a74f66c-cc2e-47c8-8ac4-c1ff901a64f5")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c215bf9f-2bf9-4211-bd93-297838d2fa22")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4869d815-5ba6-4e38-b103-3eedaa0c780e")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3b82ea4f-ea19-4779-97d7-4b6a1c586e5f")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionFermoImpianti;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9f2b19f0-f84c-4681-bd03-cecd976691b3")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("475c4e8d-4a5c-408e-8b53-7699676cddc9")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

