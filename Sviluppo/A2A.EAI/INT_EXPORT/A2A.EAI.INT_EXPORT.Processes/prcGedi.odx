﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="cc725380-e653-4a47-a23a-71d518f756a2" LowerBound="1.1" HigherBound="151.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="b8fe32d7-4857-4a96-8255-a3dc7b39bd9a" ParentLink="Module_PortType" LowerBound="4.1" HigherBound="11.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typGediInput" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="087242a0-e7bf-4088-ae37-32b3e2cc607b" ParentLink="PortType_OperationDeclaration" LowerBound="6.1" HigherBound="10.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="79137d4c-e59f-4476-a7f5-84491211d718" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="8.13" HigherBound="8.67">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schInputGedi" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="89a276c3-7546-4715-9e5d-1fb31dcabd87" ParentLink="Module_ServiceDeclaration" LowerBound="11.1" HigherBound="150.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcGedi" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="67e7ba6f-6d85-49a9-9361-60b2e96f2fe8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="24.1" HigherBound="25.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8743abdc-6905-4331-953e-196ee7e888fa" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="25.1" HigherBound="26.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2d7dc62e-b270-4a59-b3e9-135600c28994" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="32d4c983-d749-4d32-bd38-b369a4e63c8c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="27.1" HigherBound="28.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="74c6f47c-a438-411f-acd0-5cda7e7048c6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2710de41-1f86-4352-a0ec-234db514fb98" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f7d65ccf-8e4e-4091-8a0e-4bd101e4248f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d11d44a9-6ea8-4414-97fb-362538a0d91b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f6e9bf39-adf5-4d33-ad19-2f7dfccb91ad" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="21.1" HigherBound="22.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="467a0ebe-bd42-414b-88e3-5ca06139f140" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schInputGedi" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgInputGedi" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="ba198cba-f085-4e9e-a077-2e19428bcc24" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="23.1" HigherBound="24.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.msgOutputGediType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgOutputGedi" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="f77e09cd-cc71-4e88-8ae3-e452c0eeb9a0" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="c8984542-fb99-4258-9b92-dd7f44c3a822" ParentLink="ServiceBody_Statement" LowerBound="34.1" HigherBound="40.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptGediInput" />
                    <om:Property Name="MessageName" Value="msgInputGedi" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="7e7f2248-be9f-46a2-afe2-b150974be587" ParentLink="ServiceBody_Statement" LowerBound="40.1" HigherBound="50.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgInputGedi);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgInputGedi);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="c0493812-5690-4351-9a66-4e394f3386c2" ParentLink="ServiceBody_Statement" LowerBound="50.1" HigherBound="59.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;instanceId&quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),&#xD;&#xA;&quot;Flusso&quot;, &quot;Export GEDI&quot;&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="51c504d6-5649-4618-b915-6662f4fc05fb" ParentLink="ServiceBody_Statement" LowerBound="59.1" HigherBound="107.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="1f35ddfb-5291-4c34-95f8-ed4f8da5f58b" ParentLink="ComplexStatement_Statement" LowerBound="64.1" HigherBound="72.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="55e13770-859c-4b8c-b561-8d651af17768" ParentLink="ComplexStatement_Statement" LowerBound="67.1" HigherBound="69.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Maps.Gedi.mapInputGediToOutputGedi" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="afa743df-8412-4497-ae77-c8d5ad89d612" ParentLink="Transform_InputMessagePartRef" LowerBound="68.129" HigherBound="68.141">
                                <om:Property Name="MessageRef" Value="msgInputGedi" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="69b8f16d-82c9-4924-99cc-7fb614c9bc18" ParentLink="Transform_OutputMessagePartRef" LowerBound="68.36" HigherBound="68.60">
                                <om:Property Name="MessageRef" Value="msgOutputGedi" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="48deae0f-0b33-4630-8a02-328d9838aaa1" ParentLink="ComplexStatement_Statement" LowerBound="69.1" HigherBound="71.1">
                            <om:Property Name="Expression" Value="msgOutputGedi(FILE.ReceivedFileName) = msgInputGedi.NomeFile;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="File Config" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="40ba4e75-982e-4872-91fa-48c7acf0f5bf" ParentLink="Construct_MessageRef" LowerBound="65.31" HigherBound="65.44">
                            <om:Property Name="Ref" Value="msgOutputGedi" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="7f3e7bec-5d5b-4515-9369-aa56da664db4" ParentLink="ComplexStatement_Statement" LowerBound="72.1" HigherBound="74.1">
                        <om:Property Name="PortName" Value="sptGediOutput" />
                        <om:Property Name="MessageName" Value="msgOutputGedi" />
                        <om:Property Name="OperationName" Value="Send" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="2caaa82b-6041-44ad-b491-7c24b7e8ff12" ParentLink="Scope_Catch" LowerBound="77.1" HigherBound="91.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f05b529c-7d73-45cb-9be5-fd7d63548a2d" ParentLink="Catch_Statement" LowerBound="80.1" HigherBound="90.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'invio a GEDI.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="5e70ebf3-b8d6-447c-b3a3-3b971644a246" ParentLink="Scope_Catch" LowerBound="91.1" HigherBound="105.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c0f9007c-ed21-42f8-85a0-596e6329ba44" ParentLink="Catch_Statement" LowerBound="94.1" HigherBound="104.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'invio a GEDI.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="b45e7665-e512-4ff1-a47a-f1f41535b28d" ParentLink="ServiceBody_Statement" LowerBound="107.1" HigherBound="140.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="632a8d4c-359b-4d5d-b78c-3a2d3d38cc4c" ParentLink="ReallyComplexStatement_Branch" LowerBound="108.13" HigherBound="111.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="e6e56fb3-2a74-4f89-8630-8f3dc3ddcaad" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="85d688c6-4571-416b-9fd2-468e35f09a6d" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="139.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="1daf1804-cf56-4742-b150-93935b4c981d" ParentLink="ComplexStatement_Statement" LowerBound="118.1" HigherBound="135.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="e000d54d-8c80-4128-b4d6-10472bce05f5" ParentLink="ComplexStatement_Statement" LowerBound="121.1" HigherBound="134.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionExportGedi;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="7a8b8bce-64aa-4c84-ba79-b6bb31e4267a" ParentLink="Construct_MessageRef" LowerBound="119.35" HigherBound="119.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e947421d-97bb-43c6-864a-02451bead417" ParentLink="ComplexStatement_Statement" LowerBound="135.1" HigherBound="137.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e8992ff2-3c48-4871-a7e2-50765e11197c" ParentLink="ServiceBody_Statement" LowerBound="140.1" HigherBound="148.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="0c432125-ae46-4a2c-8b52-1b5a8f7b660d" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="14.1" HigherBound="16.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typGediInput" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptGediInput" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="d5a0402b-faad-4e53-be2e-393104901e50" ParentLink="PortDeclaration_CLRAttribute" LowerBound="14.1" HigherBound="15.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a48496f5-4936-4157-9f38-96c6cf489440" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="16.1" HigherBound="19.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="36" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typGediOutput" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptGediOutput" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="41675216-bb9c-4752-8dc7-4c154be0245e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="16.1" HigherBound="17.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="1262a79a-fd9b-460a-9e98-19a9d665923e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="19.1" HigherBound="21.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="90" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXPORT.Processes.typNotification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="3c9a1ab1-9935-405a-b7ec-466909e1b0cd" ParentLink="PortDeclaration_CLRAttribute" LowerBound="19.1" HigherBound="20.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal porttype typGediInput
    {
        oneway Receive
        {
            A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schInputGedi
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcGedi
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typGediInput rptGediInput;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typGediOutput sptGediOutput;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses typNotification sptNotification;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schInputGedi msgInputGedi;
        message msgOutputGediType msgOutputGedi;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c8984542-fb99-4258-9b92-dd7f44c3a822")]
            activate receive (rptGediInput.Receive, msgInputGedi);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7e7f2248-be9f-46a2-afe2-b150974be587")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(msgInputGedi);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(msgInputGedi);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0493812-5690-4351-9a66-4e394f3386c2")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "instanceId", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId(),
            "Flusso", "Export GEDI"
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("51c504d6-5649-4618-b915-6662f4fc05fb")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1f35ddfb-5291-4c34-95f8-ed4f8da5f58b")]
                    construct msgOutputGedi
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("55e13770-859c-4b8c-b561-8d651af17768")]
                        transform (msgOutputGedi.parameters) = A2A.EAI.INT_EXPORT.Messaging.Maps.Gedi.mapInputGediToOutputGedi (msgInputGedi);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("48deae0f-0b33-4630-8a02-328d9838aaa1")]
                        msgOutputGedi(FILE.ReceivedFileName) = msgInputGedi.NomeFile;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f3e7bec-5d5b-4515-9369-aa56da664db4")]
                    send (sptGediOutput.Send, msgOutputGedi);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2caaa82b-6041-44ad-b491-7c24b7e8ff12")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f05b529c-7d73-45cb-9be5-fd7d63548a2d")]
                        errorMessage.Append("Errore durante l'invio a GEDI.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(deliveryExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5e70ebf3-b8d6-447c-b3a3-3b971644a246")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0f9007c-ed21-42f8-85a0-596e6329ba44")]
                        errorMessage.Append("Errore durante l'invio a GEDI.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b45e7665-e512-4ff1-a47a-f1f41535b28d")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("85d688c6-4571-416b-9fd2-468e35f09a6d")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1daf1804-cf56-4742-b150-93935b4c981d")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e000d54d-8c80-4128-b4d6-10472bce05f5")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXPORT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXPORT.Services.ProcessServices.FlowDescriptionExportGedi;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Description(Microsys.EAI.Framework.Services.EventType.Code.Error);
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e947421d-97bb-43c6-864a-02451bead417")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e8992ff2-3c48-4871-a7e2-50765e11197c")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXPORT.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

