﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="618526a4-eaad-4330-8d70-153c14c12ecf" LowerBound="1.1" HigherBound="30.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXPORT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="a06988f1-d1e3-4797-b87f-f99d5f8b8e81" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typGediOutput" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="463c0201-c014-453c-a38d-88b02ee12ffb" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="374b26dd-793f-40c9-8e7d-8f883ad1716a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.30">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXPORT.Processes.msgOutputGediType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="3782e38a-9bd9-45f7-8fa0-0a7a28efd6fc" ParentLink="Module_PortType" LowerBound="15.1" HigherBound="22.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typNotification" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="9ada6dc0-6b8e-4800-9b52-a3ec9a8488fa" ParentLink="PortType_OperationDeclaration" LowerBound="17.1" HigherBound="21.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="762b8835-b6e0-4ad4-94f2-2feb4928fc21" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="19.13" HigherBound="19.56">
                    <om:Property Name="Ref" Value="Microsys.EAI.Framework.Schemas.Notification" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="58ce722f-6659-4233-995b-14b7b26d29cb" ParentLink="Module_ServiceDeclaration" LowerBound="22.1" HigherBound="29.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="a8b1bd7b-2d3d-48c4-8693-a3416fb60a13" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="15c960da-546a-4a15-8533-d8825f5f4e46" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgOutputGediType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="39c0250d-f937-4556-a072-0b0ef3b3ae96" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schOutputGedi" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXPORT.Processes
{
    internal messagetype msgOutputGediType
    {
        body A2A.EAI.INT_EXPORT.Messaging.Schemas.Gedi.schOutputGedi parameters;
    };
    internal porttype typGediOutput
    {
        oneway Send
        {
            msgOutputGediType
        };
    };
    internal porttype typNotification
    {
        oneway Send
        {
            Microsys.EAI.Framework.Schemas.Notification
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcObjectDefinition
    {
        body ()
        {
        }
    }
}

