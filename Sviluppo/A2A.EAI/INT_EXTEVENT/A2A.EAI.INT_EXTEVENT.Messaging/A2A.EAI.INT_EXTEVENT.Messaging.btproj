﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{BFAB451A-8ABE-4651-89AD-7E230A24CEEF}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_EXTEVENT.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_EXTEVENT.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\Quake\schControlliDigheTerremotiWs.xsd">
      <TypeName>schControlliDigheTerremotiWs</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schControlliDigheTerremotiWs_DataContracts_Dighe.xsd">
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas</Namespace>
      <TypeName>schControlliDigheTerremotiWs_DataContracts_Dighe</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schControlliDigheTerremotiWs_Serialization.xsd">
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas</Namespace>
      <TypeName>schControlliDigheTerremotiWs_Serialization</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schControlliDigheTerremotiWs_Dighe_Serialization_Arrays.xsd">
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas</Namespace>
      <TypeName>schControlliDigheTerremotiWs_Dighe_Serialization_Arrays</TypeName>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileFileUpdateStatusTypedProcedure.xsd">
      <TypeName>ProtezioneCivileFileUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schQuakeNotificationPollingTypedPolling.xsd">
      <TypeName>schQuakeNotificationPollingTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schQuakeNotificationUpdateStatusTypedProcedure.xsd">
      <TypeName>schQuakeNotificationUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileAlertTest.xsd">
      <TypeName>ProtezioneCivileAlertTest</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileFileCheckTypedProcedure.xsd">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileFileCheckTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileFileCheckTypedProcedure.Type.xsd">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileFileCheckTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileAllertaDigheWs.xsd">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAllertaDigheWs</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileAllertaDigheWs.Type.xsd">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAllertaDigheWs_Type</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\ProtezioneCivileAlert.xsd">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAlert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
    </Schema>
    <Schema Include="Schemas\Quake\QuakeTest.xsd">
      <TypeName>QuakeTest</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schN2QuakeEventInsertTypedProcedure.Type.xsd">
      <TypeName>schN2QuakeEventInsertTypedProcedure_Type</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schN2QuakeEventInsertTypedProcedure.xsd">
      <TypeName>schN2QuakeEventInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\schProtezioneCivileUpdateStatusTypedProcedure.xsd">
      <TypeName>schProtezioneCivileUpdateStatusTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\ProtezioneCivile\schProtezioneCivileTypedPolling.xsd">
      <TypeName>schProtezioneCivileTypedPolling</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schQuakeNotification.xsd">
      <TypeName>schQuakeNotification</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schEaiQuakeEventInsertResponse.xsd">
      <TypeName>schEaiQuakeEventInsertResponse</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schBiQuakeAnalysisInsertTypedProcedure.Table.xsd">
      <TypeName>schBiQuakeAnalysisInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schBiQuakeAnalysisInsertTypedProcedure.xsd">
      <TypeName>schBiQuakeAnalysisInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schEaiQuakeEventInsertTypedProcedure.Type.xsd">
      <TypeName>schEaiQuakeEventInsertTypedProcedure_Types</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schEaiQuakeEventInsertTypedProcedure.Table.xsd">
      <TypeName>schEaiQuakeEventInsertTypedProcedure_Table</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\schEaiQuakeEventInsertTypedProcedure.xsd">
      <TypeName>schEaiQuakeEventInsertTypedProcedure</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\QuakeML-Request.xsd">
      <TypeName>QuakeML_Request</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="PropertySchema.xsd">
      <TypeName>PropertySchema</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\QuakeML-1.2.xsd">
      <TypeName>QuakeML_1_2</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Quake\QuakeML-BED-1.2.xsd">
      <TypeName>QuakeML_BED_1_2</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Shared\A2A.EAI.Common.Shared.btproj">
      <Project>{fe830fb0-b65e-459d-b592-53001be3d7b6}</Project>
      <Name>A2A.EAI.Common.Shared</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Bindings\Quake\schControlliDigheTerremotiWs.BindingInfo.xml" />
    <Content Include="Bindings\Quake\schEaiQuakeEventInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\Quake\schBiQuakeAnalysisInsertTypedProcedure.bindinginfo.xml" />
    <Content Include="Bindings\ProtezioneCivile\schProtezioneCivileTypedPolling.bindinginfo.xml" />
    <Content Include="Bindings\Quake\sptQuakeNotificationUpdateStatus.bindinginfo.xml" />
    <Content Include="Bindings\Quake\rptQuakeNotificationPolling.bindinginfo.xml" />
    <Content Include="Bindings\ProtezioneCivile\ProtezioneCivileFileUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Pipeline Include="Pipelines\sppQuakeNotification.btp">
      <TypeName>sppQuakeNotification</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Quake\mapQuakeNotificationPollingToMailSend.btm">
      <SubType>Task</SubType>
      <TypeName>mapQuakeNotificationPollingToMailSend</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
    </Map>
    <Map Include="Maps\Quake\mapQuakeNotificationPollingToQuakeEventInsert.btm">
      <TypeName>mapQuakeNotificationPollingToQuakeEventInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Quake\mapQuakeNotificationPollingToUpdateStatus.btm">
      <TypeName>mapQuakeNotificationPollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\rppProtezioneCivileAlert.btp">
      <TypeName>rppProtezioneCivileAlert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\ProtezioneCivile\ProtezioneCivileAlertTestToProtezioneCivileAllertaDigheWs.btm">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAlertTestToProtezioneCivileAllertaDigheWs</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile</Namespace>
    </Map>
    <Pipeline Include="Pipelines\rppProtezioneCivile.btp">
      <SubType>Task</SubType>
      <TypeName>rppProtezioneCivile</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
    </Pipeline>
    <Map Include="Maps\ProtezioneCivile\ProtezioneCivileAlertToProtezioneCivileCheck.btm">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAlertToProtezioneCivileCheck</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile</Namespace>
    </Map>
    <Map Include="Maps\ProtezioneCivile\ProtezioneCivileAlertToProtezioneCivileAllertaDigheWs.btm">
      <SubType>Task</SubType>
      <TypeName>ProtezioneCivileAlertToProtezioneCivileAllertaDigheWs</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile</Namespace>
    </Map>
    <Pipeline Include="Pipelines\rppQuakeTest.btp">
      <TypeName>rppQuakeTest</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Quake\mapQuakeTestToEaiInsert.btm">
      <TypeName>mapQuakeTestToEaiInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Quake\mapEaiQuakeEventInsertResponseToN2QuakeEventInsert.btm">
      <TypeName>mapEaiQuakeEventInsertResponseToN2QuakeEventInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Map Include="Maps\Quake\mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs.btm">
      <TypeName>mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Content Include="Bindings\Quake\schN2QuakeEventInsertTypedProcedure.bindinginfo.xml" />
    <Map Include="Maps\ProtezioneCivile\mapProtezioneCivilePollingToUpdateStatus.btm">
      <TypeName>mapProtezioneCivilePollingToUpdateStatus</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Content Include="Bindings\ProtezioneCivile\schProtezioneCivileUpdateStatusTypedProcedure.bindinginfo.xml" />
    <Map Include="Maps\Quake\mapEaiQuakeEventInsertToNotification.btm">
      <TypeName>mapEaiQuakeEventInsertToNotification</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\rppEaiQuakeEventInsert.btp">
      <TypeName>rppEaiQuakeEventInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Quake\mapEaiQuakeEventInsertToBi.btm">
      <TypeName>mapEaiQuakeEventInsertToBi</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
    <Pipeline Include="Pipelines\rppQuake.btp">
      <TypeName>rppQuake</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Pipelines</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
    <Map Include="Maps\Quake\mapQuakeMLToEaiInsert.btm">
      <TypeName>mapQuakeMLToEaiInsert</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>