<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBusinessLogic.Integrazione.Common.Dighe" elementFormDefault="qualified" targetNamespace="http://NBDOBusinessLogic.Integrazione.Common.Dighe" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="messaggioProtezioneCivile" type="tns:MessaggioProtezioneCivile" />
  <xs:complexType name="MessaggioProtezioneCivile">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Identifier" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Sent" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="MsgType" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Info" type="tns:Info" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Info">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Onset" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Expires" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Category" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Event" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Description" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Areas" type="tns:Area" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="Area">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="AreaDesc" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="ValueName" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Value" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>