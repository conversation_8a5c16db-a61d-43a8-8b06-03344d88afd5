<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="quakeAnalysisType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="hqEventId" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="hqRegionName" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1000" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="hqOriginTime" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="hqLatitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="hqLongitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="hqDepth" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="hqMagnitudeValue" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="3" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="area" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Centrali_ID" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="NomeEstesoCentrale" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="50" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="comune" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="255" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="latitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="longitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="8" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="quakeAnalysisType" nillable="true" type="ns3:quakeAnalysisType" />
  <xs:complexType name="ArrayOfquakeAnalysisType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="quakeAnalysisType" type="ns3:quakeAnalysisType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfquakeAnalysisType" nillable="true" type="ns3:ArrayOfquakeAnalysisType" />
</xs:schema>