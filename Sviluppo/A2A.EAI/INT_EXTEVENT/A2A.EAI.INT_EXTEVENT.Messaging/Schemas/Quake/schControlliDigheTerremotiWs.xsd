<?xml version="1.0"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation=".\schControlliDigheTerremotiWs_DataContracts_Dighe.xsd" namespace="http://schemas.datacontract.org/2004/07/N2SoapWebApiCore.DataContracts.Dighe" />
  <xs:element name="inputTerremoti">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="1" name="id_terremoto" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="inputTerremotiResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/N2SoapWebApiCore.DataContracts.Dighe" minOccurs="1" name="inputTerremotiResult" nillable="true" type="q1:WsDigheResponse" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>