<?xml version="1.0"?>
<xs:schema xmlns:ser="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:tns="http://schemas.datacontract.org/2004/07/N2SoapWebApiCore.DataContracts.Dighe" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/N2SoapWebApiCore.DataContracts.Dighe" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation=".\schControlliDigheTerremotiWs_Dighe_Serialization_Arrays.xsd" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:complexType name="WsDigheResponse">
    <xs:sequence>
      <xs:element minOccurs="0" name="resLog" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="resMsg" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="statusCode" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="WsDigheResponse" nillable="true" type="tns:WsDigheResponse" />
</xs:schema>