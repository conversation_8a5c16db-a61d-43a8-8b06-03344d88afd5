<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="QuakeEventsType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="eventId" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="regionName" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="1000" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="originTime" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="latitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="3" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="longitude" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="3" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="depth" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="magnitudeType" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="32" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="magnitudeValue" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:decimal">
            <xs:totalDigits value="18" />
            <xs:fractionDigits value="3" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="isTest" nillable="true" type="xs:boolean" />

	</xs:sequence>
  </xs:complexType>
  <xs:element name="QuakeEventsType" nillable="true" type="ns3:QuakeEventsType" />
  <xs:complexType name="ArrayOfQuakeEventsType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="QuakeEventsType" type="ns3:QuakeEventsType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfQuakeEventsType" nillable="true" type="ns3:ArrayOfQuakeEventsType" />
</xs:schema>