<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns4="http://schemas.microsoft.com/Sql/2008/05/ProceduresResultSets/dbo/QuakeEventInsert" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/ProceduresResultSets/dbo/QuakeEventInsert" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">ProcedureResultSet.dbo.QuakeEventInsert</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="StoredProcedureResultSet0">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="XML_F52E2B61-18A1-11d1-B105-00805F49916B" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="StoredProcedureResultSet0" nillable="true" type="ns4:StoredProcedureResultSet0" />
  <xs:complexType name="ArrayOfStoredProcedureResultSet0">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="StoredProcedureResultSet0" type="ns4:StoredProcedureResultSet0" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfStoredProcedureResultSet0" nillable="true" type="ns4:ArrayOfStoredProcedureResultSet0" />
</xs:schema>