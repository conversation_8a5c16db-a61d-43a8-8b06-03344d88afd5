<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/ProceduresResultSets/dbo/QuakeEventN2Insert" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/TypedProcedures/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation=".\schN2QuakeEventInsertTypedProcedure.Type.xsd" namespace="http://schemas.microsoft.com/Sql/2008/05/ProceduresResultSets/dbo/QuakeEventN2Insert" />
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TypedProcedure.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="QuakeEventN2Insert">
    <xs:annotation>
      <xs:documentation>
        <doc:action xmlns:doc="http://schemas.microsoft.com/servicemodel/adapters/metadata/documentation">TypedProcedure/dbo/QuakeEventN2Insert</doc:action>
      </xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="quakeEventId" nillable="true" type="xs:long" />
        <xs:element minOccurs="0" maxOccurs="1" name="eventId" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="20" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element minOccurs="0" maxOccurs="1" name="regionName" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="1000" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element minOccurs="0" maxOccurs="1" name="originTime" nillable="true" type="xs:dateTime" />
        <xs:element minOccurs="0" maxOccurs="1" name="latitude" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:decimal">
              <xs:totalDigits value="18" />
              <xs:fractionDigits value="8" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element minOccurs="0" maxOccurs="1" name="longitude" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:decimal">
              <xs:totalDigits value="18" />
              <xs:fractionDigits value="8" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element minOccurs="0" maxOccurs="1" name="depth" nillable="true" type="xs:int" />
        <xs:element minOccurs="0" maxOccurs="1" name="magnitudeType" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:string">
              <xs:maxLength value="32" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
        <xs:element minOccurs="0" maxOccurs="1" name="magnitudeValue" nillable="true">
          <xs:simpleType>
            <xs:restriction base="xs:decimal">
              <xs:totalDigits value="18" />
              <xs:fractionDigits value="3" />
            </xs:restriction>
          </xs:simpleType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="QuakeEventN2InsertResponse">
    <xs:annotation>
      <xs:documentation>
        <doc:action xmlns:doc="http://schemas.microsoft.com/servicemodel/adapters/metadata/documentation">TypedProcedure/dbo/QuakeEventN2Insert/response</doc:action>
      </xs:documentation>
    </xs:annotation>
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" maxOccurs="1" name="StoredProcedureResultSet0" nillable="true" type="ns3:ArrayOfStoredProcedureResultSet0" />
        <xs:element minOccurs="1" maxOccurs="1" name="ReturnValue" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>