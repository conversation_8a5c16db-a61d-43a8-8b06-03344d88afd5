﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{98205E89-298C-4D85-BE0E-888230143EFD}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_EXTEVENT.Processes</RootNamespace>
    <AssemblyName>A2A.EAI.INT_EXTEVENT.Processes</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Bam.XLANGs, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsoft.BizTalk.Bam.XLANGs\v4.0_3.0.1.0__31bf3856ad364e35\Microsoft.BizTalk.Bam.XLANGs.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Azure.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=0ec334306cc72c98, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Azure.Services\v4.0_1.0.0.0__0ec334306cc72c98\Microsys.EAI.Framework.Azure.Services.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Schemas, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Microsys.EAI.Framework.Schemas\v4.0_1.0.0.0__e651f0f96466e9b3\Microsys.EAI.Framework.Schemas.dll</HintPath>
    </Reference>
    <Reference Include="Microsys.EAI.Framework.Services, Version=1.0.0.0, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>C:\Program Files (x86)\Microsys\Microsys.EAI.Framework\Microsys.EAI.Framework.Services.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Services\A2A.EAI.Common.Services.csproj">
      <Project>{16facaa8-43de-45b6-b1a8-bbbc0b8f7bbf}</Project>
      <Name>A2A.EAI.Common.Services</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\A2A.EAI.Common.Shared\A2A.EAI.Common.Shared.btproj">
      <Project>{fe830fb0-b65e-459d-b592-53001be3d7b6}</Project>
      <Name>A2A.EAI.Common.Shared</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_EXTEVENT.Messaging\A2A.EAI.INT_EXTEVENT.Messaging.btproj">
      <Project>{bfab451a-8abe-4651-89ad-7e230a24ceef}</Project>
      <Name>A2A.EAI.INT_EXTEVENT.Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\A2A.EAI.INT_EXTEVENT.Services\A2A.EAI.INT_EXTEVENT.Services.csproj">
      <Project>{99c06d9e-8d55-495d-a9c5-578cb98e120a}</Project>
      <Name>A2A.EAI.INT_EXTEVENT.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcQuake.odx">
      <TypeName>Quake</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcObjectDefinition.odx">
      <TypeName>prcObjectDefinition</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcProtezioneCivile.odx">
      <TypeName>ProtezioneCivileAllertaDighe</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcQuakeTest.odx">
      <SubType>Task</SubType>
      <TypeName>prcQuake_Copy</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcProtezioneCivileAllertaDighe.odx">
      <SubType>Task</SubType>
      <TypeName>prcProtezioneCivileAllertaDighe</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcProtezioneCivileAllertaDigheTest.odx">
      <SubType>Task</SubType>
      <TypeName>prcProtezioneCivileAllertaDighe_Copy</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
    </XLang>
  </ItemGroup>
  <ItemGroup>
    <XLang Include="prcQuakeNotification.odx">
      <TypeName>prcQuakeNotification</TypeName>
      <Namespace>A2A.EAI.INT_EXTEVENT.Processes</Namespace>
      <SubType>Task</SubType>
    </XLang>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>