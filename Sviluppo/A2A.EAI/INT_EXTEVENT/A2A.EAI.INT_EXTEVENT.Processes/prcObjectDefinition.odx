﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="8b6ddac4-613f-46ad-9ccb-bc4f20c4a396" LowerBound="1.1" HigherBound="38.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="d831eb7d-127c-4f90-8923-846bf4b2b9d5" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typEaiQuakeEventInsert" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="63abacf7-4766-4ce9-afdd-0f097192e897" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="bzt_QuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="70ab86a6-463f-490c-8931-8a09b84c73ba" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.46">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="4f9f0e07-20da-4a99-9b3d-261d4487e9fc" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="20.48" HigherBound="20.82">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="89bf33e4-e2ac-461e-a30d-d8b23012f987" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="9f2480af-ead2-4c21-9a8e-4979b1287750" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2ca99ef8-dc65-412f-a953-954b9f5eb3bf" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.56">
                    <om:Property Name="Ref" Value="Microsys.EAI.Framework.Schemas.Notification" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="35c9d86c-7d4a-4364-9ac9-d22a451f2bb6" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEmailType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a4d30dc4-6039-486d-a15f-95d611a0a60f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Services.XlangCustomFormatters.RawString" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Body" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7e077a16-2f37-41c0-899d-323309791c59" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Public" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgFaultType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d43348c9-7373-4f51-91bd-f6348a53ac5c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="BTS.soap_envelope_1__1.Fault" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultString" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="02c6a85a-6d34-4e0e-865a-459a69093b20" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgNotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="1d52d0b0-5d11-4bf8-a694-8d33e4fb688a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="eec508f1-f03f-4f9a-9c78-90d12fa5d97b" ParentLink="Module_ServiceDeclaration" LowerBound="30.1" HigherBound="37.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="1ef83211-d7de-4094-8670-edf764847a57" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype msgEmailType
    {
        body Microsys.EAI.Framework.Services.XlangCustomFormatters.RawString Body;
    };
    public messagetype msgFaultType
    {
        body BTS.soap_envelope_1__1.Fault faultString;
    };
    internal messagetype msgNotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameters;
    };
    internal porttype typEaiQuakeEventInsert
    {
        requestresponse bzt_QuakeEventInsert
        {
            msgEaiQuakeEventInsertRequestType, msgEaiQuakeEventInsertResponseType
        };
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            Microsys.EAI.Framework.Schemas.Notification
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcObjectDefinition
    {
        body ()
        {
        }
    }
}

