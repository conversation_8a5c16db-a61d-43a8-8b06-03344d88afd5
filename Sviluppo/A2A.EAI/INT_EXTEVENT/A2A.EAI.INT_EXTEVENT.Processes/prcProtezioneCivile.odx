﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="3ea04df6-2a5f-4837-b85f-43bfea41b10d" LowerBound="1.1" HigherBound="346.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="fb1b091c-2911-4001-bcde-0f371f5dc13a" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="27.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typProtezioneCivileGetFile" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3da09a54-d202-4195-bbc0-b354bcc061a4" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="getVigilanza" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="2b4587c5-8f0e-4901-aee6-017cec87be76" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="7d560662-2674-430a-9d92-eb021ca93fc6" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="20.37" HigherBound="20.59">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="OperationDeclaration" OID="f5be8c30-7561-48b3-b985-57ee023e2a89" ParentLink="PortType_OperationDeclaration" LowerBound="22.1" HigherBound="26.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="getCriticita" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="c99f069b-3486-4149-bc93-6fc5be6df618" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="24.13" HigherBound="24.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="4032cb3a-b523-4fff-860e-d4ca1aedc502" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="24.37" HigherBound="24.59">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="8b884337-dccf-46f8-8858-0c9962f5d8b8" ParentLink="Module_PortType" LowerBound="27.1" HigherBound="34.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typProtezioneCivilePolling" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="8f5a3cbb-474d-4566-9f8e-6fc8aba2df4d" ParentLink="PortType_OperationDeclaration" LowerBound="29.1" HigherBound="33.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="5b42e2e2-9dac-4b4b-8bd0-8d7b2635d198" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="31.13" HigherBound="31.48">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileTypedPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="2f442782-2565-4486-87e5-174e20f13f80" ParentLink="Module_PortType" LowerBound="34.1" HigherBound="41.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typProtezioneCivileWriteFile" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="ad5b399f-7e20-4807-b280-8af9eaaac0be" ParentLink="PortType_OperationDeclaration" LowerBound="36.1" HigherBound="40.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="SEND" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="82a04d76-a248-483c-af16-d5d72e52ef24" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="38.13" HigherBound="38.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="1b1edaed-3d45-4093-8316-8d87c4be6a21" ParentLink="Module_PortType" LowerBound="41.1" HigherBound="48.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typProtezioneCivileUpdateStatus" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="335d25b8-0d1f-4131-ab73-fd081562c713" ParentLink="PortType_OperationDeclaration" LowerBound="43.1" HigherBound="47.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="f6126f77-8a7f-4cc9-980d-63c2758786dd" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="45.13" HigherBound="45.55">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b4469f09-650a-4185-87be-87c4db5efb60" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="45.57" HigherBound="45.100">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="3a4a8841-8af7-4bb5-b39a-b4b0f0e901c0" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgProtezioneCivileTypedPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f40febec-a103-4cb8-993d-6a4729619eaf" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileTypedPolling.TypedPollingResultSet0" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="1a92c596-4423-4496-a2f5-5c4bd0f7a382" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgProtezioneCivileUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="67259f7d-2622-4e89-839d-0815d133bd12" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileUpdateStatusTypedProcedure.ProtezioneCivileUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="f88cb446-0419-423c-a672-e3913b43f751" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgProtezioneCivileUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="e37e0d3e-72ed-44f8-8f78-3dd0a62e6991" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileUpdateStatusTypedProcedure.ProtezioneCivileUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="e2868055-c792-4f72-9309-41ffb76ead71" ParentLink="Module_ServiceDeclaration" LowerBound="48.1" HigherBound="345.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcProtezioneCivile" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="5d537952-d58f-4561-89e5-a1c31020c8ae" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="71.1" HigherBound="72.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="19988503-d970-4f28-96b7-baadfc674945" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="72.1" HigherBound="73.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="658faaae-08fa-47e5-bf31-40763d352af2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="73.1" HigherBound="74.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f7892ab5-bfed-44e6-9a92-783918216158" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="74.1" HigherBound="75.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7490e405-89b6-451c-acd0-fd8147f867f6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="75.1" HigherBound="76.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c21d5d6a-5430-4bf9-ba51-3b52b2970ea7" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6290a795-3179-4e67-8161-1832694acf40" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="xmlMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="5bcad56e-1384-44f8-937d-28e71de070d5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="78.1" HigherBound="79.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d127cc57-2508-42d4-b20b-25f37865a34b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="79.1" HigherBound="80.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="getFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="91dc467f-8fa5-4717-ab59-8d203e5f5ea1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="80.1" HigherBound="81.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="isValidZip" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="733e99ba-c25e-4fb8-8734-fa2d769785b2" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bf5bdcc0-0f43-4bde-8b99-b4a9061d514c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileSendFile" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8b8aee7b-f1b7-4003-960d-65ef4824e052" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileTypedPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileTypedPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="21a2f31a-e113-436c-8b94-1a300a614060" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileGetFileRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2302b58f-afb5-4855-bb90-89730ed1fd8e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileGetFileResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="55d69b8d-5864-48dd-a145-39b9937563a5" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="af5bbb34-b7c8-4482-984f-5671227764d1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgProtezioneCivileUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="bb27f2d2-b086-4b92-aa4d-aaa717944134" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="9449cbb4-4fb8-4c78-b98e-f1c4a5c27a5d" ParentLink="ServiceBody_Statement" LowerBound="83.1" HigherBound="91.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ProtezioneCivilePollingIn" />
                    <om:Property Name="MessageName" Value="ProtezioneCivileTypedPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="2528246b-86d8-4fbe-942a-d5c48d92f882" ParentLink="ServiceBody_Statement" LowerBound="91.1" HigherBound="103.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;flowName = ProtezioneCivileTypedPolling.parameter.flowName;&#xD;&#xA;flowName = flowName.ToLower();&#xD;&#xA;&#xD;&#xA;isValidZip = false;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="dfd83f93-ad90-43d7-835a-a3af256602df" ParentLink="ServiceBody_Statement" LowerBound="103.1" HigherBound="111.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;Protezione Civile Allerta Dighe - GetFile&quot;,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="15f43036-3602-426b-83c8-33b88c6fafa1" ParentLink="ServiceBody_Statement" LowerBound="111.1" HigherBound="258.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Decision" OID="ff948a03-6d8c-4bdc-8101-476ce762991a" ParentLink="ComplexStatement_Statement" LowerBound="116.1" HigherBound="201.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="FlowName" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="adacfa93-662d-4b81-9e96-0ba3a1d32c6f" ParentLink="ReallyComplexStatement_Branch" LowerBound="117.21" HigherBound="153.1">
                            <om:Property Name="Expression" Value="flowName == &quot;vigilanza&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Vigilanza" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="c679d35c-523a-4da6-b75d-b652a55fb7b3" ParentLink="ComplexStatement_Statement" LowerBound="119.1" HigherBound="123.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Tipo&quot;, &quot;Vigilanza&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="fd84868d-6101-4ebf-bf20-b2d2892d8c89" ParentLink="ComplexStatement_Statement" LowerBound="123.1" HigherBound="125.1">
                                <om:Property Name="Expression" Value="getFileName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ProtCivGetFileName(flowName);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="GetFileName" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Decision" OID="4122b631-00af-4f6e-a98f-33b8e76038dd" ParentLink="ComplexStatement_Statement" LowerBound="125.1" HigherBound="152.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="FileName Found" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="e2d1429a-4da5-4ef4-8edd-655379b9ebcb" ParentLink="ReallyComplexStatement_Branch" LowerBound="126.25" HigherBound="145.1">
                                    <om:Property Name="Expression" Value="getFileName != &quot;No data found.&quot;" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Yes" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Construct" OID="a56e9923-3f1b-429f-bcc8-1e5fc8d5eabc" ParentLink="ComplexStatement_Statement" LowerBound="128.1" HigherBound="138.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Construct Request File Message" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessageAssignment" OID="5034241b-5ab1-477d-9ab5-b19d8de203be" ParentLink="ComplexStatement_Statement" LowerBound="131.1" HigherBound="137.1">
                                            <om:Property Name="Expression" Value="xmlMessage = new System.Xml.XmlDocument();&#xD;&#xA;xmlMessage.LoadXml(System.String.Format(@&quot;&lt;ns0:ProtezioneCivileRequest xmlns:ns0='http://A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivile'&gt;&lt;/ns0:ProtezioneCivileRequest&gt;&quot;));&#xD;&#xA;&#xD;&#xA;ProtezioneCivileGetFileRequest = xmlMessage;&#xD;&#xA;ProtezioneCivileGetFileRequest(A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileReq) = getFileName;" />
                                            <om:Property Name="ReportToAnalyst" Value="False" />
                                            <om:Property Name="Name" Value="Setup Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessageRef" OID="71a3a4fe-3a5b-490d-85cf-d7fec17430eb" ParentLink="Construct_MessageRef" LowerBound="129.39" HigherBound="129.69">
                                            <om:Property Name="Ref" Value="ProtezioneCivileGetFileRequest" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="b1662ac9-94ec-4b0a-a46f-4db2e43b2a92" ParentLink="ComplexStatement_Statement" LowerBound="138.1" HigherBound="140.1">
                                        <om:Property Name="PortName" Value="ProtezioneCivileGetFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileRequest" />
                                        <om:Property Name="OperationName" Value="getVigilanza" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="ad8e418a-542f-439a-a178-d31a6175ab74" ParentLink="ComplexStatement_Statement" LowerBound="140.1" HigherBound="142.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="ProtezioneCivileGetFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileResponse" />
                                        <om:Property Name="OperationName" Value="getVigilanza" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Send" OID="95f5294d-d912-4752-896c-121d626f35b0" ParentLink="ComplexStatement_Statement" LowerBound="142.1" HigherBound="144.1">
                                        <om:Property Name="PortName" Value="ProtezioneCivileWriteFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileResponse" />
                                        <om:Property Name="OperationName" Value="SEND" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="6d94fbb6-2e17-49f6-ab96-771c0da728cf" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="VariableAssignment" OID="ce054e2a-4e90-43eb-ae11-e88db3ab896e" ParentLink="ComplexStatement_Statement" LowerBound="147.1" HigherBound="151.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Note&quot;, &quot;File non trovato.&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="8be279ab-aa61-4e61-9ace-c6c722bbbb34" ParentLink="ReallyComplexStatement_Branch" LowerBound="153.26" HigherBound="189.1">
                            <om:Property Name="Expression" Value="flowName == &quot;criticita&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Criticità" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="54a3f0c5-5a08-4d0b-9f30-ac5341aff9dd" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="159.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Tipo&quot;, &quot;Criticità&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="f6976fb3-69cd-40bb-a87b-e52ad70fe9e1" ParentLink="ComplexStatement_Statement" LowerBound="159.1" HigherBound="161.1">
                                <om:Property Name="Expression" Value="getFileName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ProtCivGetFileName(flowName);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="GetFileName" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Decision" OID="434ba946-58f0-44ae-a56d-d565f174c908" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="188.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="FileName Found" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="409d4759-b026-46af-8bac-2d1afd265c63" ParentLink="ReallyComplexStatement_Branch" LowerBound="162.25" HigherBound="181.1">
                                    <om:Property Name="Expression" Value="getFileName != &quot;No data found.&quot;" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Yes" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Construct" OID="85a09836-f0c0-4bc8-9aa1-933e70b66135" ParentLink="ComplexStatement_Statement" LowerBound="164.1" HigherBound="174.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Construct Request File Message" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessageAssignment" OID="91efc55d-a17c-44fa-a18c-f30963983578" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="173.1">
                                            <om:Property Name="Expression" Value="xmlMessage = new System.Xml.XmlDocument();&#xD;&#xA;xmlMessage.LoadXml(System.String.Format(@&quot;&lt;ns0:ProtezioneCivileRequest xmlns:ns0='http://A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivile'&gt;&lt;/ns0:ProtezioneCivileRequest&gt;&quot;));&#xD;&#xA;&#xD;&#xA;ProtezioneCivileGetFileRequest = xmlMessage;&#xD;&#xA;ProtezioneCivileGetFileRequest(A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileReq) = getFileName;" />
                                            <om:Property Name="ReportToAnalyst" Value="False" />
                                            <om:Property Name="Name" Value="Setup Message" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessageRef" OID="632b7272-8724-4f09-9642-d5fb208e5d78" ParentLink="Construct_MessageRef" LowerBound="165.39" HigherBound="165.69">
                                            <om:Property Name="Ref" Value="ProtezioneCivileGetFileRequest" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="8c2dae50-918f-4564-9014-919622146acb" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="176.1">
                                        <om:Property Name="PortName" Value="ProtezioneCivileGetFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileRequest" />
                                        <om:Property Name="OperationName" Value="getCriticita" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="f3d28956-a331-4d52-84e0-81cbf0c4d67f" ParentLink="ComplexStatement_Statement" LowerBound="176.1" HigherBound="178.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="ProtezioneCivileGetFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileResponse" />
                                        <om:Property Name="OperationName" Value="getCriticita" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Send" OID="1acab07a-fe82-4b02-a67e-d183f4d6c3ba" ParentLink="ComplexStatement_Statement" LowerBound="178.1" HigherBound="180.1">
                                        <om:Property Name="PortName" Value="ProtezioneCivileWriteFileOut" />
                                        <om:Property Name="MessageName" Value="ProtezioneCivileGetFileResponse" />
                                        <om:Property Name="OperationName" Value="SEND" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="3fc9c692-b3d7-4440-88f0-e96ebace7e65" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="VariableAssignment" OID="69b2ef41-40d4-4777-9d17-941bab8f98fe" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="187.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Note&quot;, &quot;File non trovato.&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="988be167-a204-4b01-9a8b-2ac693f763c4" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="306b222c-4dcf-4b02-8270-0fda4f17eeaf" ParentLink="ComplexStatement_Statement" LowerBound="191.1" HigherBound="200.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Unknown flow name. &quot;);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="843c16f5-9d81-4726-9699-ea13230fda92" ParentLink="Scope_Catch" LowerBound="204.1" HigherBound="218.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d588b494-3864-4b8c-859a-d7dd623bdc30" ParentLink="Catch_Statement" LowerBound="207.1" HigherBound="217.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Protezione Civile - Error downloading file. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="a14891b0-f320-4eb5-a926-d01dc7a91285" ParentLink="Scope_Catch" LowerBound="218.1" HigherBound="230.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0ab3b367-98c7-441d-a1f1-761195e87c09" ParentLink="Catch_Statement" LowerBound="221.1" HigherBound="229.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Protezione Civile - Error writing file. &quot;);&#xD;&#xA;errorMessage.Append(deliveryExc.ErrorDescription);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="8aad3a78-5c09-489a-a687-590f21f0d337" ParentLink="Scope_Catch" LowerBound="230.1" HigherBound="243.1">
                        <om:Property Name="ExceptionName" Value="xlangExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="XLang Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="626c2aa9-6082-4e85-81af-4fe8bccfdd7c" ParentLink="Catch_Statement" LowerBound="233.1" HigherBound="242.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Protezione Civile - Error downloading file. &quot;);&#xD;&#xA;errorMessage.Append(xlangExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="7b96e1f9-7774-4e56-b6bc-bba75ee68629" ParentLink="Scope_Catch" LowerBound="243.1" HigherBound="256.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.SystemException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="19f71fe1-1856-46a8-97d2-18897d66a129" ParentLink="Catch_Statement" LowerBound="246.1" HigherBound="255.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Protezione Civile - Error downloading file. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="5ec39c73-bed1-4a5a-9b50-46cb428771c9" ParentLink="ServiceBody_Statement" LowerBound="258.1" HigherBound="302.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="UpdateStatus" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="2f55a2a7-3768-416e-95ef-1d8f55516b4f" ParentLink="ComplexStatement_Statement" LowerBound="263.1" HigherBound="271.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="39e6a040-4d7b-4060-b69c-2bc78d69c954" ParentLink="ComplexStatement_Statement" LowerBound="266.1" HigherBound="268.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.mapProtezioneCivilePollingToUpdateStatus" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="b9337c65-e73e-4800-b07f-1b04b10c5dd0" ParentLink="Transform_OutputMessagePartRef" LowerBound="267.36" HigherBound="267.81">
                                <om:Property Name="MessageRef" Value="ProtezioneCivileUpdateStatusRequest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="9d2b39d7-a988-4843-a41f-657075e80abf" ParentLink="Transform_InputMessagePartRef" LowerBound="267.180" HigherBound="267.218">
                                <om:Property Name="MessageRef" Value="ProtezioneCivileTypedPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="629ba78b-6523-40be-9dbe-fd6be5680d9c" ParentLink="ComplexStatement_Statement" LowerBound="268.1" HigherBound="270.1">
                            <om:Property Name="Expression" Value="ProtezioneCivileUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Assign Status" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="9f2a0baa-d98c-4369-a41d-92e4c82f210c" ParentLink="Construct_MessageRef" LowerBound="264.31" HigherBound="264.66">
                            <om:Property Name="Ref" Value="ProtezioneCivileUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="460fe6b3-8dc4-4656-9f89-689ff258a1c6" ParentLink="ComplexStatement_Statement" LowerBound="271.1" HigherBound="273.1">
                        <om:Property Name="PortName" Value="ProtezioneCivileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ProtezioneCivileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="b16188d6-f7a1-44b9-9271-cb7fa5cf3119" ParentLink="ComplexStatement_Statement" LowerBound="273.1" HigherBound="275.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ProtezioneCivileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ProtezioneCivileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="d8e00fcd-61af-4384-ae82-8743cdd912e0" ParentLink="Scope_Catch" LowerBound="278.1" HigherBound="289.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="09b884ce-519e-4101-a4b1-588b86cfd249" ParentLink="Catch_Statement" LowerBound="281.1" HigherBound="288.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'aggiornamento dello stato Report Metano.&quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="6c7bf460-5c71-4459-ae0c-c7619390d1b5" ParentLink="Scope_Catch" LowerBound="289.1" HigherBound="300.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="3ecacf9a-9f8d-4dc2-b104-91548a555b40" ParentLink="Catch_Statement" LowerBound="292.1" HigherBound="299.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'aggiornamento dello stato Report Metano.&quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="dbfdee81-f6f1-4550-ae8d-c362cfe7a9a7" ParentLink="ServiceBody_Statement" LowerBound="302.1" HigherBound="336.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="b1b02d04-d81e-45e6-8c28-ddbc9d492b17" ParentLink="ReallyComplexStatement_Branch" LowerBound="303.13" HigherBound="306.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="37c8314e-b3a5-42a4-b1c2-d6c7d5f75a7e" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="27378a73-6139-45ba-a6b8-dda48e987974" ParentLink="ComplexStatement_Statement" LowerBound="308.1" HigherBound="335.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="bef735bc-a425-4944-b967-64fea4a28b65" ParentLink="ComplexStatement_Statement" LowerBound="313.1" HigherBound="331.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="0298146c-f792-43d4-94d0-71d35a159aea" ParentLink="ComplexStatement_Statement" LowerBound="316.1" HigherBound="330.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtCiv;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="faed5aa1-a842-4f3e-aa46-2e56aa1ba67a" ParentLink="Construct_MessageRef" LowerBound="314.35" HigherBound="314.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="ea92e374-3717-4edd-a536-4e279ddc9cd3" ParentLink="ComplexStatement_Statement" LowerBound="331.1" HigherBound="333.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="cd9e6701-a307-4dae-b19a-e61cc830087b" ParentLink="ServiceBody_Statement" LowerBound="336.1" HigherBound="343.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5d1ac220-ac49-42e3-a2d6-06dfbd1b6863" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="51.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="204" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="e6b43f76-9ab5-47c3-ba19-d7b1ec4a4963" ParentLink="PortDeclaration_CLRAttribute" LowerBound="51.1" HigherBound="52.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="41a24a2d-6dfd-4dcc-bb44-6c61cc3256a3" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="55.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typProtezioneCivilePolling" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivilePollingIn" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="6e635244-f247-4112-a3c4-8d62fb2a49a5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2672c1b4-f38e-458d-b7d0-2734d8549e09" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="55.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="43" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typProtezioneCivileGetFile" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileGetFileOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="ae3d4e4c-b831-4029-b472-ac718adac80e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="55.1" HigherBound="56.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b4d04208-2a77-4941-abc6-ca25f5093322" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="58.1" HigherBound="61.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="141" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typProtezioneCivileUpdateStatus" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="155c30bb-6192-4d4c-9148-f58b0187f1d5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="58.1" HigherBound="59.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="36050a66-b7b1-4707-841d-2039e2e1616b" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="61.1" HigherBound="64.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="82" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typProtezioneCivileWriteFile" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileWriteFileOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="43400ba3-7ade-4f52-8a48-c6ee559c703e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="61.1" HigherBound="62.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype msgProtezioneCivileTypedPollingType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileTypedPolling.TypedPollingResultSet0 parameter;
    };
    internal messagetype msgProtezioneCivileUpdateStatusRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileUpdateStatusTypedProcedure.ProtezioneCivileUpdateStatus parameter;
    };
    internal messagetype msgProtezioneCivileUpdateStatusResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.schProtezioneCivileUpdateStatusTypedProcedure.ProtezioneCivileUpdateStatusResponse parameter;
    };
    internal porttype typProtezioneCivileGetFile
    {
        requestresponse getVigilanza
        {
            System.Xml.XmlDocument, System.Xml.XmlDocument
        };
        requestresponse getCriticita
        {
            System.Xml.XmlDocument, System.Xml.XmlDocument
        };
    };
    internal porttype typProtezioneCivilePolling
    {
        oneway Receive
        {
            msgProtezioneCivileTypedPollingType
        };
    };
    internal porttype typProtezioneCivileWriteFile
    {
        oneway SEND
        {
            System.Xml.XmlDocument
        };
    };
    internal porttype typProtezioneCivileUpdateStatus
    {
        requestresponse ProtezioneCivileUpdateStatus
        {
            msgProtezioneCivileUpdateStatusRequestType, msgProtezioneCivileUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcProtezioneCivile
    {
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typProtezioneCivilePolling ProtezioneCivilePollingIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typProtezioneCivileGetFile ProtezioneCivileGetFileOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typProtezioneCivileUpdateStatus ProtezioneCivileUpdateStatusOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typProtezioneCivileWriteFile ProtezioneCivileWriteFileOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message System.Xml.XmlDocument ProtezioneCivileSendFile;
        message msgProtezioneCivileTypedPollingType ProtezioneCivileTypedPolling;
        message System.Xml.XmlDocument ProtezioneCivileGetFileRequest;
        message System.Xml.XmlDocument ProtezioneCivileGetFileResponse;
        message msgProtezioneCivileUpdateStatusRequestType ProtezioneCivileUpdateStatusRequest;
        message msgProtezioneCivileUpdateStatusResponseType ProtezioneCivileUpdateStatusResponse;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        System.Xml.XmlDocument xmlMessage;
        System.String flowName;
        System.String getFileName;
        System.Boolean isValidZip;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9449cbb4-4fb8-4c78-b98e-f1c4a5c27a5d")]
            activate receive (ProtezioneCivilePollingIn.Receive, ProtezioneCivileTypedPolling);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            xmlMessage = new System.Xml.XmlDocument();
            flowName = "";
            getFileName = "";
            isValidZip = true;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("2528246b-86d8-4fbe-942a-d5c48d92f882")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            flowName = ProtezioneCivileTypedPolling.parameter.flowName;
            flowName = flowName.ToLower();
            
            isValidZip = false;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("dfd83f93-ad90-43d7-835a-a3af256602df")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "Protezione Civile Allerta Dighe - GetFile",
            "instanceId", activityInstanceId
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("15f43036-3602-426b-83c8-33b88c6fafa1")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("ff948a03-6d8c-4bdc-8101-476ce762991a")]
                    if (flowName == "vigilanza")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c679d35c-523a-4da6-b75d-b652a55fb7b3")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                        "Tipo", "Vigilanza"
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fd84868d-6101-4ebf-bf20-b2d2892d8c89")]
                        getFileName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ProtCivGetFileName(flowName);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4122b631-00af-4f6e-a98f-33b8e76038dd")]
                        if (getFileName != "No data found.")
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a56e9923-3f1b-429f-bcc8-1e5fc8d5eabc")]
                            construct ProtezioneCivileGetFileRequest
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("5034241b-5ab1-477d-9ab5-b19d8de203be")]
                                xmlMessage = new System.Xml.XmlDocument();
                                xmlMessage.LoadXml(System.String.Format(@"<ns0:ProtezioneCivileRequest xmlns:ns0='http://A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivile'></ns0:ProtezioneCivileRequest>"));
                                
                                ProtezioneCivileGetFileRequest = xmlMessage;
                                ProtezioneCivileGetFileRequest(A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileReq) = getFileName;
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b1662ac9-94ec-4b0a-a46f-4db2e43b2a92")]
                            send (ProtezioneCivileGetFileOut.getVigilanza, ProtezioneCivileGetFileRequest);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ad8e418a-542f-439a-a178-d31a6175ab74")]
                            receive (ProtezioneCivileGetFileOut.getVigilanza, ProtezioneCivileGetFileResponse);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("95f5294d-d912-4752-896c-121d626f35b0")]
                            send (ProtezioneCivileWriteFileOut.SEND, ProtezioneCivileGetFileResponse);
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ce054e2a-4e90-43eb-ae11-e88db3ab896e")]
                            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                            "Note", "File non trovato."
                            );
                        }
                    }
                    else if (flowName == "criticita")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("54a3f0c5-5a08-4d0b-9f30-ac5341aff9dd")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                        "Tipo", "Criticità"
                        );
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f6976fb3-69cd-40bb-a87b-e52ad70fe9e1")]
                        getFileName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ProtCivGetFileName(flowName);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("434ba946-58f0-44ae-a56d-d565f174c908")]
                        if (getFileName != "No data found.")
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("85a09836-f0c0-4bc8-9aa1-933e70b66135")]
                            construct ProtezioneCivileGetFileRequest
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("91efc55d-a17c-44fa-a18c-f30963983578")]
                                xmlMessage = new System.Xml.XmlDocument();
                                xmlMessage.LoadXml(System.String.Format(@"<ns0:ProtezioneCivileRequest xmlns:ns0='http://A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivile'></ns0:ProtezioneCivileRequest>"));
                                
                                ProtezioneCivileGetFileRequest = xmlMessage;
                                ProtezioneCivileGetFileRequest(A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileReq) = getFileName;
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("8c2dae50-918f-4564-9014-919622146acb")]
                            send (ProtezioneCivileGetFileOut.getCriticita, ProtezioneCivileGetFileRequest);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f3d28956-a331-4d52-84e0-81cbf0c4d67f")]
                            receive (ProtezioneCivileGetFileOut.getCriticita, ProtezioneCivileGetFileResponse);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1acab07a-fe82-4b02-a67e-d183f4d6c3ba")]
                            send (ProtezioneCivileWriteFileOut.SEND, ProtezioneCivileGetFileResponse);
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("69b2ef41-40d4-4777-9d17-941bab8f98fe")]
                            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                            "Note", "File non trovato."
                            );
                        }
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("306b222c-4dcf-4b02-8270-0fda4f17eeaf")]
                        errorMessage.Append("Unknown flow name. ");
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.FailedRetryNotAllowed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.ApplicationManaged;
                        
                        
                        
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("843c16f5-9d81-4726-9699-ea13230fda92")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d588b494-3864-4b8c-859a-d7dd623bdc30")]
                        errorMessage.Append("Protezione Civile - Error downloading file. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a14891b0-f320-4eb5-a926-d01dc7a91285")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ab3b367-98c7-441d-a1f1-761195e87c09")]
                        errorMessage.Append("Protezione Civile - Error writing file. ");
                        errorMessage.Append(deliveryExc.ErrorDescription);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8aad3a78-5c09-489a-a687-590f21f0d337")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException xlangExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("626c2aa9-6082-4e85-81af-4fe8bccfdd7c")]
                        errorMessage.Append("Protezione Civile - Error downloading file. ");
                        errorMessage.Append(xlangExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7b96e1f9-7774-4e56-b6bc-bba75ee68629")]
                    catch (System.SystemException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("19f71fe1-1856-46a8-97d2-18897d66a129")]
                        errorMessage.Append("Protezione Civile - Error downloading file. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5ec39c73-bed1-4a5a-9b50-46cb428771c9")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2f55a2a7-3768-416e-95ef-1d8f55516b4f")]
                    construct ProtezioneCivileUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("39e6a040-4d7b-4060-b69c-2bc78d69c954")]
                        transform (ProtezioneCivileUpdateStatusRequest.parameter) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.mapProtezioneCivilePollingToUpdateStatus (ProtezioneCivileTypedPolling.parameter);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("629ba78b-6523-40be-9dbe-fd6be5680d9c")]
                        ProtezioneCivileUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("460fe6b3-8dc4-4656-9f89-689ff258a1c6")]
                    send (ProtezioneCivileUpdateStatusOut.ProtezioneCivileUpdateStatus, ProtezioneCivileUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b16188d6-f7a1-44b9-9271-cb7fa5cf3119")]
                    receive (ProtezioneCivileUpdateStatusOut.ProtezioneCivileUpdateStatus, ProtezioneCivileUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d8e00fcd-61af-4384-ae82-8743cdd912e0")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("09b884ce-519e-4101-a4b1-588b86cfd249")]
                        errorMessage.Append("Errore durante l'aggiornamento dello stato Report Metano.");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("6c7bf460-5c71-4459-ae0c-c7619390d1b5")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3ecacf9a-9f8d-4dc2-b104-91548a555b40")]
                        errorMessage.Append("Errore durante l'aggiornamento dello stato Report Metano.");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("dbfdee81-f6f1-4550-ae8d-c362cfe7a9a7")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("27378a73-6139-45ba-a6b8-dda48e987974")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bef735bc-a425-4944-b967-64fea4a28b65")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0298146c-f792-43d4-94d0-71d35a159aea")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtCiv;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea92e374-3717-4edd-a536-4e279ddc9cd3")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cd9e6701-a307-4dae-b19a-e61cc830087b")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

