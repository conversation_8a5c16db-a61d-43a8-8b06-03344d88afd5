﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="81e7b361-75db-4367-96ca-dc47c8d976bd" LowerBound="1.1" HigherBound="430.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="4de70f44-5031-4082-b96a-959c024ade14" ParentLink="Module_PortType" LowerBound="32.1" HigherBound="39.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:portType name=&quot;WsAllertaDigheProtezioneCivileSoap&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="769cfc2b-f83d-41c5-96f0-d7142e6e9d4c" ParentLink="PortType_OperationDeclaration" LowerBound="34.1" HigherBound="38.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:operation name=&quot;ImportAllertaDigheProtezioneCivile&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="ImportAllertaDigheProtezioneCivile" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="MessageRef" OID="302a0002-9971-4e77-b68a-982761067bab" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="36.56" HigherBound="36.98">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:output message=&quot;http://NBDOWS/AppCode/WsAllertaDigheProtezioneCivile.org/:ImportAllertaDigheProtezioneCivileSoapOut&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="1720239c-a7de-42f9-a18d-c4da58a5536b" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="36.100" HigherBound="36.120">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgFaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="a50a0204-ccac-4e5e-b0a4-d5293d6c9e40" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="36.13" HigherBound="36.54">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:input message=&quot;http://NBDOWS/AppCode/WsAllertaDigheProtezioneCivile.org/:ImportAllertaDigheProtezioneCivileSoapIn&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="f22f8f50-2d25-4203-b996-b60fac511f84" ParentLink="Module_PortType" LowerBound="39.1" HigherBound="46.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheInType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="252b7901-f635-4f0f-9c39-6d6637b33617" ParentLink="PortType_OperationDeclaration" LowerBound="41.1" HigherBound="45.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="52e8fb93-1603-497d-96f4-f558d3d5280a" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="43.13" HigherBound="43.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="1d661343-27ad-43a1-be2d-1dda6797ce50" ParentLink="Module_PortType" LowerBound="46.1" HigherBound="53.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheCheckOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="b3217573-da8e-4080-a247-dfc076558632" ParentLink="PortType_OperationDeclaration" LowerBound="48.1" HigherBound="52.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileCheck" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="c25ae3f3-7223-45df-b814-e52c7d9f8676" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="50.13" HigherBound="50.49">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileCheckRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="930c2b9e-1028-43f2-b883-b3d4e3e1c9d8" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="50.51" HigherBound="50.88">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileCheckResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="f3ace929-2129-4b76-bbea-a0d950912417" ParentLink="Module_PortType" LowerBound="53.1" HigherBound="60.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheFileUpdateStatusOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="43300354-f4f0-4a7d-aeab-2eea05b420da" ParentLink="PortType_OperationDeclaration" LowerBound="55.1" HigherBound="59.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="22563255-fb62-46a7-adea-35cd9664da30" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="57.13" HigherBound="57.56">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="2e8446e3-b197-42cc-8cec-072fe8d9d04b" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="57.58" HigherBound="57.102">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="205e82e3-8d86-4bf7-bf1c-afc887719f17" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:message name=&quot;ImportAllertaDigheProtezioneCivileSoapOut&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4b8c3a4e-e0b1-4fab-8de4-8a7a1f8a3703" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAllertaDigheWs.ImportAllertaDigheProtezioneCivileResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:part name=&quot;parameters&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="7aa41cf4-ad7c-4eb3-b86f-84dbdf29768e" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:message name=&quot;ImportAllertaDigheProtezioneCivileSoapIn&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="ccbf3b4c-2c43-4a68-8cca-ffa66146ebd6" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAllertaDigheWs.ImportAllertaDigheProtezioneCivile" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:part name=&quot;parameters&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b07860a2-1807-415a-b56d-105056155465" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertInType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="1654a41a-b31c-4ebd-ba8f-ed9052ff44a0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAlert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="0fe67125-129e-4988-afd5-7da224532efe" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileFileCheckRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="68e5f9e2-7b94-41a1-ab7f-a59e07f13fc0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileCheckTypedProcedure.ProtezioneCivileFileCheck" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="450e5aed-0eca-4765-85a9-461459153f80" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileFileCheckResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="9529702d-b461-4680-a4ad-f3602eac97d7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileCheckTypedProcedure.ProtezioneCivileFileCheckResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="1964339c-778a-4ae0-8b73-8a2a15c56da2" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileFileUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="f92c0c56-05da-47be-b8d2-cde9c99a7246" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure.ProtezioneCivileFileUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="e5e25c08-408c-48c7-828f-bfcc8259f792" ParentLink="Module_MessageType" LowerBound="28.1" HigherBound="32.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileFileUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3b879039-1a4c-418c-b774-d43a1eb43ae7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure.ProtezioneCivileFileUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="41ee7764-eb3a-4207-a2bd-309be51c2505" ParentLink="Module_ServiceDeclaration" LowerBound="60.1" HigherBound="429.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcProtezioneCivileAllertaDighe" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="0798c4db-9719-45f0-a54e-21891382c911" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="83.1" HigherBound="84.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4a1cb5b8-b88b-4a29-bfc3-48cd310719ba" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="84.1" HigherBound="85.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="168244ad-51d2-4d60-b0d7-5099cd2a2f5f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="85.1" HigherBound="86.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2456ee70-a371-4c85-9884-0b83a295ddd0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="86.1" HigherBound="87.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7b6f2056-4d38-4cc2-a2e7-33654c6c3bb8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="87.1" HigherBound="88.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3f67adbf-9938-43c8-98ad-ec2d89c154b6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="88.1" HigherBound="89.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a4259e4e-4460-49f3-a8af-4cc1d9c4f4a8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="89.1" HigherBound="90.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ac7fa867-8805-4687-afad-3541603a9629" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="90.1" HigherBound="91.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f34d6cb-9b82-4cc6-ab04-45d2516ea0ac" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="91.1" HigherBound="92.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2f7d76f3-3756-47ea-abaf-dbfd6520bfd5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="92.1" HigherBound="93.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="172ce24b-3aab-44b4-9e02-3aba58bdb32d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="93.1" HigherBound="94.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="newData" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="25181ce1-3d0b-4c1c-a74a-b6b8a6054e68" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="94.1" HigherBound="95.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Services.ResultStatus" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultStatus" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7ab7a630-b2f9-4780-9916-70f0daa4a343" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="95.1" HigherBound="96.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="xmlMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f06b13fa-79a3-44d9-a3ad-c8705c723759" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="96.1" HigherBound="97.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="fileType" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f3980ca8-08de-4a34-88ea-9c45a46f0b85" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="74.1" HigherBound="75.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="78e776ec-2348-405d-b3e1-dec9cccdbe49" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="75.1" HigherBound="76.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheXml" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c4aa7914-a785-4eae-b0c9-092b090cf6d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2d640b36-40b4-42a9-9cf4-9e66ecc91b88" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="38f1536d-e397-454a-898b-bbc9551441c7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="78.1" HigherBound="79.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileCheckRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileCheckRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4894f63f-9fd2-479a-b08b-aff06da2607d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="79.1" HigherBound="80.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileCheckResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileCheckResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2e5d455a-d5e0-4e79-bcc8-3c8e8c1cd645" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="80.1" HigherBound="81.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheRaw" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="ea03c811-a730-4bbe-8d46-6815302a3b1f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="81.1" HigherBound="82.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8154686a-ac11-4270-b8fe-0b04e2abcbe7" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="82.1" HigherBound="83.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileFileUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileFileUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="7626f217-a196-4831-bc2f-07a5a32144ee" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="49309183-d055-48d5-a337-0a3d5ae5d675" ParentLink="ServiceBody_Statement" LowerBound="99.1" HigherBound="109.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheIn" />
                    <om:Property Name="MessageName" Value="ProtezioneCivileAllertaDigheRaw" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="bc0519ac-2869-41bb-b18b-024f0b10b63c" ParentLink="ServiceBody_Statement" LowerBound="109.1" HigherBound="123.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(ProtezioneCivileAllertaDigheRaw);&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(ProtezioneCivileAllertaDigheRaw);&#xD;&#xA;&#xD;&#xA;newData = false;&#xD;&#xA;&#xD;&#xA;fileType = System.String.Empty;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0574ec94-a168-4505-ada5-9ce6b98a57be" ParentLink="ServiceBody_Statement" LowerBound="123.1" HigherBound="133.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Flusso&quot;, &quot;Protezione Civile Allerta Dighe - Process&quot;,&#xD;&#xA;&quot;Test&quot;, false);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="9d1a1d4d-e7cd-4e25-a6d8-185aab6f5a1b" ParentLink="ServiceBody_Statement" LowerBound="133.1" HigherBound="328.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="17613948-d577-4f9f-94c8-9f458c3deb69" ParentLink="ComplexStatement_Statement" LowerBound="138.1" HigherBound="148.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check CADES" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageAssignment" OID="ab60ae5a-b9b1-479c-a52c-944f31e05ac2" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="147.1">
                            <om:Property Name="Expression" Value="resultStatus = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ExtractContentFromCades(ProtezioneCivileAllertaDigheRaw);&#xD;&#xA;&#xD;&#xA;xmlMessage.LoadXml(resultStatus.ReturnXmlString);&#xD;&#xA;&#xD;&#xA;ProtezioneCivileAllertaDigheXml.parameter = xmlMessage;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Extract" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="493f51f8-0bfc-4bf6-a03d-1fd2ca6db63d" ParentLink="Construct_MessageRef" LowerBound="139.31" HigherBound="139.62">
                            <om:Property Name="Ref" Value="ProtezioneCivileAllertaDigheXml" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="935c7cb4-929c-4815-9790-e528af40763d" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="150.1">
                        <om:Property Name="Expression" Value="fileType = ProtezioneCivileAllertaDigheXml.parameter.msgType;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Set File Type" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="687d0b03-ce34-4afe-bbf1-fff3abe824e2" ParentLink="ComplexStatement_Statement" LowerBound="150.1" HigherBound="308.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Check ResultCode" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="c9509739-d035-461a-b899-263a3100297b" ParentLink="ReallyComplexStatement_Branch" LowerBound="151.21" HigherBound="294.1">
                            <om:Property Name="Expression" Value="resultStatus.ResultCode == &quot;OK&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="OK" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Scope" OID="d308f0c3-c18c-4a99-ac0d-2869602adeed" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="216.1">
                                <om:Property Name="InitializedTransactionType" Value="True" />
                                <om:Property Name="IsSynchronized" Value="False" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Check New Data" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Construct" OID="d95de2be-eabc-411c-a23c-1be179d01b1f" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="167.1">
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Construct Check Message" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Transform" OID="1ec34984-2cf4-4429-86b9-d397c2a4cd1e" ParentLink="ComplexStatement_Statement" LowerBound="161.1" HigherBound="163.1">
                                        <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertToProtezioneCivileCheck" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Setup Message" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="MessagePartRef" OID="61120e1c-dca5-49d6-9f0c-b306d1d4fd70" ParentLink="Transform_InputMessagePartRef" LowerBound="162.193" HigherBound="162.234">
                                            <om:Property Name="MessageRef" Value="ProtezioneCivileAllertaDigheXml" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_3" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="MessagePartRef" OID="380e2de4-7223-4e2d-b7ed-137d0e946be1" ParentLink="Transform_OutputMessagePartRef" LowerBound="162.48" HigherBound="162.90">
                                            <om:Property Name="MessageRef" Value="ProtezioneCivileFileCheckRequest" />
                                            <om:Property Name="PartRef" Value="parameter" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="MessagePartReference_4" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="MessageAssignment" OID="aa3431ec-0da3-448f-a57f-3868b2c230a5" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="166.1">
                                        <om:Property Name="Expression" Value="ProtezioneCivileFileCheckRequest.parameter.activityId = activityInstanceId;&#xD;&#xA;ProtezioneCivileFileCheckRequest.parameter.fileName = originalFileName;" />
                                        <om:Property Name="ReportToAnalyst" Value="False" />
                                        <om:Property Name="Name" Value="MessageAssignment_1" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="MessageRef" OID="e8596baa-6e6e-488d-b185-abd50429f5de" ParentLink="Construct_MessageRef" LowerBound="159.43" HigherBound="159.75">
                                        <om:Property Name="Ref" Value="ProtezioneCivileFileCheckRequest" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="e1aa63fa-93d8-47c3-8795-9131b762c416" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                                    <om:Property Name="Expression" Value="dataInizioDb = System.DateTimeOffset.Now;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Take Time" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Send" OID="23145f94-16f1-4bc5-9329-c5520f66c610" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                                    <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheCheckOut" />
                                    <om:Property Name="MessageName" Value="ProtezioneCivileFileCheckRequest" />
                                    <om:Property Name="OperationName" Value="ProtezioneCivileFileCheck" />
                                    <om:Property Name="OperationMessageName" Value="Request" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Send" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Receive" OID="3c64e56a-eb31-4d14-ae69-615f3a9d39eb" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="173.1">
                                    <om:Property Name="Activate" Value="False" />
                                    <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheCheckOut" />
                                    <om:Property Name="MessageName" Value="ProtezioneCivileFileCheckResponse" />
                                    <om:Property Name="OperationName" Value="ProtezioneCivileFileCheck" />
                                    <om:Property Name="OperationMessageName" Value="Response" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Receive" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="b067a2e2-45d1-4a6e-8305-c80160942e5c" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="179.1">
                                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio DB&quot;, dataInizioDb,&#xD;&#xA;&quot;Data Fine DB&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata DB&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="BAM Update" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="VariableAssignment" OID="13440d1b-6c2f-4fc4-aa52-ec366d0a5e80" ParentLink="ComplexStatement_Statement" LowerBound="179.1" HigherBound="181.1">
                                    <om:Property Name="Expression" Value="newData = ProtezioneCivileFileCheckResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.newData;" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Set Vars" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="Catch" OID="550d2118-4dfa-43c8-93fd-f702be7a166f" ParentLink="Scope_Catch" LowerBound="184.1" HigherBound="199.1">
                                    <om:Property Name="ExceptionName" Value="soapExc" />
                                    <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Soap Exception" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="2d1f2411-77e3-4a83-924f-8203ea01c43f" ParentLink="Catch_Statement" LowerBound="187.1" HigherBound="198.1">
                                        <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la verifica di nuovi file.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="WriteLog" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Catch" OID="2193588a-731c-426c-85ec-e06d264d1cb6" ParentLink="Scope_Catch" LowerBound="199.1" HigherBound="214.1">
                                    <om:Property Name="ExceptionName" Value="systemExc" />
                                    <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                    <om:Property Name="IsFaultMessage" Value="False" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="System Exception" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="9f4b84b8-002a-42b6-848e-c647dcaaa6f1" ParentLink="Catch_Statement" LowerBound="202.1" HigherBound="213.1">
                                        <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la verifica di nuovi file.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="WriteLog" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Decision" OID="3088fc4c-fd3f-4d2c-9c2e-b3ba0e018bae" ParentLink="ComplexStatement_Statement" LowerBound="216.1" HigherBound="293.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="NewData" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="1f92d18b-c7bf-4318-8355-de0d2ab6942d" ParentLink="ReallyComplexStatement_Branch" LowerBound="217.25" HigherBound="293.1">
                                    <om:Property Name="Expression" Value="newData" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Yes" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="Scope" OID="d09ba5c5-bc36-470b-a3e3-b9da6b75040a" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="292.1">
                                        <om:Property Name="InitializedTransactionType" Value="True" />
                                        <om:Property Name="IsSynchronized" Value="False" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Main Transaction" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="Construct" OID="eacdad8a-59c4-4a64-9c48-84a9193cb4da" ParentLink="ComplexStatement_Statement" LowerBound="224.1" HigherBound="230.1">
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Construct WS Message" />
                                            <om:Property Name="Signal" Value="True" />
                                            <om:Element Type="Transform" OID="643f1daf-edcf-429c-8811-fe870071c6a7" ParentLink="ComplexStatement_Statement" LowerBound="227.1" HigherBound="229.1">
                                                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertToProtezioneCivileAllertaDigheWs" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="Setup Message" />
                                                <om:Property Name="Signal" Value="False" />
                                                <om:Element Type="MessagePartRef" OID="b1c1add4-9630-4143-a65d-ca84a8564579" ParentLink="Transform_InputMessagePartRef" LowerBound="228.212" HigherBound="228.253">
                                                    <om:Property Name="MessageRef" Value="ProtezioneCivileAllertaDigheXml" />
                                                    <om:Property Name="PartRef" Value="parameter" />
                                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                                    <om:Property Name="Name" Value="MessagePartReference_1" />
                                                    <om:Property Name="Signal" Value="False" />
                                                </om:Element>
                                                <om:Element Type="MessagePartRef" OID="c6ff3e0b-8ec2-4872-b658-355971bf5aa0" ParentLink="Transform_OutputMessagePartRef" LowerBound="228.52" HigherBound="228.100">
                                                    <om:Property Name="MessageRef" Value="ProtezioneCivileAllertaDigheWsRequest" />
                                                    <om:Property Name="PartRef" Value="parameters" />
                                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                                    <om:Property Name="Name" Value="MessagePartReference_2" />
                                                    <om:Property Name="Signal" Value="False" />
                                                </om:Element>
                                            </om:Element>
                                            <om:Element Type="MessageRef" OID="c805c522-d7d5-415a-9f5a-a1a5e42a2f86" ParentLink="Construct_MessageRef" LowerBound="225.47" HigherBound="225.84">
                                                <om:Property Name="Ref" Value="ProtezioneCivileAllertaDigheWsRequest" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="VariableAssignment" OID="51fa4087-46d0-4c2a-ab14-ae25f1a6cfd3" ParentLink="ComplexStatement_Statement" LowerBound="230.1" HigherBound="232.1">
                                            <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Take Time" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Send" OID="5cd41d34-1a67-439c-ae1e-b7ac14fe366e" ParentLink="ComplexStatement_Statement" LowerBound="232.1" HigherBound="234.1">
                                            <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheWsOut" />
                                            <om:Property Name="MessageName" Value="ProtezioneCivileAllertaDigheWsRequest" />
                                            <om:Property Name="OperationName" Value="ImportAllertaDigheProtezioneCivile" />
                                            <om:Property Name="OperationMessageName" Value="Request" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Send" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                        <om:Element Type="Receive" OID="67a868dc-71c1-42e0-8737-dfd564367075" ParentLink="ComplexStatement_Statement" LowerBound="234.1" HigherBound="236.1">
                                            <om:Property Name="Activate" Value="False" />
                                            <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheWsOut" />
                                            <om:Property Name="MessageName" Value="ProtezioneCivileAllertaDigheWsResponse" />
                                            <om:Property Name="OperationName" Value="ImportAllertaDigheProtezioneCivile" />
                                            <om:Property Name="OperationMessageName" Value="Response" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Receive" />
                                            <om:Property Name="Signal" Value="True" />
                                        </om:Element>
                                        <om:Element Type="VariableAssignment" OID="27117d88-4c06-4d7d-9020-2e09c3107307" ParentLink="ComplexStatement_Statement" LowerBound="236.1" HigherBound="244.1">
                                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Esito WS&quot;, ProtezioneCivileAllertaDigheWsResponse.parameters.ImportAllertaDigheProtezioneCivileResult,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="BAM Update" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                        <om:Element Type="Catch" OID="d240451f-9d97-4b55-8fde-ebc24f0f22b2" ParentLink="Scope_Catch" LowerBound="247.1" HigherBound="262.1">
                                            <om:Property Name="ExceptionName" Value="soapExc" />
                                            <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                                            <om:Property Name="IsFaultMessage" Value="False" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Soap Exception" />
                                            <om:Property Name="Signal" Value="True" />
                                            <om:Element Type="VariableAssignment" OID="0e634ff5-f23e-4428-a463-7eb4edfc37eb" ParentLink="Catch_Statement" LowerBound="250.1" HigherBound="261.1">
                                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="WriteLog" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="Catch" OID="909d64bf-21ce-4e6c-853a-a202488e7624" ParentLink="Scope_Catch" LowerBound="262.1" HigherBound="277.1">
                                            <om:Property Name="ExceptionName" Value="systemExc" />
                                            <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                                            <om:Property Name="IsFaultMessage" Value="False" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="System Exception" />
                                            <om:Property Name="Signal" Value="True" />
                                            <om:Element Type="VariableAssignment" OID="28ba2b1d-ec13-49ef-9e00-580144bfbb62" ParentLink="Catch_Statement" LowerBound="265.1" HigherBound="276.1">
                                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="WriteLog" />
                                                <om:Property Name="Signal" Value="True" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="Catch" OID="8dd09f22-c3bd-4f21-884f-5c2d16fb8f82" ParentLink="Scope_Catch" LowerBound="277.1" HigherBound="290.1">
                                            <om:Property Name="ExceptionName" Value="faultExc" />
                                            <om:Property Name="ExceptionType" Value="ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile.Fault" />
                                            <om:Property Name="IsFaultMessage" Value="False" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Fault Message" />
                                            <om:Property Name="Signal" Value="True" />
                                            <om:Element Type="VariableAssignment" OID="3d6512f0-03d6-462a-973c-5c3b02b4df0e" ParentLink="Catch_Statement" LowerBound="280.1" HigherBound="289.1">
                                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="WriteLog" />
                                                <om:Property Name="Signal" Value="True" />
                                            </om:Element>
                                        </om:Element>
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="fffa9aaa-3ea2-4742-bbb5-8d7170f3965e" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="c9762d52-0088-44a6-ab87-1f1416e189ba" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="2c202bb0-92fa-4aa1-9734-b10b0e91713c" ParentLink="ComplexStatement_Statement" LowerBound="296.1" HigherBound="307.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante estrazione CADES.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(resultStatus.ErrorMessage);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="feb8a3fa-bf0c-446f-bbf1-ac7f11f16191" ParentLink="Scope_Catch" LowerBound="311.1" HigherBound="326.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="7e611607-edd5-4c89-9d06-627a82c4bca9" ParentLink="Catch_Statement" LowerBound="314.1" HigherBound="325.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante estrazione CADES.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Scope" OID="c43f5b6b-0e01-4bc3-9538-3d13529e9080" ParentLink="ServiceBody_Statement" LowerBound="328.1" HigherBound="387.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="UpdateStatus" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="883c1dd2-5375-4bbb-ba8a-bf4abb9023e7" ParentLink="ComplexStatement_Statement" LowerBound="333.1" HigherBound="348.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Update Messagge" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageAssignment" OID="a2be1013-1c3a-439b-a3cc-e3e3f7ba7f5f" ParentLink="ComplexStatement_Statement" LowerBound="336.1" HigherBound="347.1">
                            <om:Property Name="Expression" Value="ProtezioneCivileFileUpdateStatusRequest.parameter =&#xA;Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(&#xA;&quot;A2A.EAI.INT_EXTEVENT.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0&quot;,&#xA;&quot;A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure+ProtezioneCivileFileUpdateStatus&quot;&#xA;);&#xA;&#xA;&#xA;ProtezioneCivileFileUpdateStatusRequest.parameter.fileType = fileType;&#xA;ProtezioneCivileFileUpdateStatusRequest.parameter.transactionId = activityInstanceId;&#xA;ProtezioneCivileFileUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Setup messagge" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="94b07653-aa4b-41bf-9746-7454c150329f" ParentLink="Construct_MessageRef" LowerBound="334.31" HigherBound="334.70">
                            <om:Property Name="Ref" Value="ProtezioneCivileFileUpdateStatusRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="e86f2b54-63c2-4477-9dc9-45df0a072e5e" ParentLink="ComplexStatement_Statement" LowerBound="348.1" HigherBound="350.1">
                        <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheFileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileFileUpdateStatusRequest" />
                        <om:Property Name="OperationName" Value="ProtezioneCivileFileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="40198bc4-e050-42b3-8084-9c0cd1cc7c13" ParentLink="ComplexStatement_Statement" LowerBound="350.1" HigherBound="352.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheFileUpdateStatusOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileFileUpdateStatusResponse" />
                        <om:Property Name="OperationName" Value="ProtezioneCivileFileUpdateStatus" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Catch" OID="d2d1d58a-5fca-41b1-8700-daeced1cc5cb" ParentLink="Scope_Catch" LowerBound="355.1" HigherBound="370.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="7b093466-ac1a-4a62-a064-3291f6cbbd4e" ParentLink="Catch_Statement" LowerBound="358.1" HigherBound="369.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'aggiornameto dello stato.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="bbd01755-988e-4256-8012-31ebbc8788da" ParentLink="Scope_Catch" LowerBound="370.1" HigherBound="385.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="8736d814-bf8a-4922-ba9c-6b390773771c" ParentLink="Catch_Statement" LowerBound="373.1" HigherBound="384.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante l'aggiornameto dello stato.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="635fead2-e178-4eb2-b08d-50395a851fe7" ParentLink="ServiceBody_Statement" LowerBound="387.1" HigherBound="421.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="7f8f9854-8422-4661-a3c3-29e4faea3359" ParentLink="ReallyComplexStatement_Branch" LowerBound="388.13" HigherBound="391.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="d9fb46f9-ff40-49f4-85d0-7e829fe80119" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="1fd59844-2c95-458e-a3a6-36d9d265a7bb" ParentLink="ComplexStatement_Statement" LowerBound="393.1" HigherBound="420.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="43a219a8-e2f5-486c-a36f-e49096bf5ba3" ParentLink="ComplexStatement_Statement" LowerBound="398.1" HigherBound="416.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="32eceb84-6d32-4d4e-9792-03b8e0246d17" ParentLink="ComplexStatement_Statement" LowerBound="401.1" HigherBound="415.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtezioneCivileProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageNotes = System.String.Concat(&quot;BizTalk Activity Id: &quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId());&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="fed0c7ff-224d-4a1d-ae3b-b044025eba68" ParentLink="Construct_MessageRef" LowerBound="399.35" HigherBound="399.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="b2b23244-caaa-4993-87d6-6f5151be1e3c" ParentLink="ComplexStatement_Statement" LowerBound="416.1" HigherBound="418.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="79f30c0c-00eb-4431-8974-22f01ba24926" ParentLink="ServiceBody_Statement" LowerBound="421.1" HigherBound="427.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e15c0288-7733-4d91-a812-c6ed2838eb76" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="63.1" HigherBound="65.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheIn" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="06dcf35d-b4f9-4eda-95f7-0066776cbe9f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="63.1" HigherBound="64.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4d42729a-16be-4597-9ab6-019c6b79244f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="65.1" HigherBound="67.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="129" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8067eb42-cbd9-41a8-ac98-610fb3d756a1" ParentLink="PortDeclaration_CLRAttribute" LowerBound="65.1" HigherBound="66.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5024b94e-d338-41f8-8c09-71672bf61a83" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="67.1" HigherBound="69.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="237" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="cf2bf8eb-e1ae-43f9-bb08-004b3ada2f75" ParentLink="PortDeclaration_CLRAttribute" LowerBound="67.1" HigherBound="68.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8fda4530-e456-4be3-ac3e-d3d2c8d1577f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="69.1" HigherBound="71.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="66" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheCheckOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheCheckOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="21ee81e7-d2e9-4b8a-b2b7-26039351a9b1" ParentLink="PortDeclaration_CLRAttribute" LowerBound="69.1" HigherBound="70.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="94b9c02b-1e21-466e-ba28-e007aa4853da" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="71.1" HigherBound="74.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="214" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheFileUpdateStatusOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheFileUpdateStatusOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="06ce5ba3-1117-496b-b50c-8b97af3db355" ParentLink="PortDeclaration_CLRAttribute" LowerBound="71.1" HigherBound="72.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype ProtezioneCivileAllertaDigheWsResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAllertaDigheWs.ImportAllertaDigheProtezioneCivileResponse parameters;
    };
    internal messagetype ProtezioneCivileAllertaDigheWsRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAllertaDigheWs.ImportAllertaDigheProtezioneCivile parameters;
    };
    internal messagetype ProtezioneCivileAllertInType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAlert parameter;
    };
    internal messagetype ProtezioneCivileFileCheckRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileCheckTypedProcedure.ProtezioneCivileFileCheck parameter;
    };
    internal messagetype ProtezioneCivileFileCheckResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileCheckTypedProcedure.ProtezioneCivileFileCheckResponse parameter;
    };
    internal messagetype ProtezioneCivileFileUpdateStatusRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure.ProtezioneCivileFileUpdateStatus parameter;
    };
    internal messagetype ProtezioneCivileFileUpdateStatusResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure.ProtezioneCivileFileUpdateStatusResponse parameter;
    };
    internal porttype ProtezioneCivileAllertaDigheWsOutType
    {
        requestresponse ImportAllertaDigheProtezioneCivile
        {
            ProtezioneCivileAllertaDigheWsRequestType, ProtezioneCivileAllertaDigheWsResponseType, Fault = msgFaultType
        };
    };
    internal porttype ProtezioneCivileAllertaDigheInType
    {
        oneway Receive
        {
            System.Xml.XmlDocument
        };
    };
    internal porttype ProtezioneCivileAllertaDigheCheckOutType
    {
        requestresponse ProtezioneCivileFileCheck
        {
            ProtezioneCivileFileCheckRequestType, ProtezioneCivileFileCheckResponseType
        };
    };
    internal porttype ProtezioneCivileAllertaDigheFileUpdateStatusOutType
    {
        requestresponse ProtezioneCivileFileUpdateStatus
        {
            ProtezioneCivileFileUpdateStatusRequestType, ProtezioneCivileFileUpdateStatusResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcProtezioneCivileAllertaDighe
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ProtezioneCivileAllertaDigheInType ProtezioneCivileAllertaDigheIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses ProtezioneCivileAllertaDigheWsOutType ProtezioneCivileAllertaDigheWsOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses ProtezioneCivileAllertaDigheCheckOutType ProtezioneCivileAllertaDigheCheckOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses ProtezioneCivileAllertaDigheFileUpdateStatusOutType ProtezioneCivileAllertaDigheFileUpdateStatusOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message ProtezioneCivileAllertInType ProtezioneCivileAllertaDigheXml;
        message ProtezioneCivileAllertaDigheWsRequestType ProtezioneCivileAllertaDigheWsRequest;
        message ProtezioneCivileAllertaDigheWsResponseType ProtezioneCivileAllertaDigheWsResponse;
        message ProtezioneCivileFileCheckRequestType ProtezioneCivileFileCheckRequest;
        message ProtezioneCivileFileCheckResponseType ProtezioneCivileFileCheckResponse;
        message System.Xml.XmlDocument ProtezioneCivileAllertaDigheRaw;
        message ProtezioneCivileFileUpdateStatusRequestType ProtezioneCivileFileUpdateStatusRequest;
        message ProtezioneCivileFileUpdateStatusResponseType ProtezioneCivileFileUpdateStatusResponse;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.Boolean newData;
        A2A.EAI.INT_EXTEVENT.Services.ResultStatus resultStatus;
        System.Xml.XmlDocument xmlMessage;
        System.String fileType;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("49309183-d055-48d5-a337-0a3d5ae5d675")]
            activate receive (ProtezioneCivileAllertaDigheIn.Receive, ProtezioneCivileAllertaDigheRaw);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            newData = true;
            resultStatus = new A2A.EAI.INT_EXTEVENT.Services.ResultStatus();
            xmlMessage = new System.Xml.XmlDocument();
            fileType = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bc0519ac-2869-41bb-b18b-024f0b10b63c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(ProtezioneCivileAllertaDigheRaw);
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(ProtezioneCivileAllertaDigheRaw);
            
            newData = false;
            
            fileType = System.String.Empty;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0574ec94-a168-4505-ada5-9ce6b98a57be")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "instanceId", activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Inviato a N2", "NO",
            "Flusso", "Protezione Civile Allerta Dighe - Process",
            "Test", false);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d1a1d4d-e7cd-4e25-a6d8-185aab6f5a1b")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("17613948-d577-4f9f-94c8-9f458c3deb69")]
                    construct ProtezioneCivileAllertaDigheXml
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ab60ae5a-b9b1-479c-a52c-944f31e05ac2")]
                        resultStatus = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ExtractContentFromCades(ProtezioneCivileAllertaDigheRaw);
                        
                        xmlMessage.LoadXml(resultStatus.ReturnXmlString);
                        
                        ProtezioneCivileAllertaDigheXml.parameter = xmlMessage;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("935c7cb4-929c-4815-9790-e528af40763d")]
                    fileType = ProtezioneCivileAllertaDigheXml.parameter.msgType;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("687d0b03-ce34-4afe-bbf1-fff3abe824e2")]
                    if (resultStatus.ResultCode == "OK")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d308f0c3-c18c-4a99-ac0d-2869602adeed")]
                        scope
                        {
                            body
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("d95de2be-eabc-411c-a23c-1be179d01b1f")]
                                construct ProtezioneCivileFileCheckRequest
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("1ec34984-2cf4-4429-86b9-d397c2a4cd1e")]
                                    transform (ProtezioneCivileFileCheckRequest.parameter) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertToProtezioneCivileCheck (ProtezioneCivileAllertaDigheXml.parameter);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("aa3431ec-0da3-448f-a57f-3868b2c230a5")]
                                    ProtezioneCivileFileCheckRequest.parameter.activityId = activityInstanceId;
                                    ProtezioneCivileFileCheckRequest.parameter.fileName = originalFileName;
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("e1aa63fa-93d8-47c3-8795-9131b762c416")]
                                dataInizioDb = System.DateTimeOffset.Now;
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("23145f94-16f1-4bc5-9329-c5520f66c610")]
                                send (ProtezioneCivileAllertaDigheCheckOut.ProtezioneCivileFileCheck, ProtezioneCivileFileCheckRequest);
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c64e56a-eb31-4d14-ae69-615f3a9d39eb")]
                                receive (ProtezioneCivileAllertaDigheCheckOut.ProtezioneCivileFileCheck, ProtezioneCivileFileCheckResponse);
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("b067a2e2-45d1-4a6e-8305-c80160942e5c")]
                                Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                                "Data Inizio DB", dataInizioDb,
                                "Data Fine DB", System.DateTimeOffset.Now,
                                "Durata DB", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioDb, System.DateTimeOffset.Now)
                                );
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("13440d1b-6c2f-4fc4-aa52-ec366d0a5e80")]
                                newData = ProtezioneCivileFileCheckResponse.parameter.StoredProcedureResultSet0.StoredProcedureResultSet0.newData;
                            }
                            exceptions
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("550d2118-4dfa-43c8-93fd-f702be7a166f")]
                                catch (System.Web.Services.Protocols.SoapException soapExc)
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("2d1f2411-77e3-4a83-924f-8203ea01c43f")]
                                    errorMessage.Append("Errore durante la verifica di nuovi file.");
                                    errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                    errorMessage.Append(soapExc.Message);
                                    
                                    resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                    connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                    faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                    
                                    
                                    
                                }
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("2193588a-731c-426c-85ec-e06d264d1cb6")]
                                catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9f4b84b8-002a-42b6-848e-c647dcaaa6f1")]
                                    errorMessage.Append("Errore durante la verifica di nuovi file.");
                                    errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                    errorMessage.Append(systemExc.Message);
                                    
                                    resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                    connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                                    faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                    
                                    
                                    
                                }
                            }
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3088fc4c-fd3f-4d2c-9c2e-b3ba0e018bae")]
                        if (newData)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d09ba5c5-bc36-470b-a3e3-b9da6b75040a")]
                            scope
                            {
                                body
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eacdad8a-59c4-4a64-9c48-84a9193cb4da")]
                                    construct ProtezioneCivileAllertaDigheWsRequest
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("643f1daf-edcf-429c-8811-fe870071c6a7")]
                                        transform (ProtezioneCivileAllertaDigheWsRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertToProtezioneCivileAllertaDigheWs (ProtezioneCivileAllertaDigheXml.parameter);
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51fa4087-46d0-4c2a-ab14-ae25f1a6cfd3")]
                                    dataInizioWs = System.DateTimeOffset.Now;
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5cd41d34-1a67-439c-ae1e-b7ac14fe366e")]
                                    send (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile, ProtezioneCivileAllertaDigheWsRequest);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("67a868dc-71c1-42e0-8737-dfd564367075")]
                                    receive (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile, ProtezioneCivileAllertaDigheWsResponse);
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("27117d88-4c06-4d7d-9020-2e09c3107307")]
                                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                                    "Data Inizio WS", dataInizioWs,
                                    "Data Fine WS", System.DateTimeOffset.Now,
                                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                                    "Esito WS", ProtezioneCivileAllertaDigheWsResponse.parameters.ImportAllertaDigheProtezioneCivileResult,
                                    "Inviato a N2", "SI"
                                    );
                                }
                                exceptions
                                {
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d240451f-9d97-4b55-8fde-ebc24f0f22b2")]
                                    catch (System.Web.Services.Protocols.SoapException soapExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e634ff5-f23e-4428-a463-7eb4edfc37eb")]
                                        errorMessage.Append("Errore durante la chiamata Web Service.");
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(soapExc.Message);
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                        
                                        
                                        
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("909d64bf-21ce-4e6c-853a-a202488e7624")]
                                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("28ba2b1d-ec13-49ef-9e00-580144bfbb62")]
                                        errorMessage.Append("Errore durante la chiamata Web Service.");
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(systemExc.Message);
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                                        
                                        
                                        
                                    }
                                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8dd09f22-c3bd-4f21-884f-5c2d16fb8f82")]
                                    catch (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile.Fault faultExc)
                                    {
                                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3d6512f0-03d6-462a-973c-5c3b02b4df0e")]
                                        errorMessage.Append("Errore durante la chiamata Web Service.");
                                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                                        
                                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                                        
                                    }
                                }
                            }
                        }
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("2c202bb0-92fa-4aa1-9734-b10b0e91713c")]
                        errorMessage.Append("Errore durante estrazione CADES.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(resultStatus.ErrorMessage);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("feb8a3fa-bf0c-446f-bbf1-ac7f11f16191")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7e611607-edd5-4c89-9d06-627a82c4bca9")]
                        errorMessage.Append("Errore durante estrazione CADES.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c43f5b6b-0e01-4bc3-9538-3d13529e9080")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("883c1dd2-5375-4bbb-ba8a-bf4abb9023e7")]
                    construct ProtezioneCivileFileUpdateStatusRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a2be1013-1c3a-439b-a3cc-e3e3f7ba7f5f")]
                        ProtezioneCivileFileUpdateStatusRequest.parameter =
                        Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(
                        "A2A.EAI.INT_EXTEVENT.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0",
                        "A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileFileUpdateStatusTypedProcedure+ProtezioneCivileFileUpdateStatus"
                        );
                        
                        
                        ProtezioneCivileFileUpdateStatusRequest.parameter.fileType = fileType;
                        ProtezioneCivileFileUpdateStatusRequest.parameter.transactionId = activityInstanceId;
                        ProtezioneCivileFileUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e86f2b54-63c2-4477-9dc9-45df0a072e5e")]
                    send (ProtezioneCivileAllertaDigheFileUpdateStatusOut.ProtezioneCivileFileUpdateStatus, ProtezioneCivileFileUpdateStatusRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("40198bc4-e050-42b3-8084-9c0cd1cc7c13")]
                    receive (ProtezioneCivileAllertaDigheFileUpdateStatusOut.ProtezioneCivileFileUpdateStatus, ProtezioneCivileFileUpdateStatusResponse);
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d2d1d58a-5fca-41b1-8700-daeced1cc5cb")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7b093466-ac1a-4a62-a064-3291f6cbbd4e")]
                        errorMessage.Append("Errore durante l'aggiornameto dello stato.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("bbd01755-988e-4256-8012-31ebbc8788da")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8736d814-bf8a-4922-ba9c-6b390773771c")]
                        errorMessage.Append("Errore durante l'aggiornameto dello stato.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("635fead2-e178-4eb2-b08d-50395a851fe7")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("1fd59844-2c95-458e-a3a6-36d9d265a7bb")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("43a219a8-e2f5-486c-a36f-e49096bf5ba3")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("32eceb84-6d32-4d4e-9792-03b8e0246d17")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtezioneCivileProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageNotes = System.String.Concat("BizTalk Activity Id: ", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId());
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b2b23244-caaa-4993-87d6-6f5151be1e3c")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("79f30c0c-00eb-4431-8974-22f01ba24926")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));
        }
    }
}

