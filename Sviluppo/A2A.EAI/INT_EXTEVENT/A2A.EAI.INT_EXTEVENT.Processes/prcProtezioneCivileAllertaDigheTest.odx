﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="81e7b361-75db-4367-96ca-dc47c8d976bd" LowerBound="1.1" HigherBound="182.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="9288a670-0e55-4e24-857a-78edd1496aad" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheTestInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="c6b802b2-4499-4f80-b407-ba179af7a65b" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="a27eaecc-917d-498d-97b8-8d6c0d106ddb" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.45">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertTestInType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="41ee7764-eb3a-4207-a2bd-309be51c2505" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="181.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcProtezioneCivileAllertaDigheTest" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="0798c4db-9719-45f0-a54e-21891382c911" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="28.1" HigherBound="29.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioDb" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4a1cb5b8-b88b-4a29-bfc3-48cd310719ba" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="29.1" HigherBound="30.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizioWs" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="168244ad-51d2-4d60-b0d7-5099cd2a2f5f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2456ee70-a371-4c85-9884-0b83a295ddd0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7b6f2056-4d38-4cc2-a2e7-33654c6c3bb8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3f67adbf-9938-43c8-98ad-ec2d89c154b6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a4259e4e-4460-49f3-a8af-4cc1d9c4f4a8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ac7fa867-8805-4687-afad-3541603a9629" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4f34d6cb-9b82-4cc6-ab04-45d2516ea0ac" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2f7d76f3-3756-47ea-abaf-dbfd6520bfd5" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f3980ca8-08de-4a34-88ea-9c45a46f0b85" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="24.1" HigherBound="25.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="78e776ec-2348-405d-b3e1-dec9cccdbe49" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="25.1" HigherBound="26.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertTestInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertTest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="c4aa7914-a785-4eae-b0c9-092b090cf6d3" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2d640b36-40b4-42a9-9cf4-9e66ecc91b88" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="27.1" HigherBound="28.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="7626f217-a196-4831-bc2f-07a5a32144ee" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="49309183-d055-48d5-a337-0a3d5ae5d675" ParentLink="ServiceBody_Statement" LowerBound="40.1" HigherBound="46.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheTestIn" />
                    <om:Property Name="MessageName" Value="ProtezioneCivileAllertTest" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="bc0519ac-2869-41bb-b18b-024f0b10b63c" ParentLink="ServiceBody_Statement" LowerBound="46.1" HigherBound="56.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(ProtezioneCivileAllertTest);&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(ProtezioneCivileAllertTest);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="0574ec94-a168-4505-ada5-9ce6b98a57be" ParentLink="ServiceBody_Statement" LowerBound="56.1" HigherBound="66.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;NO&quot;,&#xD;&#xA;&quot;Flusso&quot;, &quot;Protezione Civile Allerta Dighe - Process&quot;,&#xD;&#xA;&quot;Test&quot;, true);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="d09ba5c5-bc36-470b-a3e3-b9da6b75040a" ParentLink="ServiceBody_Statement" LowerBound="66.1" HigherBound="139.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="eacdad8a-59c4-4a64-9c48-84a9193cb4da" ParentLink="ComplexStatement_Statement" LowerBound="71.1" HigherBound="77.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Construct WS Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="643f1daf-edcf-429c-8811-fe870071c6a7" ParentLink="ComplexStatement_Statement" LowerBound="74.1" HigherBound="76.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertTestToProtezioneCivileAllertaDigheWs" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="1e20e34e-de71-4f34-896b-7752afb89912" ParentLink="Transform_InputMessagePartRef" LowerBound="75.200" HigherBound="75.236">
                                <om:Property Name="MessageRef" Value="ProtezioneCivileAllertTest" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="2de36203-47cd-4990-93c3-bc2d946497fb" ParentLink="Transform_OutputMessagePartRef" LowerBound="75.36" HigherBound="75.84">
                                <om:Property Name="MessageRef" Value="ProtezioneCivileAllertaDigheWsRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="c805c522-d7d5-415a-9f5a-a1a5e42a2f86" ParentLink="Construct_MessageRef" LowerBound="72.31" HigherBound="72.68">
                            <om:Property Name="Ref" Value="ProtezioneCivileAllertaDigheWsRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="51fa4087-46d0-4c2a-ab14-ae25f1a6cfd3" ParentLink="ComplexStatement_Statement" LowerBound="77.1" HigherBound="79.1">
                        <om:Property Name="Expression" Value="dataInizioWs = System.DateTimeOffset.Now;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Take Time" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Send" OID="5cd41d34-1a67-439c-ae1e-b7ac14fe366e" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="81.1">
                        <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheWsOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileAllertaDigheWsRequest" />
                        <om:Property Name="OperationName" Value="ImportAllertaDigheProtezioneCivile" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="67a868dc-71c1-42e0-8737-dfd564367075" ParentLink="ComplexStatement_Statement" LowerBound="81.1" HigherBound="83.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="ProtezioneCivileAllertaDigheWsOut" />
                        <om:Property Name="MessageName" Value="ProtezioneCivileAllertaDigheWsResponse" />
                        <om:Property Name="OperationName" Value="ImportAllertaDigheProtezioneCivile" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="27117d88-4c06-4d7d-9020-2e09c3107307" ParentLink="ComplexStatement_Statement" LowerBound="83.1" HigherBound="91.1">
                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio WS&quot;, dataInizioWs,&#xD;&#xA;&quot;Data Fine WS&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata WS&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),&#xD;&#xA;&quot;Esito WS&quot;, ProtezioneCivileAllertaDigheWsResponse.parameters.ImportAllertaDigheProtezioneCivileResult,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;&#xD;&#xA;);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="BAM Update" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Catch" OID="d240451f-9d97-4b55-8fde-ebc24f0f22b2" ParentLink="Scope_Catch" LowerBound="94.1" HigherBound="109.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="0e634ff5-f23e-4428-a463-7eb4edfc37eb" ParentLink="Catch_Statement" LowerBound="97.1" HigherBound="108.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="909d64bf-21ce-4e6c-853a-a202488e7624" ParentLink="Scope_Catch" LowerBound="109.1" HigherBound="124.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="28ba2b1d-ec13-49ef-9e00-580144bfbb62" ParentLink="Catch_Statement" LowerBound="112.1" HigherBound="123.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="8dd09f22-c3bd-4f21-884f-5c2d16fb8f82" ParentLink="Scope_Catch" LowerBound="124.1" HigherBound="137.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Fault Message" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="3d6512f0-03d6-462a-973c-5c3b02b4df0e" ParentLink="Catch_Statement" LowerBound="127.1" HigherBound="136.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Errore durante la chiamata Web Service.&quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="635fead2-e178-4eb2-b08d-50395a851fe7" ParentLink="ServiceBody_Statement" LowerBound="139.1" HigherBound="173.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="7f8f9854-8422-4661-a3c3-29e4faea3359" ParentLink="ReallyComplexStatement_Branch" LowerBound="140.13" HigherBound="143.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="d9fb46f9-ff40-49f4-85d0-7e829fe80119" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Failure" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Scope" OID="1fd59844-2c95-458e-a3a6-36d9d265a7bb" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="172.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="43a219a8-e2f5-486c-a36f-e49096bf5ba3" ParentLink="ComplexStatement_Statement" LowerBound="150.1" HigherBound="168.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="32eceb84-6d32-4d4e-9792-03b8e0246d17" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="167.1">
                                    <om:Property Name="Expression" Value="Notification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtezioneCivileProcess;&#xD;&#xA;Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.messageText = errorMessage.ToString();&#xD;&#xA;Notification.messageNotes = System.String.Concat(&quot;BizTalk Activity Id: &quot;, Microsys.EAI.Framework.Services.ProcessServices.GetActivityId());&#xD;&#xA;Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="fed0c7ff-224d-4a1d-ae3b-b044025eba68" ParentLink="Construct_MessageRef" LowerBound="151.35" HigherBound="151.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="b2b23244-caaa-4993-87d6-6f5151be1e3c" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="170.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="79f30c0c-00eb-4431-8974-22f01ba24926" ParentLink="ServiceBody_Statement" LowerBound="173.1" HigherBound="179.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="b2156abd-addc-423d-9930-0a2420f273bb" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheTestInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheTestIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="7d0cd50f-9dbe-49fd-bbee-fab85c39cbae" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4d42729a-16be-4597-9ab6-019c6b79244f" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="22.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="35" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.ProtezioneCivileAllertaDigheWsOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="ProtezioneCivileAllertaDigheWsOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8067eb42-cbd9-41a8-ac98-610fb3d756a1" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5024b94e-d338-41f8-8c09-71672bf61a83" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="24.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="119" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="cf2bf8eb-e1ae-43f9-bb08-004b3ada2f75" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d18b4dac-81bd-4611-95e6-3e10cd698170" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ProtezioneCivileAllertTestInType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="4b1df9e7-e62c-4c29-ae14-e329345b6db5" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAlertTest" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype ProtezioneCivileAllertTestInType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.ProtezioneCivileAlertTest parameter;
    };
    internal porttype ProtezioneCivileAllertaDigheTestInType
    {
        oneway Receive
        {
            ProtezioneCivileAllertTestInType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcProtezioneCivileAllertaDigheTest
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements ProtezioneCivileAllertaDigheTestInType ProtezioneCivileAllertaDigheTestIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses ProtezioneCivileAllertaDigheWsOutType ProtezioneCivileAllertaDigheWsOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message Microsys.EAI.Framework.Schemas.Notification Notification;
        message ProtezioneCivileAllertTestInType ProtezioneCivileAllertTest;
        message ProtezioneCivileAllertaDigheWsRequestType ProtezioneCivileAllertaDigheWsRequest;
        message ProtezioneCivileAllertaDigheWsResponseType ProtezioneCivileAllertaDigheWsResponse;
        System.DateTimeOffset dataInizioDb;
        System.DateTimeOffset dataInizioWs;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("49309183-d055-48d5-a337-0a3d5ae5d675")]
            activate receive (ProtezioneCivileAllertaDigheTestIn.Receive, ProtezioneCivileAllertTest);
            originalFileName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("bc0519ac-2869-41bb-b18b-024f0b10b63c")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(ProtezioneCivileAllertTest);
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(ProtezioneCivileAllertTest);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0574ec94-a168-4505-ada5-9ce6b98a57be")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "instanceId", activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Inviato a N2", "NO",
            "Flusso", "Protezione Civile Allerta Dighe - Process",
            "Test", true);
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d09ba5c5-bc36-470b-a3e3-b9da6b75040a")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eacdad8a-59c4-4a64-9c48-84a9193cb4da")]
                    construct ProtezioneCivileAllertaDigheWsRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("643f1daf-edcf-429c-8811-fe870071c6a7")]
                        transform (ProtezioneCivileAllertaDigheWsRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.ProtezioneCivile.ProtezioneCivileAlertTestToProtezioneCivileAllertaDigheWs (ProtezioneCivileAllertTest.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51fa4087-46d0-4c2a-ab14-ae25f1a6cfd3")]
                    dataInizioWs = System.DateTimeOffset.Now;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5cd41d34-1a67-439c-ae1e-b7ac14fe366e")]
                    send (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile, ProtezioneCivileAllertaDigheWsRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("67a868dc-71c1-42e0-8737-dfd564367075")]
                    receive (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile, ProtezioneCivileAllertaDigheWsResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("27117d88-4c06-4d7d-9020-2e09c3107307")]
                    Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                    "Data Inizio WS", dataInizioWs,
                    "Data Fine WS", System.DateTimeOffset.Now,
                    "Durata WS", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizioWs, System.DateTimeOffset.Now),
                    "Esito WS", ProtezioneCivileAllertaDigheWsResponse.parameters.ImportAllertaDigheProtezioneCivileResult,
                    "Inviato a N2", "SI"
                    );
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d240451f-9d97-4b55-8fde-ebc24f0f22b2")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e634ff5-f23e-4428-a463-7eb4edfc37eb")]
                        errorMessage.Append("Errore durante la chiamata Web Service.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("909d64bf-21ce-4e6c-853a-a202488e7624")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("28ba2b1d-ec13-49ef-9e00-580144bfbb62")]
                        errorMessage.Append("Errore durante la chiamata Web Service.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                        
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("8dd09f22-c3bd-4f21-884f-5c2d16fb8f82")]
                    catch (ProtezioneCivileAllertaDigheWsOut.ImportAllertaDigheProtezioneCivile.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("3d6512f0-03d6-462a-973c-5c3b02b4df0e")]
                        errorMessage.Append("Errore durante la chiamata Web Service.");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("635fead2-e178-4eb2-b08d-50395a851fe7")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("1fd59844-2c95-458e-a3a6-36d9d265a7bb")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("43a219a8-e2f5-486c-a36f-e49096bf5ba3")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("32eceb84-6d32-4d4e-9792-03b8e0246d17")]
                            Notification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            Notification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            Notification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionProtezioneCivileProcess;
                            Notification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.messageText = errorMessage.ToString();
                            Notification.messageNotes = System.String.Concat("BizTalk Activity Id: ", Microsys.EAI.Framework.Services.ProcessServices.GetActivityId());
                            Notification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b2b23244-caaa-4993-87d6-6f5151be1e3c")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("79f30c0c-00eb-4431-8974-22f01ba24926")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));
        }
    }
}

