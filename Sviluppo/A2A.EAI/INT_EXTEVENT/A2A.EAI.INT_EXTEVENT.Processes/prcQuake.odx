﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="dc686481-87fe-42a8-8432-7aea4b8a796b" LowerBound="1.1" HigherBound="322.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="953ddf3f-e09f-41fc-a46c-ab36b33d5d84" ParentLink="Module_PortType" LowerBound="48.1" HigherBound="55.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:portType name=&quot;WsControlliDigheTerremotiSoap&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="typControlliDigheTerremotiWs" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="7c370c34-2ca7-4149-b0a4-3441283e4dda" ParentLink="PortType_OperationDeclaration" LowerBound="50.1" HigherBound="54.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:operation name=&quot;inputTerremoti&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="inputTerremoti" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="MessageRef" OID="e2c95611-a24f-4eaf-9b43-4216729e119c" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="52.54" HigherBound="52.94">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:output message=&quot;http://NBDOWS/AppCode/WsTerremoti:inputTerremotiSoapOut&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="e512f1ea-5d4d-467e-a730-e52c71993394" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="52.13" HigherBound="52.52">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="AnalystComments" Value="&lt;wsdl:input message=&quot;http://NBDOWS/AppCode/WsTerremoti:inputTerremotiSoapIn&quot;/&gt;&#xD;&#xA;" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="c405c59c-ba5a-4070-a13c-63d2f7c50b6b" ParentLink="Module_PortType" LowerBound="55.1" HigherBound="62.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typQuakeStarter" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="5e663640-28a3-4804-a351-ade17cb788c9" ParentLink="PortType_OperationDeclaration" LowerBound="57.1" HigherBound="61.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="ace20f9c-4522-42a3-9551-04ff355d19fd" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="59.13" HigherBound="59.35">
                    <om:Property Name="Ref" Value="System.Xml.XmlDocument" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="b17ec715-1dd5-4717-a468-0727bc366703" ParentLink="Module_PortType" LowerBound="62.1" HigherBound="69.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typQuake" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="0c4b6cfb-3da7-44ce-9f4f-4c2cac32f0e4" ParentLink="PortType_OperationDeclaration" LowerBound="64.1" HigherBound="68.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Query" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="a64bfe92-6761-474c-9008-fc433a7af501" ParentLink="OperationDeclaration_FaultMessageRef" LowerBound="66.56" HigherBound="66.76">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgFaultType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Fault" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="b9cc86cd-ca3b-469d-aee6-5b5c1d0bc4ac" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="66.34" HigherBound="66.54">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="03cf0199-b0ef-4751-bfa7-0bc0c4a4329f" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="66.13" HigherBound="66.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="d3b51a15-87a2-42ae-b418-86b9d1e92930" ParentLink="Module_PortType" LowerBound="69.1" HigherBound="76.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="typBiQuakeEventInsert" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="60da1995-c593-4785-8c9c-b707656ea483" ParentLink="PortType_OperationDeclaration" LowerBound="71.1" HigherBound="75.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="QuakeAnalysisInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="40ed640b-8175-4e46-a2e0-ba9411480fa1" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="73.13" HigherBound="73.48">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgBiQuakeAnalysisInsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="4875c169-c9ab-40ed-bc64-56b6cc439c26" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="73.50" HigherBound="73.86">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgBiQuakeAnalysisInsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="221591a1-8d51-4f85-8045-88c0e2582b58" ParentLink="Module_PortType" LowerBound="76.1" HigherBound="83.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptN2QuakeEventInsertType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="bfff734f-9d52-4008-a9f5-35fbd1aafe23" ParentLink="PortType_OperationDeclaration" LowerBound="78.1" HigherBound="82.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="QuakeEventN2Insert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8f2d7fdf-0518-4c3a-8950-4d6170e81ef1" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="80.13" HigherBound="80.45">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="f30e42a2-da68-46d2-8b7f-e0378d8ef308" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="80.47" HigherBound="80.80">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="17c5d671-f3c6-4737-8e50-4ab7aee007d8" ParentLink="Module_PortType" LowerBound="83.1" HigherBound="90.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptControlliDigheTerremotiWsType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9a2d9cf1-3790-41b0-bd4c-ec8d9dd13c81" ParentLink="PortType_OperationDeclaration" LowerBound="85.1" HigherBound="89.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="inputTerremoti" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="9b9ba432-4ceb-4331-8c40-da442b29cc5d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="87.13" HigherBound="87.52">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="8a6725fa-98a2-4868-81f1-9bd938081b30" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="87.54" HigherBound="87.94">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bda1292c-2f5f-4c11-af3c-a338181a4d6e" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:message name=&quot;inputTerremotiSoapOut&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="msgControlliDigheTerremotiWsResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="17341f64-0cb1-4c06-b058-22cad6c7a90c" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schControlliDigheTerremotiWs.inputTerremotiResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:part name=&quot;parameters&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="3259b66d-ea70-429f-8a0b-5eb15223cbbb" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="AnalystComments" Value="&lt;wsdl:message name=&quot;inputTerremotiSoapIn&quot;/&gt;&#xD;&#xA;" />
            <om:Property Name="Name" Value="msgControlliDigheTerremotiWsRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="200ba335-bc6f-4a28-86fe-fc8603625ca1" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schControlliDigheTerremotiWs.inputTerremoti" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="AnalystComments" Value="&lt;wsdl:part name=&quot;parameters&quot;/&gt;&#xD;&#xA;" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="2f7d4f7a-2289-4850-a995-6aeb9ae85a33" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="efda3b3f-6211-460e-9f00-0fd56252f311" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_Request" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c343c4b7-3a1a-4ccd-b26e-782573c0183f" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="66ebf014-9e1f-434c-a6e6-05707b6ee1c5" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_1_2" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="93da4385-9925-4314-8584-d63e24f0b126" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiQuakeEventInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5389b56c-011a-4b02-8481-40988db78322" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schEaiQuakeEventInsertTypedProcedure.QuakeEventInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="db3d198e-4417-4957-8a29-dfaa6a1dfd6c" ParentLink="Module_MessageType" LowerBound="24.1" HigherBound="28.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgEaiQuakeEventInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="ea8ee5df-5b4e-4a15-8942-4feef208fda7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="26.1" HigherBound="27.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schEaiQuakeEventInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b31fdf20-b0d9-4764-a69d-cdc08b84df01" ParentLink="Module_MessageType" LowerBound="28.1" HigherBound="32.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgBiQuakeAnalysisInsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="24d691f4-7196-408f-a4d8-d0ad459e6230" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="30.1" HigherBound="31.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schBiQuakeAnalysisInsertTypedProcedure.QuakeAnalysisInsert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="2ffb00b0-620c-4123-8b0d-6fd237998f05" ParentLink="Module_MessageType" LowerBound="32.1" HigherBound="36.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgBiQuakeAnalysisInsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7caf96be-a9ee-405c-be1d-3fcd1d37dff0" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schBiQuakeAnalysisInsertTypedProcedure.QuakeAnalysisInsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="e8d75030-33cf-48ac-b731-2b39c9ec19b6" ParentLink="Module_MessageType" LowerBound="36.1" HigherBound="40.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeNotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="36ce555f-639b-4ba7-a76a-054c730d1a69" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="8a65d8d4-30c6-4ea5-a019-7970113acf1b" ParentLink="Module_MessageType" LowerBound="40.1" HigherBound="44.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeEventN2InsertRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3866b85d-37fe-4753-8782-0bae41219569" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schN2QuakeEventInsertTypedProcedure.QuakeEventN2Insert" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="06d0d0b7-47be-4180-ae1d-5aed3cdbf5e3" ParentLink="Module_MessageType" LowerBound="44.1" HigherBound="48.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeEventN2InsertResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="860a41c9-e54e-452b-b79b-37b40cb78fdf" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schN2QuakeEventInsertTypedProcedure.QuakeEventN2InsertResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="8d04ebc4-f042-47bd-9eec-5011bc446e73" ParentLink="Module_ServiceDeclaration" LowerBound="90.1" HigherBound="321.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcQuake" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="f8412c67-da94-40ac-87a5-ea72bc64aa98" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="125.1" HigherBound="126.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4aea64db-9afd-45b1-a45a-c03760faf532" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="126.1" HigherBound="127.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d9ec8acc-2e1c-40ac-837a-2d77c5834a20" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="127.1" HigherBound="128.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c2f89371-fd7f-46ff-a77f-eccb5f04d1e1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="128.1" HigherBound="129.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cbe03583-af31-4b63-9b1e-832e97da0a14" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="129.1" HigherBound="130.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2cbe32f4-7a93-4649-8814-953c1350309e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="130.1" HigherBound="131.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0a530311-43b7-4fff-a426-a24c98d394fe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="131.1" HigherBound="132.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTime" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="requestFromDate" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1d725476-00f1-463c-954d-1fd467bfb2c3" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="132.1" HigherBound="133.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="somethingNew" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="bd4e7ac9-441f-45a1-9ae2-aa9328db51b1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="112.1" HigherBound="113.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2373f2bf-2ded-46f1-bfda-c4a8a0a5cf8b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="113.1" HigherBound="114.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="67b2832b-c267-46aa-8aa2-34ceac024d71" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="114.1" HigherBound="115.1">
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgStart" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1bc58ca3-b389-4b7b-916e-fd6a22f13b9b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="115.1" HigherBound="116.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="171dce21-cea1-4787-a4ab-7ab66bc15a9b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="116.1" HigherBound="117.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8eca13c5-1bfe-4f6d-ac3c-00ca13867470" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="117.1" HigherBound="118.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5cca9ddb-8404-4a3c-bb30-050ded6cd20c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="118.1" HigherBound="119.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgBiQuakeAnalysisInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgBiQuakeAnalysisInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6f051a99-fcdd-416f-b062-745f6bd64990" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="119.1" HigherBound="120.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgBiQuakeAnalysisInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgBiQuakeAnalysisInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a6b071c4-f984-4765-b9e9-d2d4e3a0dc8f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="120.1" HigherBound="121.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a259995a-ec5b-4933-a9bf-0b53f4429b9e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="121.1" HigherBound="122.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeEventN2InsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d86271e5-152e-47e1-8aa9-a7b093bfeb07" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="122.1" HigherBound="123.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeEventN2InsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="abd5dede-01cf-4e4a-9076-c04aa03a230c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="123.1" HigherBound="124.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgControlliDigheTerremotiWsRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="602a089b-e42b-47e2-91b6-239feb431568" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="124.1" HigherBound="125.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgControlliDigheTerremotiWsResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="5726cb2b-467e-448f-8d76-08612a45a81b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="3c2a627c-3c21-45ef-996a-48e93f22353f" ParentLink="ServiceBody_Statement" LowerBound="135.1" HigherBound="140.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptQuakeStarter" />
                    <om:Property Name="MessageName" Value="msgStart" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="88d7285e-2540-47ec-8a2d-7c8d003409b9" ParentLink="ServiceBody_Statement" LowerBound="140.1" HigherBound="150.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;somethingNew = false;&#xD;&#xA;dataInizio = System.DateTimeOffset.Now;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="90881d57-8a2c-4335-b0f4-0693345b98a7" ParentLink="ServiceBody_Statement" LowerBound="150.1" HigherBound="274.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="5e3a9bf0-b85c-4634-a3cb-9e5e53c19298" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="167.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Quake Request" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageAssignment" OID="b75a3861-55ec-4acb-8a4c-69159fefc4a6" ParentLink="ComplexStatement_Statement" LowerBound="158.1" HigherBound="166.1">
                            <om:Property Name="Expression" Value="msgQuakeRequest.parameters = Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage(&quot;A2A.EAI.INT_EXTEVENT.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0&quot;, &quot;A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_Request&quot;);&#xD;&#xA;&#xD;&#xA;requestFromDate = System.DateTime.Today.ToUniversalTime();&#xD;&#xA;&#xD;&#xA;msgQuakeRequest.parameters.StartTime = requestFromDate.ToString(&quot;yyyy-MM-ddT00:00:00&quot;);&#xD;&#xA;msgQuakeRequest(A2A.EAI.INT_EXTEVENT.Messaging.QuakeStartDate) = requestFromDate.ToString(&quot;yyyy-MM-ddT00:00:00&quot;);&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Setup Message" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="1015b902-fc25-4b16-8c1f-8623937ce79d" ParentLink="Construct_MessageRef" LowerBound="156.31" HigherBound="156.46">
                            <om:Property Name="Ref" Value="msgQuakeRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="d1f1bc46-f73f-42f7-875f-46c0ea0a9081" ParentLink="ComplexStatement_Statement" LowerBound="167.1" HigherBound="169.1">
                        <om:Property Name="PortName" Value="sptQuake" />
                        <om:Property Name="MessageName" Value="msgQuakeRequest" />
                        <om:Property Name="OperationName" Value="Query" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="22c9cba5-0ce6-4297-8909-8669c8bf524d" ParentLink="ComplexStatement_Statement" LowerBound="169.1" HigherBound="171.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptQuake" />
                        <om:Property Name="MessageName" Value="msgQuakeResponse" />
                        <om:Property Name="OperationName" Value="Query" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="eb7f307d-fcc8-4b1b-b750-8c279b6aaca7" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="235.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="HTTP Response 200" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="480027fe-1ff8-4307-beca-f2ad9f0f1980" ParentLink="ReallyComplexStatement_Branch" LowerBound="172.21" HigherBound="235.1">
                            <om:Property Name="Expression" Value="msgQuakeResponse(WCF.InboundHttpStatusCode) == &quot;OK&quot;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="7a293fdd-0bb0-4126-992c-a4a2bcf8249e" ParentLink="ComplexStatement_Statement" LowerBound="174.1" HigherBound="182.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create EAI Insert" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="84c50f70-faec-4385-81f5-85fa22808732" ParentLink="ComplexStatement_Statement" LowerBound="177.1" HigherBound="179.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeMLToEaiInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup EAI Insert" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="b33ae255-25e7-4ae8-b501-eed87287a502" ParentLink="Transform_InputMessagePartRef" LowerBound="178.149" HigherBound="178.176">
                                        <om:Property Name="MessageRef" Value="msgQuakeResponse" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="f632822e-5ab8-40bc-9ec1-31a4aff4367b" ParentLink="Transform_OutputMessagePartRef" LowerBound="178.40" HigherBound="178.80">
                                        <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="948c4417-cebe-4108-aaa0-f1d95b2cdfe7" ParentLink="ComplexStatement_Statement" LowerBound="179.1" HigherBound="181.1">
                                    <om:Property Name="Expression" Value="msgEaiQuakeEventInsertRequest.parameters.transactionId = activityInstanceId;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Transaction ID" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="7f8d6675-3fe0-4988-96b4-fab9a21c1a74" ParentLink="Construct_MessageRef" LowerBound="175.35" HigherBound="175.64">
                                    <om:Property Name="Ref" Value="msgEaiQuakeEventInsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="0b687aad-b7cc-4f97-86ec-f0caa2e4ccae" ParentLink="ComplexStatement_Statement" LowerBound="182.1" HigherBound="184.1">
                                <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                                <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertRequest" />
                                <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="fff089cc-b55e-4125-8482-f2cc40662812" ParentLink="ComplexStatement_Statement" LowerBound="184.1" HigherBound="186.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                                <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertResponse" />
                                <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Decision" OID="e8487378-fd0e-4b94-98c3-ac06aac77fa7" ParentLink="ComplexStatement_Statement" LowerBound="186.1" HigherBound="234.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Something to Notify" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="64ce607a-da2a-47d3-94b9-13fab6176586" ParentLink="ReallyComplexStatement_Branch" LowerBound="187.25" HigherBound="234.1">
                                    <om:Property Name="Expression" Value="msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents &gt; 0" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Yes" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="VariableAssignment" OID="1d710682-7df8-43d5-a3c1-9bb6f5be3d0b" ParentLink="ComplexStatement_Statement" LowerBound="189.1" HigherBound="199.1">
                                        <om:Property Name="Expression" Value="&#xD;&#xA;somethingNew = true;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;QuakeEvents&quot;,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Test&quot;, &quot;0&quot;&#xD;&#xA;);" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Begin" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Construct" OID="7b9cf073-166f-4d9f-8eaf-d07a17f68109" ParentLink="ComplexStatement_Statement" LowerBound="199.1" HigherBound="205.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Create Notification" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="Transform" OID="0f326999-353d-4fc3-8dc1-91c5447388c1" ParentLink="ComplexStatement_Statement" LowerBound="202.1" HigherBound="204.1">
                                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertToBi" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Setup BI Request" />
                                            <om:Property Name="Signal" Value="False" />
                                            <om:Element Type="MessagePartRef" OID="1ca9903d-20b8-43df-a459-d742ac8f6bb6" ParentLink="Transform_InputMessagePartRef" LowerBound="203.160" HigherBound="203.201">
                                                <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertResponse" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_3" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                            <om:Element Type="MessagePartRef" OID="aaca1f1a-4c28-4cd6-8699-6c7ee9fdc318" ParentLink="Transform_OutputMessagePartRef" LowerBound="203.44" HigherBound="203.86">
                                                <om:Property Name="MessageRef" Value="msgBiQuakeAnalysisInsertRequest" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_4" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="MessageRef" OID="4532e29f-5efe-48af-97c8-07869810afce" ParentLink="Construct_MessageRef" LowerBound="200.39" HigherBound="200.70">
                                            <om:Property Name="Ref" Value="msgBiQuakeAnalysisInsertRequest" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="e5bbd7a4-d1f3-4599-80f5-327c0beb168d" ParentLink="ComplexStatement_Statement" LowerBound="205.1" HigherBound="207.1">
                                        <om:Property Name="PortName" Value="sptBiQuakeEventInsert" />
                                        <om:Property Name="MessageName" Value="msgBiQuakeAnalysisInsertRequest" />
                                        <om:Property Name="OperationName" Value="QuakeAnalysisInsert" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="ca213e92-fa25-4f85-a7a7-f99e04aeec05" ParentLink="ComplexStatement_Statement" LowerBound="207.1" HigherBound="209.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptBiQuakeEventInsert" />
                                        <om:Property Name="MessageName" Value="msgBiQuakeAnalysisInsertResponse" />
                                        <om:Property Name="OperationName" Value="QuakeAnalysisInsert" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Construct" OID="1e59e271-1b09-4719-8032-ed5dba15784b" ParentLink="ComplexStatement_Statement" LowerBound="209.1" HigherBound="215.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Construct N2 Message" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="Transform" OID="50bbab4e-f59f-41c4-b99d-19744138f748" ParentLink="ComplexStatement_Statement" LowerBound="212.1" HigherBound="214.1">
                                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertResponseToN2QuakeEventInsert" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Setup Message" />
                                            <om:Property Name="Signal" Value="False" />
                                            <om:Element Type="MessagePartRef" OID="c6aca439-1cb9-40ad-8d36-148f26240592" ParentLink="Transform_InputMessagePartRef" LowerBound="213.181" HigherBound="213.222">
                                                <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertResponse" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_7" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                            <om:Element Type="MessagePartRef" OID="89c01fb2-9e00-4769-b65b-8092da2303ec" ParentLink="Transform_OutputMessagePartRef" LowerBound="213.44" HigherBound="213.83">
                                                <om:Property Name="MessageRef" Value="msgQuakeEventN2InsertRequest" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_8" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="MessageRef" OID="f055a6e2-8f0a-4f5a-9f6a-cffa725b0461" ParentLink="Construct_MessageRef" LowerBound="210.39" HigherBound="210.67">
                                            <om:Property Name="Ref" Value="msgQuakeEventN2InsertRequest" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="a7881435-058c-4237-9d78-2584cfb14fcc" ParentLink="ComplexStatement_Statement" LowerBound="215.1" HigherBound="217.1">
                                        <om:Property Name="PortName" Value="sptN2QuakeEventInsert" />
                                        <om:Property Name="MessageName" Value="msgQuakeEventN2InsertRequest" />
                                        <om:Property Name="OperationName" Value="QuakeEventN2Insert" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="352fb4c4-d61f-40dc-b99e-0706a81a009d" ParentLink="ComplexStatement_Statement" LowerBound="217.1" HigherBound="219.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptN2QuakeEventInsert" />
                                        <om:Property Name="MessageName" Value="msgQuakeEventN2InsertResponse" />
                                        <om:Property Name="OperationName" Value="QuakeEventN2Insert" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="VariableAssignment" OID="64154757-8180-490c-a01e-eb1a4de6199e" ParentLink="ComplexStatement_Statement" LowerBound="219.1" HigherBound="223.1">
                                        <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="BAM Update" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="Construct" OID="7d255e30-e6f2-4b42-ac71-321261a85537" ParentLink="ComplexStatement_Statement" LowerBound="223.1" HigherBound="229.1">
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Construct WS Message" />
                                        <om:Property Name="Signal" Value="True" />
                                        <om:Element Type="Transform" OID="a649fde6-5d8c-4015-9aca-8abafed60659" ParentLink="ComplexStatement_Statement" LowerBound="226.1" HigherBound="228.1">
                                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Name" Value="Setup Message" />
                                            <om:Property Name="Signal" Value="False" />
                                            <om:Element Type="MessagePartRef" OID="0c1b5ef9-685d-4eba-96a0-1e649f745350" ParentLink="Transform_InputMessagePartRef" LowerBound="227.194" HigherBound="227.234">
                                                <om:Property Name="MessageRef" Value="msgQuakeEventN2InsertResponse" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_9" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                            <om:Element Type="MessagePartRef" OID="675f6dfe-1c4a-408c-892f-0246f636e0ad" ParentLink="Transform_OutputMessagePartRef" LowerBound="227.44" HigherBound="227.90">
                                                <om:Property Name="MessageRef" Value="msgControlliDigheTerremotiWsRequest" />
                                                <om:Property Name="PartRef" Value="parameters" />
                                                <om:Property Name="ReportToAnalyst" Value="True" />
                                                <om:Property Name="Name" Value="MessagePartReference_10" />
                                                <om:Property Name="Signal" Value="False" />
                                            </om:Element>
                                        </om:Element>
                                        <om:Element Type="MessageRef" OID="202aa460-68f3-4de9-91df-8c45db756992" ParentLink="Construct_MessageRef" LowerBound="224.39" HigherBound="224.74">
                                            <om:Property Name="Ref" Value="msgControlliDigheTerremotiWsRequest" />
                                            <om:Property Name="ReportToAnalyst" Value="True" />
                                            <om:Property Name="Signal" Value="False" />
                                        </om:Element>
                                    </om:Element>
                                    <om:Element Type="Send" OID="0e2b333c-28f8-46e1-8498-9b481372248f" ParentLink="ComplexStatement_Statement" LowerBound="229.1" HigherBound="231.1">
                                        <om:Property Name="PortName" Value="sptControlliDigheTerremotiWs" />
                                        <om:Property Name="MessageName" Value="msgControlliDigheTerremotiWsRequest" />
                                        <om:Property Name="OperationName" Value="inputTerremoti" />
                                        <om:Property Name="OperationMessageName" Value="Request" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Send" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                    <om:Element Type="Receive" OID="f5c7ea53-7831-4c33-af4f-6d20c8439c52" ParentLink="ComplexStatement_Statement" LowerBound="231.1" HigherBound="233.1">
                                        <om:Property Name="Activate" Value="False" />
                                        <om:Property Name="PortName" Value="sptControlliDigheTerremotiWs" />
                                        <om:Property Name="MessageName" Value="msgControlliDigheTerremotiWsResponse" />
                                        <om:Property Name="OperationName" Value="inputTerremoti" />
                                        <om:Property Name="OperationMessageName" Value="Response" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Receive" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="6b783b98-64a9-4106-9049-0ed449d26877" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="33e09e18-c2e5-47a5-a06c-c7f5655ac0f6" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="24738eb9-56a5-48bf-a680-c108296985ff" ParentLink="Scope_Catch" LowerBound="238.1" HigherBound="249.1">
                        <om:Property Name="ExceptionName" Value="faultExc" />
                        <om:Property Name="ExceptionType" Value="sptQuake.Query.Fault" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="faultException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d54377fb-2d20-4668-96f2-750ca28794f7" ParentLink="Catch_Statement" LowerBound="241.1" HigherBound="248.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore durante la chiamata al servizio INGV. &quot;);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f077c2e5-0437-49ce-bca9-673faa2cfa21" ParentLink="Scope_Catch" LowerBound="249.1" HigherBound="260.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="4eb20f4a-12a0-40af-886d-ce4129b8557c" ParentLink="Catch_Statement" LowerBound="252.1" HigherBound="259.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di recupero dati. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="d1f76724-c991-427e-b9c0-df833f1bd957" ParentLink="Scope_Catch" LowerBound="260.1" HigherBound="272.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="7f8dae72-e6b8-43c0-ac9b-221918cf823f" ParentLink="Catch_Statement" LowerBound="263.1" HigherBound="271.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di recupero dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="25c52e46-16c6-41b7-ba76-dca9277df7f2" ParentLink="ServiceBody_Statement" LowerBound="274.1" HigherBound="308.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="6bed5a36-fa1f-4737-a163-84558194e23a" ParentLink="ReallyComplexStatement_Branch" LowerBound="275.13" HigherBound="278.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="036c0f84-e13f-4980-93c2-041f4d17630e" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="16f42788-d022-49b9-86d9-e027cd36fb83" ParentLink="ComplexStatement_Statement" LowerBound="280.1" HigherBound="307.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="88f5cd2b-9773-40ab-86ef-f5c5720f30ff" ParentLink="ComplexStatement_Statement" LowerBound="285.1" HigherBound="303.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="408c8490-a290-4155-8d2f-0e8a94db6827" ParentLink="Construct_MessageRef" LowerBound="286.35" HigherBound="286.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="67623960-357d-4565-b1f8-5070e73b454b" ParentLink="ComplexStatement_Statement" LowerBound="288.1" HigherBound="302.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="6e58c6e9-cd76-406c-8de0-28076d715b2d" ParentLink="ComplexStatement_Statement" LowerBound="303.1" HigherBound="305.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="d3ab4bb7-2f63-4b80-915a-31c4956a8876" ParentLink="ServiceBody_Statement" LowerBound="308.1" HigherBound="319.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Something has been done" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="519ccc86-3b11-4dd6-8ce6-87733dc37b1b" ParentLink="ReallyComplexStatement_Branch" LowerBound="309.13" HigherBound="319.1">
                        <om:Property Name="Expression" Value="somethingNew" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Yes" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c08871e6-26c5-4471-9c90-d66b26f16313" ParentLink="ComplexStatement_Statement" LowerBound="311.1" HigherBound="318.1">
                            <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="BAM End" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="d53f6be2-62e6-4108-9b04-ac91d9bfe3ef" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="8a519629-c470-4abb-98ad-90771d801277" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="93.1" HigherBound="95.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typQuakeStarter" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptQuakeStarter" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="e894e75a-ee5d-4263-a6c2-535e3148214f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="93.1" HigherBound="94.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="12175de0-5ec7-4025-b2d9-3d80a3fc6501" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="95.1" HigherBound="98.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="25" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typQuake" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptQuake" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="90335b9b-dcce-4004-9ddb-15cde8255218" ParentLink="PortDeclaration_CLRAttribute" LowerBound="95.1" HigherBound="96.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e6cf3476-5c3b-4d06-a195-1f6bce865e45" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="98.1" HigherBound="101.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="62" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typEaiQuakeEventInsert" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiQuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="7aa927cd-5a6f-4a9e-9f8c-550d21c855f4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="98.1" HigherBound="99.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="7c0c2509-afda-4472-a684-d6d58c84696b" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="101.1" HigherBound="104.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="96" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typBiQuakeEventInsert" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptBiQuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="eafa9a43-6ca1-4d9e-a804-6964bfe4fad3" ParentLink="PortDeclaration_CLRAttribute" LowerBound="101.1" HigherBound="102.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="1956795b-b058-4584-b9b9-0c828075bdd6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="104.1" HigherBound="107.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="111" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptN2QuakeEventInsertType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2QuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c72cba86-b713-4a65-905a-e9538943fc11" ParentLink="PortDeclaration_CLRAttribute" LowerBound="104.1" HigherBound="105.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2eb610fc-1e40-45af-861c-22981dea4b6e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="107.1" HigherBound="110.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="128" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptControlliDigheTerremotiWsType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptControlliDigheTerremotiWs" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8c534af0-ab04-414e-bb23-47faea212846" ParentLink="PortDeclaration_CLRAttribute" LowerBound="107.1" HigherBound="108.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="38b548ca-e8d2-434b-951f-b67be7bc4b15" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="110.1" HigherBound="112.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="226" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="65b07128-34c1-465b-80e9-6b7ff8972f2a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="110.1" HigherBound="111.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype msgControlliDigheTerremotiWsResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schControlliDigheTerremotiWs.inputTerremotiResponse parameters;
    };
    internal messagetype msgControlliDigheTerremotiWsRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schControlliDigheTerremotiWs.inputTerremoti parameters;
    };
    internal messagetype msgQuakeRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_Request parameters;
    };
    internal messagetype msgQuakeResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_1_2 parameters;
    };
    internal messagetype msgEaiQuakeEventInsertRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schEaiQuakeEventInsertTypedProcedure.QuakeEventInsert parameters;
    };
    internal messagetype msgEaiQuakeEventInsertResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schEaiQuakeEventInsertResponse parameters;
    };
    internal messagetype msgBiQuakeAnalysisInsertRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schBiQuakeAnalysisInsertTypedProcedure.QuakeAnalysisInsert parameters;
    };
    internal messagetype msgBiQuakeAnalysisInsertResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schBiQuakeAnalysisInsertTypedProcedure.QuakeAnalysisInsertResponse parameters;
    };
    internal messagetype msgQuakeNotificationType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotification parameters;
    };
    internal messagetype msgQuakeEventN2InsertRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schN2QuakeEventInsertTypedProcedure.QuakeEventN2Insert parameters;
    };
    internal messagetype msgQuakeEventN2InsertResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schN2QuakeEventInsertTypedProcedure.QuakeEventN2InsertResponse parameters;
    };
    internal porttype typControlliDigheTerremotiWs
    {
        requestresponse inputTerremoti
        {
            msgControlliDigheTerremotiWsRequestType, msgControlliDigheTerremotiWsResponseType
        };
    };
    internal porttype typQuakeStarter
    {
        oneway Receive
        {
            System.Xml.XmlDocument
        };
    };
    internal porttype typQuake
    {
        requestresponse Query
        {
            msgQuakeRequestType, msgQuakeResponseType, Fault = msgFaultType
        };
    };
    internal porttype typBiQuakeEventInsert
    {
        requestresponse QuakeAnalysisInsert
        {
            msgBiQuakeAnalysisInsertRequestType, msgBiQuakeAnalysisInsertResponseType
        };
    };
    internal porttype sptN2QuakeEventInsertType
    {
        requestresponse QuakeEventN2Insert
        {
            msgQuakeEventN2InsertRequestType, msgQuakeEventN2InsertResponseType
        };
    };
    internal porttype sptControlliDigheTerremotiWsType
    {
        requestresponse inputTerremoti
        {
            msgControlliDigheTerremotiWsRequestType, msgControlliDigheTerremotiWsResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcQuake
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements typQuakeStarter rptQuakeStarter;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typQuake sptQuake;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiQuakeEventInsert sptEaiQuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typBiQuakeEventInsert sptBiQuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptN2QuakeEventInsertType sptN2QuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptControlliDigheTerremotiWsType sptControlliDigheTerremotiWs;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType sptNotification;
        message msgQuakeRequestType msgQuakeRequest;
        message msgQuakeResponseType msgQuakeResponse;
        message System.Xml.XmlDocument msgStart;
        message msgEaiQuakeEventInsertRequestType msgEaiQuakeEventInsertRequest;
        message msgEaiQuakeEventInsertResponseType msgEaiQuakeEventInsertResponse;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgBiQuakeAnalysisInsertRequestType msgBiQuakeAnalysisInsertRequest;
        message msgBiQuakeAnalysisInsertResponseType msgBiQuakeAnalysisInsertResponse;
        message msgQuakeNotificationType msgQuakeNotification;
        message msgQuakeEventN2InsertRequestType msgQuakeEventN2InsertRequest;
        message msgQuakeEventN2InsertResponseType msgQuakeEventN2InsertResponse;
        message msgControlliDigheTerremotiWsRequestType msgControlliDigheTerremotiWsRequest;
        message msgControlliDigheTerremotiWsResponseType msgControlliDigheTerremotiWsResponse;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        System.DateTime requestFromDate;
        System.Boolean somethingNew;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c2a627c-3c21-45ef-996a-48e93f22353f")]
            activate receive (rptQuakeStarter.Receive, msgStart);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            somethingNew = true;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("88d7285e-2540-47ec-8a2d-7c8d003409b9")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            somethingNew = false;
            dataInizio = System.DateTimeOffset.Now;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("90881d57-8a2c-4335-b0f4-0693345b98a7")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5e3a9bf0-b85c-4634-a3cb-9e5e53c19298")]
                    construct msgQuakeRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b75a3861-55ec-4acb-8a4c-69159fefc4a6")]
                        msgQuakeRequest.parameters = Microsys.EAI.Framework.Services.ProcessServices.ConstructMessage("A2A.EAI.INT_EXTEVENT.Messaging, Version=1.0.0.0, Culture=neutral, PublicKeyToken=3e560864e91bc2e0", "A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeML_Request");
                        
                        requestFromDate = System.DateTime.Today.ToUniversalTime();
                        
                        msgQuakeRequest.parameters.StartTime = requestFromDate.ToString("yyyy-MM-ddT00:00:00");
                        msgQuakeRequest(A2A.EAI.INT_EXTEVENT.Messaging.QuakeStartDate) = requestFromDate.ToString("yyyy-MM-ddT00:00:00");
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1f1bc46-f73f-42f7-875f-46c0ea0a9081")]
                    send (sptQuake.Query, msgQuakeRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("22c9cba5-0ce6-4297-8909-8669c8bf524d")]
                    receive (sptQuake.Query, msgQuakeResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb7f307d-fcc8-4b1b-b750-8c279b6aaca7")]
                    if (msgQuakeResponse(WCF.InboundHttpStatusCode) == "OK")
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7a293fdd-0bb0-4126-992c-a4a2bcf8249e")]
                        construct msgEaiQuakeEventInsertRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("84c50f70-faec-4385-81f5-85fa22808732")]
                            transform (msgEaiQuakeEventInsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeMLToEaiInsert (msgQuakeResponse.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("948c4417-cebe-4108-aaa0-f1d95b2cdfe7")]
                            msgEaiQuakeEventInsertRequest.parameters.transactionId = activityInstanceId;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0b687aad-b7cc-4f97-86ec-f0caa2e4ccae")]
                        send (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fff089cc-b55e-4125-8482-f2cc40662812")]
                        receive (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e8487378-fd0e-4b94-98c3-ac06aac77fa7")]
                        if (msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents > 0)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1d710682-7df8-43d5-a3c1-9bb6f5be3d0b")]
                            
                            somethingNew = true;
                            
                            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                            "Data Inizio", dataInizio,
                            "Flusso", "QuakeEvents",
                            "instanceId", activityInstanceId,
                            "Test", "0"
                            );
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7b9cf073-166f-4d9f-8eaf-d07a17f68109")]
                            construct msgBiQuakeAnalysisInsertRequest
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0f326999-353d-4fc3-8dc1-91c5447388c1")]
                                transform (msgBiQuakeAnalysisInsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertToBi (msgEaiQuakeEventInsertResponse.parameters);
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e5bbd7a4-d1f3-4599-80f5-327c0beb168d")]
                            send (sptBiQuakeEventInsert.QuakeAnalysisInsert, msgBiQuakeAnalysisInsertRequest);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("ca213e92-fa25-4f85-a7a7-f99e04aeec05")]
                            receive (sptBiQuakeEventInsert.QuakeAnalysisInsert, msgBiQuakeAnalysisInsertResponse);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e59e271-1b09-4719-8032-ed5dba15784b")]
                            construct msgQuakeEventN2InsertRequest
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("50bbab4e-f59f-41c4-b99d-19744138f748")]
                                transform (msgQuakeEventN2InsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertResponseToN2QuakeEventInsert (msgEaiQuakeEventInsertResponse.parameters);
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a7881435-058c-4237-9d78-2584cfb14fcc")]
                            send (sptN2QuakeEventInsert.QuakeEventN2Insert, msgQuakeEventN2InsertRequest);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("352fb4c4-d61f-40dc-b99e-0706a81a009d")]
                            receive (sptN2QuakeEventInsert.QuakeEventN2Insert, msgQuakeEventN2InsertResponse);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("64154757-8180-490c-a01e-eb1a4de6199e")]
                            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                            "Inviato a N2", "SI"
                            );
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7d255e30-e6f2-4b42-ac71-321261a85537")]
                            construct msgControlliDigheTerremotiWsRequest
                            {
                                [Microsoft.XLANGs.BaseTypes.DesignerPosition("a649fde6-5d8c-4015-9aca-8abafed60659")]
                                transform (msgControlliDigheTerremotiWsRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs (msgQuakeEventN2InsertResponse.parameters);
                            }
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e2b333c-28f8-46e1-8498-9b481372248f")]
                            send (sptControlliDigheTerremotiWs.inputTerremoti, msgControlliDigheTerremotiWsRequest);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5c7ea53-7831-4c33-af4f-6d20c8439c52")]
                            receive (sptControlliDigheTerremotiWs.inputTerremoti, msgControlliDigheTerremotiWsResponse);
                        }
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("24738eb9-56a5-48bf-a680-c108296985ff")]
                    catch (sptQuake.Query.Fault faultExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d54377fb-2d20-4668-96f2-750ca28794f7")]
                        errorMessage.Append("Si è verificato un errore durante la chiamata al servizio INGV. ");
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.GetErrorDescription(faultExc.faultString));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.WebServices;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f077c2e5-0437-49ce-bca9-673faa2cfa21")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4eb20f4a-12a0-40af-886d-ce4129b8557c")]
                        errorMessage.Append("Si è verificato un errore in fase di recupero dati. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1f76724-c991-427e-b9c0-df833f1bd957")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f8dae72-e6b8-43c0-ac9b-221918cf823f")]
                        errorMessage.Append("Si è verificato un errore in fase di recupero dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("25c52e46-16c6-41b7-ba76-dca9277df7f2")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("16f42788-d022-49b9-86d9-e027cd36fb83")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("88f5cd2b-9773-40ab-86ef-f5c5720f30ff")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("67623960-357d-4565-b1f8-5070e73b454b")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6e58c6e9-cd76-406c-8de0-28076d715b2d")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d3ab4bb7-2f63-4b80-915a-31c4956a8876")]
            if (somethingNew)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("c08871e6-26c5-4471-9c90-d66b26f16313")]
                Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                "errorMessage", errorMessage.ToString(), 
                "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
                "Data Fine", System.DateTimeOffset.Now,
                "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
                );
            }
        }
    }
}

