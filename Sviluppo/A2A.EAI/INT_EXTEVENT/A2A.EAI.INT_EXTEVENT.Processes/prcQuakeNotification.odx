﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="9f1f383b-ef75-4e85-851e-31685686f367" LowerBound="1.1" HigherBound="326.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="b60c1d8b-489d-45cf-a2ca-7d953a96b65f" ParentLink="Module_PortType" LowerBound="24.1" HigherBound="31.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="rptQuakeNotificationPollingType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="d87e5e25-4683-4b69-91ef-21db595a7f05" ParentLink="PortType_OperationDeclaration" LowerBound="26.1" HigherBound="30.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="75131589-cf5c-42f8-bb0e-d6648509ae6d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="28.13" HigherBound="28.44">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationPollingType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="55d59c79-e9c7-4990-bf9f-dba3f040cf5e" ParentLink="Module_PortType" LowerBound="31.1" HigherBound="38.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptQuakeNotificationUpdateStatusType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="9d05e028-43d9-435c-9b36-c80d4d97d819" ParentLink="PortType_OperationDeclaration" LowerBound="33.1" HigherBound="37.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="QuakeNotificationUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="c6d6b8eb-b086-4f9c-88e1-7f17a50cde35" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="35.13" HigherBound="35.56">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationUpdateStatusRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="MessageRef" OID="a2139cee-87a1-4653-89a8-7981c397b08c" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="35.58" HigherBound="35.102">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationUpdateStatusResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="035a9080-d7ad-48df-8e61-e7c4583c915e" ParentLink="Module_PortType" LowerBound="38.1" HigherBound="45.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="sptQuakeNotificationType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="3078e3ef-6bb0-426b-81d1-29d7538b6d40" ParentLink="PortType_OperationDeclaration" LowerBound="40.1" HigherBound="44.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptQuakeNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="8ce27a95-7210-4ba4-b766-ce111d5017fd" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="42.13" HigherBound="42.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgMailSendType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="f65a0a49-c151-4ea9-ad75-87ef58ee6cd2" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="42.30" HigherBound="42.53">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgMailSendResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="4798881e-43f8-4e57-8bc3-832fcf296030" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeNotificationPollingType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7a4f80cc-9d9f-48a8-b47f-32a615106c55" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationPollingTypedPolling.TypedPollingResultSet0" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a0cd62ec-3191-4b13-bc97-d4254beccaad" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeNotificationUpdateStatusRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="9c736b52-862b-43fc-b5a8-2b267877c174" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationUpdateStatusTypedProcedure.QuakeNotificationUpdateStatus" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="e66e9f07-51bd-452a-bb4b-9cb45ac818c1" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeNotificationUpdateStatusResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="d6da5cb3-4735-4aec-b1f5-ca3ff021f02b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationUpdateStatusTypedProcedure.QuakeNotificationUpdateStatusResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="d98d6eb2-5f8f-4402-a5dc-159594067b11" ParentLink="Module_MessageType" LowerBound="16.1" HigherBound="20.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgMailSendType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="a82be9b2-f02c-4eb0-aef6-74ca3ecf298a" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="18.1" HigherBound="19.1">
                <om:Property Name="ClassName" Value="A2A.EAI.Common.Shared.Schemas.MailSend" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b003e4fd-b1b9-4caf-9676-b0b7d063aa80" ParentLink="Module_MessageType" LowerBound="20.1" HigherBound="24.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgMailSendResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="cca90ce8-e289-4180-80fd-eeceb9365554" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="22.1" HigherBound="23.1">
                <om:Property Name="ClassName" Value="System.Xml.XmlDocument" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="2c2d602c-6452-4f8b-85a6-5a82ee70d124" ParentLink="Module_ServiceDeclaration" LowerBound="45.1" HigherBound="325.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcQuakeNotification" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="fc4acba3-3e19-45c0-8d14-30ed1473ab20" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="70.1" HigherBound="71.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1fab6691-db12-401e-b1dd-7b192de25a29" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="71.1" HigherBound="72.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1d1507a5-e9a9-4892-b003-1e3d80ca0897" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="72.1" HigherBound="73.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessageUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a1db4024-e3e3-4c5e-83a0-889695482900" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="73.1" HigherBound="74.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Int32" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="updateStatusRetries" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a725a03c-51f8-4411-b330-169a7d9c1be9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="74.1" HigherBound="75.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCodeUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="78856f4d-a1f2-492b-8584-30f9ebb8bbb6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="75.1" HigherBound="76.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="subject" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="af594f3a-b38d-4b6f-bdad-c596be3a62e2" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="76.1" HigherBound="77.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ede81580-810f-4323-9089-8be4f684507a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="77.1" HigherBound="78.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="99e72902-07c7-4ec8-96c0-7617afcc3888" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="78.1" HigherBound="79.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c2131b3a-d5ee-4852-b721-20520bd16b13" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="79.1" HigherBound="80.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="emailBody" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3ddeb638-fe43-46c5-b766-79f439c036a0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="80.1" HigherBound="81.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="emailAddress" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="07d60b5f-ab5d-4d58-ac1d-ed0f026fe801" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="81.1" HigherBound="82.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f3c839c6-7da9-4ab1-a47b-3896944d5be0" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="82.1" HigherBound="83.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a67eace7-f9cd-4292-a43d-0c66da5bf092" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2ffade4f-58f4-4b57-83ae-439ef3a973a8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2fca1d0e-aa4e-4b70-95b1-8cdb0b424681" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e452f76b-01bd-48cc-b0f2-6bf8ad1e1af1" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="edd07d98-329a-43be-9602-7e01f71ae938" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEmailType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgMail" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5138b38e-5e39-4bbe-ad25-6608fa034936" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeNotificationPolling" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="9eb16ce0-e7bc-461a-bc29-8dd2cc1bf52b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationUpdateStatusRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeNotificationUpdateStatusRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="30535d07-2c5b-4950-aaad-25de4821e60b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeNotificationUpdateStatusResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeNotificationUpdateStatusResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="f92a182e-bee4-4275-a3c6-765fae0f0c4f" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgMailSendType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgMailSend" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1eea12e8-aa96-4628-8635-31c8a734ff70" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgMailSendResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgMailSendResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="7ed8b50f-6ad6-4811-ab97-57e35cfc367f" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="d28c4d16-d1b7-4291-88c9-de11b37165df" ParentLink="ServiceBody_Statement" LowerBound="85.1" HigherBound="94.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptQuakeNotificationPolling" />
                    <om:Property Name="MessageName" Value="msgQuakeNotificationPolling" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a524ed2b-67fd-4258-9827-c3faae8413c4" ParentLink="ServiceBody_Statement" LowerBound="94.1" HigherBound="109.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;updateStatusRetries = 0;&#xD;&#xA;errorMessageUpdateStatus = System.String.Empty;&#xD;&#xA;flowName = &quot;Quake Notification&quot;;&#xD;&#xA;&#xD;&#xA;emailAddress = System.String.Empty;&#xD;&#xA;subject = System.String.Empty;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="27807656-c992-43f1-aa15-5a9dc6889633" ParentLink="ServiceBody_Statement" LowerBound="109.1" HigherBound="119.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;QuakeEventsNotification&quot;,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Test&quot;, msgQuakeNotificationPolling.parameter.isTest&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="e6629315-3e41-44a5-9319-30fc132b5059" ParentLink="ServiceBody_Statement" LowerBound="119.1" HigherBound="207.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="b382c1f1-3595-4128-8a20-f9e9eac92b2c" ParentLink="ComplexStatement_Statement" LowerBound="124.1" HigherBound="130.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create Notification Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="33387e2c-efbd-4cee-8da1-23a68204f54f" ParentLink="ComplexStatement_Statement" LowerBound="127.1" HigherBound="129.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToQuakeEventInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup Insert" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="b6343f21-1d61-41e4-b439-c46db4f10d4f" ParentLink="Transform_InputMessagePartRef" LowerBound="128.169" HigherBound="128.206">
                                <om:Property Name="MessageRef" Value="msgQuakeNotificationPolling" />
                                <om:Property Name="PartRef" Value="parameter" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_11" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="af06d034-4d5b-46ce-8c72-a3bdb775a696" ParentLink="Transform_OutputMessagePartRef" LowerBound="128.36" HigherBound="128.76">
                                <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_12" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="1b87a214-a665-4b9d-b4b5-21f03034f93b" ParentLink="Construct_MessageRef" LowerBound="125.31" HigherBound="125.60">
                            <om:Property Name="Ref" Value="msgEaiQuakeEventInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="63633a94-9f9e-4025-b377-72033dffcd16" ParentLink="ComplexStatement_Statement" LowerBound="130.1" HigherBound="132.1">
                        <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                        <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertRequest" />
                        <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="4f242ed8-6f8a-4856-b0b3-096fe30cde70" ParentLink="ComplexStatement_Statement" LowerBound="132.1" HigherBound="134.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                        <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertResponse" />
                        <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="24a6183f-9e03-47e3-bedc-14c1a48359ed" ParentLink="ComplexStatement_Statement" LowerBound="134.1" HigherBound="179.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Something to Notify" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="47a7e1bd-d591-4ee7-a325-59cad0bbf3b4" ParentLink="ReallyComplexStatement_Branch" LowerBound="135.21" HigherBound="179.1">
                            <om:Property Name="Expression" Value="msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents &gt; 0" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Decision" OID="8bfb8e00-22a5-4f16-9c09-caeb30fceb3c" ParentLink="ComplexStatement_Statement" LowerBound="137.1" HigherBound="148.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Is Test" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="DecisionBranch" OID="d8c2c476-c325-42e6-9597-a99438256dc0" ParentLink="ReallyComplexStatement_Branch" LowerBound="138.25" HigherBound="143.1">
                                    <om:Property Name="Expression" Value="msgQuakeNotificationPolling.parameter.isTest" />
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="true" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="VariableAssignment" OID="5b7fd55c-b5f6-4717-9c46-647927290487" ParentLink="ComplexStatement_Statement" LowerBound="140.1" HigherBound="142.1">
                                        <om:Property Name="Expression" Value="subject = &quot;Test / Esercitazione di Notifica Terremoto&quot;;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set Subject" />
                                        <om:Property Name="Signal" Value="True" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="DecisionBranch" OID="9ba8c290-71bc-41fc-ac96-893c246f9c8d" ParentLink="ReallyComplexStatement_Branch">
                                    <om:Property Name="IsGhostBranch" Value="True" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Else" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="VariableAssignment" OID="77ceea9a-a445-4c02-a87a-fe736e0cf008" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="147.1">
                                        <om:Property Name="Expression" Value="subject = &quot;Notifica Terremoto&quot;;" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="Set Subject" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Construct" OID="46ed3165-ab31-4676-b469-ac2af32efe3a" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="168.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="63824ab5-c8cf-4920-80d2-f48a6b8ff1d8" ParentLink="Construct_MessageRef" LowerBound="149.35" HigherBound="149.55">
                                    <om:Property Name="Ref" Value="msgQuakeNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="46b5c5c9-62bf-401c-a1c2-4efda0823009" ParentLink="Construct_MessageRef" LowerBound="149.57" HigherBound="149.68">
                                    <om:Property Name="Ref" Value="msgMailSend" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="11b1848b-a0e4-416e-a4ba-44fdf6d4b181" ParentLink="ComplexStatement_Statement" LowerBound="151.1" HigherBound="153.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertToNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="a4d9029c-5957-4947-ad60-5d8acaf0d452" ParentLink="Transform_InputMessagePartRef" LowerBound="152.155" HigherBound="152.196">
                                        <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertResponse" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_5" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="3037c07f-1648-4291-948d-64e817e0567c" ParentLink="Transform_OutputMessagePartRef" LowerBound="152.40" HigherBound="152.71">
                                        <om:Property Name="MessageRef" Value="msgQuakeNotification" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_6" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="Transform" OID="e3a3fae7-8bda-482a-b983-16fc9be9d40b" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="155.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToMailSend" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup MailSend" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="e1764b74-a6a3-4a39-9ff4-e73a64933e0b" ParentLink="Transform_InputMessagePartRef" LowerBound="154.146" HigherBound="154.183">
                                        <om:Property Name="MessageRef" Value="msgQuakeNotificationPolling" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="283207bc-e9d9-46e8-b23d-3c60be7f4377" ParentLink="Transform_OutputMessagePartRef" LowerBound="154.40" HigherBound="154.61">
                                        <om:Property Name="MessageRef" Value="msgMailSend" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="9d47ab2f-003a-4d99-90bb-76492e5595d5" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="167.1">
                                    <om:Property Name="Expression" Value="// BODY&#xD;&#xA;emailBody = msgQuakeNotification.parameters;&#xD;&#xA;&#xD;&#xA;// TO&#xD;&#xA;emailAddress = msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.sendEmailTo;&#xD;&#xA;&#xD;&#xA;msgMailSend.parameter.htmlContent = emailBody.InnerXml;&#xD;&#xA;msgMailSend.parameter.subject = subject;&#xD;&#xA;msgMailSend.parameter.mailFrom = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName, &quot;earthQuakeMailFrom&quot;);&#xD;&#xA;msgMailSend.parameter.mailCc = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName, &quot;earthQuakeMailCc&quot;);&#xD;&#xA;msgMailSend.parameter.mailTo = emailAddress;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Create Email Message" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="289b66da-df51-4dfa-b52a-bb9ebbdd0c98" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="170.1">
                                <om:Property Name="PortName" Value="sptQuakeNotification" />
                                <om:Property Name="MessageName" Value="msgMailSend" />
                                <om:Property Name="OperationName" Value="sptQuakeNotification" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="b73a2f95-af03-4474-8a2a-c5d476446048" ParentLink="ComplexStatement_Statement" LowerBound="170.1" HigherBound="172.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptQuakeNotification" />
                                <om:Property Name="MessageName" Value="msgMailSendResponse" />
                                <om:Property Name="OperationName" Value="sptQuakeNotification" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="ebba7bb2-3d06-4212-8d53-c560d6525c63" ParentLink="ComplexStatement_Statement" LowerBound="172.1" HigherBound="178.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Note&quot;, System.String.Concat(&quot;Sent for event: &quot;, msgQuakeNotificationPolling.parameter.eventId)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;A2A.EAI.INT_EXTEVENT.Services.ProcessServices.AddQuakeRelationShip(msgMailSendResponse.parameter, activityInstanceId);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="49f7a445-1198-4ed4-940f-2c4fbaf928e9" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="46d60b58-f835-4dda-b34a-d0dcc41244e6" ParentLink="Scope_Catch" LowerBound="182.1" HigherBound="193.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="8002328f-3d41-47bc-99d7-596d161a82f5" ParentLink="Catch_Statement" LowerBound="185.1" HigherBound="192.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di invio mail. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="17ab3d92-1c64-4799-ad1f-e972c4df527d" ParentLink="Scope_Catch" LowerBound="193.1" HigherBound="205.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="cbde9b9f-2f15-4cde-ad78-8fba1113298a" ParentLink="Catch_Statement" LowerBound="196.1" HigherBound="204.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di invio mail. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="While" OID="f0d923c0-4272-4805-8358-f256b8d2d696" ParentLink="ServiceBody_Statement" LowerBound="207.1" HigherBound="282.1">
                    <om:Property Name="Expression" Value="updateStatusRetries &lt;= 5&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Update Status" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="VariableAssignment" OID="1dc51e87-9718-403f-b1d7-65f9347d26d4" ParentLink="ComplexStatement_Statement" LowerBound="210.1" HigherBound="213.1">
                        <om:Property Name="Expression" Value="resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;errorMessageUpdateStatus = System.String.Empty;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="InitializeLoop" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Scope" OID="0efed6dc-ec42-42e4-91a4-45215fed5f33" ParentLink="ComplexStatement_Statement" LowerBound="213.1" HigherBound="251.1">
                        <om:Property Name="InitializedTransactionType" Value="True" />
                        <om:Property Name="IsSynchronized" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Update Status" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Construct" OID="1e169b58-8a38-422e-8cb5-265a78b3fa96" ParentLink="ComplexStatement_Statement" LowerBound="218.1" HigherBound="226.1">
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create Update Status" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessageRef" OID="82fa19b2-86b7-4390-ada6-417a4ecf532c" ParentLink="Construct_MessageRef" LowerBound="219.35" HigherBound="219.74">
                                <om:Property Name="Ref" Value="msgQuakeNotificationUpdateStatusRequest" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Transform" OID="b08d8b3a-2805-4890-9730-cb1de22de1a7" ParentLink="ComplexStatement_Statement" LowerBound="221.1" HigherBound="223.1">
                                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToUpdateStatus" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup Update Status" />
                                <om:Property Name="Signal" Value="False" />
                                <om:Element Type="MessagePartRef" OID="af9bd440-c9eb-4124-a786-c57105b5b652" ParentLink="Transform_InputMessagePartRef" LowerBound="222.178" HigherBound="222.215">
                                    <om:Property Name="MessageRef" Value="msgQuakeNotificationPolling" />
                                    <om:Property Name="PartRef" Value="parameter" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="MessagePartReference_1" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessagePartRef" OID="90ef52e0-5052-4bc2-a65e-16d2026b0938" ParentLink="Transform_OutputMessagePartRef" LowerBound="222.40" HigherBound="222.89">
                                    <om:Property Name="MessageRef" Value="msgQuakeNotificationUpdateStatusRequest" />
                                    <om:Property Name="PartRef" Value="parameter" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="MessagePartReference_2" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="MessageAssignment" OID="0618e4fb-2718-400f-998c-802a655bc053" ParentLink="ComplexStatement_Statement" LowerBound="223.1" HigherBound="225.1">
                                <om:Property Name="Expression" Value="msgQuakeNotificationUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="False" />
                                <om:Property Name="Name" Value="Parameters" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Send" OID="984ed799-fded-41c5-81db-1944ddd623f7" ParentLink="ComplexStatement_Statement" LowerBound="226.1" HigherBound="228.1">
                            <om:Property Name="PortName" Value="sptQuakeNotificationUpdateStatus" />
                            <om:Property Name="MessageName" Value="msgQuakeNotificationUpdateStatusRequest" />
                            <om:Property Name="OperationName" Value="QuakeNotificationUpdateStatus" />
                            <om:Property Name="OperationMessageName" Value="Request" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Send" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Receive" OID="b0606eef-69c4-401f-a5ed-5555301101d6" ParentLink="ComplexStatement_Statement" LowerBound="228.1" HigherBound="230.1">
                            <om:Property Name="Activate" Value="False" />
                            <om:Property Name="PortName" Value="sptQuakeNotificationUpdateStatus" />
                            <om:Property Name="MessageName" Value="msgQuakeNotificationUpdateStatusResponse" />
                            <om:Property Name="OperationName" Value="QuakeNotificationUpdateStatus" />
                            <om:Property Name="OperationMessageName" Value="Response" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Receive" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="Catch" OID="6b95b319-348d-44cd-b8db-5f3709b85b08" ParentLink="Scope_Catch" LowerBound="233.1" HigherBound="241.1">
                            <om:Property Name="ExceptionName" Value="soapExc" />
                            <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                            <om:Property Name="IsFaultMessage" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Soap Exception" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="4d576e1a-4352-4507-abb0-1c5f1581997b" ParentLink="Catch_Statement" LowerBound="236.1" HigherBound="240.1">
                                <om:Property Name="Expression" Value="errorMessageUpdateStatus = soapExc.Message;&#xD;&#xA;&#xD;&#xA;resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="Catch" OID="9dfb34cc-dc60-4689-bd66-e240ddf02a9b" ParentLink="Scope_Catch" LowerBound="241.1" HigherBound="249.1">
                            <om:Property Name="ExceptionName" Value="systemExc" />
                            <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                            <om:Property Name="IsFaultMessage" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="System Exception" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="40c72f09-a9d2-4597-a496-1b4c4b2067f8" ParentLink="Catch_Statement" LowerBound="244.1" HigherBound="248.1">
                                <om:Property Name="Expression" Value="errorMessageUpdateStatus = systemExc.Message;&#xD;&#xA;&#xD;&#xA;resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="10ad88ed-63b0-4163-8eff-d818a43e48a3" ParentLink="ComplexStatement_Statement" LowerBound="251.1" HigherBound="281.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Transaction Succeded" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="f2d42666-9c2d-472d-b4a5-d12d422ca84a" ParentLink="ReallyComplexStatement_Branch" LowerBound="252.17" HigherBound="257.1">
                            <om:Property Name="Expression" Value="resultCodeUpdateStatus == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="51239073-eef1-4a4f-b1c4-4e9148e73b8c" ParentLink="ComplexStatement_Statement" LowerBound="254.1" HigherBound="256.1">
                                <om:Property Name="Expression" Value="updateStatusRetries = 100;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Set Exit" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="90878d08-9d03-422e-a913-0bb5202c8fcf" ParentLink="ReallyComplexStatement_Branch" LowerBound="257.22" HigherBound="264.1">
                            <om:Property Name="Expression" Value="resultCodeUpdateStatus != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded &amp;&amp; updateStatusRetries &lt; 5" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Retry" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="80ee0056-d0e7-4689-ba02-9f8ac50f3a17" ParentLink="ComplexStatement_Statement" LowerBound="259.1" HigherBound="261.1">
                                <om:Property Name="Expression" Value="updateStatusRetries = updateStatusRetries + 1;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Retry" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="Delay" OID="42c8ecc2-4f35-41b2-a9fd-4af3f8573e3e" ParentLink="ComplexStatement_Statement" LowerBound="261.1" HigherBound="263.1">
                                <om:Property Name="Timeout" Value="new System.TimeSpan(0, 0, 0, 1);" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Delay" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="3d5f4480-0ef3-4c36-9244-2fdb5d959f24" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="350e09e5-55d8-473b-a0c3-bfc12a1d84b1" ParentLink="ComplexStatement_Statement" LowerBound="266.1" HigherBound="280.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(System.String.Format(&quot;{0}: Update status error. &quot;, flowName));&#xD;&#xA;errorMessage.Append(errorMessageUpdateStatus);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.BizTalk;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;updateStatusRetries = 100;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Error Management" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="0e9facc3-acf7-4735-96db-c4cae532fb58" ParentLink="ServiceBody_Statement" LowerBound="282.1" HigherBound="316.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="f961832a-b4ec-4a2c-8bf5-0b21f8e8f23b" ParentLink="ReallyComplexStatement_Branch" LowerBound="283.13" HigherBound="286.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="f328e021-f13a-4338-8d8e-1903c40c3eb6" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="411508fe-4ac8-46ac-9fb4-1f8b8eb78037" ParentLink="ComplexStatement_Statement" LowerBound="288.1" HigherBound="315.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="ea595ce8-7520-4e4b-af6d-23afba2c56d2" ParentLink="ComplexStatement_Statement" LowerBound="293.1" HigherBound="311.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="b9f060f7-a495-4b79-ac06-677fa5760a0f" ParentLink="Construct_MessageRef" LowerBound="294.35" HigherBound="294.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="abca48db-2901-4ed3-a610-4608a3951880" ParentLink="ComplexStatement_Statement" LowerBound="296.1" HigherBound="310.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="6d7baa8f-9b6d-4aa3-b545-36af94e577cc" ParentLink="ComplexStatement_Statement" LowerBound="311.1" HigherBound="313.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="b5bdc450-89a1-4c5f-aea3-8396cda16c97" ParentLink="ServiceBody_Statement" LowerBound="316.1" HigherBound="323.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="6d43e94d-a59a-4b9d-bc7d-602b050cac78" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.rptQuakeNotificationPollingType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptQuakeNotificationPolling" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="cdc7abc0-2933-4e0c-8477-4499d783e1b5" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="a2ab5252-7bf0-4b9f-951b-b30134454be7" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="50.1" HigherBound="53.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="163" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptQuakeNotificationUpdateStatusType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptQuakeNotificationUpdateStatus" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="a487e196-0e8d-415f-890d-ee138c7aa42b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="50.1" HigherBound="51.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="06535104-7883-424f-8642-bfd76260c759" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="53.1" HigherBound="55.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="246" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="3c2557ce-aa05-4b3b-b046-62a373d82f0d" ParentLink="PortDeclaration_CLRAttribute" LowerBound="53.1" HigherBound="54.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="5d8da672-1672-410e-9cf6-c851f9ac0356" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="55.1" HigherBound="58.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="32" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typEaiQuakeEventInsert" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiQuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="80c387e7-e8c1-4c97-bc9e-5901d0b6e26f" ParentLink="PortDeclaration_CLRAttribute" LowerBound="55.1" HigherBound="56.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ae4f60f2-d863-4f86-a2e4-25f75f0fde04" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="58.1" HigherBound="60.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="62" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptQuakeNotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptQuakeNotification" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="91acd408-2375-4c13-91fd-b9dd5b5a4b02" ParentLink="PortDeclaration_CLRAttribute" LowerBound="58.1" HigherBound="59.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype msgQuakeNotificationPollingType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationPollingTypedPolling.TypedPollingResultSet0 parameter;
    };
    internal messagetype msgQuakeNotificationUpdateStatusRequestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationUpdateStatusTypedProcedure.QuakeNotificationUpdateStatus parameter;
    };
    internal messagetype msgQuakeNotificationUpdateStatusResponseType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.schQuakeNotificationUpdateStatusTypedProcedure.QuakeNotificationUpdateStatusResponse parameter;
    };
    internal messagetype msgMailSendType
    {
        body A2A.EAI.Common.Shared.Schemas.MailSend parameter;
    };
    internal messagetype msgMailSendResponseType
    {
        body System.Xml.XmlDocument parameter;
    };
    internal porttype rptQuakeNotificationPollingType
    {
        oneway Receive
        {
            msgQuakeNotificationPollingType
        };
    };
    internal porttype sptQuakeNotificationUpdateStatusType
    {
        requestresponse QuakeNotificationUpdateStatus
        {
            msgQuakeNotificationUpdateStatusRequestType, msgQuakeNotificationUpdateStatusResponseType
        };
    };
    internal porttype sptQuakeNotificationType
    {
        requestresponse sptQuakeNotification
        {
            msgMailSendType, msgMailSendResponseType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcQuakeNotification
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements rptQuakeNotificationPollingType rptQuakeNotificationPolling;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptQuakeNotificationUpdateStatusType sptQuakeNotificationUpdateStatus;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiQuakeEventInsert sptEaiQuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port uses sptQuakeNotificationType sptQuakeNotification;
        message msgQuakeNotificationType msgQuakeNotification;
        message msgEaiQuakeEventInsertResponseType msgEaiQuakeEventInsertResponse;
        message msgEaiQuakeEventInsertRequestType msgEaiQuakeEventInsertRequest;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgEmailType msgMail;
        message msgQuakeNotificationPollingType msgQuakeNotificationPolling;
        message msgQuakeNotificationUpdateStatusRequestType msgQuakeNotificationUpdateStatusRequest;
        message msgQuakeNotificationUpdateStatusResponseType msgQuakeNotificationUpdateStatusResponse;
        message msgMailSendType msgMailSend;
        message msgMailSendResponseType msgMailSendResponse;
        System.DateTimeOffset dataInizio;
        System.String flowName;
        System.String errorMessageUpdateStatus;
        System.Int32 updateStatusRetries;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCodeUpdateStatus;
        System.String subject;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.Xml.XmlDocument emailBody;
        System.String emailAddress;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d28c4d16-d1b7-4291-88c9-de11b37165df")]
            activate receive (rptQuakeNotificationPolling.Receive, msgQuakeNotificationPolling);
            flowName = "";
            errorMessageUpdateStatus = "";
            subject = "";
            errorMessage = new System.Text.StringBuilder();
            emailBody = new System.Xml.XmlDocument();
            emailAddress = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a524ed2b-67fd-4258-9827-c3faae8413c4")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            updateStatusRetries = 0;
            errorMessageUpdateStatus = System.String.Empty;
            flowName = "Quake Notification";
            
            emailAddress = System.String.Empty;
            subject = System.String.Empty;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("27807656-c992-43f1-aa15-5a9dc6889633")]
            dataInizio = System.DateTimeOffset.Now;
            
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "QuakeEventsNotification",
            "instanceId", activityInstanceId,
            "Test", msgQuakeNotificationPolling.parameter.isTest
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e6629315-3e41-44a5-9319-30fc132b5059")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b382c1f1-3595-4128-8a20-f9e9eac92b2c")]
                    construct msgEaiQuakeEventInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("33387e2c-efbd-4cee-8da1-23a68204f54f")]
                        transform (msgEaiQuakeEventInsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToQuakeEventInsert (msgQuakeNotificationPolling.parameter);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("63633a94-9f9e-4025-b377-72033dffcd16")]
                    send (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4f242ed8-6f8a-4856-b0b3-096fe30cde70")]
                    receive (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("24a6183f-9e03-47e3-bedc-14c1a48359ed")]
                    if (msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents > 0)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8bfb8e00-22a5-4f16-9c09-caeb30fceb3c")]
                        if (msgQuakeNotificationPolling.parameter.isTest)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5b7fd55c-b5f6-4717-9c46-647927290487")]
                            subject = "Test / Esercitazione di Notifica Terremoto";
                        }
                        else 
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("77ceea9a-a445-4c02-a87a-fe736e0cf008")]
                            subject = "Notifica Terremoto";
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("46ed3165-ab31-4676-b469-ac2af32efe3a")]
                        construct msgQuakeNotification, msgMailSend
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("11b1848b-a0e4-416e-a4ba-44fdf6d4b181")]
                            transform (msgQuakeNotification.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertToNotification (msgEaiQuakeEventInsertResponse.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3a3fae7-8bda-482a-b983-16fc9be9d40b")]
                            transform (msgMailSend.parameter) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToMailSend (msgQuakeNotificationPolling.parameter);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9d47ab2f-003a-4d99-90bb-76492e5595d5")]
                            // BODY
                            emailBody = msgQuakeNotification.parameters;
                            
                            // TO
                            emailAddress = msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.sendEmailTo;
                            
                            msgMailSend.parameter.htmlContent = emailBody.InnerXml;
                            msgMailSend.parameter.subject = subject;
                            msgMailSend.parameter.mailFrom = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName, "earthQuakeMailFrom");
                            msgMailSend.parameter.mailCc = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName, "earthQuakeMailCc");
                            msgMailSend.parameter.mailTo = emailAddress;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("289b66da-df51-4dfa-b52a-bb9ebbdd0c98")]
                        send (sptQuakeNotification.sptQuakeNotification, msgMailSend);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b73a2f95-af03-4474-8a2a-c5d476446048")]
                        receive (sptQuakeNotification.sptQuakeNotification, msgMailSendResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ebba7bb2-3d06-4212-8d53-c560d6525c63")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                        "Note", System.String.Concat("Sent for event: ", msgQuakeNotificationPolling.parameter.eventId)
                        );
                        
                        A2A.EAI.INT_EXTEVENT.Services.ProcessServices.AddQuakeRelationShip(msgMailSendResponse.parameter, activityInstanceId);
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("46d60b58-f835-4dda-b34a-d0dcc41244e6")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8002328f-3d41-47bc-99d7-596d161a82f5")]
                        errorMessage.Append("Si è verificato un errore in fase di invio mail. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("17ab3d92-1c64-4799-ad1f-e972c4df527d")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("cbde9b9f-2f15-4cde-ad78-8fba1113298a")]
                        errorMessage.Append("Si è verificato un errore in fase di invio mail. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f0d923c0-4272-4805-8358-f256b8d2d696")]
            while (updateStatusRetries <= 5)
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("1dc51e87-9718-403f-b1d7-65f9347d26d4")]
                resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
                errorMessageUpdateStatus = System.String.Empty;
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("0efed6dc-ec42-42e4-91a4-45215fed5f33")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e169b58-8a38-422e-8cb5-265a78b3fa96")]
                        construct msgQuakeNotificationUpdateStatusRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b08d8b3a-2805-4890-9730-cb1de22de1a7")]
                            transform (msgQuakeNotificationUpdateStatusRequest.parameter) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeNotificationPollingToUpdateStatus (msgQuakeNotificationPolling.parameter);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0618e4fb-2718-400f-998c-802a655bc053")]
                            msgQuakeNotificationUpdateStatusRequest.parameter.transactionStatus = System.Convert.ToInt32(resultCode);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("984ed799-fded-41c5-81db-1944ddd623f7")]
                        send (sptQuakeNotificationUpdateStatus.QuakeNotificationUpdateStatus, msgQuakeNotificationUpdateStatusRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b0606eef-69c4-401f-a5ed-5555301101d6")]
                        receive (sptQuakeNotificationUpdateStatus.QuakeNotificationUpdateStatus, msgQuakeNotificationUpdateStatusResponse);
                    }
                    exceptions
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6b95b319-348d-44cd-b8db-5f3709b85b08")]
                        catch (System.Web.Services.Protocols.SoapException soapExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4d576e1a-4352-4507-abb0-1c5f1581997b")]
                            errorMessageUpdateStatus = soapExc.Message;
                            
                            resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9dfb34cc-dc60-4689-bd66-e240ddf02a9b")]
                        catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("40c72f09-a9d2-4597-a496-1b4c4b2067f8")]
                            errorMessageUpdateStatus = systemExc.Message;
                            
                            resultCodeUpdateStatus = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        }
                    }
                }
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("10ad88ed-63b0-4163-8eff-d818a43e48a3")]
                if (resultCodeUpdateStatus == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("51239073-eef1-4a4f-b1c4-4e9148e73b8c")]
                    updateStatusRetries = 100;
                }
                else if (resultCodeUpdateStatus != Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded && updateStatusRetries < 5)
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("80ee0056-d0e7-4689-ba02-9f8ac50f3a17")]
                    updateStatusRetries = updateStatusRetries + 1;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("42c8ecc2-4f35-41b2-a9fd-4af3f8573e3e")]
                    delay new System.TimeSpan(0, 0, 0, 1);
                }
                else 
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("350e09e5-55d8-473b-a0c3-bfc12a1d84b1")]
                    errorMessage.Append(System.String.Format("{0}: Update status error. ", flowName));
                    errorMessage.Append(errorMessageUpdateStatus);
                    
                    resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                    connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.BizTalk;
                    faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
                    
                    updateStatusRetries = 100;
                    
                    
                    
                    
                    
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e9facc3-acf7-4735-96db-c4cae532fb58")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("411508fe-4ac8-46ac-9fb4-1f8b8eb78037")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ea595ce8-7520-4e4b-af6d-23afba2c56d2")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("abca48db-2901-4ed3-a610-4608a3951880")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6d7baa8f-9b6d-4aa3-b545-36af94e577cc")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b5bdc450-89a1-4c5f-aea3-8396cda16c97")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

