﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="dc686481-87fe-42a8-8432-7aea4b8a796b" LowerBound="1.1" HigherBound="186.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_EXTEVENT.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="ServiceDeclaration" OID="8d04ebc4-f042-47bd-9eec-5011bc446e73" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="185.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="prcQuakeTest" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="VariableDeclaration" OID="86046e87-0f5b-4b63-8aba-55c68a146b9a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4aea64db-9afd-45b1-a45a-c03760faf532" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="d9ec8acc-2e1c-40ac-837a-2d77c5834a20" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c2f89371-fd7f-46ff-a77f-eccb5f04d1e1" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cbe03583-af31-4b63-9b1e-832e97da0a14" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="2cbe32f4-7a93-4649-8814-953c1350309e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0a530311-43b7-4fff-a426-a24c98d394fe" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTime" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="requestFromDate" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1bc58ca3-b389-4b7b-916e-fd6a22f13b9b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="31.1" HigherBound="32.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="171dce21-cea1-4787-a4ab-7ab66bc15a9b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="32.1" HigherBound="33.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgEaiQuakeEventInsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgEaiQuakeEventInsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8eca13c5-1bfe-4f6d-ac3c-00ca13867470" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="33.1" HigherBound="34.1">
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="a259995a-ec5b-4933-a9bf-0b53f4429b9e" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="34.1" HigherBound="35.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeEventN2InsertRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="d86271e5-152e-47e1-8aa9-a7b093bfeb07" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="35.1" HigherBound="36.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeEventN2InsertResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeEventN2InsertResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="abd5dede-01cf-4e4a-9076-c04aa03a230c" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgControlliDigheTerremotiWsRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="602a089b-e42b-47e2-91b6-239feb431568" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgControlliDigheTerremotiWsResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgControlliDigheTerremotiWsResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="41b7cb80-845a-42d7-801d-b473b99d5ae8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeTestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="msgQuakeTest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="5726cb2b-467e-448f-8d76-08612a45a81b" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="3c2a627c-3c21-45ef-996a-48e93f22353f" ParentLink="ServiceBody_Statement" LowerBound="48.1" HigherBound="52.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="rptQuakeTestIn" />
                    <om:Property Name="MessageName" Value="msgQuakeTest" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="88d7285e-2540-47ec-8a2d-7c8d003409b9" ParentLink="ServiceBody_Statement" LowerBound="52.1" HigherBound="60.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="591de9b8-3364-479c-a3e3-b5c9e25f1a18" ParentLink="ServiceBody_Statement" LowerBound="60.1" HigherBound="69.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Flusso&quot;, &quot;QuakeEventsTest&quot;,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Test&quot;, &quot;1&quot;&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Scope" OID="90881d57-8a2c-4335-b0f4-0693345b98a7" ParentLink="ServiceBody_Statement" LowerBound="69.1" HigherBound="142.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="7a293fdd-0bb0-4126-992c-a4a2bcf8249e" ParentLink="ComplexStatement_Statement" LowerBound="74.1" HigherBound="82.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Create EAI Insert" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="dd0d4509-7645-44b0-9978-d31f7f95963b" ParentLink="ComplexStatement_Statement" LowerBound="77.1" HigherBound="79.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeTestToEaiInsert" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Create EAI Insert" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="99f9c18e-5965-499e-b846-a5245a19c145" ParentLink="Transform_InputMessagePartRef" LowerBound="78.147" HigherBound="78.170">
                                <om:Property Name="MessageRef" Value="msgQuakeTest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_11" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="d929422f-591e-44d1-8632-7bd08bdf9837" ParentLink="Transform_OutputMessagePartRef" LowerBound="78.36" HigherBound="78.76">
                                <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_12" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="948c4417-cebe-4108-aaa0-f1d95b2cdfe7" ParentLink="ComplexStatement_Statement" LowerBound="79.1" HigherBound="81.1">
                            <om:Property Name="Expression" Value="msgEaiQuakeEventInsertRequest.parameters.transactionId = activityInstanceId;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Transaction ID" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="7f8d6675-3fe0-4988-96b4-fab9a21c1a74" ParentLink="Construct_MessageRef" LowerBound="75.31" HigherBound="75.60">
                            <om:Property Name="Ref" Value="msgEaiQuakeEventInsertRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="0b687aad-b7cc-4f97-86ec-f0caa2e4ccae" ParentLink="ComplexStatement_Statement" LowerBound="82.1" HigherBound="84.1">
                        <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                        <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertRequest" />
                        <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="fff089cc-b55e-4125-8482-f2cc40662812" ParentLink="ComplexStatement_Statement" LowerBound="84.1" HigherBound="86.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="sptEaiQuakeEventInsert" />
                        <om:Property Name="MessageName" Value="msgEaiQuakeEventInsertResponse" />
                        <om:Property Name="OperationName" Value="bzt_QuakeEventInsert" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="e8487378-fd0e-4b94-98c3-ac06aac77fa7" ParentLink="ComplexStatement_Statement" LowerBound="86.1" HigherBound="114.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Something to Notify" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="64ce607a-da2a-47d3-94b9-13fab6176586" ParentLink="ReallyComplexStatement_Branch" LowerBound="87.21" HigherBound="114.1">
                            <om:Property Name="Expression" Value="msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents &gt; 0" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="1e59e271-1b09-4719-8032-ed5dba15784b" ParentLink="ComplexStatement_Statement" LowerBound="89.1" HigherBound="95.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Construct N2 Message" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="50bbab4e-f59f-41c4-b99d-19744138f748" ParentLink="ComplexStatement_Statement" LowerBound="92.1" HigherBound="94.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertResponseToN2QuakeEventInsert" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Message" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="c6aca439-1cb9-40ad-8d36-148f26240592" ParentLink="Transform_InputMessagePartRef" LowerBound="93.177" HigherBound="93.218">
                                        <om:Property Name="MessageRef" Value="msgEaiQuakeEventInsertResponse" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_7" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="89c01fb2-9e00-4769-b65b-8092da2303ec" ParentLink="Transform_OutputMessagePartRef" LowerBound="93.40" HigherBound="93.79">
                                        <om:Property Name="MessageRef" Value="msgQuakeEventN2InsertRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_8" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageRef" OID="f055a6e2-8f0a-4f5a-9f6a-cffa725b0461" ParentLink="Construct_MessageRef" LowerBound="90.35" HigherBound="90.63">
                                    <om:Property Name="Ref" Value="msgQuakeEventN2InsertRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="a7881435-058c-4237-9d78-2584cfb14fcc" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="97.1">
                                <om:Property Name="PortName" Value="sptN2QuakeEventInsert" />
                                <om:Property Name="MessageName" Value="msgQuakeEventN2InsertRequest" />
                                <om:Property Name="OperationName" Value="QuakeEventN2Insert" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="352fb4c4-d61f-40dc-b99e-0706a81a009d" ParentLink="ComplexStatement_Statement" LowerBound="97.1" HigherBound="99.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptN2QuakeEventInsert" />
                                <om:Property Name="MessageName" Value="msgQuakeEventN2InsertResponse" />
                                <om:Property Name="OperationName" Value="QuakeEventN2Insert" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Construct" OID="7d255e30-e6f2-4b42-ac71-321261a85537" ParentLink="ComplexStatement_Statement" LowerBound="99.1" HigherBound="105.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Construct WS Message" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="a649fde6-5d8c-4015-9aca-8abafed60659" ParentLink="ComplexStatement_Statement" LowerBound="102.1" HigherBound="104.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Message" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="567831f0-5697-4a7a-bcfa-357eaddf1ce6" ParentLink="Transform_InputMessagePartRef" LowerBound="103.190" HigherBound="103.230">
                                        <om:Property Name="MessageRef" Value="msgQuakeEventN2InsertResponse" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="410b2537-7c34-4fcf-851d-51a1c5643356" ParentLink="Transform_OutputMessagePartRef" LowerBound="103.40" HigherBound="103.86">
                                        <om:Property Name="MessageRef" Value="msgControlliDigheTerremotiWsRequest" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageRef" OID="202aa460-68f3-4de9-91df-8c45db756992" ParentLink="Construct_MessageRef" LowerBound="100.35" HigherBound="100.70">
                                    <om:Property Name="Ref" Value="msgControlliDigheTerremotiWsRequest" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="0e2b333c-28f8-46e1-8498-9b481372248f" ParentLink="ComplexStatement_Statement" LowerBound="105.1" HigherBound="107.1">
                                <om:Property Name="PortName" Value="sptControlliDigheTerremotiWs" />
                                <om:Property Name="MessageName" Value="msgControlliDigheTerremotiWsRequest" />
                                <om:Property Name="OperationName" Value="inputTerremoti" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Receive" OID="f5c7ea53-7831-4c33-af4f-6d20c8439c52" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="109.1">
                                <om:Property Name="Activate" Value="False" />
                                <om:Property Name="PortName" Value="sptControlliDigheTerremotiWs" />
                                <om:Property Name="MessageName" Value="msgControlliDigheTerremotiWsResponse" />
                                <om:Property Name="OperationName" Value="inputTerremoti" />
                                <om:Property Name="OperationMessageName" Value="Response" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Receive" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="40a1042b-3182-4583-b55b-d5f3170c1a8b" ParentLink="ComplexStatement_Statement" LowerBound="109.1" HigherBound="113.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;Inviato a N2&quot;, &quot;SI&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="6b783b98-64a9-4106-9049-0ed449d26877" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="f077c2e5-0437-49ce-bca9-673faa2cfa21" ParentLink="Scope_Catch" LowerBound="117.1" HigherBound="128.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="soapException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="4eb20f4a-12a0-40af-886d-ce4129b8557c" ParentLink="Catch_Statement" LowerBound="120.1" HigherBound="127.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di recupero dati. &quot;);&#xD;&#xA;errorMessage.Append(soapExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="d1f76724-c991-427e-b9c0-df833f1bd957" ParentLink="Scope_Catch" LowerBound="128.1" HigherBound="140.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="System.Exception" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="systemException" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="7f8dae72-e6b8-43c0-ac9b-221918cf823f" ParentLink="Catch_Statement" LowerBound="131.1" HigherBound="139.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Si è verificato un errore in fase di recupero dati. &quot;);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="25c52e46-16c6-41b7-ba76-dca9277df7f2" ParentLink="ServiceBody_Statement" LowerBound="142.1" HigherBound="176.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="6bed5a36-fa1f-4737-a163-84558194e23a" ParentLink="ReallyComplexStatement_Branch" LowerBound="143.13" HigherBound="146.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="036c0f84-e13f-4980-93c2-041f4d17630e" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="16f42788-d022-49b9-86d9-e027cd36fb83" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="175.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="88f5cd2b-9773-40ab-86ef-f5c5720f30ff" ParentLink="ComplexStatement_Statement" LowerBound="153.1" HigherBound="171.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="408c8490-a290-4155-8d2f-0e8a94db6827" ParentLink="Construct_MessageRef" LowerBound="154.35" HigherBound="154.50">
                                    <om:Property Name="Ref" Value="msgNotification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="67623960-357d-4565-b1f8-5070e73b454b" ParentLink="ComplexStatement_Statement" LowerBound="156.1" HigherBound="170.1">
                                    <om:Property Name="Expression" Value="msgNotification = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;&#xD;&#xA;msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;&#xD;&#xA;msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;&#xD;&#xA;msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;msgNotification.messageText = errorMessage.ToString();&#xD;&#xA;msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="6e58c6e9-cd76-406c-8de0-28076d715b2d" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="173.1">
                                <om:Property Name="PortName" Value="sptNotification" />
                                <om:Property Name="MessageName" Value="msgNotification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="48b96be3-eafc-497a-8cfc-48409807b41e" ParentLink="ServiceBody_Statement" LowerBound="176.1" HigherBound="183.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="38b548ca-e8d2-434b-951f-b67be7bc4b15" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="18.1" HigherBound="20.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="166" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptNotification" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="65b07128-34c1-465b-80e9-6b7ff8972f2a" ParentLink="PortDeclaration_CLRAttribute" LowerBound="18.1" HigherBound="19.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="f8497014-da9d-43cf-a669-521c1855807e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="20.1" HigherBound="22.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="0" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.rptQuakeTestInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="rptQuakeTestIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="d57d89da-1944-4aac-8663-64a11a70099b" ParentLink="PortDeclaration_CLRAttribute" LowerBound="20.1" HigherBound="21.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e6cf3476-5c3b-4d06-a195-1f6bce865e45" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="22.1" HigherBound="25.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="31" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.typEaiQuakeEventInsert" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptEaiQuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="7aa927cd-5a6f-4a9e-9f8c-550d21c855f4" ParentLink="PortDeclaration_CLRAttribute" LowerBound="22.1" HigherBound="23.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="1956795b-b058-4584-b9b9-0c828075bdd6" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="25.1" HigherBound="28.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="76" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptN2QuakeEventInsertType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptN2QuakeEventInsert" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="c72cba86-b713-4a65-905a-e9538943fc11" ParentLink="PortDeclaration_CLRAttribute" LowerBound="25.1" HigherBound="26.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="2eb610fc-1e40-45af-861c-22981dea4b6e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="28.1" HigherBound="31.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="98" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_EXTEVENT.Processes.sptControlliDigheTerremotiWsType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sptControlliDigheTerremotiWs" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="8c534af0-ab04-414e-bb23-47faea212846" ParentLink="PortDeclaration_CLRAttribute" LowerBound="28.1" HigherBound="29.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b2b58d73-c216-4346-99b6-8a07a308b59f" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="msgQuakeTestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="8b5c5591-a73e-4a9e-b096-bce81cd8d117" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeTest" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="4339c5b1-a5bc-4c3f-a5c2-70cf69226ffe" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="rptQuakeTestInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="a69a4ec1-1cef-436b-88cf-aa9765827c6d" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="1db2ef00-de4f-47b9-9b88-6e73ca2d2a72" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_EXTEVENT.Processes.msgQuakeTestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_EXTEVENT.Processes
{
    internal messagetype msgQuakeTestType
    {
        body A2A.EAI.INT_EXTEVENT.Messaging.Schemas.Quake.QuakeTest parameters;
    };
    internal porttype rptQuakeTestInType
    {
        oneway Receive
        {
            msgQuakeTestType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service prcQuakeTest
    {
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType sptNotification;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements rptQuakeTestInType rptQuakeTestIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses typEaiQuakeEventInsert sptEaiQuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptN2QuakeEventInsertType sptN2QuakeEventInsert;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses sptControlliDigheTerremotiWsType sptControlliDigheTerremotiWs;
        message msgEaiQuakeEventInsertRequestType msgEaiQuakeEventInsertRequest;
        message msgEaiQuakeEventInsertResponseType msgEaiQuakeEventInsertResponse;
        message Microsys.EAI.Framework.Schemas.Notification msgNotification;
        message msgQuakeEventN2InsertRequestType msgQuakeEventN2InsertRequest;
        message msgQuakeEventN2InsertResponseType msgQuakeEventN2InsertResponse;
        message msgControlliDigheTerremotiWsRequestType msgControlliDigheTerremotiWsRequest;
        message msgControlliDigheTerremotiWsResponseType msgControlliDigheTerremotiWsResponse;
        message msgQuakeTestType msgQuakeTest;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        System.DateTime requestFromDate;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("3c2a627c-3c21-45ef-996a-48e93f22353f")]
            activate receive (rptQuakeTestIn.Receive, msgQuakeTest);
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("88d7285e-2540-47ec-8a2d-7c8d003409b9")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("591de9b8-3364-479c-a3e3-b5c9e25f1a18")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "Data Inizio", dataInizio,
            "Flusso", "QuakeEventsTest",
            "instanceId", activityInstanceId,
            "Test", "1"
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("90881d57-8a2c-4335-b0f4-0693345b98a7")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7a293fdd-0bb0-4126-992c-a4a2bcf8249e")]
                    construct msgEaiQuakeEventInsertRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dd0d4509-7645-44b0-9978-d31f7f95963b")]
                        transform (msgEaiQuakeEventInsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapQuakeTestToEaiInsert (msgQuakeTest.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("948c4417-cebe-4108-aaa0-f1d95b2cdfe7")]
                        msgEaiQuakeEventInsertRequest.parameters.transactionId = activityInstanceId;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0b687aad-b7cc-4f97-86ec-f0caa2e4ccae")]
                    send (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fff089cc-b55e-4125-8482-f2cc40662812")]
                    receive (sptEaiQuakeEventInsert.bzt_QuakeEventInsert, msgEaiQuakeEventInsertResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("e8487378-fd0e-4b94-98c3-ac06aac77fa7")]
                    if (msgEaiQuakeEventInsertResponse.parameters.GeneratedEvents.generatedEvents > 0)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e59e271-1b09-4719-8032-ed5dba15784b")]
                        construct msgQuakeEventN2InsertRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("50bbab4e-f59f-41c4-b99d-19744138f748")]
                            transform (msgQuakeEventN2InsertRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapEaiQuakeEventInsertResponseToN2QuakeEventInsert (msgEaiQuakeEventInsertResponse.parameters);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("a7881435-058c-4237-9d78-2584cfb14fcc")]
                        send (sptN2QuakeEventInsert.QuakeEventN2Insert, msgQuakeEventN2InsertRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("352fb4c4-d61f-40dc-b99e-0706a81a009d")]
                        receive (sptN2QuakeEventInsert.QuakeEventN2Insert, msgQuakeEventN2InsertResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7d255e30-e6f2-4b42-ac71-321261a85537")]
                        construct msgControlliDigheTerremotiWsRequest
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a649fde6-5d8c-4015-9aca-8abafed60659")]
                            transform (msgControlliDigheTerremotiWsRequest.parameters) = A2A.EAI.INT_EXTEVENT.Messaging.Maps.Quake.mapN2QuakeEventInsertResponseToControlliDigheTerremotiWs (msgQuakeEventN2InsertResponse.parameters);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e2b333c-28f8-46e1-8498-9b481372248f")]
                        send (sptControlliDigheTerremotiWs.inputTerremoti, msgControlliDigheTerremotiWsRequest);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5c7ea53-7831-4c33-af4f-6d20c8439c52")]
                        receive (sptControlliDigheTerremotiWs.inputTerremoti, msgControlliDigheTerremotiWsResponse);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("40a1042b-3182-4583-b55b-d5f3170c1a8b")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
                        "Inviato a N2", "SI"
                        );
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f077c2e5-0437-49ce-bca9-673faa2cfa21")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4eb20f4a-12a0-40af-886d-ce4129b8557c")]
                        errorMessage.Append("Si è verificato un errore in fase di recupero dati. ");
                        errorMessage.Append(soapExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("d1f76724-c991-427e-b9c0-df833f1bd957")]
                    catch (System.Exception systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f8dae72-e6b8-43c0-ac9b-221918cf823f")]
                        errorMessage.Append("Si è verificato un errore in fase di recupero dati. ");
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("25c52e46-16c6-41b7-ba76-dca9277df7f2")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("16f42788-d022-49b9-86d9-e027cd36fb83")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("88f5cd2b-9773-40ab-86ef-f5c5720f30ff")]
                        construct msgNotification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("67623960-357d-4565-b1f8-5070e73b454b")]
                            msgNotification = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            msgNotification.applicationName = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ApplicationName;
                            msgNotification.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            msgNotification.flowGroup = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowGroup;
                            msgNotification.flowDescription = A2A.EAI.INT_EXTEVENT.Services.ProcessServices.FlowDescriptionQuake;
                            msgNotification.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            msgNotification.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            msgNotification.messageText = errorMessage.ToString();
                            msgNotification.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            msgNotification.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("6e58c6e9-cd76-406c-8de0-28076d715b2d")]
                        send (sptNotification.Send, msgNotification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("48b96be3-eafc-497a-8cfc-48409807b41e")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_EXTEVENT.Services.ProcessServices.ActivityNameProtezioneCivile, activityInstanceId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
        }
    }
}

