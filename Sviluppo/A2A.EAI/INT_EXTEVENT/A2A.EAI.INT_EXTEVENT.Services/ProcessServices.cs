﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.Net.Http;
using Microsoft.XLANGs.BaseTypes;
using System.IO;
using System.Text;
using System.Xml.Linq;
using System.Xml;
using System.Security.Cryptography.Pkcs;
using Newtonsoft.Json;

namespace A2A.EAI.INT_EXTEVENT.Services
{
    [Serializable]
    public class ResultStatus
    {
        public string ResultCode { get; set; }
        public string ErrorMessage { get; set; }
        public string ReturnXmlString { get; set; }
    }

    [Serializable]
    public class ProcessServices
    {

        #region BAM

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_EXTEVENT";

        public const string ActivityNameProtezioneCivile = "Protezione Civile";
        public const string ActivityNameQuake = "Hearthquake Event";
        public const string MailSenderActivityName = "Mail Sender";

        public const string FlowDescriptionQuake = "Hearthquake Event";
        public const string FlowDescriptionProtCiv = "Protezione Civile GetFile";
        public const string FlowDescriptionProtezioneCivileProcess = "Protezione Civile Process";
        public const string FlowDescriptionProtezioneCivileProcessTest = "Protezione Civile Process - TEST";

        #endregion


        public static string ProtCivGetFileName(string flowName)
        {
            string returnValue = string.Empty;
            string url = string.Empty;
            string stringToFind = string.Empty;
            int fileLength;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                if (flowName == "criticita")
                {
                    url = ConfigurationHelper.GetValue(ApplicationName, "ProtezioneCivileCriticitaUrl");
                    stringToFind = ConfigurationHelper.GetValue(ApplicationName, "ProtezioneCivileCriticitaStringToFind");
                    fileLength = 17;
                }
                else
                {
                    url = ConfigurationHelper.GetValue(ApplicationName, "ProtezioneCivileVigilanzaUrl");
                    stringToFind = ConfigurationHelper.GetValue(ApplicationName, "ProtezioneCivileVigilanzaStringToFind");
                    fileLength = 12;
                }

                HttpClient httpClient = new HttpClient
                {
                    BaseAddress = new Uri(url)
                };
                var body = httpClient.GetStringAsync(url).Result;

                if (body.Contains(stringToFind))
                {
                    returnValue = body.Substring(body.IndexOf(stringToFind, 0) + stringToFind.Length, fileLength);
                }
                else
                {
                    returnValue = "No data found.";
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        public static bool IsValidZipFile(XLANGPart messagePart)
        {
            bool returnValue = false;
            try
            {
                
                LoggingServices.TraceDebugInfo("IN");
                Stream streamPart = (Stream)messagePart.RetrieveAs(typeof(Stream));
                string value = string.Empty;
                using (var reader = new StreamReader(streamPart, Encoding.UTF8))
                {
                    value = reader.ReadToEnd();
                }
                if (value == "File non trovato")
                {
                    returnValue = false;
                }
                else
                {
                    returnValue = true;
                }

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }
            return returnValue;
        }

        #region CADES

        public static string ExtractContentFromCades(byte[] cadesSignature)
        {
            LoggingServices.TraceDebugInfo("IN");
            string returnValue = string.Empty;

            try
            {
                // Create a SignedCms object and decode the CADES signature
                SignedCms signedCms = new SignedCms();
                signedCms.Decode(cadesSignature);

                // Get the content information
                ContentInfo contentInfo = signedCms.ContentInfo;

                // Extract the content data
                byte[] contentData = contentInfo.Content;

                // Assuming the content is text, you can convert it to string
                returnValue = Encoding.UTF8.GetString(contentData);

            }
            catch (Exception ex)
            {
                LoggingServices.TraceError(ex);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        public static ResultStatus ExtractContentFromCades(XLANGPart messagePart)
        {
            LoggingServices.TraceDebugInfo("IN");

            XmlDocument testXmlDocument = new XmlDocument();
            ResultStatus resultStatus = new ResultStatus
            {
                ResultCode = "KO",
                ReturnXmlString = string.Empty
            };

            Stream streamPart = (Stream)messagePart.RetrieveAs(typeof(Stream));
            string value = string.Empty;
            using (var xmlString = new StreamReader(streamPart, Encoding.UTF8))
            {
                value = xmlString.ReadToEnd();
            }

            try
            {
                testXmlDocument.LoadXml(value);
                resultStatus.ReturnXmlString = testXmlDocument.InnerXml.Replace("bvxml", "alert");
                resultStatus.ResultCode = "OK";
            }
            catch (Exception ex)
            {
                LoggingServices.TraceError(ex);
                resultStatus.ResultCode = "KO";
                resultStatus.ErrorMessage = ex.Message;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }


            if (resultStatus.ResultCode == "KO")
            {
                try
                {
                    // Read the CADES signature file
                    byte[] cadesSignature;

                    Stream streamPartCades = (Stream)messagePart.RetrieveAs(typeof(Stream));
                    using (var binaryReader = new BinaryReader(streamPartCades))
                    {
                        cadesSignature = binaryReader.ReadBytes((int)streamPartCades.Length);
                    }
                   
                    // Extract content from the CADES signature
                    string extractedContent = ExtractContentFromCades(cadesSignature);

                    if (!string.IsNullOrEmpty(extractedContent))
                    {
                        testXmlDocument.LoadXml(extractedContent);
                        resultStatus.ReturnXmlString = testXmlDocument.InnerXml.Replace("bvxml", "alert");
                        resultStatus.ResultCode = "OK";
                    }
                    else
                    {
                        resultStatus.ResultCode = "KO";
                        resultStatus.ErrorMessage = "Empty Message";
                    }

                }
                catch (Exception ex)
                {
                    LoggingServices.TraceError(ex);
                    resultStatus.ResultCode = "KO";
                    resultStatus.ErrorMessage = ex.Message;
                }
                finally
                {
                    LoggingServices.TraceDebugInfo("OUT");
                }
            }

            return resultStatus;
        }

        #endregion

        public static void AddQuakeRelationShip(XLANGPart message, string callerOrchestrationId)
        {
            MailSenderResponse returnValue = new MailSenderResponse();
            string responseBody = string.Empty;

            try
            {

                LoggingServices.TraceDebugInfo("IN");

                var body = (Stream)message.RetrieveAs(typeof(Stream));

                StreamReader reader = new StreamReader(body);
                responseBody = reader.ReadToEnd();

                dynamic bodyObject = JsonConvert.DeserializeObject<dynamic>(responseBody);

                returnValue.operation_status = bodyObject.operation_status;
                returnValue.errorMessage = bodyObject.errorMessage;
                returnValue.value = bodyObject.value;

                if (!string.IsNullOrEmpty(returnValue.value) && !string.IsNullOrEmpty(callerOrchestrationId))
                    BamHelper.AddRelationship(
                        ActivityNameProtezioneCivile,
                        callerOrchestrationId,
                        MailSenderActivityName,
                        returnValue.value,
                        ""
                    );

            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc, responseBody);
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT - Return {returnValue}", returnValue);
            }


        }


        [Serializable]
        public class MailSenderResponse
        {
            public string operation_status { get; set; }
            public string value { get; set; }
            public string errorMessage { get; set; }
        }
    }
}
