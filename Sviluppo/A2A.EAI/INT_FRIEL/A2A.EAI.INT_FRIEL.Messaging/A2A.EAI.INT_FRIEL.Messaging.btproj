﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{13FA2F85-C796-4495-9F58-61F8F59E74F2}</ProjectGuid>
    <ProjectTypeGuids>{EF7E3281-CD33-11D4-8326-00C04FA0CE8D};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <OutputType>library</OutputType>
    <GenericProcessing>true</GenericProcessing>
    <RootNamespace>A2A.EAI.INT_FRIEL.Messaging</RootNamespace>
    <AssemblyName>A2A.EAI.INT_FRIEL.Messaging</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <BpelCompliance>True</BpelCompliance>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup>
    <AssemblyOriginatorKeyFile>A2A.EAI.snk</AssemblyOriginatorKeyFile>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.BizTalk.Pipeline.Components, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL" />
    <Reference Include="Microsys.EAI.Framework.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=e651f0f96466e9b3, processorArchitecture=MSIL" />
    <Reference Include="System">
      <Name>System</Name>
    </Reference>
    <Reference Include="System.Xml">
      <Name>System.XML</Name>
    </Reference>
    <Reference Include="System.Configuration">
      <Name>System.Configuration</Name>
    </Reference>
    <Reference Include="Microsoft.BizTalk.Pipeline">
      <SpecificVersion>False</SpecificVersion>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.DefaultPipelines">
      <Name>Microsoft.BizTalk.DefaultPipelines</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.GlobalPropertySchemas">
      <Name>Microsoft.BizTalk.GlobalPropertySchemas</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.BizTalk.TestTools">
      <Name>Microsoft.BizTalk.TestTools</Name>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.XLANGs.BaseTypes">
      <Name>Microsoft.XLANGs.BaseTypes</Name>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="A2A.EAI.snk" />
    <Schema Include="Schemas\Disponibilita\FrielDisponibilita.xsd">
      <TypeName>FrielDisponibilita</TypeName>
      <Namespace>A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita</Namespace>
      <SubType>Task</SubType>
    </Schema>
    <Schema Include="Schemas\Disponibilita\GediDisponibilita.xsd">
      <TypeName>GediDisponibilita</TypeName>
      <Namespace>A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita</Namespace>
      <SubType>Task</SubType>
    </Schema>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Schemas\Metering\" />
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipelines\In\FrielDisponibilita.btp">
      <TypeName>FrielDisponibilita</TypeName>
      <Namespace>A2A.EAI.INT_FRIEL.Messaging.Pipelines.In</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Pipeline Include="Pipelines\Out\GediDisponibilita.btp">
      <TypeName>GediDisponibilita</TypeName>
      <Namespace>A2A.EAI.INT_FRIEL.Messaging.Pipelines.Out</Namespace>
      <SubType>Task</SubType>
    </Pipeline>
  </ItemGroup>
  <ItemGroup>
    <Map Include="Maps\Disponibilita\FrielDisponibilitaToGedi.btm">
      <TypeName>FrielDisponibilitaToGedi</TypeName>
      <Namespace>A2A.EAI.INT_FRIEL.Messaging.Maps.Disponibilita</Namespace>
      <SubType>Task</SubType>
    </Map>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\A2A.EAI.INT_FRIEL.Services\A2A.EAI.INT_FRIEL.Services.csproj">
      <Project>{820d161e-afac-40ca-96f0-2be5260d1ec3}</Project>
      <Name>A2A.EAI.INT_FRIEL.Services</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="$(MSBuildExtensionsPath)\Microsoft\BizTalk\BizTalkC.targets" />
</Project>