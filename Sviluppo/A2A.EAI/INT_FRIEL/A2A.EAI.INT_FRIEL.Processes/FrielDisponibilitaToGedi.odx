﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="180d512f-255c-498f-a5ac-6fe8ffdb109f" LowerBound="1.1" HigherBound="208.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_FRIEL.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="b65a0967-026a-477d-8f07-edf2fba2abc1" ParentLink="Module_PortType" LowerBound="12.1" HigherBound="19.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FrielDisponibilitaInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="f81fd934-33b7-40ad-8282-7c0bb0071e3d" ParentLink="PortType_OperationDeclaration" LowerBound="14.1" HigherBound="18.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="47d5a8cc-c952-40b6-b0a5-179df769fb9e" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="16.13" HigherBound="16.35">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_FRIEL.Processes.FrielDisponibilitaType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="1023486f-12ba-4019-837e-fc985a7a0347" ParentLink="Module_PortType" LowerBound="19.1" HigherBound="26.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FrielDisponibilitaToGediMailOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="eac2a2f9-4507-4f4f-a346-958f7e0d4e4d" ParentLink="PortType_OperationDeclaration" LowerBound="21.1" HigherBound="25.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="21e60e7d-ea1d-4e5f-aee6-4bc4aa4e3f1c" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="23.13" HigherBound="23.34">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_FRIEL.Processes.GediDisponibilitaType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="dfb7af28-db99-4adb-8242-37cd3fb70fca" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FrielDisponibilitaType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="1b991942-6cd6-45c8-910b-40613acd21b2" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.FrielDisponibilita" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="b2378d6d-fd05-4b43-95b9-205c94fb2ae5" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="GediDisponibilitaType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="bc621f94-6bdb-434e-a80c-22fac12c87d7" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.GediDisponibilita" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="b33d57db-34a3-4c9b-b274-ff784047e6b0" ParentLink="Module_ServiceDeclaration" LowerBound="26.1" HigherBound="207.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="FrielDisponibilitaToGedi" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="47223135-a529-427b-820a-0cdde06a7afd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="39.1" HigherBound="40.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="attachFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed426a2a-f9c7-4fc2-b74a-c90a56e8ab94" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="40.1" HigherBound="41.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="attachFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="abb46df1-9400-47a2-8e91-7c73ac751141" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="41.1" HigherBound="42.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="de98ff2d-a377-4be4-80c5-a7b84bc408ba" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="42.1" HigherBound="43.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="322c1803-ae35-45bf-9300-ceeae89456bd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="43.1" HigherBound="44.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cd454fd7-2b83-4c6d-968d-fe66b5d1498c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="44.1" HigherBound="45.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3a12aea8-1838-4b4e-b202-efe9962edb61" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="45.1" HigherBound="46.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="e2545b65-cdbb-4585-8d94-9c08c5efa2a8" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="46.1" HigherBound="47.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="3e1c3c12-b98e-4a0a-97d0-92edf22ccb64" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="47.1" HigherBound="48.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f298d7d6-42a3-45ca-92d9-dbf0f2bf90bd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="48.1" HigherBound="49.1">
                <om:Property Name="InitialValue" Value="&quot;&quot;" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ec105591-c8fe-449c-a475-306955f1e905" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="49.1" HigherBound="50.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="9d8ab9ac-5cec-47ee-937a-71d7ca0e03eb" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="36.1" HigherBound="37.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.FrielDisponibilitaType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="FrielDisponibilita" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="23846c72-7532-44e8-8683-9223a54c2f6b" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="37.1" HigherBound="38.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.GediDisponibilitaType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GediDisponibilita" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b74ab7fb-2492-44a2-96b2-ab6a1ed94d35" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="38.1" HigherBound="39.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="ffb1d723-4a96-4543-b073-0ba5cce0b712" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="cb3f2c82-9429-46f4-a813-29515970dc36" ParentLink="ServiceBody_Statement" LowerBound="52.1" HigherBound="61.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="FrielDisponibilitaIn" />
                    <om:Property Name="MessageName" Value="FrielDisponibilita" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e30f5b9e-bf83-4440-8371-207e836a6394" ParentLink="ServiceBody_Statement" LowerBound="61.1" HigherBound="75.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(FrielDisponibilita);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(FrielDisponibilita);&#xD;&#xA;&#xD;&#xA;flowName = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowDescriptionDisponibilita;&#xD;&#xA;attachFileName = System.String.Concat(&quot;meteologica_availability_&quot;, System.DateTimeOffset.Now.ToString(&quot;yyyyMMddHHmmss&quot;), &quot;.csv&quot;);&#xD;&#xA;attachFilePath = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(&quot;A2A.EAI&quot;, &quot;frielDisponibilitaMailAttachPath&quot;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="a603fa20-a3cc-44c2-bcd2-f175a6996e9f" ParentLink="ServiceBody_Statement" LowerBound="75.1" HigherBound="86.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_FRIEL.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;Nome File&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Percorso Backup&quot;, archiveFilePath,&#xD;&#xA;&quot;Percorso Sorgente&quot;, System.IO.Path.GetDirectoryName(originalFileName),&#xD;&#xA;&quot;Flusso&quot;, flowName&#xD;&#xA;);" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="c0ff8cfa-3f24-41d9-bd9a-187f47a706ee" ParentLink="ServiceBody_Statement" LowerBound="86.1" HigherBound="166.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Decision" OID="011f8e77-97c5-41e5-a4c2-87a4f3e46567" ParentLink="ComplexStatement_Statement" LowerBound="91.1" HigherBound="135.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="File Is Empty" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="a73481c7-6ae8-450c-be81-b48b5922d0f5" ParentLink="ReallyComplexStatement_Branch" LowerBound="92.21" HigherBound="99.1">
                            <om:Property Name="Expression" Value="A2A.EAI.INT_FRIEL.Services.ProcessServices.CountXmlNode(FrielDisponibilita.parameters, &quot;/*[local-name()='FrielDisponibilita' and namespace-uri()='http://A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.FrielDisponibilita']/*[local-name()='Items' and namespace-uri()='']&quot;) == 0" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="f0d8ec3a-3e6b-4607-8f85-bdfb6f327f0d" ParentLink="ComplexStatement_Statement" LowerBound="95.1" HigherBound="98.1">
                                <om:Property Name="Expression" Value="errorMessage.Append(&quot;Friel FILE has no rows. &quot;);&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="WriteLog" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="8baed8a3-4127-476e-a9b9-08d729762032" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="9a3d0d25-4ac6-4e53-8635-5e3158fd05a5" ParentLink="ComplexStatement_Statement" LowerBound="101.1" HigherBound="107.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create Gedi Msg" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="Transform" OID="e3ffba7b-2e0b-438d-8eff-ec86c1649786" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_FRIEL.Messaging.Maps.Disponibilita.FrielDisponibilitaToGedi" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup Message" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="979dd244-cb75-46f0-b226-50021781c132" ParentLink="Transform_InputMessagePartRef" LowerBound="105.145" HigherBound="105.174">
                                        <om:Property Name="MessageRef" Value="FrielDisponibilita" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="31ae31ba-0ff9-4075-af67-b722b20cec3e" ParentLink="Transform_OutputMessagePartRef" LowerBound="105.40" HigherBound="105.68">
                                        <om:Property Name="MessageRef" Value="GediDisponibilita" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageRef" OID="9dbe2ccf-52b8-45db-b477-30bf2f83e831" ParentLink="Construct_MessageRef" LowerBound="102.35" HigherBound="102.52">
                                    <om:Property Name="Ref" Value="GediDisponibilita" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="4e944868-1445-40b8-b9b0-48da10971c1e" ParentLink="ComplexStatement_Statement" LowerBound="107.1" HigherBound="111.1">
                                <om:Property Name="Expression" Value="FrielDisponibilitaToGediMailOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(attachFilePath, attachFileName);&#xD;&#xA;FrielDisponibilitaToGediMailOut(Microsoft.XLANGs.BaseTypes.TransportType) = &quot;FILE&quot;;&#xD;&#xA;&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Port Address" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Send" OID="ca09bf22-6ffa-4257-bfb6-2c7d43ee34bd" ParentLink="ComplexStatement_Statement" LowerBound="111.1" HigherBound="113.1">
                                <om:Property Name="PortName" Value="FrielDisponibilitaToGediMailOut" />
                                <om:Property Name="MessageName" Value="GediDisponibilita" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="Construct" OID="77f97a55-c5c6-40ae-b480-a08779820213" ParentLink="ComplexStatement_Statement" LowerBound="113.1" HigherBound="132.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Create Mail Attach" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="9752c3d0-f3d8-4f70-be4f-4d4ffd933359" ParentLink="ComplexStatement_Statement" LowerBound="116.1" HigherBound="131.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_FRIEL.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = &quot;meteologica_availability&quot;;&#xD;&#xA;Notification.parameter.flowDescription = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();&#xD;&#xA;Notification.parameter.messageText = &quot;E' stata rilevata una Nuova indisponibilità per l' impianto FRIEL&quot;;&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;Notification.parameter.attachment = System.IO.Path.Combine(attachFilePath, attachFileName);&#xD;&#xA;Notification.parameter.emailAddress = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue(&quot;A2A.EAI&quot;, &quot;frielDisponibilitaMailTo&quot;);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Create Attach" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="8b668bc6-2b07-4d59-85e0-96c72bfeed38" ParentLink="Construct_MessageRef" LowerBound="114.35" HigherBound="114.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="b3d3f504-c1aa-430f-a21a-e67636ea6cc0" ParentLink="ComplexStatement_Statement" LowerBound="132.1" HigherBound="134.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="b879c63a-de03-4a1c-987e-99ab88d6b5b9" ParentLink="Scope_Catch" LowerBound="138.1" HigherBound="151.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="d722955c-596a-467d-869e-edbfc248f960" ParentLink="Catch_Statement" LowerBound="141.1" HigherBound="150.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Error sending data to Gedi. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="7737cb8f-51dc-47e0-86d3-9cfd4acd3380" ParentLink="Scope_Catch" LowerBound="151.1" HigherBound="164.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f8157cd2-64f5-4efe-b531-c1f9ae269830" ParentLink="Catch_Statement" LowerBound="154.1" HigherBound="163.1">
                            <om:Property Name="Expression" Value="errorMessage.Append(&quot;Error sending data to Gedi. &quot;);&#xD;&#xA;errorMessage.Append(System.String.Format(&quot;File di input: '{0}' - File salvato in: '{1}'; &quot;, originalFileName, archiveFilePath));&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="0abb43f9-7b4d-4bed-a8c6-fba56916d27b" ParentLink="ServiceBody_Statement" LowerBound="166.1" HigherBound="199.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="10ab0ec6-ec49-41b1-8c9f-f345ab969ad3" ParentLink="ReallyComplexStatement_Branch" LowerBound="167.13" HigherBound="170.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="646fad42-9205-43b5-a723-d304e32e0e1c" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="ae2add04-6dcd-4abc-93e2-fe963af0a79b" ParentLink="ComplexStatement_Statement" LowerBound="172.1" HigherBound="198.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="934dc2dc-b486-45f5-a0da-a8850d471714" ParentLink="ComplexStatement_Statement" LowerBound="177.1" HigherBound="194.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="74921fe8-ce77-4f43-8f1b-6ddeecbe1821" ParentLink="ComplexStatement_Statement" LowerBound="180.1" HigherBound="193.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_FRIEL.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = flowName;&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="b214bcec-b74c-4558-81f0-adafd32e53a3" ParentLink="Construct_MessageRef" LowerBound="178.35" HigherBound="178.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="00478d0b-4344-43ef-b070-48d21b37555f" ParentLink="ComplexStatement_Statement" LowerBound="194.1" HigherBound="196.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="7f93487e-6b4b-4fb6-8d67-1699630b594d" ParentLink="ServiceBody_Statement" LowerBound="199.1" HigherBound="205.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_FRIEL.Services.ProcessServices.ActivityName, activityInstanceId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(),&#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="545f4c0e-e4af-4ceb-a137-cb1a1f0aa4af" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="29.1" HigherBound="31.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.FrielDisponibilitaInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="FrielDisponibilitaIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="5004f48b-1c4c-480e-a81b-0c139ab418d0" ParentLink="PortDeclaration_CLRAttribute" LowerBound="29.1" HigherBound="30.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4f8358b4-4c28-41ec-885c-c7472e9f143c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="31.1" HigherBound="33.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="80" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="DirectBindingAttribute" OID="d3d79655-48d4-4c52-8373-420e57c3980e" ParentLink="PortDeclaration_CLRAttribute" LowerBound="31.1" HigherBound="32.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="4afb52e1-d687-4e80-bb90-78bf2c6e083c" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="33.1" HigherBound="36.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="44" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_FRIEL.Processes.FrielDisponibilitaToGediMailOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="FrielDisponibilitaToGediMailOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="PhysicalBindingAttribute" OID="26028872-ac54-4343-97e1-dbb262dbad56" ParentLink="PortDeclaration_CLRAttribute" LowerBound="33.1" HigherBound="34.1">
                    <om:Property Name="InPipeline" Value="Microsoft.BizTalk.DefaultPipelines.XMLReceive" />
                    <om:Property Name="OutPipeline" Value="A2A.EAI.INT_FRIEL.Messaging.Pipelines.Out.GediDisponibilita" />
                    <om:Property Name="TransportType" Value="HTTP" />
                    <om:Property Name="URI" Value="http://tempURI" />
                    <om:Property Name="IsDynamic" Value="True" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_FRIEL.Processes
{
    internal messagetype FrielDisponibilitaType
    {
        body A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.FrielDisponibilita parameters;
    };
    internal messagetype GediDisponibilitaType
    {
        body A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.GediDisponibilita parameters;
    };
    internal porttype FrielDisponibilitaInType
    {
        oneway Receive
        {
            FrielDisponibilitaType
        };
    };
    internal porttype FrielDisponibilitaToGediMailOutType
    {
        oneway Send
        {
            GediDisponibilitaType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service FrielDisponibilitaToGedi
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements FrielDisponibilitaInType FrielDisponibilitaIn;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        [Microsoft.XLANGs.BaseTypes.PhysicalBinding(typeof(A2A.EAI.INT_FRIEL.Messaging.Pipelines.Out.GediDisponibilita))]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses dynamic FrielDisponibilitaToGediMailOutType FrielDisponibilitaToGediMailOut;
        message FrielDisponibilitaType FrielDisponibilita;
        message GediDisponibilitaType GediDisponibilita;
        message NotificationType Notification;
        System.String attachFilePath;
        System.String attachFileName;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String originalFileName;
        System.String flowName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("cb3f2c82-9429-46f4-a813-29515970dc36")]
            activate receive (FrielDisponibilitaIn.Receive, FrielDisponibilita);
            attachFilePath = "";
            attachFileName = "";
            originalFileName = "";
            flowName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e30f5b9e-bf83-4440-8371-207e836a6394")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(FrielDisponibilita);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(FrielDisponibilita);
            
            flowName = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowDescriptionDisponibilita;
            attachFileName = System.String.Concat("meteologica_availability_", System.DateTimeOffset.Now.ToString("yyyyMMddHHmmss"), ".csv");
            attachFilePath = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue("A2A.EAI", "frielDisponibilitaMailAttachPath");
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("a603fa20-a3cc-44c2-bcd2-f175a6996e9f")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_FRIEL.Services.ProcessServices.ActivityName, activityInstanceId,
            "instanceId", activityInstanceId,
            "Data Inizio", dataInizio,
            "Nome File", System.IO.Path.GetFileName(originalFileName),
            "Percorso Backup", archiveFilePath,
            "Percorso Sorgente", System.IO.Path.GetDirectoryName(originalFileName),
            "Flusso", flowName
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("c0ff8cfa-3f24-41d9-bd9a-187f47a706ee")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("011f8e77-97c5-41e5-a4c2-87a4f3e46567")]
                    if (A2A.EAI.INT_FRIEL.Services.ProcessServices.CountXmlNode(FrielDisponibilita.parameters, "/*[local-name()='FrielDisponibilita' and namespace-uri()='http://A2A.EAI.INT_FRIEL.Messaging.Schemas.Disponibilita.FrielDisponibilita']/*[local-name()='Items' and namespace-uri()='']") == 0
                        )
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f0d8ec3a-3e6b-4607-8f85-bdfb6f327f0d")]
                        errorMessage.Append("Friel FILE has no rows. ");
                        
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("9a3d0d25-4ac6-4e53-8635-5e3158fd05a5")]
                        construct GediDisponibilita
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e3ffba7b-2e0b-438d-8eff-ec86c1649786")]
                            transform (GediDisponibilita.parameters) = A2A.EAI.INT_FRIEL.Messaging.Maps.Disponibilita.FrielDisponibilitaToGedi (FrielDisponibilita.parameters);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e944868-1445-40b8-b9b0-48da10971c1e")]
                        FrielDisponibilitaToGediMailOut(Microsoft.XLANGs.BaseTypes.Address) = System.IO.Path.Combine(attachFilePath, attachFileName);
                        FrielDisponibilitaToGediMailOut(Microsoft.XLANGs.BaseTypes.TransportType) = "FILE";
                        
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("ca09bf22-6ffa-4257-bfb6-2c7d43ee34bd")]
                        send (FrielDisponibilitaToGediMailOut.Send, GediDisponibilita);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("77f97a55-c5c6-40ae-b480-a08779820213")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9752c3d0-f3d8-4f70-be4f-4d4ffd933359")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_FRIEL.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = "meteologica_availability";
                            Notification.parameter.flowDescription = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowGroup;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Information.ToString();
                            Notification.parameter.messageText = "E' stata rilevata una Nuova indisponibilità per l' impianto FRIEL";
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleInformationNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                            Notification.parameter.attachment = System.IO.Path.Combine(attachFilePath, attachFileName);
                            Notification.parameter.emailAddress = Microsys.EAI.Framework.Azure.Services.ConfigurationHelper.GetValue("A2A.EAI", "frielDisponibilitaMailTo");
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b3d3f504-c1aa-430f-a21a-e67636ea6cc0")]
                        send (NotificationOut.Send, Notification);
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("b879c63a-de03-4a1c-987e-99ab88d6b5b9")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d722955c-596a-467d-869e-edbfc248f960")]
                        errorMessage.Append("Error sending data to Gedi. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(deliveryExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("7737cb8f-51dc-47e0-86d3-9cfd4acd3380")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f8157cd2-64f5-4efe-b531-c1f9ae269830")]
                        errorMessage.Append("Error sending data to Gedi. ");
                        errorMessage.Append(System.String.Format("File di input: '{0}' - File salvato in: '{1}'; ", originalFileName, archiveFilePath));
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0abb43f9-7b4d-4bed-a8c6-fba56916d27b")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("ae2add04-6dcd-4abc-93e2-fe963af0a79b")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("934dc2dc-b486-45f5-a0da-a8850d471714")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("74921fe8-ce77-4f43-8f1b-6ddeecbe1821")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_FRIEL.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_FRIEL.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = flowName;
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("00478d0b-4344-43ef-b070-48d21b37555f")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f93487e-6b4b-4fb6-8d67-1699630b594d")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_FRIEL.Services.ProcessServices.ActivityName, activityInstanceId,
            "errorMessage", errorMessage.ToString(),
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now));
        }
    }
}

