﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="b252f04c-a818-4bce-af7e-f6b014ca4b99" LowerBound="1.1" HigherBound="23.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_FRIEL.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="4621b285-4c7a-4753-9737-c361e61dda61" ParentLink="Module_PortType" LowerBound="8.1" HigherBound="15.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationOutType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="34a51734-5298-41ae-8020-01d27fae948e" ParentLink="PortType_OperationDeclaration" LowerBound="10.1" HigherBound="14.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="03a313f6-a1bc-4e59-94f0-a48856ec1a1d" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="12.13" HigherBound="12.29">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_FRIEL.Processes.NotificationType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="c4cbb049-c0f6-4005-935c-2844010205c3" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="NotificationType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="15637636-a57c-427e-b110-ab079f70c27f" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="Microsys.EAI.Framework.Schemas.Notification" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameter" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="fc48674a-bd60-4b8d-a267-72e06cbe5820" ParentLink="Module_ServiceDeclaration" LowerBound="15.1" HigherBound="22.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="ObjectDefinition" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="ServiceBody" OID="518df32d-d156-41a8-89fd-53dc45184cda" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_FRIEL.Processes
{
    internal messagetype NotificationType
    {
        body Microsys.EAI.Framework.Schemas.Notification parameter;
    };
    internal porttype NotificationOutType
    {
        oneway Send
        {
            NotificationType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service ObjectDefinition
    {
        body ()
        {
        }
    }
}

