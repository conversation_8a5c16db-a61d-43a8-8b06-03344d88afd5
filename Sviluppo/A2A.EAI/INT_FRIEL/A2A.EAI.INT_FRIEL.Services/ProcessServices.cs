﻿using Microsys.EAI.Framework.Azure.Services;
using System;
using System.Xml;

namespace A2A.EAI.INT_FRIEL.Services
{
    [Serializable]
    public class ProcessServices
    {
        /// <summary>NBDO Settlement Activity Name (for BAM purpose)</summary>
        public const string ActivityName = "Friel";

        /// <summary>Application Name</summary>
        public const string ApplicationName = "A2A.EAI";

        /// <summary>Flow Code</summary>
        public const string FlowGroup = "INT_FRIEL";

        public const string FlowDescriptionDisponibilita = "Friel Disponibilita To Gedi";

        /// <summary>
        /// Count XML Node
        /// </summary>
        /// <param name="doc"></param>
        /// <param name="nodeName"></param>
        /// <returns></returns>
        public static int CountXmlNode(XmlDocument doc, string nodeName)
        {
            int returnValue = 0;

            try
            {
                LoggingServices.TraceDebugInfo("IN");

                returnValue = doc.SelectNodes(nodeName).Count;
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }

        /// <summary>
        /// Convert Italian date without offset to UTC
        /// </summary>
        /// <param name="inputDate"></param>
        /// <returns></returns>
        public string ConvertDateFromItalianToUtc(string inputDate)
        {
            string returnValue = string.Empty;

            try
            {
                LoggingServices.TraceDebugInfo($"IN - inputDate: {inputDate}");

                DateTime italianDate = DateTime.ParseExact(inputDate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture);
                TimeZoneInfo italianTimeZone = TimeZoneInfo.FindSystemTimeZoneById("Central European Standard Time");
                DateTime utcDate = TimeZoneInfo.ConvertTimeToUtc(italianDate, italianTimeZone);
                
                returnValue = utcDate.ToString("yyyy-MM-dd HH:mm:ss");
            }
            catch (Exception exc)
            {
                LoggingServices.TraceError(exc);
                throw;
            }
            finally
            {
                LoggingServices.TraceDebugInfo("OUT");
            }

            return returnValue;
        }
    }
}
