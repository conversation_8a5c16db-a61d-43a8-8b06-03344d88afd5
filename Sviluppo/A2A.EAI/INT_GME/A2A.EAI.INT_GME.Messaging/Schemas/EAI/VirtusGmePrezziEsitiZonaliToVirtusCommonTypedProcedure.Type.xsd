<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="VirtusGmePrezziEsitiZonaliType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="tsDate" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="10" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
		<xs:element minOccurs="0" maxOccurs="1" name="MarketIndex" nillable="true">
			<xs:simpleType>
				<xs:restriction base="xs:string">
					<xs:maxLength value="100" />
				</xs:restriction>
			</xs:simpleType>
		</xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Zone" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="tsHour" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="tsPeriod" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="BuyPrice" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="SellPrice" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Generation" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="Consumption" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="VirtusGmePrezziEsitiZonaliType" nillable="true" type="ns3:VirtusGmePrezziEsitiZonaliType" />
  <xs:complexType name="ArrayOfVirtusGmePrezziEsitiZonaliType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="VirtusGmePrezziEsitiZonaliType" type="ns3:VirtusGmePrezziEsitiZonaliType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfVirtusGmePrezziEsitiZonaliType" nillable="true" type="ns3:ArrayOfVirtusGmePrezziEsitiZonaliType" />
</xs:schema>