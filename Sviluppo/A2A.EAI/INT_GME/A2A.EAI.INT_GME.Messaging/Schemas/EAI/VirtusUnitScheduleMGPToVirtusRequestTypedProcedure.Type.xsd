<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="VirtusReplyGenericType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="attr1" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="attr2" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="attr3" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="attr4" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="attr5" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="attr6" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="ts" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="tsDate" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="tsHour" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" maxOccurs="1" name="val" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="100" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:element name="VirtusReplyGenericType" nillable="true" type="ns3:VirtusReplyGenericType" />
  <xs:complexType name="ArrayOfVirtusReplyGenericType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="VirtusReplyGenericType" type="ns3:VirtusReplyGenericType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfVirtusReplyGenericType" nillable="true" type="ns3:ArrayOfVirtusReplyGenericType" />
</xs:schema>