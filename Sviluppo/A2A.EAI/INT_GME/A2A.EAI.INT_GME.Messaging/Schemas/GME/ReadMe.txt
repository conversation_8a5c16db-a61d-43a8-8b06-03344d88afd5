Aggiunto XBID e PVAFF allo schema SimpleTypesv1_0.xsd

<simpleType name="fifteenMinuteScheduleMarketType">
	<restriction base="string">
	  ...
	  <enumeration value="XBID1" />
	  <enumeration value="XBID2" />
	  <enumeration value="XBID3" />
	  <enumeration value="XBID4" />
	  <enumeration value="XBID5" />
	  <enumeration value="XBID6" />
	  <enumeration value="XBID7" />
	  <enumeration value="XBID8" />
	  <enumeration value="XBID9" />
	  <enumeration value="XBID10" />
	  <enumeration value="XBID11" />
	  <enumeration value="XBID12" />
	  <enumeration value="XBID13" />
	  <enumeration value="XBID14" />
	  <enumeration value="XBID15" />
	  <enumeration value="XBID16" />
	  <enumeration value="XBID17" />
	  <enumeration value="XBID18" />
	  <enumeration value="XBID19" />
	  <enumeration value="XBID20" />
	  <enumeration value="XBID21" />
	  <enumeration value="XBID22" />
	  <enumeration value="XBID23" />
	  <enumeration value="XBID24" />
	  <enumeration value="XBID25" />
	  <enumeration value="PVAFF" />
	</restriction>
</simpleType>

Aggiunto XB1-25 in MarketType
<simpleType name="marketType">
    <restriction base="string">
      ...
      <enumeration value="XB1" />
      <enumeration value="XB2" />
      <enumeration value="XB3" />
      <enumeration value="XB4" />
      <enumeration value="XB5" />
      <enumeration value="XB6" />
      <enumeration value="XB7" />
      <enumeration value="XB8" />
      <enumeration value="XB9" />
      <enumeration value="XB10" />
      <enumeration value="XB11" />
      <enumeration value="XB12" />
      <enumeration value="XB13" />
      <enumeration value="XB14" />
      <enumeration value="XB15" />
      <enumeration value="XB16" />
      <enumeration value="XB17" />
      <enumeration value="XB18" />
      <enumeration value="XB19" />
      <enumeration value="XB20" />
      <enumeration value="XB21" />
      <enumeration value="XB22" />
      <enumeration value="XB23" />
      <enumeration value="XB24" />
      <enumeration value="XB25" />
    </restriction>
  </simpleType>

  Aggiunto FinalXbid in unitScheduleType

  <simpleType name="unitScheduleType">
    <restriction base="string">
      ...
      <enumeration value="FinalXbid" />
    </restriction>
  </simpleType>

  Aggiunto AFRR in MarketType
  <simpleType name="marketType">
  <restriction base="string">
  ..
    <enumeration value="AFRR" />
   </restriction>
  </simpleType>

  Aggiunto Period in BidSubmittal
  Messo Hour opzionale (minoccurs = 0)

  Aggiunti SellOffers e BuyOffers in BidSubmittal
  <element minOccurs="0" maxOccurs="1" name="BuyOffers">
	<complexType>
		<sequence>
			<element maxOccurs="unbounded" name="Offer">
			<complexType>
				<attribute name="Price" type="pd:localeDecimal0" />
				<attribute name="Scope" type="pd:scopeType" />
			</complexType>
			</element>
		</sequence>
	</complexType>
  </element>
  <element minOccurs="0" maxOccurs="1" name="SellOffers">
	<complexType>
		<sequence>
			<element maxOccurs="unbounded" name="Offer">
			<complexType>
				<attribute name="Price" type="pd:localeDecimal0" />
				<attribute name="Scope" type="pd:scopeType" />
			</complexType>
			</element>
		</sequence>
	</complexType>
  </element>