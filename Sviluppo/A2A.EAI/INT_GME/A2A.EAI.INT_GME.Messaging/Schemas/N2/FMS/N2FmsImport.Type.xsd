<?xml version="1.0"?>
<xs:schema xmlns:tns="http://www.ibm.com/edipower/xml/sbil/Fms/ws" elementFormDefault="qualified" targetNamespace="http://www.ibm.com/edipower/xml/sbil/Fms/ws" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="dati" type="tns:FileXls" />
  <xs:complexType name="FileXls">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="CreationDate" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="righe" type="tns:ArrayOfRigaUpXls" />
      <xs:element minOccurs="0" maxOccurs="1" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Mittente" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfRigaUpXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RigaUpXls" nillable="true" type="tns:RigaUpXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RigaUpXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="CodiceUP" type="xs:string" />
      <xs:element minOccurs="1" maxOccurs="1" name="Data" type="xs:dateTime" />
      <xs:element minOccurs="0" maxOccurs="1" name="ore" type="tns:ArrayOfOraXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfOraXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="OraXls" nillable="true" type="tns:OraXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="OraXls">
    <xs:attribute name="Mercato" type="xs:string" />
    <xs:attribute name="ora" type="xs:int" use="required" />
    <xs:attribute name="q1" type="xs:decimal" use="required" />
    <xs:attribute name="q2" type="xs:decimal" use="required" />
    <xs:attribute name="q3" type="xs:decimal" use="required" />
    <xs:attribute name="q4" type="xs:decimal" use="required" />
  </xs:complexType>
</xs:schema>