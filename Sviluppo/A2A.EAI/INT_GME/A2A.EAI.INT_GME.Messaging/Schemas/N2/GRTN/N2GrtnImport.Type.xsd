<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Orchestrations.BDM_Import.Schema.ImportFileGrtnWS" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Orchestrations.BDM_Import.Schema.ImportFileGrtnWS" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="dati" type="tns:PIPEDocument" />
  <xs:complexType name="PIPEDocument">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="PIPTransaction" type="tns:PIPEDocumentPIPTransaction" />
    </xs:sequence>
    <xs:attribute name="ReferenceNumber" type="xs:string" />
    <xs:attribute name="CreationDate" type="xs:string" />
    <xs:attribute name="Version" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransaction">
    <xs:sequence>
      <xs:element minOccurs="1" maxOccurs="1" name="EstimatedDemandInformation" nillable="true" type="tns:PIPEDocumentPIPTransactionEstimatedDemandInformation" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MarketResult" nillable="true" type="tns:PIPEDocumentPIPTransactionMarketResult" />
      <xs:element minOccurs="1" maxOccurs="1" name="MustRunSchedule" nillable="true" type="tns:PIPEDocumentPIPTransactionMustRunSchedule" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UnitSchedule" nillable="true" type="tns:PIPEDocumentPIPTransactionUnitSchedule" />
      <xs:element minOccurs="1" maxOccurs="1" name="ZoneInformation" nillable="true" type="tns:PIPEDocumentPIPTransactionZoneInformation" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="FifteenMinuteSchedule" nillable="true" type="tns:PIPEDocumentPIPTransactionFifteenMinuteSchedule" />
      <xs:element minOccurs="1" maxOccurs="1" name="EstimatedPriceInformation" nillable="true" type="tns:PIPEDocumentPIPTransactionEstimatedPriceInformation" />
    </xs:sequence>
    <xs:attribute name="ReferenceNumber" type="xs:string" />
    <xs:attribute name="InboundMessageCreationDate" type="xs:string" />
    <xs:attribute name="InboundMessageCreationTime" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedDemandInformation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ZoneDetail" type="tns:PIPEDocumentPIPTransactionEstimatedDemandInformationZoneDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedDemandInformationZoneDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Zone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EstimatedDemand" nillable="true" type="tns:PIPEDocumentPIPTransactionEstimatedDemandInformationZoneDetailEstimatedDemand" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedDemandInformationZoneDetailEstimatedDemand">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="Hour" type="xs:string" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMarketResult">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MarketDetail" type="tns:PIPEDocumentPIPTransactionMarketResultMarketDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMarketResultMarketDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ZoneDetail" type="tns:PIPEDocumentPIPTransactionMarketResultMarketDetailZoneDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMarketResultMarketDetailZoneDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Zone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Interval" type="tns:PIPEDocumentPIPTransactionMarketResultMarketDetailZoneDetailInterval" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMarketResultMarketDetailZoneDetailInterval">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="BuyPrice" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="SellPrice" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Generation" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Consumption" type="xs:string" />
    </xs:sequence>
    <xs:attribute name="Hour" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMustRunSchedule">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MustRunDetail" type="tns:PIPEDocumentPIPTransactionMustRunScheduleMustRunDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMustRunScheduleMustRunDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ZoneDetail" type="tns:PIPEDocumentPIPTransactionMustRunScheduleMustRunDetailZoneDetail" />
    </xs:sequence>
    <xs:attribute name="MustRun" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMustRunScheduleMustRunDetailZoneDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Zone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Hour" nillable="true" type="tns:PIPEDocumentPIPTransactionMustRunScheduleMustRunDetailZoneDetailHour" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionMustRunScheduleMustRunDetailZoneDetailHour">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="Quantity" type="xs:string" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionUnitSchedule">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="UnitReferenceNumber" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ReferenceMarketParticipantNumber" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Quantity" nillable="true" type="tns:PIPEDocumentPIPTransactionUnitScheduleQuantity" />
    </xs:sequence>
    <xs:attribute name="Type" type="xs:string" />
    <xs:attribute name="Cummulative" type="xs:string" />
    <xs:attribute name="MarketParticipantNumber" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionUnitScheduleQuantity">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="Hour" type="xs:string" />
        <xs:attribute name="UnitOfMeasure" type="xs:string" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionZoneInformation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="MarketDetail" type="tns:PIPEDocumentPIPTransactionZoneInformationMarketDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionZoneInformationMarketDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="InterZonalDetail" type="tns:PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="FromZone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ToZone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="InterZonalLimits" type="tns:PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetailInterZonalLimits" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="InterZonalLimitsPeriod" type="tns:PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetailInterZonalLimitsPeriod" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetailInterZonalLimits">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Hour" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ConnectionLimit" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ConnectionCoefficient" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionZoneInformationMarketDetailInterZonalDetailInterZonalLimitsPeriod">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Period" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ConnectionLimit" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="ConnectionCoefficient" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionFifteenMinuteSchedule">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="UnitReferenceNumber" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="HourDetail" type="tns:PIPEDocumentPIPTransactionFifteenMinuteScheduleHourDetail" />
    </xs:sequence>
    <xs:attribute name="Type" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionFifteenMinuteScheduleHourDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Hour" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Quantity" nillable="true" type="tns:PIPEDocumentPIPTransactionFifteenMinuteScheduleHourDetailQuantity" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionFifteenMinuteScheduleHourDetailQuantity">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="QuarterInterval" type="xs:string" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedPriceInformation">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="ZoneDetail" type="tns:PIPEDocumentPIPTransactionEstimatedPriceInformationZoneDetail" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedPriceInformationZoneDetail">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="Zone" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" name="EstimatedPrice" nillable="true" type="tns:PIPEDocumentPIPTransactionEstimatedPriceInformationZoneDetailEstimatedPrice" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PIPEDocumentPIPTransactionEstimatedPriceInformationZoneDetailEstimatedPrice">
    <xs:simpleContent>
      <xs:extension base="xs:string">
        <xs:attribute name="Hour" type="xs:string" />
      </xs:extension>
    </xs:simpleContent>
  </xs:complexType>
</xs:schema>