<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:ns3="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/Sql/2008/05/Types/TableTypes/dbo" version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:annotation>
    <xs:appinfo>
      <fileNameHint xmlns="http://schemas.microsoft.com/servicemodel/adapters/metadata/xsd">TableType.dbo</fileNameHint>
    </xs:appinfo>
  </xs:annotation>
  <xs:complexType name="UnitaProduttiveAbilitazioneType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="UnitReferenceNumber" nillable="true">
        <xs:simpleType>
          <xs:restriction base="xs:string">
            <xs:maxLength value="20" />
          </xs:restriction>
        </xs:simpleType>
      </xs:element>
      <xs:element minOccurs="0" maxOccurs="1" name="referenceDate" nillable="true" type="xs:dateTime" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="UnitaProduttiveAbilitazioneType" nillable="true" type="ns3:UnitaProduttiveAbilitazioneType" />
  <xs:complexType name="ArrayOfUnitaProduttiveAbilitazioneType">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="UnitaProduttiveAbilitazioneType" type="ns3:UnitaProduttiveAbilitazioneType" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfUnitaProduttiveAbilitazioneType" nillable="true" type="ns3:ArrayOfUnitaProduttiveAbilitazioneType" />
</xs:schema>