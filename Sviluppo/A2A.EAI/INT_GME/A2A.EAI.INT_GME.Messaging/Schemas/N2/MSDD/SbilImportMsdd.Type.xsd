<?xml version="1.0"?>
<xs:schema xmlns:tns="http://www.ibm.com/edipower/xml/sbil/Msdd/ws" elementFormDefault="qualified" targetNamespace="http://www.ibm.com/edipower/xml/sbil/Msdd/ws" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="dati" type="tns:FileXls" />
  <xs:complexType name="FileXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="righe" type="tns:ArrayOfRigaUpXls" />
      <xs:element minOccurs="0" maxOccurs="1" name="NomeFile" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ArrayOfRigaUpXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RigaUpXls" nillable="true" type="tns:RigaUpXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="RigaUpXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" name="ore" type="tns:ArrayOfOraXls" />
      <xs:element minOccurs="0" maxOccurs="1" name="Periodi" type="tns:ArrayOfPeriodoXls" />
    </xs:sequence>
    <xs:attribute name="CodiceUP" type="xs:string" />
    <xs:attribute name="DataOra" type="xs:dateTime" use="required" />
    <xs:attribute name="Mercato" type="xs:string" />
    <xs:attribute name="Type" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ArrayOfOraXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="OraXls" nillable="true" type="tns:OraXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="OraXls">
    <xs:attribute name="ora" type="xs:int" use="required" />
    <xs:attribute name="Quantita" type="xs:decimal" use="required" />
    <xs:attribute name="UnitaMisura" type="xs:string" />
  </xs:complexType>
  <xs:complexType name="ArrayOfPeriodoXls">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="PeriodoXls" nillable="true" type="tns:PeriodoXls" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="PeriodoXls">
    <xs:attribute name="Period" type="xs:int" use="required" />
    <xs:attribute name="TimeResolution" type="xs:string" />
    <xs:attribute name="Quantita" type="xs:decimal" use="required" />
    <xs:attribute name="UnitaMisura" type="xs:string" />
  </xs:complexType>
</xs:schema>