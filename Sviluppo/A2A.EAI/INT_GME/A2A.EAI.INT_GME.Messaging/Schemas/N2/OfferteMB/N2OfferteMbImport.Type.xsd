<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Orchestrations.BDM_ImportF6.Schema.ImportF6OfferteMB" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Orchestrations.BDM_ImportF6.Schema.ImportF6OfferteMB" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="importOfferteMB" type="tns:ImportF6OfferteMB" />
  <xs:complexType name="ImportF6OfferteMB">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Lettura" type="tns:DettaglioImportF6OfferteMB" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="LetturaTimeResolution" type="tns:DettaglioImportF6OfferteMBTimeResolution" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioImportF6OfferteMB">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Hour" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="DettaglioHourOfferteMB" type="tns:DettaglioHourOfferteMB" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioHourOfferteMB">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="UnitReferenceNumber" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="GMEReferenceNumber" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="DettaglioGMEReferenceNumberOfferteMB" type="tns:DettaglioGMEReferenceNumberOfferteMB" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioGMEReferenceNumberOfferteMB">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="TipoServizio" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Step" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Scope" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="VerifiedSourceOffer" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="ValidatedQuantity" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="ValidatedPrice" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioImportF6OfferteMBTimeResolution">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Market" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Period" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="TimeResolution" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="DettaglioHourOfferteMB" type="tns:DettaglioHourOfferteMB" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>