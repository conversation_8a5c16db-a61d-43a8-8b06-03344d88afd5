<?xml version="1.0"?>
<xs:schema xmlns:tns="http://NBDOBiztalk.Orchestrations.BDM_ImportF6.Schema.ImportF6PrezziZonaliMRR" elementFormDefault="qualified" targetNamespace="http://NBDOBiztalk.Orchestrations.BDM_ImportF6.Schema.ImportF6PrezziZonaliMRR" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="importPrezziZonaliMRR" type="tns:ImportF6PrezziZonaliMRR" />
  <xs:complexType name="ImportF6PrezziZonaliMRR">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="NomeFile" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="Lettura" type="tns:DettaglioF6PrezziZonaliMRR" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioF6PrezziZonaliMRR">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Mercato" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Date" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Hour" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Zona" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="unbounded" form="unqualified" name="DettaglioZona" type="tns:DettaglioZona" />
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="DettaglioZona">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="DateHour" type="xs:string" />
      <xs:element minOccurs="0" maxOccurs="1" form="unqualified" name="Amount" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
</xs:schema>