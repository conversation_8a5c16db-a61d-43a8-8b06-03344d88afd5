﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="8cad8b7c-a0b2-471e-8087-02f6892a42d6" LowerBound="1.1" HigherBound="279.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_GME.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="19ca891c-6b2e-4e0d-ab23-825947bb00c4" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbInType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="OperationDeclaration" OID="4bd49914-ff1e-449b-8b35-ba460ad60aff" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="e4d71ed4-7f89-4aa6-abd9-72f8aada3c61" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="8e098887-2e36-412d-a499-9fc5bec699a2" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EaiVirtusReplyToIpexOfferteMbOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="cbab2e48-7187-40a6-b5ed-2e7d9ab1c415" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMb" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="f623daf6-f659-4bb5-9633-14e67c28b3f1" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.50">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="2c0a8747-3ce4-4b57-836e-907aadd24ca9" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.52" HigherBound="27.90">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="3bb46b5a-4262-42c3-b591-9f0c2f8f7ff1" ParentLink="Module_PortType" LowerBound="30.1" HigherBound="37.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="IpexOfferteMbOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="0435041e-47c7-442c-87d1-835bfafd3cc7" ParentLink="PortType_OperationDeclaration" LowerBound="32.1" HigherBound="36.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="6932083f-4883-496f-9c87-8f2c4d39101c" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="34.13" HigherBound="34.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.GmePipeDocumentType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="a5ded80e-2a2a-46be-97a9-f14ed20fa2b4" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="41c2fae5-54e5-4d37-a2f7-3c837a4a9c16" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMbTypedProcedure.VirtusReplyToIpexOfferteMb" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="bb8c68d5-51e9-459b-9e90-9f788f263216" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3eb5c499-4a40-41b7-9f29-cbb96d2b32ac" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMbTypedProcedure.VirtusReplyToIpexOfferteMbResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="04c3babd-02db-40a6-80e2-1532cb424d6a" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbEnvelopeType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="dfe3a1a4-ba16-485d-a7b3-b8bfe0b2f332" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.Virtus.VirtusReplyToIpexOfferteMbEnvelope" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="04e569cf-bc12-4e22-be69-6b93753f6f62" ParentLink="Module_ServiceDeclaration" LowerBound="37.1" HigherBound="278.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusToIpexOfferteMb" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="44eb354f-8ab3-4a83-9aa2-cc37e5692823" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="warningMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6c8943b9-bd3e-4992-bc64-ff294d9d6cca" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="0a77b5da-272a-4a7b-9aea-04e742d003a6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="8dc2d5cc-b88d-4347-baa0-4e1dc66a32a4" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="XmlEnvelopeMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="11c8a3cb-d17a-4e74-bd3d-c48161c52dca" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ed39ef91-6712-4a89-8b8d-31c81d8f7c2a" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="outputFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f9a9402e-38ff-45ba-aebd-ce83c33625da" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="4be9e08d-91ef-4e92-840a-d32f0d88cd1c" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="11fe9181-c986-4ee8-a3ab-ee447ff89a28" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ca4b03fa-922c-4146-a4bb-9ac6cc3902fd" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="7d28dabd-c5df-4f56-b413-f297aa9c4269" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="cb2c251b-779a-40a4-a0ea-aa91257fb844" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="53960746-5624-4df0-84e1-90694578138b" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b27586a2-d31b-4638-915d-e1e272b65306" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="2b2443dc-9f4c-4434-aac1-397cf7777722" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReply" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="b4042bb2-c0ba-4383-b914-56042a089d05" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="add1dc6e-e56b-4941-822e-d21fe505b945" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.GmePipeDocumentType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GmePipeDocument" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="8762acd7-c15d-41de-b0b9-3fcebf9f918d" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6c07e1f5-a00f-444d-b40f-269426f11dd0" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="4751a0c4-a99a-4c00-b2e1-ac605c7008cd" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbEnvelopeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbEnvelope" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="47411015-a714-4400-bf10-d2e047dc5216" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="fa572536-75bf-4a1f-86ee-062e7090f768" ParentLink="ServiceBody_Statement" LowerBound="72.1" HigherBound="84.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="VirtusReplyToIpexOfferteMbIn" />
                    <om:Property Name="MessageName" Value="VirtusReply" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="5f6efffe-0dee-4155-ac86-30b23f1db580" ParentLink="ServiceBody_Statement" LowerBound="84.1" HigherBound="101.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;flowName = &quot;IPEX_OFFERTE_MSD&quot;;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VirtusReply);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VirtusReply);&#xD;&#xA;activityId = A2A.EAI.Common.Services.VirtusServices.GetRelatedActivityByFileName(originalFileName, activityInstanceId);&#xD;&#xA;&#xD;&#xA;outputFileName = System.String.Empty;&#xD;&#xA;sendNotification = false;&#xD;&#xA;warningMessage = System.String.Empty;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Decision" OID="d20df764-cb54-4882-b755-990f7a302dc0" ParentLink="ServiceBody_Statement" LowerBound="101.1" HigherBound="107.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="IPEX_OFFERTE_MB or IPEX_OFFERTE_MB_SF" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="4263a118-8f50-46d0-aa76-9b32241e9ded" ParentLink="ReallyComplexStatement_Branch" LowerBound="102.13" HigherBound="107.1">
                        <om:Property Name="Expression" Value="originalFileName.Contains(&quot;_SF_&quot;)" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="IPEX_OFFERTE_MSD_SF" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="1e241bd3-9edd-4b79-bd87-0a478eaa5442" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                            <om:Property Name="Expression" Value="flowName = &quot;IPEX_OFFERTE_MB_SF&quot;;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Set FlowName" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="50dc2906-ae08-4e2c-9ca5-aa346eecfa0f" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="89c4885e-09f1-439d-950f-5f453b348921" ParentLink="ServiceBody_Statement" LowerBound="107.1" HigherBound="119.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, flowName,&#xD;&#xA;&quot;Nome File Input&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Percorso Backup&quot;, archiveFilePath,&#xD;&#xA;&quot;Percorso Sorgente&quot;, System.IO.Path.GetDirectoryName(originalFileName),&#xD;&#xA;&quot;Inviato&quot;, &quot;No&quot;&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="0ddd3957-3a6b-4cde-a10e-5f2f535b2f7d" ParentLink="ServiceBody_Statement" LowerBound="119.1" HigherBound="232.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="bf70d638-1f7f-4ce5-a5d9-04f900345c3b" ParentLink="ComplexStatement_Statement" LowerBound="124.1" HigherBound="132.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup EAI Tranform" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="58ef277c-3846-4004-86e4-25edd3179419" ParentLink="ComplexStatement_Statement" LowerBound="127.1" HigherBound="129.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMb.VirtusReplyToEaiIpexOfferteMb" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup EAI" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="MessagePartRef" OID="8d799ba9-4612-40b2-8e90-bd70ed392e41" ParentLink="Transform_OutputMessagePartRef" LowerBound="128.36" HigherBound="128.80">
                                <om:Property Name="MessageRef" Value="VirtusReplyToIpexOfferteMbRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_2" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="ae85dbae-f580-4593-a98c-372f636ac72e" ParentLink="Transform_InputMessagePartRef" LowerBound="128.168" HigherBound="128.190">
                                <om:Property Name="MessageRef" Value="VirtusReply" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_1" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageAssignment" OID="899401bc-1bb5-46fe-9976-f6961ddace02" ParentLink="ComplexStatement_Statement" LowerBound="129.1" HigherBound="131.1">
                            <om:Property Name="Expression" Value="VirtusReplyToIpexOfferteMbRequest.parameters.receiver = flowName;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="Set Params" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="83ce48d0-0f1c-41d3-9500-01672d310e54" ParentLink="Construct_MessageRef" LowerBound="125.31" HigherBound="125.64">
                            <om:Property Name="Ref" Value="VirtusReplyToIpexOfferteMbRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="5553c294-ecca-49d2-a02d-1c498ade5eb7" ParentLink="ComplexStatement_Statement" LowerBound="132.1" HigherBound="134.1">
                        <om:Property Name="PortName" Value="EaiVirtusReplyToIpexOfferteMbOut" />
                        <om:Property Name="MessageName" Value="VirtusReplyToIpexOfferteMbRequest" />
                        <om:Property Name="OperationName" Value="VirtusReplyToIpexOfferteMb" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="faf401ca-b8be-4a16-bc40-9fc440196e49" ParentLink="ComplexStatement_Statement" LowerBound="134.1" HigherBound="136.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EaiVirtusReplyToIpexOfferteMbOut" />
                        <om:Property Name="MessageName" Value="VirtusReplyToIpexOfferteMbResponse" />
                        <om:Property Name="OperationName" Value="VirtusReplyToIpexOfferteMb" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="29af7f3d-7712-40dc-ae13-74e06bd8d8f7" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="138.1">
                        <om:Property Name="Expression" Value="XmlEnvelopeMessage.LoadXml(VirtusReplyToIpexOfferteMbResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.Envelope);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Get XmlEnvelopeMessage" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Construct" OID="91a5d224-a933-4733-932c-a5c80b810647" ParentLink="ComplexStatement_Statement" LowerBound="138.1" HigherBound="144.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Build Envelope" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageAssignment" OID="351f286a-5646-4f34-8831-d17e3207ecf6" ParentLink="ComplexStatement_Statement" LowerBound="141.1" HigherBound="143.1">
                            <om:Property Name="Expression" Value="VirtusReplyToIpexOfferteMbEnvelope.parameters = XmlEnvelopeMessage;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbEnvelope" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="ad7a9167-d8fc-445f-8e1e-bbe458d6502f" ParentLink="Construct_MessageRef" LowerBound="139.31" HigherBound="139.65">
                            <om:Property Name="Ref" Value="VirtusReplyToIpexOfferteMbEnvelope" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="5d4dfa99-cb1e-4b13-98f1-1f79f8315ed2" ParentLink="ComplexStatement_Statement" LowerBound="144.1" HigherBound="147.1">
                        <om:Property Name="Expression" Value="outputFileName = VirtusReplyToIpexOfferteMbEnvelope.parameters.fileName;&#xD;&#xA;warningMessage = VirtusReplyToIpexOfferteMbEnvelope.parameters.warningMessage;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Set fileName &amp; warnMessage" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="Decision" OID="a4fd4771-8bd8-483e-ba29-bd88c676964a" ParentLink="ComplexStatement_Statement" LowerBound="147.1" HigherBound="162.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="warningMessage reported" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="70b75d3b-9207-4ba7-9c90-2e69a1e69c48" ParentLink="ReallyComplexStatement_Branch" LowerBound="148.21" HigherBound="155.1">
                            <om:Property Name="Expression" Value="warningMessage.StartsWith(&quot;ERROR:&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Blocking" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="b06b8456-b9b7-4750-85bc-7fa53c70626c" ParentLink="ComplexStatement_Statement" LowerBound="150.1" HigherBound="154.1">
                                <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;errorMessage.Append(warningMessage);&#xD;&#xA;sendNotification = true;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Manage Errors" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="17e4240d-0b2c-45aa-a736-5ec5bb04627a" ParentLink="ReallyComplexStatement_Branch" LowerBound="155.26" HigherBound="162.1">
                            <om:Property Name="Expression" Value="warningMessage.StartsWith(&quot;WARNING:&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Warnings" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="bf082dd1-ff6a-4976-a443-3272179bc742" ParentLink="ComplexStatement_Statement" LowerBound="157.1" HigherBound="161.1">
                                <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;errorMessage.Append(warningMessage);&#xD;&#xA;sendNotification = false;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Manage Errors" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="4d8cbd6f-50d4-4ce1-a6b1-10edf531ad89" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="9bbfb931-9471-4b1a-8f0b-291749a32560" ParentLink="ComplexStatement_Statement" LowerBound="162.1" HigherBound="188.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Offerte Presenti" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="928713c9-4a39-41ce-89e2-7bd49a06f29e" ParentLink="ReallyComplexStatement_Branch" LowerBound="163.21" HigherBound="181.1">
                            <om:Property Name="Expression" Value="VirtusReplyToIpexOfferteMbEnvelope.parameters.numOfMessages &gt; 0&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="aee5ebe1-5c91-4a77-9d86-c8c66bf269b0" ParentLink="ComplexStatement_Statement" LowerBound="165.1" HigherBound="173.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup BidSubmittal" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="eb20a0bc-893d-4212-9e2a-d0af327ae659" ParentLink="Construct_MessageRef" LowerBound="166.35" HigherBound="166.50">
                                    <om:Property Name="Ref" Value="GmePipeDocument" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="7f93486c-1ac0-4cf3-a1cb-d28b9aa46262" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="170.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMb.VirtusReplyToIpexOfferteMbEnvelopeToGmeBidSubmittal" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup MSG" />
                                    <om:Property Name="Signal" Value="True" />
                                    <om:Element Type="MessagePartRef" OID="b753b40d-fc49-4d71-af2b-64175b199dc1" ParentLink="Transform_InputMessagePartRef" LowerBound="169.175" HigherBound="169.220">
                                        <om:Property Name="MessageRef" Value="VirtusReplyToIpexOfferteMbEnvelope" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_3" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="2cd327ed-4986-4c55-938d-dc2d97f22621" ParentLink="Transform_OutputMessagePartRef" LowerBound="169.40" HigherBound="169.65">
                                        <om:Property Name="MessageRef" Value="GmePipeDocument" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_4" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="78523c81-5722-43c4-9659-09cc41e0c35c" ParentLink="ComplexStatement_Statement" LowerBound="170.1" HigherBound="172.1">
                                    <om:Property Name="Expression" Value="GmePipeDocument(FILE.ReceivedFileName) = outputFileName;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="File Name" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="452d9f05-fb8d-4278-a5b4-6d5a62b4f0f4" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="175.1">
                                <om:Property Name="PortName" Value="IpexOfferteMbOut" />
                                <om:Property Name="MessageName" Value="GmePipeDocument" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="8c6be15a-b7fb-4576-a4da-b627098010c6" ParentLink="ComplexStatement_Statement" LowerBound="175.1" HigherBound="180.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Inviato&quot;, &quot;Si&quot;,&#xD;&#xA;&quot;Nome File Output&quot;, outputFileName&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="e11c846b-af13-400e-a600-099b134f092f" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="fe201283-c600-4072-8ede-5d641811d7e1" ParentLink="ComplexStatement_Statement" LowerBound="183.1" HigherBound="187.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Inviato&quot;, &quot;No (Empty)&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="692b5f5a-c907-468b-a5b6-f911c01e9c09" ParentLink="Scope_Catch" LowerBound="191.1" HigherBound="204.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="f2465144-bf99-4b65-9df3-35d9ae0193ff" ParentLink="Catch_Statement" LowerBound="194.1" HigherBound="203.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;sendNotification = true;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="fd49ded0-bd29-4e46-8cf2-0e7f369ee0f3" ParentLink="Scope_Catch" LowerBound="204.1" HigherBound="217.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="250a9c38-8289-4d4d-ba62-8e7e8970262d" ParentLink="Catch_Statement" LowerBound="207.1" HigherBound="216.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;errorMessage.Append(deliveryExc.ErrorDescription);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;sendNotification = true;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="0e6b04f7-97b7-4e5e-8237-ac7315ff0f11" ParentLink="Scope_Catch" LowerBound="217.1" HigherBound="230.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="c4e193b8-84a4-480b-95a8-dc46ab493ba4" ParentLink="Catch_Statement" LowerBound="220.1" HigherBound="229.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;sendNotification = true;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="d7d0534f-9136-4591-a612-a3643447c291" ParentLink="ServiceBody_Statement" LowerBound="232.1" HigherBound="268.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="926b38aa-1c4e-4ab3-837e-1a28c6bd0ae9" ParentLink="ReallyComplexStatement_Branch" LowerBound="233.13" HigherBound="236.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="25b5ecb3-cde7-4840-92c2-e59593a9b521" ParentLink="ReallyComplexStatement_Branch" LowerBound="236.18" HigherBound="239.1">
                        <om:Property Name="Expression" Value="sendNotification == false" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Warnings" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="341da4b0-de28-46bd-bd03-4d0b2a612e87" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="9ead1ccb-295a-40e5-a9a5-5dde30acc7e2" ParentLink="ComplexStatement_Statement" LowerBound="241.1" HigherBound="267.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="5f3488c8-e45c-4033-a9cf-5930be86d002" ParentLink="ComplexStatement_Statement" LowerBound="246.1" HigherBound="263.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="e7061788-edae-4f0b-b375-b14f939fc36a" ParentLink="ComplexStatement_Statement" LowerBound="249.1" HigherBound="262.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_GME.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_GME.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, &quot; - &quot;, flowName);&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="5d26fdc9-da58-4a72-bb5a-e9516517bbbf" ParentLink="Construct_MessageRef" LowerBound="247.35" HigherBound="247.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="c2323cff-9be0-4eac-892c-fd374e9a9b32" ParentLink="ComplexStatement_Statement" LowerBound="263.1" HigherBound="265.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="f5375571-be4a-47fc-99af-c164705a5e11" ParentLink="ServiceBody_Statement" LowerBound="268.1" HigherBound="276.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="e8d67ae6-b4ff-4ea5-8887-5f4a5004e6c1" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="40.1" HigherBound="42.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMbInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMbIn" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="682f1314-92b2-4c9d-a511-9f2a96fbb58c" ParentLink="PortDeclaration_CLRAttribute" LowerBound="40.1" HigherBound="41.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9545f44d-f149-4451-a9f6-3147cfd147e7" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="42.1" HigherBound="45.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="55" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.EaiVirtusReplyToIpexOfferteMbOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EaiVirtusReplyToIpexOfferteMbOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="e2995fc0-18ba-4cee-abca-34da341c1b16" ParentLink="PortDeclaration_CLRAttribute" LowerBound="42.1" HigherBound="43.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="02cacddc-7895-4bc9-bc43-de84cc733bf9" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="45.1" HigherBound="48.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="122" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.IpexOfferteMbOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="IpexOfferteMbOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="83b9d5ca-5785-49f5-bb39-0f628df1a090" ParentLink="PortDeclaration_CLRAttribute" LowerBound="45.1" HigherBound="46.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="144df560-1f5f-459e-a11d-49da082e2612" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="199" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="1b10646a-eae1-4567-934e-a864ec944900" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_GME.Processes
{
    internal messagetype VirtusReplyToIpexOfferteMbRequestType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMbTypedProcedure.VirtusReplyToIpexOfferteMb parameters;
    };
    internal messagetype VirtusReplyToIpexOfferteMbResponseType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMbTypedProcedure.VirtusReplyToIpexOfferteMbResponse parameters;
    };
    internal messagetype VirtusReplyToIpexOfferteMbEnvelopeType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.Virtus.VirtusReplyToIpexOfferteMbEnvelope parameters;
    };
    internal porttype VirtusReplyToIpexOfferteMbInType
    {
        oneway Receive
        {
            VirtusReplyType
        };
    };
    internal porttype EaiVirtusReplyToIpexOfferteMbOutType
    {
        requestresponse VirtusReplyToIpexOfferteMb
        {
            VirtusReplyToIpexOfferteMbRequestType, VirtusReplyToIpexOfferteMbResponseType
        };
    };
    internal porttype IpexOfferteMbOutType
    {
        oneway Send
        {
            GmePipeDocumentType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service VirtusToIpexOfferteMb
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements VirtusReplyToIpexOfferteMbInType VirtusReplyToIpexOfferteMbIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EaiVirtusReplyToIpexOfferteMbOutType EaiVirtusReplyToIpexOfferteMbOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses IpexOfferteMbOutType IpexOfferteMbOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message VirtusReplyType VirtusReply;
        message NotificationType Notification;
        message GmePipeDocumentType GmePipeDocument;
        message VirtusReplyToIpexOfferteMbRequestType VirtusReplyToIpexOfferteMbRequest;
        message VirtusReplyToIpexOfferteMbResponseType VirtusReplyToIpexOfferteMbResponse;
        message VirtusReplyToIpexOfferteMbEnvelopeType VirtusReplyToIpexOfferteMbEnvelope;
        System.String warningMessage;
        System.Boolean sendNotification;
        System.String archiveFilePath;
        System.Xml.XmlDocument XmlEnvelopeMessage;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String outputFileName;
        System.String originalFileName;
        System.String flowName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String activityInstanceId;
        System.String activityId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("fa572536-75bf-4a1f-86ee-062e7090f768")]
            activate receive (VirtusReplyToIpexOfferteMbIn.Receive, VirtusReply);
            warningMessage = "";
            sendNotification = true;
            archiveFilePath = "";
            XmlEnvelopeMessage = new System.Xml.XmlDocument();
            outputFileName = "";
            originalFileName = "";
            flowName = "";
            errorMessage = new System.Text.StringBuilder();
            activityInstanceId = "";
            activityId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f6efffe-0dee-4155-ac86-30b23f1db580")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            flowName = "IPEX_OFFERTE_MSD";
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VirtusReply);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VirtusReply);
            activityId = A2A.EAI.Common.Services.VirtusServices.GetRelatedActivityByFileName(originalFileName, activityInstanceId);
            
            outputFileName = System.String.Empty;
            sendNotification = false;
            warningMessage = System.String.Empty;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d20df764-cb54-4882-b755-990f7a302dc0")]
            if (originalFileName.Contains("_SF_"))
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("1e241bd3-9edd-4b79-bd87-0a478eaa5442")]
                flowName = "IPEX_OFFERTE_MB_SF";
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("89c4885e-09f1-439d-950f-5f453b348921")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId,
            "Flusso", flowName,
            "Nome File Input", System.IO.Path.GetFileName(originalFileName),
            "Percorso Backup", archiveFilePath,
            "Percorso Sorgente", System.IO.Path.GetDirectoryName(originalFileName),
            "Inviato", "No"
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ddd3957-3a6b-4cde-a10e-5f2f535b2f7d")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("bf70d638-1f7f-4ce5-a5d9-04f900345c3b")]
                    construct VirtusReplyToIpexOfferteMbRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("58ef277c-3846-4004-86e4-25edd3179419")]
                        transform (VirtusReplyToIpexOfferteMbRequest.parameters) = A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMb.VirtusReplyToEaiIpexOfferteMb (VirtusReply.parameters);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("899401bc-1bb5-46fe-9976-f6961ddace02")]
                        VirtusReplyToIpexOfferteMbRequest.parameters.receiver = flowName;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5553c294-ecca-49d2-a02d-1c498ade5eb7")]
                    send (EaiVirtusReplyToIpexOfferteMbOut.VirtusReplyToIpexOfferteMb, VirtusReplyToIpexOfferteMbRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("faf401ca-b8be-4a16-bc40-9fc440196e49")]
                    receive (EaiVirtusReplyToIpexOfferteMbOut.VirtusReplyToIpexOfferteMb, VirtusReplyToIpexOfferteMbResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("29af7f3d-7712-40dc-ae13-74e06bd8d8f7")]
                    XmlEnvelopeMessage.LoadXml(VirtusReplyToIpexOfferteMbResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.Envelope);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("91a5d224-a933-4733-932c-a5c80b810647")]
                    construct VirtusReplyToIpexOfferteMbEnvelope
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("351f286a-5646-4f34-8831-d17e3207ecf6")]
                        VirtusReplyToIpexOfferteMbEnvelope.parameters = XmlEnvelopeMessage;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("5d4dfa99-cb1e-4b13-98f1-1f79f8315ed2")]
                    outputFileName = VirtusReplyToIpexOfferteMbEnvelope.parameters.fileName;
                    warningMessage = VirtusReplyToIpexOfferteMbEnvelope.parameters.warningMessage;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("a4fd4771-8bd8-483e-ba29-bd88c676964a")]
                    if (warningMessage.StartsWith("ERROR:"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b06b8456-b9b7-4750-85bc-7fa53c70626c")]
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        errorMessage.Append(warningMessage);
                        sendNotification = true;
                    }
                    else if (warningMessage.StartsWith("WARNING:"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("bf082dd1-ff6a-4976-a443-3272179bc742")]
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        errorMessage.Append(warningMessage);
                        sendNotification = false;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("9bbfb931-9471-4b1a-8f0b-291749a32560")]
                    if (VirtusReplyToIpexOfferteMbEnvelope.parameters.numOfMessages > 0)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("aee5ebe1-5c91-4a77-9d86-c8c66bf269b0")]
                        construct GmePipeDocument
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("7f93486c-1ac0-4cf3-a1cb-d28b9aa46262")]
                            transform (GmePipeDocument.parameter) = A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMb.VirtusReplyToIpexOfferteMbEnvelopeToGmeBidSubmittal (VirtusReplyToIpexOfferteMbEnvelope.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("78523c81-5722-43c4-9659-09cc41e0c35c")]
                            GmePipeDocument(FILE.ReceivedFileName) = outputFileName;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("452d9f05-fb8d-4278-a5b4-6d5a62b4f0f4")]
                        send (IpexOfferteMbOut.Send, GmePipeDocument);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8c6be15a-b7fb-4576-a4da-b627098010c6")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
                        "Inviato", "Si",
                        "Nome File Output", outputFileName
                        );
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("fe201283-c600-4072-8ede-5d641811d7e1")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
                        "Inviato", "No (Empty)"
                        );
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("692b5f5a-c907-468b-a5b6-f911c01e9c09")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f2465144-bf99-4b65-9df3-35d9ae0193ff")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        sendNotification = true;
                        
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("fd49ded0-bd29-4e46-8cf2-0e7f369ee0f3")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("250a9c38-8289-4d4d-ba62-8e7e8970262d")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(deliveryExc.Message);
                        errorMessage.Append(deliveryExc.ErrorDescription);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        sendNotification = true;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0e6b04f7-97b7-4e5e-8237-ac7315ff0f11")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c4e193b8-84a4-480b-95a8-dc46ab493ba4")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        sendNotification = true;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("d7d0534f-9136-4591-a612-a3643447c291")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (sendNotification == false)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("9ead1ccb-295a-40e5-a9a5-5dde30acc7e2")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("5f3488c8-e45c-4033-a9cf-5930be86d002")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e7061788-edae-4f0b-b375-b14f939fc36a")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_GME.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_GME.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, " - ", flowName);
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c2323cff-9be0-4eac-892c-fd374e9a9b32")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5375571-be4a-47fc-99af-c164705a5e11")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

