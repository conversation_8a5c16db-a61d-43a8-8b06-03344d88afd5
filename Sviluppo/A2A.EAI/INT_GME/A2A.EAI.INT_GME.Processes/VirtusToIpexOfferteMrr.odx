﻿#if __DESIGNER_DATA
#error Do not define __DESIGNER_DATA.
<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<om:MetaModel MajorVersion="1" MinorVersion="3" Core="2b131234-7959-458d-834f-2dc0769ce683" ScheduleModel="66366196-361d-448d-976f-cab5e87496d2" xmlns:om="http://schemas.microsoft.com/BizTalk/2003/DesignerData">
    <om:Element Type="Module" OID="89cfb1f4-547b-43cc-ba3e-394a686d2672" LowerBound="1.1" HigherBound="276.1">
        <om:Property Name="ReportToAnalyst" Value="True" />
        <om:Property Name="Name" Value="A2A.EAI.INT_GME.Processes" />
        <om:Property Name="Signal" Value="False" />
        <om:Element Type="PortType" OID="2cfbaf29-fbe6-4224-801c-33bef51aaf39" ParentLink="Module_PortType" LowerBound="16.1" HigherBound="23.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrInType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="c31f7f6d-d13f-4dc9-8d84-9b9ebd4a6d9a" ParentLink="PortType_OperationDeclaration" LowerBound="18.1" HigherBound="22.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Receive" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="51f7b56c-7002-4927-aed6-9b078986f219" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="20.13" HigherBound="20.28">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="ce64ccf5-e63e-42f0-b206-782f95add9d3" ParentLink="Module_PortType" LowerBound="23.1" HigherBound="30.1">
            <om:Property Name="Synchronous" Value="True" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="EaiVirtusReplyToIpexOfferteMrrOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="8e9c42ff-243e-4583-8429-11eb29ba95fd" ParentLink="PortType_OperationDeclaration" LowerBound="25.1" HigherBound="29.1">
                <om:Property Name="OperationType" Value="RequestResponse" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrr" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="f66380c9-bed6-417a-b018-cc0e741c3ee4" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="27.13" HigherBound="27.51">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrRequestType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="MessageRef" OID="679be1cb-c033-4e1b-94cc-91a12a00309a" ParentLink="OperationDeclaration_ResponseMessageRef" LowerBound="27.53" HigherBound="27.92">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrResponseType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Response" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="PortType" OID="5da71924-985b-4611-bc49-be39de5413cc" ParentLink="Module_PortType" LowerBound="30.1" HigherBound="37.1">
            <om:Property Name="Synchronous" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="IpexOfferteMrrOutType" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="OperationDeclaration" OID="19505891-2455-43cd-a97e-56ad825a5832" ParentLink="PortType_OperationDeclaration" LowerBound="32.1" HigherBound="36.1">
                <om:Property Name="OperationType" Value="OneWay" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Send" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="MessageRef" OID="55b6eb8b-a325-4555-b35c-99c31e641034" ParentLink="OperationDeclaration_RequestMessageRef" LowerBound="34.13" HigherBound="34.32">
                    <om:Property Name="Ref" Value="A2A.EAI.INT_GME.Processes.GmePipeDocumentType" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Request" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="3b17d72c-a283-431b-87a3-373cccf812e3" ParentLink="Module_MessageType" LowerBound="4.1" HigherBound="8.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrRequestType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="7ed27c5f-e097-48bb-b0b0-e39a3204546b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="6.1" HigherBound="7.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMrrTypedProcedure.VirtusReplyToIpexOfferteMrr" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="931e0665-378d-4214-9b70-1620628cef1c" ParentLink="Module_MessageType" LowerBound="8.1" HigherBound="12.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrResponseType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="5040fd7c-e2db-452b-96a5-29b4e3f06f1b" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="10.1" HigherBound="11.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMrrTypedProcedure.VirtusReplyToIpexOfferteMrrResponse" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="MultipartMessageType" OID="de673ea7-4341-4ad1-baca-18d910debdc4" ParentLink="Module_MessageType" LowerBound="12.1" HigherBound="16.1">
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrEnvelopeType" />
            <om:Property Name="Signal" Value="True" />
            <om:Element Type="PartDeclaration" OID="3d88d4de-85fd-4ab9-ba0c-e8bd82085456" ParentLink="MultipartMessageType_PartDeclaration" LowerBound="14.1" HigherBound="15.1">
                <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Schemas.Virtus.VirtusReplyToIpexOfferteMrrEnvelope" />
                <om:Property Name="IsBodyPart" Value="True" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="parameters" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
        </om:Element>
        <om:Element Type="ServiceDeclaration" OID="93ed5200-068f-4d69-8914-c720c3fdb175" ParentLink="Module_ServiceDeclaration" LowerBound="37.1" HigherBound="275.1">
            <om:Property Name="InitializedTransactionType" Value="False" />
            <om:Property Name="IsInvokable" Value="False" />
            <om:Property Name="TypeModifier" Value="Internal" />
            <om:Property Name="ReportToAnalyst" Value="True" />
            <om:Property Name="Name" Value="VirtusToIpexOfferteMrr" />
            <om:Property Name="Signal" Value="False" />
            <om:Element Type="VariableDeclaration" OID="1cb3b301-6291-40db-9289-653972181370" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="56.1" HigherBound="57.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="warningMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="523765b4-c7eb-464d-a77b-b6633020ce81" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="57.1" HigherBound="58.1">
                <om:Property Name="InitialValue" Value="true" />
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.Boolean" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="sendNotification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="6473d3d2-06ba-4b9e-bf7f-907d9c2d1a36" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="58.1" HigherBound="59.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Xml.XmlDocument" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="XmlEnvelopeMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="9284404d-a3e5-4c9b-b0f2-19aec0499023" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="59.1" HigherBound="60.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ProcessResult.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="resultCode" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="26c38824-d014-4e21-a269-ce9557251bf6" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="60.1" HigherBound="61.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="outputFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="75813faa-9e85-43fc-b9bb-339dcb41285e" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="61.1" HigherBound="62.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="originalFileName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b4c31a2d-5225-4a41-a045-7046b6b82886" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="62.1" HigherBound="63.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="flowName" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="aa91e21e-df27-4a11-9254-c2970e88fd38" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="63.1" HigherBound="64.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.FaultCategory.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="faultCategory" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="c58fd7f8-52bd-410d-8388-2060c8f94d09" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="64.1" HigherBound="65.1">
                <om:Property Name="UseDefaultConstructor" Value="True" />
                <om:Property Name="Type" Value="System.Text.StringBuilder" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="errorMessage" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="f7b6731e-8710-4093-a1b1-cb4430816ccc" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="65.1" HigherBound="66.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.DateTimeOffset" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="dataInizio" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="b148b8d4-3fbf-4df8-a426-c7099b9b639d" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="66.1" HigherBound="67.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="Microsys.EAI.Framework.Services.ConnectedSystem.Code" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="connectedSystem" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="1513077e-13af-4b9b-890e-529ea4542fc9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="67.1" HigherBound="68.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="archiveFilePath" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="a843e00f-bae8-429d-9ad1-3a5a62aaaec9" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="68.1" HigherBound="69.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityInstanceId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="VariableDeclaration" OID="ebfea0f4-d3d5-4a01-b271-cd903261809f" ParentLink="ServiceDeclaration_VariableDeclaration" LowerBound="69.1" HigherBound="70.1">
                <om:Property Name="UseDefaultConstructor" Value="False" />
                <om:Property Name="Type" Value="System.String" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="activityId" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="5d6274ac-e94c-4194-bbc6-2df4cea62bb5" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="50.1" HigherBound="51.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReply" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="22834abf-dc79-41e8-98b8-0f9d846f2fe8" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="51.1" HigherBound="52.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrRequestType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrRequest" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="1a285a8d-d1f5-4b17-a3a3-34fdaae6b029" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="52.1" HigherBound="53.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrResponseType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrResponse" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="fecd0721-7ea7-4ef4-af29-0ba5bacf8041" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="53.1" HigherBound="54.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrEnvelopeType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrEnvelope" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="e61ee935-fe65-4b86-99aa-87797a463688" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="54.1" HigherBound="55.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.NotificationType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="Notification" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="MessageDeclaration" OID="6c932f32-f46b-46bc-ae6f-346511994745" ParentLink="ServiceDeclaration_MessageDeclaration" LowerBound="55.1" HigherBound="56.1">
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.GmePipeDocumentType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="GmePipeDocument" />
                <om:Property Name="Signal" Value="True" />
            </om:Element>
            <om:Element Type="ServiceBody" OID="66849ccf-26cb-47ee-96c6-e400813caaed" ParentLink="ServiceDeclaration_ServiceBody">
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="Receive" OID="408f714a-354f-4111-851c-d8cab200686b" ParentLink="ServiceBody_Statement" LowerBound="72.1" HigherBound="84.1">
                    <om:Property Name="Activate" Value="True" />
                    <om:Property Name="PortName" Value="VirtusReplyToIpexOfferteMrrIn" />
                    <om:Property Name="MessageName" Value="VirtusReply" />
                    <om:Property Name="OperationName" Value="Receive" />
                    <om:Property Name="OperationMessageName" Value="Request" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Start" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="VariableAssignment" OID="dcae4f80-1423-46be-b8f1-758fb8d570a2" ParentLink="ServiceBody_Statement" LowerBound="84.1" HigherBound="101.1">
                    <om:Property Name="Expression" Value="activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();&#xD;&#xA;&#xD;&#xA;errorMessage = new System.Text.StringBuilder();&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;&#xD;&#xA;&#xD;&#xA;flowName = &quot;IPEX_OFFERTE_MRR&quot;;&#xD;&#xA;&#xD;&#xA;originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VirtusReply);&#xD;&#xA;archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VirtusReply);&#xD;&#xA;activityId = A2A.EAI.Common.Services.VirtusServices.GetRelatedActivityByFileName(originalFileName, activityInstanceId);&#xD;&#xA;&#xD;&#xA;outputFileName = System.String.Empty;&#xD;&#xA;sendNotification = false;&#xD;&#xA;warningMessage = System.String.Empty;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Initialize Environment" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
                <om:Element Type="Decision" OID="4da9a6cc-8636-4d14-8f32-684aa4f2efb8" ParentLink="ServiceBody_Statement" LowerBound="101.1" HigherBound="107.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="IPEX_OFFERTE_MRR or IPEX_OFFERTE_MRR_SF" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="be347b56-9102-410b-a8f7-cadba4c2050a" ParentLink="ReallyComplexStatement_Branch" LowerBound="102.13" HigherBound="107.1">
                        <om:Property Name="Expression" Value="originalFileName.Contains(&quot;_SF_&quot;)" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="IPEX_OFFERTE_MSD_SF" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="43086868-1c7c-4ca1-8bd9-9ef7b7a57fd5" ParentLink="ComplexStatement_Statement" LowerBound="104.1" HigherBound="106.1">
                            <om:Property Name="Expression" Value="flowName = &quot;IPEX_OFFERTE_MRR_SF&quot;;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Set FlowName" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="0beef693-a2e4-436d-8f2c-83170dc07278" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="beaad5b3-705c-43f6-8171-6a0cb8a89b06" ParentLink="ServiceBody_Statement" LowerBound="107.1" HigherBound="119.1">
                    <om:Property Name="Expression" Value="dataInizio = System.DateTimeOffset.Now;&#xD;&#xA;&#xD;&#xA;Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Data Inizio&quot;, dataInizio,&#xD;&#xA;&quot;instanceId&quot;, activityInstanceId,&#xD;&#xA;&quot;Flusso&quot;, flowName,&#xD;&#xA;&quot;Nome File Input&quot;, System.IO.Path.GetFileName(originalFileName),&#xD;&#xA;&quot;Percorso Backup&quot;, archiveFilePath,&#xD;&#xA;&quot;Percorso Sorgente&quot;, System.IO.Path.GetDirectoryName(originalFileName),&#xD;&#xA;&quot;Inviato&quot;, &quot;No&quot;&#xD;&#xA;);&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM Begin" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
                <om:Element Type="Scope" OID="0f5e1b44-df73-4872-b79e-61178d26c66a" ParentLink="ServiceBody_Statement" LowerBound="119.1" HigherBound="229.1">
                    <om:Property Name="InitializedTransactionType" Value="True" />
                    <om:Property Name="IsSynchronized" Value="False" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Main Transaction" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="Construct" OID="c2a3726b-95a0-4403-a2e4-d8b19b9daa9c" ParentLink="ComplexStatement_Statement" LowerBound="124.1" HigherBound="130.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Setup EAI Transform" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="Transform" OID="23c60dba-b873-4658-97a6-a4d2c11e5f6e" ParentLink="ComplexStatement_Statement" LowerBound="127.1" HigherBound="129.1">
                            <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMrr.VirtusReplyToEaiIpexOfferteMrr" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Setup EAI" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="MessagePartRef" OID="fb0f3a1c-9d1c-4ee3-9bc8-0b1660e6fc73" ParentLink="Transform_InputMessagePartRef" LowerBound="128.171" HigherBound="128.193">
                                <om:Property Name="MessageRef" Value="VirtusReply" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_5" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                            <om:Element Type="MessagePartRef" OID="4532a4e1-2cc4-4eaf-b5ec-132a6c5f2705" ParentLink="Transform_OutputMessagePartRef" LowerBound="128.36" HigherBound="128.81">
                                <om:Property Name="MessageRef" Value="VirtusReplyToIpexOfferteMrrRequest" />
                                <om:Property Name="PartRef" Value="parameters" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="MessagePartReference_6" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="MessageRef" OID="f4ce2c89-9324-4cd1-8280-cd88f855578e" ParentLink="Construct_MessageRef" LowerBound="125.31" HigherBound="125.65">
                            <om:Property Name="Ref" Value="VirtusReplyToIpexOfferteMrrRequest" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Send" OID="0ed7902d-9342-4430-b017-7fcdafa8cf5c" ParentLink="ComplexStatement_Statement" LowerBound="130.1" HigherBound="132.1">
                        <om:Property Name="PortName" Value="EaiVirtusReplyToIpexOfferteMrrOut" />
                        <om:Property Name="MessageName" Value="VirtusReplyToIpexOfferteMrrRequest" />
                        <om:Property Name="OperationName" Value="VirtusReplyToIpexOfferteMrr" />
                        <om:Property Name="OperationMessageName" Value="Request" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Send" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Receive" OID="f5326673-b9db-465f-8d97-c6af250c3c57" ParentLink="ComplexStatement_Statement" LowerBound="132.1" HigherBound="134.1">
                        <om:Property Name="Activate" Value="False" />
                        <om:Property Name="PortName" Value="EaiVirtusReplyToIpexOfferteMrrOut" />
                        <om:Property Name="MessageName" Value="VirtusReplyToIpexOfferteMrrResponse" />
                        <om:Property Name="OperationName" Value="VirtusReplyToIpexOfferteMrr" />
                        <om:Property Name="OperationMessageName" Value="Response" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Receive" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="20e3b615-35a1-41cf-854c-bc87bfea04b0" ParentLink="ComplexStatement_Statement" LowerBound="134.1" HigherBound="136.1">
                        <om:Property Name="Expression" Value="XmlEnvelopeMessage.LoadXml(VirtusReplyToIpexOfferteMrrResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.Envelope);&#xD;&#xA;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Get XmlEnvelopeMessage" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Construct" OID="4e031ab9-6ccc-4f37-8004-2f4ee5240042" ParentLink="ComplexStatement_Statement" LowerBound="136.1" HigherBound="142.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Build Envelope" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="MessageAssignment" OID="8132a3c9-367a-470c-b81a-b5e10e32ed9f" ParentLink="ComplexStatement_Statement" LowerBound="139.1" HigherBound="141.1">
                            <om:Property Name="Expression" Value="VirtusReplyToIpexOfferteMrrEnvelope.parameters = XmlEnvelopeMessage;" />
                            <om:Property Name="ReportToAnalyst" Value="False" />
                            <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrEnvelope" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                        <om:Element Type="MessageRef" OID="e42594e8-052e-43e3-b9ed-68ffe1347e90" ParentLink="Construct_MessageRef" LowerBound="137.31" HigherBound="137.66">
                            <om:Property Name="Ref" Value="VirtusReplyToIpexOfferteMrrEnvelope" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="VariableAssignment" OID="362bec97-5a25-4545-a963-eb8407cedb22" ParentLink="ComplexStatement_Statement" LowerBound="142.1" HigherBound="145.1">
                        <om:Property Name="Expression" Value="outputFileName = VirtusReplyToIpexOfferteMrrEnvelope.parameters.fileName;&#xD;&#xA;warningMessage = VirtusReplyToIpexOfferteMrrEnvelope.parameters.warningMessage;" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Set fileName &amp; warnMessage" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="Decision" OID="424c93c6-6c32-4970-87f0-285a45ee3a22" ParentLink="ComplexStatement_Statement" LowerBound="145.1" HigherBound="160.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="warningMessage reported" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="9228df84-300a-4f58-8ab8-965e0987878c" ParentLink="ReallyComplexStatement_Branch" LowerBound="146.21" HigherBound="153.1">
                            <om:Property Name="Expression" Value="warningMessage.StartsWith(&quot;ERROR:&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Blocking" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="VariableAssignment" OID="c45e7863-09fc-49ac-abd4-41f852a4dc67" ParentLink="ComplexStatement_Statement" LowerBound="148.1" HigherBound="152.1">
                                <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;errorMessage.Append(warningMessage);&#xD;&#xA;sendNotification = true;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Manage Errors" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="ac93ce47-e680-44bf-8f8d-89a5b6566573" ParentLink="ReallyComplexStatement_Branch" LowerBound="153.26" HigherBound="160.1">
                            <om:Property Name="Expression" Value="warningMessage.StartsWith(&quot;WARNING:&quot;)" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Warnings" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="d4885985-2317-4318-9097-27945ced4409" ParentLink="ComplexStatement_Statement" LowerBound="155.1" HigherBound="159.1">
                                <om:Property Name="Expression" Value="resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;&#xD;&#xA;errorMessage.Append(warningMessage);&#xD;&#xA;sendNotification = false;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Manage Errors" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="0d2de151-2fd3-4346-a550-a87c0285fba0" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Decision" OID="4247e13f-4f72-4129-a372-17f5a805250c" ParentLink="ComplexStatement_Statement" LowerBound="160.1" HigherBound="186.1">
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Offerte Presenti" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="DecisionBranch" OID="788d2d5a-e0a4-441b-95b0-650f3bbd9d35" ParentLink="ReallyComplexStatement_Branch" LowerBound="161.21" HigherBound="179.1">
                            <om:Property Name="Expression" Value="VirtusReplyToIpexOfferteMrrEnvelope.parameters.numOfMessages &gt; 0&#xD;&#xA;" />
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Yes" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="Construct" OID="dca16c87-5330-4e64-91d5-b9fb6cba1701" ParentLink="ComplexStatement_Statement" LowerBound="163.1" HigherBound="171.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Setup BidSubmittal" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageRef" OID="ae91ce88-73c0-4824-a59e-89e5147523af" ParentLink="Construct_MessageRef" LowerBound="164.35" HigherBound="164.50">
                                    <om:Property Name="Ref" Value="GmePipeDocument" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                                <om:Element Type="Transform" OID="5920af7c-b89e-4982-a9b2-02e4fdee8ad5" ParentLink="ComplexStatement_Statement" LowerBound="166.1" HigherBound="168.1">
                                    <om:Property Name="ClassName" Value="A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMrr.VirtusReplyToIpexOfferteMrrEnvelopeToGmeBidSubmittal" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Name" Value="Setup MSG" />
                                    <om:Property Name="Signal" Value="False" />
                                    <om:Element Type="MessagePartRef" OID="0eebbb28-3d4f-45fa-8e50-cdc12037bf79" ParentLink="Transform_InputMessagePartRef" LowerBound="167.177" HigherBound="167.223">
                                        <om:Property Name="MessageRef" Value="VirtusReplyToIpexOfferteMrrEnvelope" />
                                        <om:Property Name="PartRef" Value="parameters" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_1" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                    <om:Element Type="MessagePartRef" OID="c93c9783-66e6-4f85-b996-b457220c4949" ParentLink="Transform_OutputMessagePartRef" LowerBound="167.40" HigherBound="167.65">
                                        <om:Property Name="MessageRef" Value="GmePipeDocument" />
                                        <om:Property Name="PartRef" Value="parameter" />
                                        <om:Property Name="ReportToAnalyst" Value="True" />
                                        <om:Property Name="Name" Value="MessagePartReference_2" />
                                        <om:Property Name="Signal" Value="False" />
                                    </om:Element>
                                </om:Element>
                                <om:Element Type="MessageAssignment" OID="b29c5d7f-7cfa-4954-a881-ae5386cbe0fb" ParentLink="ComplexStatement_Statement" LowerBound="168.1" HigherBound="170.1">
                                    <om:Property Name="Expression" Value="GmePipeDocument(FILE.ReceivedFileName) = outputFileName;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="File Name" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="f42b6427-56fc-4514-a8c3-839897df6ef4" ParentLink="ComplexStatement_Statement" LowerBound="171.1" HigherBound="173.1">
                                <om:Property Name="PortName" Value="IpexOfferteMrrOut" />
                                <om:Property Name="MessageName" Value="GmePipeDocument" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                            <om:Element Type="VariableAssignment" OID="b8352864-6d4e-486d-95dc-c51cbdc622c3" ParentLink="ComplexStatement_Statement" LowerBound="173.1" HigherBound="178.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Inviato&quot;, &quot;Si&quot;,&#xD;&#xA;&quot;Nome File Output&quot;, outputFileName&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                        <om:Element Type="DecisionBranch" OID="7a0ee5dc-3854-4eab-a0fa-07d24b6b1584" ParentLink="ReallyComplexStatement_Branch">
                            <om:Property Name="IsGhostBranch" Value="True" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Else" />
                            <om:Property Name="Signal" Value="False" />
                            <om:Element Type="VariableAssignment" OID="02737155-1f32-4937-b20b-405a49cc6f5f" ParentLink="ComplexStatement_Statement" LowerBound="181.1" HigherBound="185.1">
                                <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;Inviato&quot;, &quot;No (Empty)&quot;&#xD;&#xA;);&#xD;&#xA;" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="BAM Update" />
                                <om:Property Name="Signal" Value="False" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="263b2959-2dea-455a-bbd4-8a9d5b67b7cd" ParentLink="Scope_Catch" LowerBound="189.1" HigherBound="201.1">
                        <om:Property Name="ExceptionName" Value="soapExc" />
                        <om:Property Name="ExceptionType" Value="System.Web.Services.Protocols.SoapException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Soap Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="41870308-1acf-48f5-a981-67af31219134" ParentLink="Catch_Statement" LowerBound="192.1" HigherBound="200.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;&#xD;&#xA;sendNotification = true;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="0bf62ee1-5080-46da-93d4-86d2524fa440" ParentLink="Scope_Catch" LowerBound="201.1" HigherBound="214.1">
                        <om:Property Name="ExceptionName" Value="deliveryExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.DeliveryFailureException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Delivery Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="b37fb4f9-beff-48fa-81e9-b7559385b5a3" ParentLink="Catch_Statement" LowerBound="204.1" HigherBound="213.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(deliveryExc.Message);&#xD;&#xA;errorMessage.Append(deliveryExc.ErrorDescription);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;&#xD;&#xA;sendNotification = true;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="True" />
                        </om:Element>
                    </om:Element>
                    <om:Element Type="Catch" OID="56a550f5-6e3e-4a5d-b1b9-ae69d8c2455a" ParentLink="Scope_Catch" LowerBound="214.1" HigherBound="227.1">
                        <om:Property Name="ExceptionName" Value="systemExc" />
                        <om:Property Name="ExceptionType" Value="Microsoft.XLANGs.BaseTypes.XLANGsException" />
                        <om:Property Name="IsFaultMessage" Value="False" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="System Exception" />
                        <om:Property Name="Signal" Value="True" />
                        <om:Element Type="VariableAssignment" OID="eb57a445-d177-4c77-be33-9fc91a4337e9" ParentLink="Catch_Statement" LowerBound="217.1" HigherBound="226.1">
                            <om:Property Name="Expression" Value="errorMessage.AppendFormat(&quot;INT_GME - '{0}' - Errore in fase di elaborazione dati. &quot;, flowName);&#xD;&#xA;errorMessage.Append(systemExc.Message);&#xD;&#xA;&#xD;&#xA;resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;&#xD;&#xA;connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;&#xD;&#xA;faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;&#xD;&#xA;sendNotification = true;&#xD;&#xA;&#xD;&#xA;" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="WriteLog" />
                            <om:Property Name="Signal" Value="False" />
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="Decision" OID="336913ff-5b86-4ef2-b1f9-8821a40c05ae" ParentLink="ServiceBody_Statement" LowerBound="229.1" HigherBound="265.1">
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="Process Result" />
                    <om:Property Name="Signal" Value="True" />
                    <om:Element Type="DecisionBranch" OID="58a585b2-72a9-4496-b0d2-8cc3f1735ce0" ParentLink="ReallyComplexStatement_Branch" LowerBound="230.13" HigherBound="233.1">
                        <om:Property Name="Expression" Value="resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="OK" />
                        <om:Property Name="Signal" Value="True" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="823cefce-371b-40f0-8998-fcfbc644de75" ParentLink="ReallyComplexStatement_Branch" LowerBound="233.18" HigherBound="236.1">
                        <om:Property Name="Expression" Value="sendNotification == false" />
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Warnings" />
                        <om:Property Name="Signal" Value="False" />
                    </om:Element>
                    <om:Element Type="DecisionBranch" OID="9a7b549c-bb9c-40ad-8b3c-a18924a4f489" ParentLink="ReallyComplexStatement_Branch">
                        <om:Property Name="IsGhostBranch" Value="True" />
                        <om:Property Name="ReportToAnalyst" Value="True" />
                        <om:Property Name="Name" Value="Else" />
                        <om:Property Name="Signal" Value="False" />
                        <om:Element Type="Scope" OID="f38ee884-d574-4e59-a539-39d1d5cfbdf3" ParentLink="ComplexStatement_Statement" LowerBound="238.1" HigherBound="264.1">
                            <om:Property Name="InitializedTransactionType" Value="True" />
                            <om:Property Name="IsSynchronized" Value="False" />
                            <om:Property Name="ReportToAnalyst" Value="True" />
                            <om:Property Name="Name" Value="Mail Notification" />
                            <om:Property Name="Signal" Value="True" />
                            <om:Element Type="Construct" OID="dde8d670-9028-4d54-bdfc-06bc77c38adf" ParentLink="ComplexStatement_Statement" LowerBound="243.1" HigherBound="260.1">
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Failure Notification" />
                                <om:Property Name="Signal" Value="True" />
                                <om:Element Type="MessageAssignment" OID="9c048ad9-ec53-4535-89e1-ad6854a2256d" ParentLink="ComplexStatement_Statement" LowerBound="246.1" HigherBound="259.1">
                                    <om:Property Name="Expression" Value="Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();&#xD;&#xA;&#xD;&#xA;Notification.parameter.applicationName = A2A.EAI.INT_GME.Services.ProcessServices.ApplicationName;&#xD;&#xA;Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();&#xD;&#xA;Notification.parameter.flowGroup = A2A.EAI.INT_GME.Services.ProcessServices.FlowGroup;&#xD;&#xA;Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, &quot; - &quot;, flowName);&#xD;&#xA;Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);&#xD;&#xA;Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();&#xD;&#xA;Notification.parameter.messageText = errorMessage.ToString();&#xD;&#xA;Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;&#xD;&#xA;Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);&#xD;&#xA;" />
                                    <om:Property Name="ReportToAnalyst" Value="False" />
                                    <om:Property Name="Name" Value="Setup Notification" />
                                    <om:Property Name="Signal" Value="True" />
                                </om:Element>
                                <om:Element Type="MessageRef" OID="4d662591-04dd-4b09-a0c1-3d020fc653fe" ParentLink="Construct_MessageRef" LowerBound="244.35" HigherBound="244.47">
                                    <om:Property Name="Ref" Value="Notification" />
                                    <om:Property Name="ReportToAnalyst" Value="True" />
                                    <om:Property Name="Signal" Value="False" />
                                </om:Element>
                            </om:Element>
                            <om:Element Type="Send" OID="e476b495-b3c0-43f6-8829-493cb4b8e73a" ParentLink="ComplexStatement_Statement" LowerBound="260.1" HigherBound="262.1">
                                <om:Property Name="PortName" Value="NotificationOut" />
                                <om:Property Name="MessageName" Value="Notification" />
                                <om:Property Name="OperationName" Value="Send" />
                                <om:Property Name="OperationMessageName" Value="Request" />
                                <om:Property Name="ReportToAnalyst" Value="True" />
                                <om:Property Name="Name" Value="Send Notification" />
                                <om:Property Name="Signal" Value="True" />
                            </om:Element>
                        </om:Element>
                    </om:Element>
                </om:Element>
                <om:Element Type="VariableAssignment" OID="e031830a-bc0c-46f1-ad3a-6815158c5ef6" ParentLink="ServiceBody_Statement" LowerBound="265.1" HigherBound="273.1">
                    <om:Property Name="Expression" Value="Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,&#xD;&#xA;&quot;errorMessage&quot;, errorMessage.ToString(), &#xD;&#xA;&quot;resultCode&quot;, Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),&#xD;&#xA;&quot;Data Fine&quot;, System.DateTimeOffset.Now,&#xD;&#xA;&quot;Durata Totale&quot;, A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)&#xD;&#xA;);&#xD;&#xA;&#xD;&#xA;" />
                    <om:Property Name="ReportToAnalyst" Value="True" />
                    <om:Property Name="Name" Value="BAM End" />
                    <om:Property Name="Signal" Value="True" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="3ffc499e-9fce-4149-9058-a2c2ee996544" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="40.1" HigherBound="42.1">
                <om:Property Name="PortModifier" Value="Implements" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="-1" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.VirtusReplyToIpexOfferteMrrInType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="VirtusReplyToIpexOfferteMrrIn" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="LogicalBindingAttribute" OID="efcd4f92-6d6d-429a-b139-840f2ec18fe2" ParentLink="PortDeclaration_CLRAttribute" LowerBound="40.1" HigherBound="41.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9e7d4a51-5831-4f2f-9030-dbb32a2ccf2a" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="42.1" HigherBound="45.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="50" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.EaiVirtusReplyToIpexOfferteMrrOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="EaiVirtusReplyToIpexOfferteMrrOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="513fd565-d95f-4611-ba75-a0faa44dece0" ParentLink="PortDeclaration_CLRAttribute" LowerBound="42.1" HigherBound="43.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="9fbaa5f8-5b16-40b5-9fd5-c942b768238e" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="45.1" HigherBound="48.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Left" />
                <om:Property Name="PortIndex" Value="98" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="Transmitted" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.IpexOfferteMrrOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="IpexOfferteMrrOut" />
                <om:Property Name="Signal" Value="True" />
                <om:Element Type="LogicalBindingAttribute" OID="9e7bbe84-2ba2-49c8-aeea-622003f081f7" ParentLink="PortDeclaration_CLRAttribute" LowerBound="45.1" HigherBound="46.1">
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
            <om:Element Type="PortDeclaration" OID="ce23585b-12f5-40ec-81a3-0b2eb2b5a913" ParentLink="ServiceDeclaration_PortDeclaration" LowerBound="48.1" HigherBound="50.1">
                <om:Property Name="PortModifier" Value="Uses" />
                <om:Property Name="Orientation" Value="Right" />
                <om:Property Name="PortIndex" Value="192" />
                <om:Property Name="IsWebPort" Value="False" />
                <om:Property Name="OrderedDelivery" Value="False" />
                <om:Property Name="DeliveryNotification" Value="None" />
                <om:Property Name="Type" Value="A2A.EAI.INT_GME.Processes.NotificationOutType" />
                <om:Property Name="ParamDirection" Value="In" />
                <om:Property Name="ReportToAnalyst" Value="True" />
                <om:Property Name="Name" Value="NotificationOut" />
                <om:Property Name="Signal" Value="False" />
                <om:Element Type="DirectBindingAttribute" OID="b4ec2bcb-b472-40c4-ae30-7219c219f720" ParentLink="PortDeclaration_CLRAttribute" LowerBound="48.1" HigherBound="49.1">
                    <om:Property Name="DirectBindingType" Value="MessageBox" />
                    <om:Property Name="Signal" Value="False" />
                </om:Element>
            </om:Element>
        </om:Element>
    </om:Element>
</om:MetaModel>
#endif // __DESIGNER_DATA
[Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
module A2A.EAI.INT_GME.Processes
{
    internal messagetype VirtusReplyToIpexOfferteMrrRequestType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMrrTypedProcedure.VirtusReplyToIpexOfferteMrr parameters;
    };
    internal messagetype VirtusReplyToIpexOfferteMrrResponseType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.EAI.VirtusReplyToIpexOfferteMrrTypedProcedure.VirtusReplyToIpexOfferteMrrResponse parameters;
    };
    internal messagetype VirtusReplyToIpexOfferteMrrEnvelopeType
    {
        body A2A.EAI.INT_GME.Messaging.Schemas.Virtus.VirtusReplyToIpexOfferteMrrEnvelope parameters;
    };
    internal porttype VirtusReplyToIpexOfferteMrrInType
    {
        oneway Receive
        {
            VirtusReplyType
        };
    };
    internal porttype EaiVirtusReplyToIpexOfferteMrrOutType
    {
        requestresponse VirtusReplyToIpexOfferteMrr
        {
            VirtusReplyToIpexOfferteMrrRequestType, VirtusReplyToIpexOfferteMrrResponseType
        };
    };
    internal porttype IpexOfferteMrrOutType
    {
        oneway Send
        {
            GmePipeDocumentType
        };
    };
    [Microsoft.XLANGs.BaseTypes.BPELExportable(false)]
    internal service VirtusToIpexOfferteMrr
    {
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        port implements VirtusReplyToIpexOfferteMrrInType VirtusReplyToIpexOfferteMrrIn;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses EaiVirtusReplyToIpexOfferteMrrOutType EaiVirtusReplyToIpexOfferteMrrOut;
        [Microsoft.XLANGs.BaseTypes.LogicalBinding()]
        [Microsoft.XLANGs.BaseTypes.DeliveryNotification(Microsoft.XLANGs.BaseTypes.DeliveryNotification.NotificationLevel.Transmitted)]
        port uses IpexOfferteMrrOutType IpexOfferteMrrOut;
        [Microsoft.XLANGs.BaseTypes.DirectBinding()]
        port uses NotificationOutType NotificationOut;
        message VirtusReplyType VirtusReply;
        message VirtusReplyToIpexOfferteMrrRequestType VirtusReplyToIpexOfferteMrrRequest;
        message VirtusReplyToIpexOfferteMrrResponseType VirtusReplyToIpexOfferteMrrResponse;
        message VirtusReplyToIpexOfferteMrrEnvelopeType VirtusReplyToIpexOfferteMrrEnvelope;
        message NotificationType Notification;
        message GmePipeDocumentType GmePipeDocument;
        System.String warningMessage;
        System.Boolean sendNotification;
        System.Xml.XmlDocument XmlEnvelopeMessage;
        Microsys.EAI.Framework.Services.ProcessResult.Code resultCode;
        System.String outputFileName;
        System.String originalFileName;
        System.String flowName;
        Microsys.EAI.Framework.Services.FaultCategory.Code faultCategory;
        System.Text.StringBuilder errorMessage;
        System.DateTimeOffset dataInizio;
        Microsys.EAI.Framework.Services.ConnectedSystem.Code connectedSystem;
        System.String archiveFilePath;
        System.String activityInstanceId;
        System.String activityId;
        body ()
        {
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("408f714a-354f-4111-851c-d8cab200686b")]
            activate receive (VirtusReplyToIpexOfferteMrrIn.Receive, VirtusReply);
            warningMessage = "";
            sendNotification = true;
            XmlEnvelopeMessage = new System.Xml.XmlDocument();
            outputFileName = "";
            originalFileName = "";
            flowName = "";
            errorMessage = new System.Text.StringBuilder();
            archiveFilePath = "";
            activityInstanceId = "";
            activityId = "";
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("dcae4f80-1423-46be-b8f1-758fb8d570a2")]
            activityInstanceId = Microsys.EAI.Framework.Services.ProcessServices.GetActivityId();
            
            errorMessage = new System.Text.StringBuilder();
            resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded;
            connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
            faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.None;
            
            flowName = "IPEX_OFFERTE_MRR";
            
            originalFileName = Microsys.EAI.Framework.Services.ProcessServices.GetReceivedFileName(VirtusReply);
            archiveFilePath = Microsys.EAI.Framework.Schemas.CustomContextProperties.GetArchivePath(VirtusReply);
            activityId = A2A.EAI.Common.Services.VirtusServices.GetRelatedActivityByFileName(originalFileName, activityInstanceId);
            
            outputFileName = System.String.Empty;
            sendNotification = false;
            warningMessage = System.String.Empty;
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("4da9a6cc-8636-4d14-8f32-684aa4f2efb8")]
            if (originalFileName.Contains("_SF_"))
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("43086868-1c7c-4ca1-8bd9-9ef7b7a57fd5")]
                flowName = "IPEX_OFFERTE_MRR_SF";
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("beaad5b3-705c-43f6-8171-6a0cb8a89b06")]
            dataInizio = System.DateTimeOffset.Now;
            
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
            "Data Inizio", dataInizio,
            "instanceId", activityInstanceId,
            "Flusso", flowName,
            "Nome File Input", System.IO.Path.GetFileName(originalFileName),
            "Percorso Backup", archiveFilePath,
            "Percorso Sorgente", System.IO.Path.GetDirectoryName(originalFileName),
            "Inviato", "No"
            );
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("0f5e1b44-df73-4872-b79e-61178d26c66a")]
            scope
            {
                body
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("c2a3726b-95a0-4403-a2e4-d8b19b9daa9c")]
                    construct VirtusReplyToIpexOfferteMrrRequest
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("23c60dba-b873-4658-97a6-a4d2c11e5f6e")]
                        transform (VirtusReplyToIpexOfferteMrrRequest.parameters) = A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMrr.VirtusReplyToEaiIpexOfferteMrr (VirtusReply.parameters);
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0ed7902d-9342-4430-b017-7fcdafa8cf5c")]
                    send (EaiVirtusReplyToIpexOfferteMrrOut.VirtusReplyToIpexOfferteMrr, VirtusReplyToIpexOfferteMrrRequest);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("f5326673-b9db-465f-8d97-c6af250c3c57")]
                    receive (EaiVirtusReplyToIpexOfferteMrrOut.VirtusReplyToIpexOfferteMrr, VirtusReplyToIpexOfferteMrrResponse);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("20e3b615-35a1-41cf-854c-bc87bfea04b0")]
                    XmlEnvelopeMessage.LoadXml(VirtusReplyToIpexOfferteMrrResponse.parameters.StoredProcedureResultSet0.StoredProcedureResultSet0.Envelope);
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4e031ab9-6ccc-4f37-8004-2f4ee5240042")]
                    construct VirtusReplyToIpexOfferteMrrEnvelope
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("8132a3c9-367a-470c-b81a-b5e10e32ed9f")]
                        VirtusReplyToIpexOfferteMrrEnvelope.parameters = XmlEnvelopeMessage;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("362bec97-5a25-4545-a963-eb8407cedb22")]
                    outputFileName = VirtusReplyToIpexOfferteMrrEnvelope.parameters.fileName;
                    warningMessage = VirtusReplyToIpexOfferteMrrEnvelope.parameters.warningMessage;
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("424c93c6-6c32-4970-87f0-285a45ee3a22")]
                    if (warningMessage.StartsWith("ERROR:"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("c45e7863-09fc-49ac-abd4-41f852a4dc67")]
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        errorMessage.Append(warningMessage);
                        sendNotification = true;
                    }
                    else if (warningMessage.StartsWith("WARNING:"))
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("d4885985-2317-4318-9097-27945ced4409")]
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.SuccededWithWarnings;
                        errorMessage.Append(warningMessage);
                        sendNotification = false;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("4247e13f-4f72-4129-a372-17f5a805250c")]
                    if (VirtusReplyToIpexOfferteMrrEnvelope.parameters.numOfMessages > 0)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dca16c87-5330-4e64-91d5-b9fb6cba1701")]
                        construct GmePipeDocument
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("5920af7c-b89e-4982-a9b2-02e4fdee8ad5")]
                            transform (GmePipeDocument.parameter) = A2A.EAI.INT_GME.Messaging.Maps.VirtusToIpexOfferteMrr.VirtusReplyToIpexOfferteMrrEnvelopeToGmeBidSubmittal (VirtusReplyToIpexOfferteMrrEnvelope.parameters);
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("b29c5d7f-7cfa-4954-a881-ae5386cbe0fb")]
                            GmePipeDocument(FILE.ReceivedFileName) = outputFileName;
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("f42b6427-56fc-4514-a8c3-839897df6ef4")]
                        send (IpexOfferteMrrOut.Send, GmePipeDocument);
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b8352864-6d4e-486d-95dc-c51cbdc622c3")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
                        "Inviato", "Si",
                        "Nome File Output", outputFileName
                        );
                    }
                    else 
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("02737155-1f32-4937-b20b-405a49cc6f5f")]
                        Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
                        "Inviato", "No (Empty)"
                        );
                    }
                }
                exceptions
                {
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("263b2959-2dea-455a-bbd4-8a9d5b67b7cd")]
                    catch (System.Web.Services.Protocols.SoapException soapExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("41870308-1acf-48f5-a981-67af31219134")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(Microsys.EAI.Framework.Services.LoggingServices.ExtractErrorMessage(soapExc.Message));
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.SQL;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Soap;
                        sendNotification = true;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("0bf62ee1-5080-46da-93d4-86d2524fa440")]
                    catch (Microsoft.XLANGs.BaseTypes.DeliveryFailureException deliveryExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("b37fb4f9-beff-48fa-81e9-b7559385b5a3")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(deliveryExc.Message);
                        errorMessage.Append(deliveryExc.ErrorDescription);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.File;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.Delivery;
                        sendNotification = true;
                    }
                    [Microsoft.XLANGs.BaseTypes.DesignerPosition("56a550f5-6e3e-4a5d-b1b9-ae69d8c2455a")]
                    catch (Microsoft.XLANGs.BaseTypes.XLANGsException systemExc)
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("eb57a445-d177-4c77-be33-9fc91a4337e9")]
                        errorMessage.AppendFormat("INT_GME - '{0}' - Errore in fase di elaborazione dati. ", flowName);
                        errorMessage.Append(systemExc.Message);
                        
                        resultCode = Microsys.EAI.Framework.Services.ProcessResult.Code.Failed;
                        connectedSystem = Microsys.EAI.Framework.Services.ConnectedSystem.Code.Unspecified;
                        faultCategory = Microsys.EAI.Framework.Services.FaultCategory.Code.System;
                        sendNotification = true;
                        
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("336913ff-5b86-4ef2-b1f9-8821a40c05ae")]
            if (resultCode == Microsys.EAI.Framework.Services.ProcessResult.Code.Succeded)
            {
            }
            else if (sendNotification == false)
            {
            }
            else 
            {
                [Microsoft.XLANGs.BaseTypes.DesignerPosition("f38ee884-d574-4e59-a539-39d1d5cfbdf3")]
                scope
                {
                    body
                    {
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("dde8d670-9028-4d54-bdfc-06bc77c38adf")]
                        construct Notification
                        {
                            [Microsoft.XLANGs.BaseTypes.DesignerPosition("9c048ad9-ec53-4535-89e1-ad6854a2256d")]
                            Notification.parameter = new Microsys.EAI.Framework.Schemas.Notification();
                            
                            Notification.parameter.applicationName = A2A.EAI.INT_GME.Services.ProcessServices.ApplicationName;
                            Notification.parameter.flowCode = Microsys.EAI.Framework.Services.ProcessServices.GetOrchestrationName();
                            Notification.parameter.flowGroup = A2A.EAI.INT_GME.Services.ProcessServices.FlowGroup;
                            Notification.parameter.flowDescription = System.String.Concat(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, " - ", flowName);
                            Notification.parameter.messageCategory = Microsys.EAI.Framework.Services.FaultCategory.Description(faultCategory);
                            Notification.parameter.messageFaultCode = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageSeverity = Microsys.EAI.Framework.Services.EventType.Code.Error.ToString();
                            Notification.parameter.messageText = errorMessage.ToString();
                            Notification.parameter.messageTitle = Microsys.EAI.Framework.Services.LoggingServices.MessageTitleFailureNotification;
                            Notification.parameter.sourceIdentifier = Microsys.EAI.Framework.Services.ConnectedSystem.Description(connectedSystem);
                        }
                        [Microsoft.XLANGs.BaseTypes.DesignerPosition("e476b495-b3c0-43f6-8829-493cb4b8e73a")]
                        send (NotificationOut.Send, Notification);
                    }
                }
            }
            [Microsoft.XLANGs.BaseTypes.DesignerPosition("e031830a-bc0c-46f1-ad3a-6815158c5ef6")]
            Microsys.EAI.Framework.Azure.Services.BamHelper.WriteActivity(A2A.EAI.INT_GME.Services.ProcessServices.ActivityNameFromVirtus, activityId,
            "errorMessage", errorMessage.ToString(), 
            "resultCode", Microsys.EAI.Framework.Services.ProcessResult.Description(resultCode),
            "Data Fine", System.DateTimeOffset.Now,
            "Durata Totale", A2A.EAI.Common.Services.ProcessServices.CalculateDuration(dataInizio, System.DateTimeOffset.Now)
            );
            
        }
    }
}

